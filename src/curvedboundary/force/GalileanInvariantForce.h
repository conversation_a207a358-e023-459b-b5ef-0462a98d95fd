#pragma once

#include "../../Definitions.h"
#include "core/math/Vector3.h"
#include "field/Field.h"
#include "stencil_new/D3Q19.h"
#include "../mesh/TriangleMesh.h"
#include <vector>

namespace walberla {
namespace curvedboundary {

// Galilean invariant force calculation for curved boundaries
class GalileanInvariantForce {
public:
    struct ForceResult {
        Vector3<Real> totalForce;
        Vector3<Real> totalTorque;
        Vector3<Real> pressureForce;
        Vector3<Real> viscousForce;
        Real dragCoefficient;
        Real liftCoefficient;
        Real momentCoefficient;
    };
    
    // Different force calculation methods
    enum ForceMethod {
        MOMENTUM_EXCHANGE,      // Standard momentum exchange
        STRESS_INTEGRATION,     // Stress tensor integration
        GALILEAN_INVARIANT     // Galilean invariant formulation
    };
    
    GalileanInvariantForce(Real rho0 = 1.0, Real nu = 0.1);
    
    // Calculate forces on a single body
    ForceResult calculateForces(
        const TriangleMesh& mesh,
        const Field<Real,19>& pdf<PERSON>ield,
        const Field<Real,3>& velField,
        const Field<Real,1>& densField,
        const Vector3<Real>& freeStreamVel,
        Real characteristicLength,
        ForceMethod method = GALILEAN_INVARIANT
    );
    
    // Calculate forces with filtering (for LES)
    ForceResult calculateFilteredForces(
        const TriangleMesh& mesh,
        const Field<Real,19>& pdfField,
        const Field<Real,3>& velField,
        const Field<Real,1>& densField,
        const Vector3<Real>& freeStreamVel,
        Real characteristicLength,
        Real filterWidth,
        ForceMethod method = GALILEAN_INVARIANT
    );
    
    // Set reference values
    void setReferenceValues(Real density, Real viscosity) {
        rho0_ = density;
        nu_ = viscosity;
    }
    
    // Enable/disable force smoothing
    void enableSmoothing(bool enable, Real smoothingFactor = 0.5) {
        useSmoothing_ = enable;
        smoothingFactor_ = smoothingFactor;
    }
    
private:
    Real rho0_;  // Reference density
    Real nu_;    // Kinematic viscosity
    bool useSmoothing_;
    Real smoothingFactor_;
    
    // Previous forces for smoothing
    Vector3<Real> previousForce_;
    Vector3<Real> previousTorque_;
    
    // Momentum exchange method
    Vector3<Real> calculateMomentumExchange(
        const TriangleMesh& mesh,
        const Field<Real,19>& pdfField,
        const std::vector<std::pair<Vector3<Uint>, Uint>>& boundaryLinks
    );
    
    // Stress integration method
    Vector3<Real> calculateStressIntegration(
        const TriangleMesh& mesh,
        const Field<Real,3>& velField,
        const Field<Real,1>& densField
    );
    
    // Galilean invariant method
    Vector3<Real> calculateGalileanInvariant(
        const TriangleMesh& mesh,
        const Field<Real,19>& pdfField,
        const Field<Real,3>& velField,
        const Vector3<Real>& bodyVelocity
    );
    
    // Compute stress tensor at a point
    Matrix3<Real> computeStressTensor(
        Uint x, Uint y, Uint z,
        const Field<Real,19>& pdfField,
        const Field<Real,3>& velField,
        const Field<Real,1>& densField
    );
    
    // Compute velocity gradient tensor
    Matrix3<Real> computeVelocityGradient(
        Uint x, Uint y, Uint z,
        const Field<Real,3>& velField
    );
    
    // Apply exponential smoothing filter
    Vector3<Real> applySmoothing(const Vector3<Real>& currentForce);
    
    // Compute force coefficients
    void computeCoefficients(
        ForceResult& result,
        const Vector3<Real>& freeStreamVel,
        Real characteristicLength
    );
    
    // Get boundary links for momentum exchange
    std::vector<std::pair<Vector3<Uint>, Uint>> getBoundaryLinks(
        const TriangleMesh& mesh,
        const Field<Real,19>& pdfField
    );
};

// Force filtering for LES applications
class ForceFilter {
public:
    enum FilterType {
        BOX,
        GAUSSIAN,
        SPECTRAL
    };
    
    ForceFilter(FilterType type = BOX, Real width = 2.0);
    
    // Apply spatial filtering to force field
    Vector3<Real> filterForce(
        const Vector3<Real>& force,
        const Vector3<Real>& position,
        const Field<Real,3>& forceField
    );
    
    // Apply temporal filtering
    Vector3<Real> temporalFilter(
        const Vector3<Real>& currentForce,
        Real dt
    );
    
private:
    FilterType filterType_;
    Real filterWidth_;
    std::vector<Vector3<Real>> forceHistory_;
    static const size_t maxHistorySize_ = 10;
    
    Real boxKernel(Real r);
    Real gaussianKernel(Real r);
};

} // namespace curvedboundary
} // namespace walberla