
set term pngcairo enhanced fontscale 1.2 size 1280,960
set output "ux.png"


set grid 
set xlabel "x [-]" 
set ylabel "ux [-]" 



plot 	"test.txt" u 1:3 w l lw 1 t "Exact", \
	"/home/<USER>/welBerla/d2/uHorizontalExact_0.dat" u ($1-1):3 w lp lw 2 lc 7 t "Exact-octave" , \
	"/home/<USER>/welBerla/d2/TyGreen.txt" u 1:4 w p lw 2 lc 0 t "LBM"


set output "uy.png"
set ylabel "uy [-]"

plot 	"test.txt" u 1:4 w l lw 1 t "Exact", \
	"/home/<USER>/welBerla/d2/uHorizontalExact_0.dat" u ($1-1):4 w lp lw 2 lc 7 t "Exact-octave", \
	"/home/<USER>/welBerla/d2/TyGreen.txt" u 1:5 w p lw 2 lc 0 t "LBM"
