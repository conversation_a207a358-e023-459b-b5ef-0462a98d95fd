#ifndef MAPCONTAINER_HH
#define MAPCONTAINER_HH

template<class Configure>

struct MapContainer{
        typedef typename Configure::Key 	Key 	;
        typedef typename Configure::value_type	value_type 	;//value_type
        typedef typename Configure::MapType	MapType ;
        typedef typename Configure::pair	pair 	;
        typedef typename Configure::Ret       	Ret 	; //pair<iterator, bool>
	typedef typename Configure::Iterator  	Iterator;
	typedef typename Configure::Const_Iterator  	Const_Iterator;	
  public:
	MapContainer () {}// default constructor
	
	



inline Ret      Add(Const_Iterator _Val){return m_map.insert(_Val);}
inline Ret      Add(const value_type* _Val){return m_map.insert(_Val);}

	inline Ret      Add(const value_type&  _Val){return m_map.insert(_Val);}
	inline Iterator Add(Iterator _Where,const value_type& _Val){ 
	  return m_map.insert(_Where,_Val);
	}
	
	inline Iterator find( const Key& _Key)const { return m_map.find(_Key) ;}

  private:
	MapType m_map ;
};

#endif