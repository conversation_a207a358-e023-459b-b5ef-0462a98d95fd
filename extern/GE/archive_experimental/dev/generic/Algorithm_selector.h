#ifndef ALGORITHM_SELECTOR_HH
#define ALGORITHM_SELECTOR_HH

#include <iostream>

using std::cout ;
using std::endl ;

// default
template <int b>
struct Algorithm_selector
{
	template <typename T>
	static void implementation(T& object){
        	std::cout << "Default Implementation :) \n" ;
    	}	
};

// specailisation 1 
template<>
struct Algorithm_selector<1> // true := use optimized version
{
 	template <typename T>
    	static void implementation(T& object)
    	{
        	cout<<"Optimzin version #1 :==) \n";
        	object.optimised_implementation ();
    	}
};

// // Add more as you need :) 
// // specailisation  2
// template<>
// struct Algorithm_selector<2> // true := use optimized version
// {
//     template <typename T>
//         static void implementation(T& object){
//             cout<<"Optimzin version #1 :==) \n";
//             object.optimised_implementation ();
//         }
// };

// class trait
template<typename T>
struct support_optimised_implementation
{
	enum { value = 0};
};

template<>
struct support_optimised_implementation<ObjectB>
{
	enum { value = 1};//          static bool value = true ;
};

 
//generaic function
template <typename T>
void algorithm (T & object)
{
        algorithm_selector< support_optimised_implementation< T >::value  >::implementation(object);
}
 
#endif 

