// g++ main2.cpp // -L/usr/local/lib -I/usr/local/include -lboost_thread  -lboost_system  -lboost_filesystem
// ./a.out
#include <boost/ptr_container/ptr_vector.hpp>
#include <boost/utility.hpp>
#include <algorithm>
#include <iostream>
#include <fstream>
#include <map>
#include <vector>
#include <stdexcept>		// stdexcept header file contains runtime_error
#include <tr1/unordered_map> // hash table
// #include <boost/progress.hpp>
#include <boost/unordered_map.hpp>
#if defined(linux)||defined(__linux)||defined(__APPLE__)//any thing else than Windows
  #include <sys/time.h>
  #include <sys/types.h>
  #include <stdlib.h>
  #include <sys/resource.h>
#endif
// =============================================================================





using namespace std;
using namespace boost;
using std::runtime_error;   // standard C++ library class runtime_error

// =============================================================================
// Compute the wall time.
// =============================================================================
inline double WcTiming(void)
{
	struct timeval tp;
	gettimeofday(&tp, NULL);
	return (double) (tp.tv_sec + tp.tv_usec/1000000.0);
}
// =============================================================================





// g++ main2.cpp  -O3 -DNDEBUG  -DBOOST_MAP		..> use boost hash table
// g++ main2.cpp  -O3 -DNDEBUG  -DTR1			..> use tr1 hash table


// doc :
// hash_table<key,value>
// template < class Key, class T, class Compare = less<Key>,
//            class Allocator = allocator<pair<const Key,T> > > class map;

int main()
{
	#if defined(TR1) && defined (BOOST_MAP)
		throw std::runtime_error ("you can´t choose both of std::tr1::unordered_map and boost::unordered_map!!!") ; 
	#endif
	

	#ifdef TR1
		typedef std::tr1::unordered_map<int, string> map_t;
		std::string file_name ("results_tr1.dat") ;
	#else 
		#ifdef BOOST_MAP
			typedef boost::unordered_map<int, string> map_t;
			std::string file_name ("results_boost.dat") ;
		#else
			typedef std::map<int, string> map_t ;
			std::string file_name ("results_std.dat") ;
			map_t imap ;
		#endif
	#endif

	std::fstream myfile ;
	
	myfile.open (file_name.c_str() , std::fstream::out) ;
	myfile << "#size \t Insert took[sec]\t Find[sec]\n" ; 
	
	
	for ( double size =  1E03; size <= 1E8 ; size*=2 )
	{
		#if defined(TR1) || defined (BOOST_MAP)
			map_t imap (size);
		#endif

		myfile << size << "\t" ;
		const double t0 = WcTiming () ;
		{
			//boost::progress_timer t1;
			//boost::progress_display prog_bar(size);
			for(int i=0; i<size; ++i) {
			      imap[i] = "test";
			//  ++prog_bar;
			}
		}

		//std::cout << "\t---> Insert took :  "<< WcTiming () - t0 << "\t[sec]\t\t";
		myfile << WcTiming () - t0 << "\t" ; 
		const double t1 = WcTiming () ;
		{
			// boost::progress_timer t2;
			string buff;
			for(int i=0; i<size; ++i) {
			    buff = imap[i];
			}
		}
		//std::cout << "\t---> Find took :  " <<  WcTiming () - t1 << "\t[sec]\n";
		myfile << WcTiming () - t1 << "\n" ;
	}
	return (0) ;
}

// runnning on fauia 144 (note that these results are really old)
// =============================================================================
// no optimize 
// =============================================================================
	  // typedef std::tr1::unordered_map
		// 4.70 s	INSERT
		// 0.97 s	FIND

	  // typedef std::map<int, string> map_t;
		// 12.20 s	INSERT	
		// 7.16 s	FIND

	  // typedef boost::unordered_map
		// 4.95 s	INSERT
		// 1.41 s	FIND

// =============================================================================
//  Optimize code : g++ main2.cpp -O3 -DNDEBUG
// =============================================================================
	  // typedef std::tr1::unordered_map
		// 2.97 s	INSERT
		// 0.66 s	FIND

	  // typedef std::map<int, string> map_t;
		// 5.15 s	INSERT
		// 3.26 s	FIND

	  // typedef boost::unordered_map
		// 2.38 s	INSERT
		// 0.68 s	FIND


		





// =============================================================================
// 
// new benchmark, April 2012 (g++ 4.6.3)
// size = 1E7;
// =============================================================================

// typedef std::map<int, string> map_t;
// 	   
// 	    no optimized version 
// 	    ***************************************************
// 	    8.18 s	INSERT
// 	    4.27 s	FIND
// 
// 	    Optimized version  g++ main2.cpp  -O3 -DNDEBUG 
// 	    ***************************************************
// 	    3.71 s	INSERT
// 	    1.77 s	FIND
// 
// 
// 
// typedef boost::unordered_map<int, string> map_t;
// 
// 	    no optimized version 
// 	    ***************************************************
// 	    4.10 s	INSERT
// 	    2.25 s	FIND
// 
// 	    Optimized version  g++ main2.cpp  -O3 -DNDEBUG 
// 	    ***************************************************
// 	    1.15 s	INSERT
// 	    0.16 s	FIND
// 
// 
// 
// typedef std::tr1::unordered_map<int, string> map_t;
// 
// 	    no optimized version 
// 	    ***************************************************
// 	    2.52 s	INSERT
// 	    0.38 s	FIND
// 
// 
// 
// 	    Optimized version  g++ main2.cpp  -O3 -DNDEBUG 
// 	    ***************************************************
// 	    1.16 s	INSERT
// 	    0.16 s	FIND








// =============================================================================
// 
// new benchmark, April 2012 (g++ 4.6.3)
// size = 1E8;
// Optimized version :  g++ main2.cpp  -O3 -DNDEBUG
// =============================================================================
// typedef std::tr1::unordered_map<int, string> map_t; 		 constructor ->(size )
// ****************************************************************************************************  
// 		---> Insert took :  10.9571[s]			---> Insert took :  9.04005	[sec]
// 		---> Find took :  1.623[s]			---> Find took :  1.62962	[sec]

// typedef boost::unordered_map<int, string> map_t;
// ***************************************************
// 		---> Insert took :  10.6848	[sec]		---> Insert took :  9.11774	[sec]
// 		---> Find took :  1.73311	[sec]		---> Find took :  1.67753	[sec]

// typedef std::map<int, string> map_t;
// ***************************************************
// 		---> Insert took :  56.177	[sec]
// 		---> Find took :  30.5265	[sec]





