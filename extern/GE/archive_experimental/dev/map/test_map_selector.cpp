#include "map_selector.h"
#include <iostream>
#include <cassert>


//! template <typename Key , typename Value, int selector> map_selector {}; 
//! -----------------------------------------------------------------------
//! selector: |	use map < , >
//! -------------------------------
//!  1	      |	 map<Key , Value  >
//!  2	      |  map<Key , Value *>
//!  3	      |  map<Key*, Value  >
//!  else     |  map<Key*, Value *>
//! -------------------------------

using std::cout ;
using std::endl ;

using ge::map_selector  ;



struct map_config {
      typedef  char  Key ; 
      typedef  int   Value ;
      typedef map_selector<Key,Value,1>::type  	my_map; 
      typedef my_map::iterator  		my_map_iterator ;
      typedef std::pair<my_map_iterator,bool>   my_map_ret;
};


int main ()
{
// typedef 
      typedef map_selector<char,int , 1>::type     my_map_1 ; 
      typedef map_selector<char,int , 1>::iterator my_iterator_1;
// daclaration       
      my_map_1 mymap    ;
      my_iterator_1  it ;
      std::pair < my_iterator_1 ,bool> ret;
      
      
// assert       
      assert (mymap.size() == 0  ) ;
      
// first insert function version (single parameter):
	  mymap.insert ( std::pair<char,int>('a',100) );
	  mymap.insert ( std::pair<char,int>('z',200) );
	  
	
      ret=mymap.insert (std::pair<char,int>('z',500) );
      
      assert (ret.second==false ) ;
      
      if (ret.second==false)
      {
	cout << "element 'z' already existed";
	cout << " with a value of " << ret.first->second << endl;
      }
      



// second insert function version (with hint position):
      it=mymap.begin();
      mymap.insert (it, std::pair<char,int>('b',300));  // max efficiency inserting
      mymap.insert (it, std::pair<char,int>('c',400));  // no max efficiency inserting

      
// third insert function version (range insertion):
      my_map_1 anothermap;
      anothermap.insert(mymap.begin(),mymap.find('c'));
    
    
    

// showing contents:
      cout << "mymap contains:\n";
      for ( it=mymap.begin() ; it != mymap.end(); it++ )
	cout << (*it).first << " => " << (*it).second << endl;

      cout << "anothermap contains:\n";
      for ( it=anothermap.begin() ; it != anothermap.end(); it++ )
	cout << (*it).first << " => " << (*it).second << endl;

      return 0;

} 
