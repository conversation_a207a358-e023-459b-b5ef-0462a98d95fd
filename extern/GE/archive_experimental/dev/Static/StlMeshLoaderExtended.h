#ifndef MESHLOADER_HH
#define MESHLOADER_HH


// =============================================================================
// includes
// =============================================================================
#include "../misc/FileDef.h"
#include <stdexcept> // stdexcept header file contains runtime_error
#include <fstream>
#include <string>
#include <iostream> 
#include <sstream>
#include <typeinfo>
#include "../gmath/Point3D.h" // Vector3D
#include "../gmath/GEMath.h"
#include "../misc/Exception.h" //ErrorOpening
// =============================================================================



using std::runtime_error;   // standard C++ library class runtime_error



// =============================================================================
/*!
// @class StlMeshLoaderExtended
// @brief STL Mesh Loader,only static member function, no constructor, 
// 		destructor ... stc
// @note Only stl file format is supported now!!! 
*/
// =============================================================================
namespace ge
{

	template<typename Object_type >
	class StlMeshLoaderExtended
	{
	    public:
		// =============================================================
		// typedefs
		// =============================================================
		typedef typename Object_type::real		real;
		typedef typename Object_type::mesh_type	mesh_type;
		typedef typename mesh_type::point_type	point_type;
		typedef typename mesh_type::vector_type	vector_type;
		typedef typename mesh_type::edge_type		edge_type;
		typedef typename mesh_type::triangle_type	triangle_type;
		typedef typename mesh_type::polygons_list	polygons_list;
		// =============================================================
	    private:
		typedef typename polygons_list::iterator	iterator ;
		typedef typename polygons_list::const_iterator	const_iterator ;
		// =============================================================

		public:
		inline static void Load(const std::string&,mesh_type& obj);
		inline static void Load(mesh_type& obj);// get file name directly from the obj
		inline static void FileInfo(const mesh_type& obj);// you should first Load to get file info!!!

		private:
		// =============================================================
		// Private member functions
		// =============================================================
		// @{
		inline static bool IsBinary(const std::string&  fname) ;
		inline static std::size_t FileSize(const std::string&  fname);
		inline static void Read_from_binary_file(const std::string& fname,mesh_type& obj);
		inline static void Read_from_ascii_file (const std::string& fname,mesh_type& obj);
		// @}
	};

	
	// =====================================================================
	// 
	// =====================================================================
	template<typename Configure>
	inline void StlMeshLoaderExtended<Configure>::Load(typename StlMeshLoaderExtended<Configure>::mesh_type& obj)
	{
		const std::string fname = obj.GetFileName() ;
		//TODO add assert here
		if (fname.empty() )
			throw InvalidArgument(__FILE__,__LINE__,"Object the you want to load have empty name.");
		#ifdef DEBUG
		std::cerr << " -->  fileName: "<< fname << "\n" ;
		#endif
		if  ( IsBinary(fname) )
		{
			#ifdef DEBUG
			std::cerr << " -->  Read from binary format\n" ;
			#endif
			obj.GetType()= BINARY ;
			Read_from_binary_file(fname,obj) ;
		}
			else
		{
			#ifdef DEBUG
			std::cerr << " -->  Read from ascii format\n" ;
			#endif
			obj.GetType()= ASCII ;
			Read_from_ascii_file(fname,obj)  ;
		}
	}


	
	// =====================================================================
	// 
	// =====================================================================
	template<typename Configure>
	inline void StlMeshLoaderExtended<Configure>::Load (const std::string& fname,typename StlMeshLoaderExtended<Configure>::mesh_type& obj)
	{
		if  ( IsBinary(fname) )
		{
			#ifdef DEBUG
			std::cerr << " -->  Read from binary format\n" ;
			#endif
			obj.GetType()= BINARY ;
			Read_from_binary_file(fname,obj) ;
		} else {
			#ifdef DEBUG
			std::cerr << " -->  Read from ascii format\n" ;
			#endif
			obj.GetType()= ASCII ;
			Read_from_ascii_file(fname,obj)  ;
		}
	}



	template<typename Configure>
	inline void StlMeshLoaderExtended<Configure>::FileInfo(const mesh_type& obj)
	{
		#ifdef DEBUG
		std::cerr<<"// =============================================================================\n";
		std::cerr<<"//	file info \n";
		std::cerr<<"// =============================================================================\n";
		//std::cerr << "File name	:  "<< obj.GetFileName()<<"\n" ;
		std::cerr << "File name	:  "<< obj.GetFileName()<<"\n" ;
		std::cerr << "Format		:  \".stl\" \n"  ;
		std::cerr << "Type		:  " ;
		#else
		std::cout<<"// =============================================================================\n";
		std::cout<<"//	file info \n";
		std::cout<<"// =============================================================================\n";
		//std::cerr << "File name	:  "<< obj.GetFileName()<<"\n" ;
		std::cout << "File name	:  "<< obj.GetFileName()<<"\n" ;
		std::cout << "Format		:  \".stl\" \n"  ;
		std::cout << "Type		:  " ;
		#endif
		if (obj.GetType()== ASCII){
			std::cout << "ASCII\n";
		} else if (obj.GetType()== BINARY){
		std::cout << "BINARY\n";
		}else{
			std::cerr<<"Error unknown stl file format\n" ;
		}
		//FIXME
		//std::cout << "Size		:  "<< FileSize( obj.GetFileName() )<<" bytes" << std::endl;
		std::cout << "File header	:  "<< obj.GetHeaderFile()   <<std::endl;
	}



	// =====================================================================
	//
	// TODO its not working in case of stl-file wich have only 1 triangle
	// =====================================================================
	template<typename Configure>
	inline bool StlMeshLoaderExtended<Configure>::IsBinary(const std::string& fname) 
	{
		FILE* pFile = fopen (fname.c_str() ,"r");
		if (pFile == NULL) /*{fputs ("File error",stderr); exit (1);}*/
			throw ErrorOpening( __FILE__,__LINE__, "reason :Could not open input file." );
		
		rewind(pFile);
		unsigned char tmp[128];
		fseek (pFile, 84, SEEK_SET); // skip the first 84 bytes (header)
		fread (tmp, sizeof(tmp), 1, pFile);
		
		for(int i = 0; i < sizeof(tmp); i++)
		{
			if(tmp[i] > 127)
				return true ;
		}

		fclose (pFile);// close
		return false ;
	}
	// =====================================================================


        template<typename Configure>
	inline std::size_t StlMeshLoaderExtended<Configure>::FileSize(const std::string& fname)
	{
//		std::fstream f(fname,std::ios_base::binary|std::ios_base::in);
//		if(f.fail() )
//			throw runtime_error("Could not open input file.");
//			// Get the size
//		f.seekg(0, std::ios_base::end);
//		return (std::size_t ) f.tellg();

		FILE* pFile = fopen (fname.c_str() ,"r");
		if (pFile==NULL) /*{fputs ("File error",stderr); exit (1);}*/
			  throw ErrorOpening( __FILE__,__LINE__, "reason :Could not open input file." );

		fseek (pFile, 0, SEEK_END ) ; // set pointer to end of the file
		long  size  = ftell(pFile) ;
		rewind(pFile); //return pointer to begin of file
		
		fclose (pFile) ;
		return (std::size_t) size    ;
		
	}


	// =====================================================================
	// 
	// =====================================================================
	template<typename Configure>
	inline void StlMeshLoaderExtended<Configure>::Read_from_binary_file(
		const std::string& fname,
		typename StlMeshLoaderExtended<Configure>::mesh_type& obj)
	{
		typedef typename StlMeshLoaderExtended<Configure>::real		     	real ;
		typedef typename StlMeshLoaderExtended<Configure>::vector_type 	vector_type ;
		typedef typename StlMeshLoaderExtended<Configure>::point_type  	point_type ;
		typedef typename StlMeshLoaderExtended<Configure>::triangle_type	triangle_type ;

                const std::string myFileName =  fname ;

                std::ifstream f ( myFileName.c_str (), std::ios::binary|std::ios::in ) ;//(fname,std::ios_base::binary|std::ios_base::in);
		if(f.fail() )
			throw ErrorOpening( __FILE__,__LINE__, "reason :Could not open input file." );
		// Get the size
		f.seekg(0, std::ios_base::end);
		std::size_t fileSize = (int) f.tellg();
		f.seekg(0, std::ios_base::beg);
		char Comment [81] ;	//Read the Comment + number of the triangles
		f.read(Comment, 80 );  	// no data significance

		Comment [80] = 0;
		( obj.GetHeaderFile() ) = Comment ;
		const std::size_t nt = ReadInt32 (f);// Get the number of triangles:
		
		#ifdef DEBUG
			std::cout << "The fileSize  is= "<< fileSize << "\n";
			std::cout << "Header is: "     << Comment  << "\n";
			std::cout << "the  number of triangles : "<<nt<<"\n";
		#endif
		
		if (fileSize != (84  + nt* 50 ) ) // Stl protocol
			throw runtime_error ("Error during Openig the File.");

		typename StlMeshLoaderExtended<Configure>::real CoordMin_[3] , CoordMax_[3];
		
		for (int i=0 ; i<3 ; ++ i) {
			CoordMin_[i] = 100000.;/*FLT_MAX*/
			CoordMax_[i] = 0.0    ;/*FLT_MIN*/
		}

		for (std::size_t i=0 ; i<nt ; ++i )
		{
			const vector_type Nf  = (ReadVector3D<real>(f));
			const point_type  V0_ = (ReadPoint3D<real> (f));
			const point_type  V1_ = (ReadPoint3D<real> (f));
			const point_type  V2_ = (ReadPoint3D<real> (f));

			MINMAX(V0_.m_x,V1_.m_x,V2_.m_x,CoordMin_[0],CoordMax_[0]);
			MINMAX(V0_.m_y,V1_.m_y,V2_.m_y,CoordMin_[1],CoordMax_[1]);
			MINMAX(V0_.m_z,V1_.m_z,V2_.m_z,CoordMin_[2],CoordMax_[2]);

// 			const triangle_type T;
// 			T.push_back();
			(obj.GetPolyListMesh()).push_back( V0_,V1_,V2_,Nf );
			f.seekg(2,std::ios_base::cur);// Skip the flat
		}
		obj.AbbMin()= point_type(CoordMin_[0],CoordMin_[1],CoordMin_[2]);
		obj.AbbMax()= point_type(CoordMax_[0],CoordMax_[1],CoordMax_[2]);
		
		obj.GetLengthX() = abs (CoordMax_[0] - CoordMin_[0]) ;
		obj.GetLengthY() = abs (CoordMax_[1] - CoordMin_[1]) ;
		obj.GetLengthZ() = abs (CoordMax_[2] - CoordMin_[2]) ;
		f.close();
	}

	// =====================================================================
	// 
	// TODO please re-implement this function using std::fstream 
	// 	because fscanf its really src of many problems.
	// =====================================================================


	// c style using fscanf 
	template<typename Configure>
	inline void StlMeshLoaderExtended<Configure>::Read_from_ascii_file	(
		const std::string& fname, // file name
		typename StlMeshLoaderExtended<Configure>::mesh_type& obj) // object
	{
		typedef typename StlMeshLoaderExtended<Configure>::real		    real ;
		typedef typename StlMeshLoaderExtended<Configure>::vector_type   vector_type ;
		typedef typename StlMeshLoaderExtended<Configure>::point_type    point_type ;
		typedef typename StlMeshLoaderExtended<Configure>::triangle_type triangle_type ;

		FILE * pFile;
		pFile = fopen ( fname.c_str() ,"r");
		
		std::cerr << "-----> fname : "  << fname << "\n";
		//assert ( (pFile != NULL) );
		if ( pFile == NULL ) 
			throw  runtime_error ("Error Oppening file.!!! yahoo") ;

		rewind(pFile);			// beginning of the file.
		
		char str1[81] ;

		//while( getc(pFile) != '\n') // skip the first line
		fgets(str1, sizeof(str1), pFile);

		std::string tmp = str1 ;
		// TODO remove "solid" from the header file
		size_t pos = tmp.find_first_of ("solid");
		if ( pos != string::npos ){// found
// 			  std::string tt =  tmp.substr (pos) ;
// 			  obj.GetHeaderFile() = tt; 
			  obj.GetHeaderFile() = tmp ;
		}else{
			  std::cerr << "solid NOT  FOUNDED\n" ; 
			  obj.GetHeaderFile() = tmp ; // not founded
		}
		
		real CoordMin_[3],CoordMax_[3];

		for (int i=0 ; i<3 ; ++ i) {
			CoordMin_[i] = 100000.;/*FLT_MAX*/
			CoordMax_[i] = 0.0    ;/*FLT_MIN*/
		}
		
		vector_type Nf ;
		point_type V0 ;
		point_type V1 ;
		point_type V2 ;
		
		while ( !feof(pFile) )
		{
			// the official website for stl-file format,
			// say all numbers are floats
			// -->TODO %lf ?
			
			
// 			if( typeid(real) == typeid(float) )
// 			{
				if ( fscanf(pFile,"%*s %*s %f %f %f\n",&Nf[0],&Nf[1],&Nf[2]) != 3 )
				//ifif ( fscanf(pFile,"%*s %*s %lf %lf %lf\n",&Nf[0],&Nf[1],&Nf[2]) != 3 )
					continue;
				fscanf(pFile,"%*s %*s");//outer loop
				fscanf(pFile,"%*s %f %f %f\n",&V0.m_x,&V0.m_y,&V0.m_z);
				fscanf(pFile,"%*s %f %f %f\n",&V1.m_x,&V1.m_y,&V1.m_z);
				fscanf(pFile,"%*s %f %f %f\n",&V2.m_x,&V2.m_y,&V2.m_z);
// 				fscanf(pFile,"%*s %lf %lf %lf\n",&V0.m_x,&V0.m_y,&V0.m_z);
// 				fscanf(pFile,"%*s %lf %lf %lf\n",&V1.m_x,&V1.m_y,&V1.m_z);
// 				fscanf(pFile,"%*s %lf %lf %lf\n",&V2.m_x,&V2.m_y,&V2.m_z);
				fscanf(pFile,"%*s"); // endloop
				fscanf(pFile,"%*s"); // endfacet
// 			}
// 			else if( typeid(real) == typeid(double) )
// 			{
// 				if ( fscanf(pFile,"%*s %*s %lf %lf %lf\n",&Nf[0],&Nf[1],&Nf[2]) != 3 )
// 					continue;
// 				fscanf(pFile,"%*s %*s");//outer loop
// 				fscanf(pFile,"%*s %lf %lf %lf\n",&V0.m_x,&V0.m_y,&V0.m_z);
// 				fscanf(pFile,"%*s %lf %lf %lf\n",&V1.m_x,&V1.m_y,&V1.m_z);
// 				fscanf(pFile,"%*s %lf %lf %lf\n",&V2.m_x,&V2.m_y,&V2.m_z);
// 				fscanf(pFile,"%*s"); // endloop
// 				fscanf(pFile,"%*s"); // endfacet
// 			}
// 			else {
// 				cerr << "Error: Can not read ascii file" 
// 				<< typeid(real).type_info::name() << endl;
// 			}


			MINMAX (V0.m_x,V1.m_x,V2.m_x,CoordMin_[0],CoordMax_[0]);
			MINMAX (V0.m_y,V1.m_y,V2.m_y,CoordMin_[1],CoordMax_[1]);
			MINMAX (V0.m_z,V1.m_z,V2.m_z,CoordMin_[2],CoordMax_[2]);

			(obj.GetPolyListMesh()).push_back( V0,V1,V2,Nf);
		}
		obj.AbbMin()=point_type(CoordMin_[0],CoordMin_[1],CoordMin_[2]);
		obj.AbbMax()=point_type(CoordMax_[0],CoordMax_[1],CoordMax_[2]);
				
		obj.GetLengthX() = abs (CoordMax_[0] - CoordMin_[0]) ;
		obj.GetLengthY() = abs (CoordMax_[1] - CoordMin_[1]) ;
		obj.GetLengthZ() = abs (CoordMax_[2] - CoordMin_[2]) ;
		fclose (pFile);
	}



#ifdef CPPSTYLE // its not working properly, FIXME  
	// c++ style using std::ifstream 
	template<typename Configure>
	inline void StlMeshLoaderExtended<Configure>::Read_from_ascii_file	(
		const std::string& fname,
		typename StlMeshLoaderExtended<Configure>::mesh_type& obj)
	{
		typedef typename StlMeshLoaderExtended<Configure>::real real ;
		typedef typename StlMeshLoaderExtended<Configure>::vector_type   vector_type ;
		typedef typename StlMeshLoaderExtended<Configure>::point_type    point_type ;
		typedef typename StlMeshLoaderExtended<Configure>::triangle_type triangle_type ;

		std::ifstream myfile;
		
		myfile.open ( fname.c_str() );
		
		if (!myfile.is_open()) 
			std::cout << "Error opening file" <<std::endl;
		
		string line ,str,str2, str3 ; // to decompose the line in to sring, after that save in mymap
		size_t found_vertex , found_outer_loop ,  found_endloop ;


		typename StlMeshLoaderExtended<Configure>::real CoordMin_[3],CoordMax_[3];

		for (int i=0 ; i<3 ; ++ i) {
			CoordMin_[i] = 100000.;/*FLT_MAX*/
			CoordMax_[i] = 0.0    ;/*FLT_MIN*/
		}
		
		vector_type Nf ;
		point_type V0 ;
		point_type V1 ;
		point_type V2 ;

		getline(myfile, line);
		obj.GetHeaderFile() = line ;
		while(!myfile.eof() && line.find("endsolid")  == string::npos )
		{
			//std::cout<<"Line is "<<line<<std::endl;
			getline(myfile, line);

			/*if line contains facet normal*/
			if ( line.find("facet normal") != string::npos)
			{
					  std::stringstream ss   ;
					  ss << line ;
					  ss >> str   >> str2 ;
					  ss >> Nf[0] >> Nf[1] >> Nf[2];
			}
			getline(myfile, line);
			
			//assert( line.find("outer loop") != string::npos );
			//  std::cout<<"error not found outer_loop in STLFileReader\n";

			stringstream ss1 , ss2 , ss3   ;
     
			getline(myfile, line);
					  ss1 << line  ; //str <=>  vertex 
					  ss1 >> str3 >> V0.m_x >>  V0.m_y >> V0.m_z ;
  
			getline(myfile, line);			
					  ss2 << line  ; 
					  ss2 >> str3 >> V1.m_x >>  V1.m_y >> V1.m_z ;

			getline(myfile, line);			
					  ss3 << line  ; 
					  ss3 >> str3 >> V2.m_x >>  V2.m_y >> V2.m_z ;

			getline(myfile, line);

			//TODO
			//assert ( line.find("endloop") == string::npos ) ;
					  
			MINMAX (V0.m_x,V1.m_x,V2.m_x,CoordMin_[0],CoordMax_[0]);
			MINMAX (V0.m_y,V1.m_y,V2.m_y,CoordMin_[1],CoordMax_[1]);
			MINMAX (V0.m_z,V1.m_z,V2.m_z,CoordMin_[2],CoordMax_[2]);
			
			// TODO verify if better use an other triangle contr
			const triangle_type  T(V0,V1,V2,Nf/*,CoordMin_,CoordMax_*/) ;
			obj.GetPolyListMesh().push_back( T );
			getline(myfile, line) ;
			//std::cout << "Line is " << line << std::endl; // header file
			getline(myfile, line);
		} // end while
		obj.AbbMin()= point_type(CoordMin_[0],CoordMin_[1],CoordMin_[2]);
		obj.AbbMax()= point_type(CoordMax_[0],CoordMax_[1],CoordMax_[2]);
				
		obj.GetLengthX() = abs (CoordMax_[0] - CoordMin_[0]) ;
		obj.GetLengthY() = abs (CoordMax_[1] - CoordMin_[1]) ;
		obj.GetLengthZ() = abs (CoordMax_[2] - CoordMin_[2]) ;
		myfile.close();
}

#else
// no thing

#endif // CPPSTYLE



	//=====================================================================
}// end namespace ge


#endif  // end file.
