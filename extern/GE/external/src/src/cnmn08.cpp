/*******************************************************************************
ConminC++, to solve constrained or unconstrained function minimization.
Copyright (C) 1999  <PERSON><PERSON> <PERSON><PERSON> Dunn

This program is free software; you can redistribute it and/or
modify it under the terms of the GNU General Public License
as published by the Free Software Foundation; either version 2
of the License, or (at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this program; if not, write to the Free Software
Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA  02111-1307, USA.
*******************************************************************************/

//---------------------------------------------------------------------------
#pragma hdrstop

#include "cnmn08.h"
#include "conmin.h"
#ifndef UNIX
#include <vcl.h>
#endif

//---------------------------------------------------------------------------
#pragma package(smart_init)
void Conmin::cnmn08(int &ndb, int &ner, double *c, int *ms1, double **b)
{
//     ROUTINE TO SOLVE SPECIAL LINEAR PROBLEM FOR IMPOSING S-TRANSPOSE
//     TIMES S.LE.1 BOUNDS IN THE MODIFIED METHOD OF FEASIBLE DIRECTIONS.
//     BY G. N. VANDERPLAATS                             APRIL, 1972.
//     NASA-AMES RESEARCH CENTER,  MOFFETT FIELD, CALIF.
//     REF.  'STRUCTURAL OPTIMIZATION BY METHODS OF FEASIBLE DIRECTIONS',
//     G. N. VANDERPLAATS AND F. MOSES, JOURNAL OF COMPUTERS
//     AND STRUCTURES, VOL 3, PP 739-755, 1973.
//     FORM OF L. P. IS BX=C WHERE 1ST NDB COMPONENTS OF X CONTAIN VECTOR
//     U AND LAST NDB COMPONENTS CONTAIN VECTOR V.  CONSTRAINTS ARE
//     U.GE.0, V.GE.0, AND U-TRANSPOSE TIMES V = 0.
//     NER = ERROR FLAG.  IF NER.NE.0 ON RETURN, PROCESS HAS NOT
//     CONVERGED IN 5*NDB ITERATIONS.
//     VECTOR MS1 IDENTIFIES THE SET OF BASIC VARIABLES.
//
//
//     revision history
//     v1.1, 5/1/95, sharon padula, use coding standards
//     v2.0, 6/1/98, jack dunn, converted to C++ class
//
//

int kk,jj,m2,iter1,nmax,ichk;
double cb,bi,eps,cbmin,cbmax,c1,bb,bb1;
//
//     CHOOSE INITIAL BASIC VARIABLES AS V, AND INITIALIZE VECTOR MS1
//
ner=1;
m2=2*ndb;
//     CALCULATE CBMIN AND EPS AND INITIALIZE MS1.
eps=-1.0e+10;
cbmin=0.;
for(int i=1; i<=ndb; i++)
  {
  bi=b[i][i];
  cbmax=0.;
  if(bi<-1.0e-6) cbmax=c[i]/bi;
  if(bi>eps) eps=bi;
  if(cbmax>cbmin) cbmin=cbmax;
  ms1[i]=0;
  }
eps *= .0001;
//     IF (EPS.LT.-1.0E-10) EPS=-1.0E-10
//
//  E-10 CHANGED TO E-03 ON 1/12/81
//
if(eps < -1.0e-3) eps=-1.0e-3;
if(eps > -.0001) eps=-.0001;
cbmin=cbmin*1.0e-6;
//     IF (CBMIN.LT.1.0D-10) CBMIN=1.0D-10
//
//  E-10 CHANGED TO E-05 ON 1/12/81
//
if(cbmin<1.0e-05) cbmin=1.0e-05;
iter1=0;
nmax=5*ndb;
//
//     **********             BEGIN NEW ITERATION              **********
//

L20:
iter1++;
if(iter1>nmax) return;
//     FIND MAX. C(I)/B(I,I) FOR I=1,NDB.
cbmax = .9*cbmin;
ichk=0;
for(int i=1; i<=ndb; i++)
  {
  c1=c[i];
  bi=b[i][i];
  //     if(BI.>EPS || C1>0.) continue;
  if(bi>eps || c1>-1.0e-5) continue;
  //
  //  0. CHANGED TO -1.0E-05 ON 1/12/81
  //
  cb=c1/bi;
  if(cb<=cbmax) continue;
  ichk=i;
  cbmax=cb;
  }
if(cbmax<cbmin) goto L70;
if(ichk==0) goto L70;
//     UPDATE VECTOR MS1.
jj=ichk;
if(ms1[jj]==0) jj=ichk+ndb;
kk=jj+ndb;
if(kk>m2) kk=jj-ndb;
ms1[kk]=ichk;
ms1[jj]=0;
//
//                     PIVOT OF B(ICHK,ICHK)
//
bb=1./b[ichk][ichk];
for(int j=1; j<=ndb; j++) b[ichk][j]=bb*b[ichk][j];
c[ichk]=cbmax;
b[ichk][ichk]=bb;
//     ELIMINATE COEFICIENTS ON VARIABLE ENTERING BASIS AND STORE
//     COEFICIENTS ON VARIABLE LEAVING BASIS IN THEIR PLACE.
for(int i=1; i<=ndb; i++)
  {
  if(i==ichk) continue;
  bb1=b[i][ichk];
  b[i][ichk]=0.;
  for(int j=1; j<=ndb; j++) b[i][j] -= bb1*b[ichk][j];
  c[i] -= bb1*cbmax;
  }
goto L20;

L70:
ner=0;
//
//     STORE ONLY COMPONENTS OF U-VECTOR IN 'C'.  USE B(I,1) FOR
//     TEMPORARY STORAGE
//
for(int i=1; i<=ndb; i++) b[i][1]=c[i];
for(int i=1; i<=ndb; i++)
  {
  c[i]=0.;
  if(ms1[i]>0) c[i]=b[ms1[i]][1];
  if(c[i]<0) c[i]=0.;
  }
return;
}
