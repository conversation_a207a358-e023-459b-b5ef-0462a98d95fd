#ifndef BOUNDINGSPHERESTEST_HH
#define BOUNDINGSPHERESTEST_HH
	

// ref
// http://www.gamedev.net/page/resources/_/reference/programming/game-programming/collision-detection/simple-bounding-sphere-collision-detection-r1234

// Bounding-Sphere Collision Detection
template<typename object_type>
inline bool BoundingSpheresTest(object_type* obj1, object_type* obj2)
{
	typedef typename object_type::vector_type vector_type ;
	typedef typename object_type::real real ;
	
	//Initialize the return value
	*t = 0.0f;

	// Relative velocity
	const vector_type	dv	= obj2->V() - obj1->V();
	// Relative position
	const vector_type	dp	= obj2->GetCOM() - obj1->GetCOM();
	//Minimal distance squared
	const real r = obj1->Radios() + obj2->Radios();
	
	//dP^2-r^2
	const real pp = dp.x * dp.x + dp.y * dp.y + dp.z * dp.z - r*r;
	//(1)Check if the spheres are already intersecting
	if ( pp < 0 ) return true;

	//dP*dV
	const real pv	= dp.x * dv.x + dp.y * dv.y + dp.z * dv.z;
	//(2)Check if the spheres are moving away from each other
	if ( pv >= 0 ) return false;

	//dV^2
	const real vv = dv.x * dv.x + dv.y * dv.y + dv.z * dv.z;
	//(3)Check if the spheres can intersect within 1 frame
	if ( (pv + vv) <= 0 && (vv + 2 * pv + pp) >= 0 ) return false;

	//tmin = -dP*dV/dV*2
	//the time when the distance between the spheres is minimal
	const real tmin = -pv/vv;

	//Discriminant/(4*dV^2) = -(dp^2-r^2+dP*dV*tmin)
	return ( pp + pv * tmin > 0 );
}



#endif
