#ifndef SLIST_H
#define SLIST_H



#include<cassert>
#include<iterator>

template<class T>
class slist
{
  public:
	// =====================================================================
	/*! @brief types
	//  Some types of the class get public names. Then it is possible to use 
	//  them outside the class without knowing the implementation.
	*/
	// =====================================================================
	typedef T 		value_type;	
	typedef std::ptrdiff_t	difference_type;
	typedef T* 		pointer;
	typedef T& 		reference;

	slist() : firstElement(0), Count(0) {}
    
    
	// =====================================================================
	/* 
	//  copy constructor, destructor and assignment operator are omitted! 
	//  The implementation of push_front() creates a new list element and
	//   inserts it at the beginningof the list:
	*/
	// =====================================================================
	inline void push_front(const T& Datum) { // insert at beginning
		firstElement = new ListElement(Datum, firstElement);
		++Count;
	}
    
  private:
	// =====================================================================
	struct ListElement
	{
	    T    		Data;
	    ListElement 	*Next;
	 // public:
	    ListElement(const T& Datum, ListElement* p) // constructor
	    : Data(Datum), Next(p) {}
	};
	// =====================================================================
	ListElement *firstElement;
	std::size_t Count;
	// =====================================================================
   public:
	class iterator
	{
	    public:
		// =============================================================
		//!@brief types public:
		// =============================================================
		typedef std::forward_iterator_tag iterator_category;
		typedef T value_type;
		typedef T* pointer;
		typedef T& reference;
		typedef size_t size_type;
		typedef ptrdiff_t difference_type;
		
		// =============================================================
		//!@brief cconstructor  
		// =============================================================
		iterator(ListElement* Init = 0)
		      : current(Init){}

		inline T& operator*(){
			return current->Data;
		}
		// =============================================================
		//!@brief  dereferencing
		// =============================================================
		inline const T& operator*() const {
			return current->Data;
		}
		
		
		// =============================================================
		///!@brief preﬁx
		// =============================================================
		inline iterator& operator++()
		{ 
			if(current) // not yet arrived at the end?
			current = current->Next;
			return *this;
		}
		
		
		// =============================================================
		//
		// =============================================================
		inline iterator operator++(int)
		{
			iterator temp = *this;
			++ *this;
			return temp;
		}
		
		// =============================================================
		///!@brief  postﬁx
		// =============================================================
		inline bool operator == (const iterator& x) const 
		{
			return current == x.current;
		}
		
		
		// =============================================================
		//
		// =============================================================
		inline bool operator!=(const iterator& x) const {
			return current != x.current;
		}
		
	    private:
		// =============================================================
		//!@brief  pointer to current element
		// =============================================================
		ListElement* current; 
	}; // end class iterator
	
	// ====================================================================
	/*! @note As can be seen above, in the postﬁx variation of the ++ operator, the copy con-
	//  structor is needed for initialization and return of temp. For this reason, the preﬁx
	//  variation should be preferred where possible.
	*/
	// ===================================================================== 
	 
	
	
	// =====================================================================
	//  use the iterator class:
	inline iterator begin() const {
	  return iterator(firstElement);
	}
	//  use the iterator class:
	inline iterator end()   const {
	  return iterator();
	}
      
      };

#endif // SIMPLELIST_H


