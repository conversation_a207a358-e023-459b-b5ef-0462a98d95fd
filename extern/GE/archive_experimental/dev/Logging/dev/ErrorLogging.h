#ifndef ERRORLOGGING_HH
#define ERRORLOGGING_HH


// =============================================================================
// main logging class implementation
// its similar to Klaus implementation in waLberla framework
// =============================================================================
#include <iostream>
#include <fstream>
#include <string>
#include <ctime> // get time
#include <boost/scoped_ptr.hpp>	//scoped_ptr
#include <boost/utility.hpp>	//noncopyable
#include <stdexcept>
#include <memory>
// =============================================================================



#define OPENNEWLOGFILE 0
	
	// =====================================================================
	// =====================================================================
	struct ErrorLogging : private boost::noncopyable
	{
		ErrorLogging();
		~ErrorLogging();
		inline static ErrorLogging& Instance ();
		inline static void Configure(const std::string& f_name);
		inline static void Log(const std::string& msg) ;
  	public:
		std::fstream m_file ;
		static std::string fileName;
		std::time_t  m_Ctime;
	};


	// =====================================================================
	// =====================================================================
	std::string ErrorLogging::fileName;// Initialize static data member

	// =====================================================================
	ErrorLogging::ErrorLogging()
	{
		const std::string tmp = ErrorLogging::fileName;
		if ( tmp.empty() )
		  throw std::runtime_error( "ErrorLogging not configured!" );
		#ifdef OPENNEWLOGFILE
		m_file.open (tmp.c_str() );
		#else
		m_file.open (tmp.c_str(), std::ofstream::out | std::ofstream::app);
		#endif
	}
	
	ErrorLogging::~ErrorLogging()
	{
		m_file.close();
	}

	// =====================================================================
	// 
	// =====================================================================
	inline ErrorLogging& ErrorLogging::Instance ()/*const*/
	{
		typedef std::auto_ptr<ErrorLogging>Ptr;//typedef boost::scoped_ptr<ErrorLogging>Ptr;
		static Ptr my_loger;
		if( !my_loger.get() )
		{
			my_loger = Ptr(new ErrorLogging() );
		}

	}
	// =====================================================================

	// =====================================================================
	// configure file nanme
	// =====================================================================
	inline void ErrorLogging::Configure(const std::string& f_name)
	{
		fileName=f_name;
	}
	
	
	inline void ErrorLogging::Log (const std::string& msg)
	{
		(ErrorLogging::Instance().m_file) << "Error : " << msg  << "\n";
	}





#endif


