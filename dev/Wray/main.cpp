#include <iostream>
#include <string>
#include <sstream>
#include <fstream>      // std::ifstream
#include <iomanip>      // std::setprecision

#include <cmath>
#include <limits>


int  main()
{
	std::cout << "length : " << std::numeric_limits< double>::digits10 + 1 <<  std::endl;

	std::ifstream ifs ( "vel.dat"  , std::ifstream::in);
	//std::ifstream ifs ( "velocity.dat");
	int count =0 ;
	while   (ifs.good()) {
	    std::string line ;
	     while (  std::getline(ifs, line)  )
	     {
        	std::stringstream tmp ;
     	   	tmp  << line  ;
		//long double x ,y ,z , ux ,uy ,uz  ;
		double x ,y ,z , ux ,uy ,uz  ;

		tmp >> x  >> y  >> z  >> ux  >> uy  >> uz     ;
		//if ( ux==0 && uy ==0 && uz==0 )
		{
		   //   std::cout << "count "<< count << "\n";
//		      std::cout << " x,y,z,ux,uy,uz \t" 
			std::cout << std::setprecision(std::numeric_limits< double>::digits10 + 1) << 
//			std::cout   <<  std::setprecision(18)<<
			x  << "\t" << y   <<"\t" << z  << "\t"<<  
			ux << "\t" << uy  <<"\t" << uz << "\n";
		}
		++ count ; 
	     }

	}
	return (0);				

}
