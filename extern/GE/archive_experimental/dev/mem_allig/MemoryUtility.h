#ifndef MEMORYUTILITY_H
#define MEMORYUT<PERSON><PERSON>Y_H

/*

#include <stdlib.h>

void *memalign(size_t blocksize, size_t bytes);

The memalign() function returns a block of memory of size bytes aligned to blocksize.
MULTITHREAD SAFETY LEVEL

PARAMETERS

blocksize 

    Is the size of the aligned block.
bytes 

    Is the number of bytes to be allocated.

*/

#ifndef L1_CACHE_LINE
#define L1_CACHE_LINE 64
#endif
/*!
  *  \brief Allocates memory block.
  *  \param size Size of the memory block requested
  *  \return pointer to an allocated block of memory or NULL if there's not enough free memory.
*/

void * AllocAlligned(size_t size)
{
	return memalign(L1_CACHE_LINE, size);
}


/*!
  *  \brief deallocates the block of the memory.
  *  \param  pointer to an the block of memory. 
*/
void FreeAligned (void* ptr)
{
	free(ptr);
}

void *av_malloc(unsigned int size)
{
    void *ptr;
    ptr = memalign(16,size);
    ptr = malloc(size);
    return ptr;
}



#endif
