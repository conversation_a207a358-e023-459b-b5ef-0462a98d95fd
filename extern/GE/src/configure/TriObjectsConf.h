#ifndef TRIOBJECTSCONF_HH
#define TRIOBJECTSCONF_HH

// =============================================================================
/*
//	This program is free software; you can redistribute it and/or
//	modify it under the terms of the GNU General Public License
//	as published by the Free Software Foundation; either version 2
//	of the License, or (at your option) any later version.
//
//	This program is distributed in the hope that it will be useful,
//	but WITHOUT ANY WARRANTY; without even the implied warranty of
//	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
//	GNU General Public License for more details.
//
//	You should have received a copy of the GNU General Public License
//	along with this program; if not, write to the Free Software
//	Foundation, Inc., 51 Franklin Street, Fifth Floor,
//	Boston, MA  02110-1301, USA.
//
//	
//	Copyright (C) 2010, Cherif <<EMAIL>>
*/
// =============================================================================


// =============================================================================
// includes
// =============================================================================

#include "Config_Def.h"

      
      
      
// =============================================================================
//!@brief Configure List for triangles Mesh Objects
//!@param TriObjectType is template parameter decribe the triangles-Meshobject
//!@note  any TriObjectType Object should be inheritaned directly from the 
//!	* Base class for all kind of TriObjects (see /accel/TriShapeBase.h)
//!	* exmaple: sphere.
//!<AUTHOR>
// =============================================================================
      template<class TriObjectType> 	// triangle-Base-Class of all kinds of 
					// triangles Mesh-object
      struct TriObjectsConf // triangle Meshes class 
      {
	      typedef TriObjectType	      objectType;//triangles
	      typedef typename ConfigListDefault<objectType>::FinalListType objectsList;
      };
      
// =============================================================================

  
#endif // TRIOBJECTSCONF_HH

