#ifndef SPHERE_HH
#define SPHERE_HH


#include "TriShapeBase.h"
#include <vector>
#include <cmath> // sqrt

#include <iomanip>
#include <fstream>
#include <cstdlib>


#include <cassert>  // stdexcept header file contains runtime_error


//==============================================================================
/*!
// @brief  class Sphere, to generate triangular-Mesh approximation of an sphere 
// 
//    
*/
//==============================================================================
namespace ge
{
	/*! == <Point3D float> == */
	struct Point3D
	{
	      float x, y, z;
// 	      Point3D (const float& X, const float&Y,const float&Z) : x(X), y(Y), z (Z) {} 
// 	      Point3D (const Point3D& other)// copy constructor 
// 	      {
// 		  x = other.x  ;
// 		  y = other.y  ;
// 		  z = other.z  ;
// 	      }
	};
	
	
	inline std::ostream& operator << (std::ostream& out, const Point3D& point)
	{
	    out << point.x  << " " << point.y << " " << point.z << endl ;
	    return out ;
	}
	
	struct Triangle {
	      Point3D v0 , v1, v2;	/*! <vertices >*/
	      Point3D  c;         	/*! <centroid >*/
	      Point3D normal ;		/*! <vector normals >*/
	};	
	
	struct Sphere : TriShapeBase<Sphere>
	{
	  public:
		explicit Sphere () {}  // default constructor
		explicit Sphere (const Point& center, const double& radius,const int& n_facet     )  ; // The number of facets will be (4^iterations) * 8 
		// explicit Sphere (const Point& center, const double& radius,const int& n_iteration ) ; // The number of facets will be (4^iterations) * 8 
		
		inline void implementation() const { } /* algorithm implementation. */
		inline void Trace() const  { std::cout << "Sphere::Trace ----> sphere [ " << id_ << " ]\n" ;} 
		inline bool Intersect (const Ray& ray ) ;
	  private :
		Point    m_center;
		double   m_radius;
		std::vector<Triangle *> m_mesh ;
	  public:
		inline bool Create (const int& iter) ; // return true if succsess, else return false
	  public:
		inline void ExportToStl (const char* fname)const ;
	};
	
	
	// Uniform Distribution -> hypercube rejection method :
	// To apply this to a unit cube at the origin, choose coordinates (x,y,z) each uniformly distributed on the interval [-1,1].
	// If the length of this vector is greater than 1 then reject it, otherwise normalise it and use it as a sample. 
	inline bool Sphere::Create (const int& iterations)
	{
	  

	  
	  /*! <unit cube at the origin> */
	  Point3D corner [6] = {  0 ,  0  , 1,  	// corner[0] 
				  0 ,  0  ,-1,  	// corner[1] 
				 -1 , -1  , 0,  	// corner[2] 
				  1 , -1  , 0,  	// corner[3]  
				  1 ,  1  , 0,  	// corner[4] 
				 -1 ,  1  , 0 };	// corner[5] 
				 
				 
	    Point3D  pa,pb,pc;
	    int nt = 0,ntold;			 
	    const float a = 1.0 / sqrt(2.0);
	     
	    /* Create the level 0 object */
		  for (int i=0;i<6;i++) {
		      corner[i].x *= a;
		      corner[i].y *= a;
		  }
		

		  Triangle * f  ;
		  nt = 8;
		
		  f[0].v0 = corner[0];
		  f[0].v1 = corner[3];
		  f[0].v2 = corner[4];
		  
		  
		  f[1].v0 = corner[0]; f[1].v1 = corner[4]; f[1].v2 = corner[5];
		  f[2].v0 = corner[0]; f[2].v1 = corner[5]; f[2].v2 = corner[2];
		  f[3].v0 = corner[0]; f[3].v1 = corner[2]; f[3].v2 = corner[3];
		  f[4].v0 = corner[1]; f[4].v1 = corner[4]; f[4].v2 = corner[3];
		  f[5].v0 = corner[1]; f[5].v1 = corner[5]; f[5].v2 = corner[4];
		  f[6].v0 = corner[1]; f[6].v1 = corner[2]; f[6].v2 = corner[5];
		  f[7].v0 = corner[1]; f[7].v1 = corner[3]; f[7].v2 = corner[2];
	
		  std::cout << "Create function -> " <<  iterations << std::endl ;
// 	    return true;
	}
	
	
	inline void Sphere::ExportToStl (const char* fname )const
	{
	   	FILE * pFile;
		pFile = fopen ( fname,"w");
		assert ( (pFile != NULL) );

		fprintf(pFile, "%s\n", "Solid 3d object Created Using GE tool. autor:Cherif.Mihoubi");
		
		assert (m_mesh.size() !=0 ) ;
		
		//TODO compute the normals
		for (int i=0; i< m_mesh.size() ; i++ )
		{
		  // fprintf(pFile, "%s %lf %lf %lf\n","facet normal ", Nf[0] , Nf[1] , Nf[2]);
		  fprintf(pFile, "%s %lf %lf %lf\n","facet normal ", 0 , 1 , 2);
		  fprintf(pFile, "%s\n","outer loop" );//outer loop

		  Triangle tmp = *m_mesh[i] ;

		  fprintf(pFile, "%s %lf %lf %lf\n", "     vertex " ,tmp.v0.x , tmp.v0.y , tmp.v0.z  );
		  fprintf(pFile, "%s %lf %lf %lf\n", "     vertex " ,tmp.v1.x , tmp.v1.y , tmp.v1.z  );
		  fprintf(pFile, "%s %lf %lf %lf\n", "     vertex " ,tmp.v2.x , tmp.v2.y , tmp.v2.z  );
		  fprintf(pFile, "%s\n", "endloop"); // end outer loop
		  fprintf(pFile, "%s\n","endfacet" ); // end facet
		}
		fprintf(pFile, "%s", "\nendsolid");

		fclose (pFile);
	}
}


#endif 

