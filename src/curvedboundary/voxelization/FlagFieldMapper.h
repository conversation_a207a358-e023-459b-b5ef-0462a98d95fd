#pragma once

#include "../../Definitions.h"
#include "domain_decomposition/BlockDataID.h"
#include "domain_decomposition/IBlock.h"
#include "field/FlagField.h"
#include "VoxelData.h"
#include "../mesh/TriangleMesh.h"
#include <memory>
#include <map>

namespace walberla {
namespace curvedboundary {

// Maps voxelization results to waLBerla flag fields
class FlagFieldMapper {
public:
    // Mapping configuration for each mesh
    struct MeshMapping {
        VoxelData::BCType bcType = VoxelData::NOSLIP;
        FlagUID obstacleFlag;      // Flag for obstacle cells (e.g., OBST)
        FlagUID boundaryFlag;      // Flag for boundary cells (e.g., BOUZIDI)
        FlagUID fluidFlag;         // Flag for fluid cells (e.g., LIQUID)
        bool removeFluidFromObstacles = true;
        bool markNearObstacle = true;
        Vector3<Real> velocity = Vector3<Real>(0.0);  // For moving boundaries
    };
    
    FlagFieldMapper();
    
    // Set default flags
    void setDefaultFlags(
        const FlagUID& fluid,
        const FlagUID& obstacle,
        const FlagUID& boundary,
        const FlagUID& nearObstacle
    );
    
    // Add mesh-specific mapping
    void addMeshMapping(Uint meshID, const MeshMapping& mapping) {
        meshMappings_[meshID] = mapping;
    }
    
    // Map voxel data to flag field
    void mapToFlagField(
        const VoxelDataField& voxelData,
        IBlock* block,
        BlockDataID flagFieldID
    );
    
    // Map with custom mapping per cell
    void mapToFlagFieldWithCallback(
        const VoxelDataField& voxelData,
        IBlock* block,
        BlockDataID flagFieldID,
        std::function<FlagUID(const VoxelData&)> mappingFunction
    );
    
    // Clear flags in a region
    void clearFlags(
        IBlock* block,
        BlockDataID flagFieldID,
        const AABB& region,
        const FlagUID& flagToClear
    );
    
    // Restore fluid flags in cleared regions
    void restoreFluidFlags(
        IBlock* block,
        BlockDataID flagFieldID,
        const VoxelDataField& voxelData,
        const FlagUID& fluidFlag
    );
    
    // Get boundary cells for a specific mesh
    std::vector<Cell> getBoundaryCells(
        const VoxelDataField& voxelData,
        Uint meshID
    ) const;
    
    // Create boundary links for LBM
    struct BoundaryLink {
        Cell fluidCell;
        Cell boundaryCell;
        Uint direction;  // D3Q19 direction
        Real q;          // Bouzidi parameter
        Vector3<Real> wallVelocity;
        Vector3<Real> wallNormal;
    };
    
    std::vector<BoundaryLink> createBoundaryLinks(
        const VoxelDataField& voxelData,
        IBlock* block,
        BlockDataID flagFieldID
    ) const;
    
private:
    // Default flags
    FlagUID defaultFluidFlag_;
    FlagUID defaultObstacleFlag_;
    FlagUID defaultBoundaryFlag_;
    FlagUID defaultNearObstacleFlag_;
    
    // Mesh-specific mappings
    std::map<Uint, MeshMapping> meshMappings_;
    
    // Helper to get flag for voxel
    FlagUID getFlagForVoxel(const VoxelData& voxel) const;
    
    // Check if cell should be marked as near-obstacle
    bool shouldMarkNearObstacle(
        const VoxelDataField& voxelData,
        Uint x, Uint y, Uint z
    ) const;
};

// Helper class for dynamic flag updates
class DynamicFlagUpdater {
public:
    DynamicFlagUpdater(std::shared_ptr<FlagFieldMapper> mapper);
    
    // Update flags for moving mesh
    void updateMovingMesh(
        const VoxelDataField& oldVoxelData,
        const VoxelDataField& newVoxelData,
        IBlock* block,
        BlockDataID flagFieldID
    );
    
    // Track cells that need restoration
    void trackCellsForRestoration(
        const VoxelDataField& voxelData,
        const std::vector<Cell>& cells
    );
    
    // Restore tracked cells to fluid
    void restoreTrackedCells(
        IBlock* block,
        BlockDataID flagFieldID
    );
    
private:
    std::shared_ptr<FlagFieldMapper> mapper_;
    std::vector<Cell> cellsToRestore_;
    
    // Find cells that changed between voxelizations
    std::vector<Cell> findChangedCells(
        const VoxelDataField& oldData,
        const VoxelDataField& newData
    ) const;
};

} // namespace curvedboundary
} // namespace walberla