#include "../configure/Configure.h"
#include "../ds/List.h"

// ==========================================================================
//! 				Example of configuration 
// ========================================================================== 
// template< class T> class PtrList ;                  // forward declaration
// template <class OptCounterList> class TracedList ;  // forward declaration

struct TracedIntListConfig
{
  typedef int                             ElementType; // object type
  typedef const ElementType               ElementArgumentType;
  typedef MonomorphicCopier<ElementType>  Copier;      // PolymorphicCopier  | MonomorphicCopier | EmptyCopier
  typedef ElementDestroyer<ElementType>   Destroyer;
  typedef DynamicTypeChecker<ElementType> TypeChecker; // DynamicTypeChecker | EmptyTypeChecker
// with or without tracing.cplusplus.com/forum/general/1414/
// typedef PtrList<TracedIntListConfig>     FinalListType; // wihtout tracing
   typedef TracedList< PtrList<TracedIntListConfig> >    FinalListType; // with tracing 
};



struct IntListConfig
{
  typedef int                             ElementType; // object type
  typedef const ElementType               ElementArgumentType;
  typedef MonomorphicCopier<ElementType>  Copier;      // PolymorphicCopier  | MonomorphicCopier | EmptyCopier
  typedef ElementDestroyer<ElementType>   Destroyer;
  typedef DynamicTypeChecker<ElementType> TypeChecker; // DynamicTypeChecker | EmptyTypeChecker
// with or without tracing.cplusplus.com/forum/general/1414/
  typedef PtrList<TracedIntListConfig>     FinalListType; // wihtout tracing
//    typedef TracedList< PtrList<TracedIntListConfig> >    FinalListType; // with tracing 
};
//

// ==========================================================================
// testing 
// ==========================================================================

int main ()
{
      typedef TracedIntListConfig::FinalListType List;
//   typedef ListConfig::FinalListType List;
  
      List* ls = 0;
	    push_front(1,ls);
	    push_front(2,ls);
	    push_front(3,ls);
	    
      print_list(ls);   // prints "3 2 1"
  
      return (0) ;
}

