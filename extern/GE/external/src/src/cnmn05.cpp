/*******************************************************************************
ConminC++, to solve constrained or unconstrained function minimization.
Copyright (C) 1999  <PERSON><PERSON> <PERSON><PERSON> Dunn

This program is free software; you can redistribute it and/or
modify it under the terms of the GNU General Public License
as published by the Free Software Foundation; either version 2
of the License, or (at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this program; if not, write to the Free Software
Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA  02111-1307, USA.
*******************************************************************************/

//---------------------------------------------------------------------------
#pragma hdrstop

#include "cnmn05.h"
#include "conmin.h"
#include <iostream>
#include <iomanip>
#include <cmath>
#ifndef UNIX
#include <vcl.h>
#endif

//---------------------------------------------------------------------------
#pragma package(smart_init)
void Conmin::cnmn05(double *g, double *df, double **a, double *s, double **b,
     double *c, double &slope, double &phi, int *isc, int *ic, int *ms1,
     int &nvc)
{
//     ROUTINE TO SOLVE DIRECTION FINDING PROBLEM IN MODIFIED METHOD OF
//     FEASIBLE DIRECTIONS.
//     BY G. N. VANDERPLAATS                            MAY, 1972.
//     NASA-AMES RESEARCH CENTER, MOFFETT FIELD, CALIF.
//     NORM OF S VECTOR USED HERE IS S-TRANSPOSE TIMES S.LE.1.
//     IF NVC = 0 FIND DIRECTION BY ZOUTENDIJK'S METHOD.  OTHERWISE
//     FIND MODIFIED DIRECTION.
//
//     revision history
//     v1.1, 5/1/95, sharon padula, use coding standards
//     v2.0, 6/1/98, jack dunn, converted to C++ class
//
//
double thmax,ct1,ct2,ctb,c1,ctd,ctc,tht,gg,s1,sg;
int nci,ncj,i,j1,nac1,ndb,ner;
const int ndv1=ndv+1;
const int ndv2=ndv+2;
nac1=nac+1;
nvc=0;
thmax=0.;
cta=fabs(ct);
ct1=1./cta;
ctam=fabs(ctmin);
ctb=fabs(ctl);
ct2=1./ctb;
ctbm=fabs(ctlmin);
a1=1.;
for(int ii=1; ii<=nac; ii++)
  {
  //     CALCULATE THETA
  nci=ic[ii];
  ncj=1;
  if(nci<=ncon) ncj=isc[nci];
  c1=g[nci];
  ctd=ct1;
  ctc=ctam;
  if(ncj<=0) goto L10;
  ctc=ctbm;
  ctd=ct2;

L10:
  if(c1>ctc) nvc++;
  tht=0.;
  gg=1. + ctd*c1;
  if(ncj==0 || c1>ctc) tht=theta*gg*gg;
  if(tht>50.) tht=50.;
  if(tht>thmax) thmax=tht;
  a[ndv1][ii]=tht;
  //
  //                   NORMALIZE GRADIENTS OF CONSTRAINTS
  //
  a[ndv2][ii]=1.;
  if(nci>ncon) continue;
  a1=0.;
  for(int j=1; j<=ndv; j++) a1 += pow(a[j][ii],2.);
  if(a1<1.0e-20) a1=1.0e-20;
  a1=sqrt(a1);
  a[ndv2][ii]=a1;
  a1=1./a1;
  for(int j=1; j<=ndv; j++) a[j][ii] *= a1;
  }
//
//    CHECK FOR ZERO GRADIENT.  PROGRAM CHANGE-FEB, 1981, GV.
//
i=0;

L41:
i++;

L42:
if(a[ndv2][i]>1.0e-6) goto L45;
//     ZERO GRADIENT IS FOUND.  WRITE ERROR MESSAGE.
if(iprint == Debug1 || iprint == Debug2 || iprint == Debug3 || iprint == Debug4)
  cout << "     ** constraint" << setw(5) << ic[i] << " has zero gradient"
       << endl << "     Deleted from active set";
//     REDUCE NAC BY ONE.
nac--;
//     SHIFT COLUMNS OF A AND ROWS OF IC IF I.LE.NAC.
if(i>nac) goto  L46;
//     SHIFT.
for(int j=1; j<=nac; j++)
  {
  j1=j+1;
  ic[j]=ic[j1];
  for(int k=1; k<=ndv2; k++) a[k][j] = a[k][j1];
  }
if(i<=nac) goto L42;

L45:
if(i<nac) goto L41;

L46:
if(nac<=0) return;
nac1=nac+1;
//     DETERMINE IF CONSTRAINTS ARE VIOLATED.
nvc=0;
for(int ii=1; ii<=nac; ii++)
  {
  nci=ic[ii];
  ncj=1;
  if(nci<=ncon) ncj=isc[nci];
  ctc=ctam;
  if(ncj>0) ctc=ctbm;
  if(g[nci]>ctc) nvc++;
  }
//
//     NORMALIZE GRADIENT OF OBJECTIVE FUNCTION AND STORE IN NAC+1
//     COLUMN OF A
//
a1=0.;
for(int ii=1; ii<=ndv; ii++) a1 += pow(df[ii],2.);
if(a1<1.0e-20) a1=1.0e-20;
a1=sqrt(a1);
a1=1./a1;
for(int ii=1; ii<=ndv; ii++) a[ii][nac1]=a1*df[ii];
//     BUILD C VECTOR.
if(nvc>0) goto L80;
//
//                BUILD C FOR CLASSICAL METHOD
//
ndb=nac1;
a[ndv1][ndb]=1.;
for(int ii=1; ii<=ndb; ii++) c[ii] = -a[ndv1][ii];
goto L110;

L80:
//
//                   BUILD C FOR MODIFIED METHOD
//
ndb=nac;
a[ndv1][nac1]=-phi;
//
//           SCALE THETA'S SO THAT MAXIMUM THETA IS UNITY
//
if(thmax>0.00001) thmax=1./thmax;
for(int ii=1; ii<=ndb; ii++) a[ndv1][ii] *= thmax;
for(int ii=1; ii<=ndb; ii++)
  {
  c[ii]=0.;
  for(int j=1; j<=ndv1; j++) c[ii] += a[j][ii]*a[j][nac1];
  }

L110:
//
//                      BUILD B MATRIX
//
for(int ii=1; ii<=ndb; ii++)
  for(int j=1; j<=ndb; j++)
    {
    b[ii][j]=0.;
    for(int k=1; k<=ndv1; k++) b[ii][j] -= a[k][ii]*a[k][j];
    }
//
//                    SOLVE SPECIAL L. P. PROBLEM
//
cnmn08(ndb,ner,c,ms1,b);
if(iprint == Debug1 || iprint == Debug2 || iprint == Debug3 ||
   iprint == Debug4 && ner > 0)
 cout << endl << endl << "     * * Direction finding process did not converge"
       << endl << "     * * s-vector may not be valid";
//     CALCULATE RESULTING DIRECTION VECTOR, S.
slope=0.;
//
//                  USABLE-FEASIBLE DIRECTION
//
for(int ii=1; ii<=ndv; ii++)
  {
  s1=0.;
  if(nvc>0) s1=-a[ii][nac1];
  for(int j=1; j<=ndb; j++) s1 -= a[ii][j]*c[j];
  slope += s1*df[ii];
  s[ii]=s1;
  }
s[ndv1]=1.;
if(nvc>0) s[ndv1] = -a[ndv1][nac1];
for(int j=1; j<=ndb; j++) s[ndv1] -= a[ndv1][j]*c[j];
//
//     CHECK TO INSURE THE S-VECTOR IS FEASIBLE.
//     PROGRAM MOD-FEB, 1981, GV.
//
for(int j=1; j<=nac; j++)
  {
  //     S DOT DEL(G).
  sg=0.;
  for (int ii=1; ii<=ndv; ii++) sg += s[ii]*a[ii][j];
  //     IF(SG>0.) GOTO 176
  //
  //  THIS CHANGE MADE ON 4/8/81 FOR G. VANDERPLAATS
  //
  if(sg>1.0e-04) goto L176;
  //     FEASIBLE FOR THIS CONSTRAINT.  CONTINUE.
  }
goto L179;

L176:
//     S-VECTOR IS NOT FEASIBLE DUE TO SOME NUMERICAL PROBLEM.
if(iprint == Debug1 || iprint == Debug2 || iprint == Debug3 || iprint == Debug4)
cout << "     ** Calculated s-vector is not feasible" << endl <<
        "     Bta is set to zero";
s[ndv1]=0.;
nvc=0;
return;

L179:
//
//                  NORMALIZE S TO MAX ABS OF UNITY
//
s1=0.;
for(int ii=1; ii<=ndv; ii++)
  {
  a1=fabs(s[ii]);
  if(a1>s1) s1=a1;
  }
//     IF (S1.LT.1.0E-10) RETURN
//
//  E-10 CHANGED TO E-04 ON 1/12/81
//
if(s1<1.0e-4) return;
s1=1./s1;
for(int ii=1; ii<=ndv; ii++) s[ii] *= s1;
slope *= s1;
s[ndv1] *= s1;
return;
}
