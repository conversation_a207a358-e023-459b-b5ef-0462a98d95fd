#pragma once

#include "../../Definitions.h"
#include "core/math/Vector3.h"
#include "core/math/AABB.h"
#include "domain_decomposition/Domain.h"
#include "field/Field.h"
#include "../mesh/TriangleMesh.h"
#include "VoxelData.h"
#include "RayTriangleIntersection.h"
#include <memory>
#include <vector>

namespace walberla {
namespace curvedboundary {

// Main voxelization class for converting triangle meshes to voxel representation
class Voxelizer {
public:
    // Voxelization options
    struct Options {
        bool useScanConversion = true;     // Use scan conversion for solid voxelization
        bool markNearBoundary = true;      // Mark cells adjacent to boundaries
        bool computeDistanceField = true;  // Compute distance to surface
        Real boundaryThickness = 0.5;      // Thickness for boundary detection (in cells)
        Uint numRaysForAmbiguous = 6;      // Number of rays for ambiguous cases
        bool useMultiThreading = true;     // Use OpenMP for parallelization
    };
    
    Voxelizer(const Options& options = Options());
    
    // Main voxelization function
    std::shared_ptr<VoxelDataField> voxelizeMesh(
        const TriangleMesh& mesh,
        const Domain& domain,
        const AABB& boundingBox
    );
    
    // Voxelize multiple meshes
    std::shared_ptr<VoxelDataField> voxelizeMeshes(
        const std::vector<std::shared_ptr<TriangleMesh>>& meshes,
        const Domain& domain,
        const AABB& boundingBox
    );
    
    // Test if a point is inside the mesh
    bool isPointInside(
        const Vector3<Real>& point,
        const TriangleMesh& mesh
    ) const;
    
    // Find cells intersecting the mesh surface
    std::vector<Vector3<Uint>> findBoundaryCells(
        const TriangleMesh& mesh,
        const AABB& boundingBox,
        Real cellSize
    ) const;
    
    // Compute distance field for a region
    void computeDistanceField(
        VoxelDataField& voxelData,
        const TriangleMesh& mesh,
        const AABB& region
    );
    
    // Get voxelization statistics
    struct Statistics {
        Uint totalCells;
        Uint insideCells;
        Uint boundaryCells;
        Uint nearBoundaryCells;
        Real voxelizationTime;
        Real distanceFieldTime;
    };
    
    const Statistics& getStatistics() const { return stats_; }
    
private:
    Options options_;
    mutable Statistics stats_;
    
    // Internal voxelization methods
    void voxelizeSolid(
        VoxelDataField& voxelData,
        const TriangleMesh& mesh,
        Uint meshID
    );
    
    void voxelizeBoundary(
        VoxelDataField& voxelData,
        const TriangleMesh& mesh,
        Uint meshID
    );
    
    void markNearBoundaryCells(
        VoxelDataField& voxelData
    );
    
    // Scan conversion for triangle
    void scanConvertTriangle(
        VoxelDataField& voxelData,
        const Triangle& triangle,
        Uint triangleID,
        Uint meshID
    );
    
    // Check if cell intersects triangle
    bool cellIntersectsTriangle(
        const Vector3<Real>& cellCenter,
        Real cellSize,
        const Triangle& triangle
    ) const;
    
    // Robust inside/outside test for ambiguous cases
    bool robustInsideTest(
        const Vector3<Real>& point,
        const TriangleMesh& mesh
    ) const;
    
    // Compute Bouzidi q parameter
    Real computeBouzidiQ(
        const Vector3<Real>& cellCenter,
        const Vector3<Real>& closestPoint,
        const Vector3<Real>& normal,
        Real cellSize
    ) const;
    
    // Helper to get cell bounds
    AABB getCellAABB(
        const Vector3<Uint>& cell,
        Real cellSize
    ) const;
};

// Flood fill algorithm for connected component analysis
class FloodFill {
public:
    // Flood fill to mark connected regions
    static void fill3D(
        VoxelDataField& field,
        const Vector3<Uint>& seed,
        VoxelData::CellType fillType
    );
    
    // Find exterior cells using flood fill from boundaries
    static void markExteriorCells(
        VoxelDataField& field
    );
    
    // Fill small holes in the voxelization
    static void fillHoles(
        VoxelDataField& field,
        Uint maxHoleSize = 10
    );
    
private:
    // Check if cell is valid for filling
    static bool canFill(
        const VoxelDataField& field,
        const Vector3<Uint>& cell,
        VoxelData::CellType targetType
    );
};

} // namespace curvedboundary
} // namespace walberla