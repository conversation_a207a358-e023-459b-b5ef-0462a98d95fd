// Circular Cylinder Turbulence Test - WALE Model
// Tests turbulence models with curved STL geometry

simdata {
    // Domain size - similar to square cylinder but optimized for circular
    domainX     60;       // Streamwise
    domainY     30;       // Cross-stream
    domainZ     20;       // Spanwise
    dx          0.001;    // Grid spacing
    
    // Flow parameters (consistent with physics checker)
    Uref        0.05;     // Reference velocity
    Lref        0.006;    // Reference length (cylinder diameter = 6*dx)
    Re          3;        // Reynolds number = Uref*Lref/nu

    // Simulation control
    timesteps   200;      // Sufficient for wake development
    nu          1.0e-4;   // Kinematic viscosity
    rho         1.0;      // Density
    maxPermittedVel 0.08; // Maximum velocity limit
    
    dump;  // Output all computed parameters
}

// WALE turbulence model (different from previous tests)
turbulence {
    model               wale;             // Use our WALE model
    waleConstant        0.5;             // Standard WALE constant
    filterWidth         2.0;             // Filter width = 2*dx
    strainRateMethod    nonequilibrium;  // Non-equilibrium method
}

// STL geometry configuration
stl {
    // Circular cylinder STL file
    cylinder {
        file        meshes/cylinder.stl;
        position    30.0 15.0 10.0;      // Center in domain
        scale       6.0;                 // Diameter = 6 lattice units
        rotation    0.0 0.0 0.0;         // No rotation
        boundary    noslip;              // No-slip boundary condition
    }
}

// Simulation output monitoring
simoutput {
    AVG_DENS {
        time 25;  // Output average density every 25 steps
    }
}

// Logging configuration
logging {
    logfile circular_cylinder_wale.log;
    append  0;
}

// Boundary conditions (same successful format as square cylinder)
init {
    // North boundary - pressure outlet
    north {                    
        prs_nils {               
            x_L  1..'domainX';   
            z_L  1..'domainZ';
            prs_L 0.0;
        }
    }
    
    // South boundary - velocity inlet
    south {
        vel_in {
            x_L      1..'domainX';
            z_L      1..'domainZ';
            ux_L   0.04;  // Streamwise velocity
            uy_L   0.0;   // No cross-stream velocity
            uz_L   0.0;   // No spanwise velocity
        }
    }
    
    // East boundary - no slip wall
    east {
        noslip {
            y_L  0..'domainY'+1;
            z_L  1..'domainZ';
        }
    }
    
    // West boundary - no slip wall
    west {
        noslip {
            y_L  0..'domainY'+1;
            z_L  1..'domainZ';
        }
    }
    
    // Top boundary - no slip wall
    top {
        noslip {
            x_L    0..'domainX'+1;
            y_L    0..'domainY'+1;
        }
    }
    
    // Bottom boundary - no slip wall
    bottom {
        noslip {
            x_L  0..'domainX'+1;
            y_L  0..'domainY'+1;
        }
    }
}

// Output configuration for turbulence visualization
paraview {
    filename    circular_cylinder_wale;
    xstart      0;
    xend        60;
    ystart      0;
    yend        30;
    zstart      10;
    zend        10;       // Mid-plane slice
    writevelocity  1;
    writepressure  1;
    writeeddyvisc  1;     // Critical: output eddy viscosity
    writeflags     1;     // Show STL geometry
    interval       25;    // Output every 25 steps
    physical       1;     // Use physical units
}

// Additional slice for wake analysis
paraview_wake {
    filename    circular_cylinder_wake;
    xstart      30;       // Start at cylinder center
    xend        55;       // Extend into wake region
    ystart      5;
    yend        25;
    zstart      10;
    zend        10;       // Mid-plane slice
    writevelocity  1;
    writeeddyvisc  1;     // Focus on turbulent wake
    interval       50;    // Less frequent output
    physical       1;
}
