// #include "Mesh.h"
#include "Container_type.h"
#include "Algorithm.h"
#include <fstream> // file 
using std::fstream ;
using namespace ge ;

// typedef ge::Mesh  mesh  ;


// void read (const char*fileName, mesh& m) // notconst
//  {   
//    fstream myFile;
// 
//    myFile.open (fileName );
// 
//    myFile.close();
// }

class X { // does no thing

} ;

int main ()
{
  
      std::cout<< "\n" ;
      std::cout<< "=====================================\n" ;
      std::cout<< "   Asssociative Constainer Type test \n";
      std::cout<< "=====================================\n\n" ;

	Container <int , std::vector< int > > my_container ;

	assert  (my_container.size() == 0 ) ;
	
	my_container.push_back(2) ;
	
	assert  (my_container.size() == 1 ) ;
	

	for (int i=0 ; i < 10 ; ++i)
	      my_container.push_back(i) ;
	
	
	Container <int , std::vector< int > >::iterator  it ;
	for (it = my_container.begin() ; it < my_container.end() ; ++it)
	      std::cout << " *it -> " << *it << std::endl ;
	
	it = Find ( my_container.begin(), my_container.end(), 5 ) ;
	
	assert ( Find ( my_container.begin(), 
			my_container.end(), 
			5)  			 != my_container.end()   ) ; // assert that the container contains the value 5
	
	
	std::cout << "Iter -> " << *it  << std::endl ;
	
	
	
	
	//! using default comparison:
	  std::sort (my_container.begin(), my_container.end());
	
	//! binary_search
	  std::cout << "looking for a 3... ";
	  if (  std::binary_search (my_container.begin(), my_container.end(),3) )
		std::cout << "found!\n"; 
	  else 
		std::cout << "not found.\n";
	
	// assert ( std::binary_search (my_container.begin(), my_container.end(),0) ) ; // shoiuld failed
	assert ( std::binary_search (my_container.begin(), my_container.end(),1) ) ;
	assert ( std::binary_search (my_container.begin(), my_container.end(),2) ) ;
	assert ( std::binary_search (my_container.begin(), my_container.end(),3) ) ;
	
	
	
	
	return  (0) ;
}

