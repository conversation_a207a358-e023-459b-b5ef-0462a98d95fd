#ifndef VISITOR_HH
#define VISITOR_HH

struct Transform; // forward declaration
struct Geometry;  // forward declaration



struct Visitor 
{
    virtual void visit_transform( Transform* ) = 0;
    virtual void visit_geometry ( Geometry*  ) = 0;
    virtual ~Visitor() {}
};


struct Render : public Visitor {
    virtual void visit_transform( Transform* ); // render Transform
    virtual void visit_geometry ( Geometry*  ); // render Geometry
};


struct Optimize : public Visitor {
    virtual void visit_transform( Transform* ); // optimize Transform
    virtual void visit_geometry ( Geometry*  ); // optimize Geometry
};

struct Node {
    virtual void accept( Visitor* ) = 0;
    virtual ~Node() {}
};

struct Transform : public Node {
    virtual void accept( Visitor* v) { v->visit_transform( this); }
};
struct Geometry : public Node {
    virtual void accept( Visitor* v) { v->visit_geometry( this); }
};


#endif 
