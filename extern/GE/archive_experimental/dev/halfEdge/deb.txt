
==============
 m_Vertices :
==============
 vertex [0]  <x, y, z>   <0 , 0 , 0>
 vertex [1]  <x, y, z>   <0 , 1 , 0>
 vertex [2]  <x, y, z>   <0.5 , 0.5 , 0.5>
 vertex [3]  <x, y, z>   <-0.5 , 0.5 , 0.5>

==============
 m_Faces    :
==============
 f [0]  <v0, v1, v2>   <0 , 2 , 1>
 f [1]  <v0, v1, v2>   <1 , 2 , 3>
 f [2]  <v0, v1, v2>   <0 , 1 , 3>
 f [3]  <v0, v1, v2>   <0 , 3 , 2>

==============
 EdgeMap  :
==============
they are  12 edges.
 Map [8]  <v_start , v_end>   <0 , 1>
 Map [2]  <v_start , v_end>   <0 , 2>
 Map [11] <v_start , v_end>   <0 , 3>
 Map [1]  <v_start , v_end>   <1 , 0>
 Map [5]  <v_start , v_end>   <1 , 2>
 Map [6]  <v_start , v_end>   <1 , 3>
 Map [10] <v_start , v_end>   <2 , 0>
 Map [0]  <v_start , v_end>   <2 , 1>
 Map [3]  <v_start , v_end>   <2 , 3>
 Map [7]  <v_start , v_end>   <3 , 0>
 Map [4]  <v_start , v_end>   <3 , 1>
 Map [9]  <v_start , v_end>   <3 , 2>

===================
 Half-edges table :
===================
they are  12 half edges.             face_index
 he [0]  <v_end , opp>   <1 , 5>        0
 he [1]  <v_end , opp>   <0 , 8>        0
 he [2]  <v_end , opp>   <2 , 10>       0
 he [3]  <v_end , opp>   <3 , 9>        1
 he [4]  <v_end , opp>   <1 , 6>        1
 he [5]  <v_end , opp>   <2 , 0>        1
 he [6]  <v_end , opp>   <3 , 4>        2
 he [7]  <v_end , opp>   <0 , 11>       2
 he [8]  <v_end , opp>   <1 , 1>        2
 he [9]  <v_end , opp>   <2 , 3>        3
 he [10] <v_end , opp>   <0 , 2>        3
 he [11] <v_end , opp>   <3 , 7>        3

