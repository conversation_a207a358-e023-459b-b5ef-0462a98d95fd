/*******************************************************************************
ConminC++, to solve constrained or unconstrained function minimization.
Copyright (C) 1999  <PERSON><PERSON> <PERSON><PERSON> Dunn

This program is free software; you can redistribute it and/or
modify it under the terms of the GNU General Public License
as published by the Free Software Foundation; either version 2
of the License, or (at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this program; if not, write to the Free Software
Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA  02111-1307, USA.
*******************************************************************************/

// class Conmin header
//---------------------------------------------------------------------------
#ifndef conminH
#define conminH
#include <iostream>

using namespace std;
//---------------------------------------------------------------------------
enum info_list { Cal_all, Cal_active};
enum print_list { None, Init_final, Debug1, Debug2, Debug3, Debug4 };
enum grad_cal { Fdiff, External, Combined };
enum objective_fcn { Linear, NonLinear };
enum info_grad_list { All, Partial };
enum side_constraints { Bounded, Unbounded };
enum Igoto { Finished, Initial, Scale, GradientFD, SolveOneDSearch, OneDSearch};

class Conmin
{
        public:
        Conmin(int ndv=4, int ncon=3, int maxnac=3); // default
        virtual ~Conmin();  //  virtual destructor
        Conmin(const Conmin&);  // Copy operator declaration
        Conmin& operator=(const Conmin&);  // Assigment operator declaration
        Igoto conmin();  // Entry/Exit point
        void InitParameters();  // Set default user parameters
        
        // output stream functions
        //  Output stream function for enum type info list
        friend ostream& operator<<(ostream&s , info_list info);
        //  Output stream function for enum type print list
        friend ostream& operator<<(ostream&s , print_list iprint);
        //  Output stream function for enum type grad_cal
        friend ostream& operator<<(ostream&s , grad_cal nfdg);
        //  Output stream function for enum type objective_fcn
        friend ostream& operator<<(ostream&s , objective_fcn linear_obj);
        //  Output stream function for enum type info_grad_list
        friend ostream& operator<<(ostream&s , info_grad_list infog);
        //  Output stream function for enum type side_constraints
        friend ostream& operator<<(ostream&s , side_constraints nside);
        //  Output stream function for enum type Igoto
        friend ostream& operator<<(ostream&s , Igoto igoto);
        
        // Control variables
        
        print_list iprint;  // Print parameter
        info_list info;  // Supplied info parameter
        int itmax; // Maximum number of iterations
        info_grad_list infog; // Gradient calculation flag
        grad_cal nfdg;  // Finite difference information parameter
        side_constraints nside;  // Side constraint parameter
        int icndir; // Conjugate direction restart parameter
        int nscal; // Scaling control parameter
        objective_fcn linear_obj;  // Linear objective function
        int itrm; // Number of consecutive iterations to indicate convergence
        int nfeasct; //  Number of iteration attempts, added to Conmin orginal
        double fdch; // Relative change in decision variable for finite diff
        double fdchm; // Minimum absolute step in finite diff gradient cal
        double ct; // Constraint thickness parameter
        double ctmin; // Minimum absolute value of ct
        double ctl; // Constraint thickness parameter (linear and side constraints)
        double ctlmin; // Minimum absolute value of ctl
        double phi; // Participation coefficient
        double theta; // Mean value of the push-off factor
        double delfun; // Minimum relative change in the objective function for conv
        double dabfun; // Minimum absolute change in the objective function for conv
        double alphax; // Maximum fractional change in any x
        double abobj1; // Fractional change attempted at first step
        Igoto igoto; // Calculaton control
        int iter;  // Iteration number
        double obj; // Value of objective function
        double *x;  // Vector of decision variables
        double *df; // Analytic gradient of the objective function
        double *g; // Vector containing all constraint function value
        int *isc; // Linear constraint identification vector
        int *ic; // Constraint identification vector
        double **a; //  Gradient of active or violated constraints
        double **b; // Used in determining direction S
        double *s; // Move direction in the NDV-dimensional optimization space
        double *g1; // Temporary storage of constraint values G
        double *g2; // Temporary storage of constraint values G
        double *c; // Used with array B in determining direction vector S
        int *ms1; // Used with array B in determining direction vector S
        double *vlb; // Lower Bounds of x
        double *vub; // Upper bounds of x
        double *scal; // vector of scaling parameters
        int nac; // number of active constraints
        int nvc; // number of violated constraints
        
        private:
        void cnmn01(int &jgoto, double *x, double *df, double *g, int *isc,
             int *ic, double **a, double *g1, double *vlb, double *vub, double *scal,
             double *c, int *ncal, double &dx, double &dx1, double &fi, double &xi,
             int &iii );
        void cnmn02(int &jdir, double &slope, double &dftdf1, double *df, double *s);
        void cnmn03(double *x, double *s, double &slope, double & alp,
             double &fff, double &a1, double &a2, double &a3, double &a4,
             double &f1, double &f2, double &f3, double &f4, double &app,
             int *ncal, int& kount, int &jgoto);
        void cnmn04(int &ii, double &xbar, double &eps, double &x1, double &y1,
             double &slope, double &x2, double &y2, double &x3, double &y3,
             double &x4, double &y4);
        void cnmn05(double *g, double *df, double **a, double *s, double **b,
             double *c, double &slope, double &phi, int *isc, int *ic, int *ms1,
             int &nvc);
        void cnmn06(double *x, double *vlb, double *vub, double *g,
             double *scal, double *df, double *s, double *g1, double *g2,
             double &ctam, double &ctmb, double &slope, double &alp,
             double &a2, double &a3, double &a4, double &f1, double &f2, double &f3,
             double &f4, double &cv1, double &cv2, double &cv3, double &cv4,
             double &alpca, double &alpfes, double &alpln, double &alpmin, double &alpnc,
             double &alpsav, double &alpsid, double &alptot, int *isc, int *ncal,
             int &nvc, int &icount, int &igood1, int &igood2, int &igood3, int &igood4,
             int &ibest, int &iii, int &nlnc, int &jgoto);
        void cnmn07(int &ii ,double &xbar, double &eps, double &x1, double &y1,
             double &x2,double &y2,double &x3,double &y3);
        void cnmn08(int &ndb, int &ner, double *c, int *ms1, double **b);
        void CopyParameters(const Conmin& rhs);
        
        // Internal variables
        int n1, n2, n3, n4, n5;
        int ndv;  // Number of decision variables
        int ncon; // Number of constraints
        int maxnac; // Maximium number of active constraints
        //  Following variables were in COMMON block consave.
        double dm1,dm2,dm3,dm4,dm5,dm6,dm7,dm8,dm9,dm10,dm11,dm12,
               dct,dctl,abobj,cta,ctam,ctbm,obj1,slope,dx,dx1,fi,xi,dftdf1,alp,
               fff,a1,a2,a3,a4,f1,f2,f3,f4,cv1,cv2,cv3,cv4,app,alpca,alpfes,alpln,
               alpmin,alpnc,alpsav,alpsid,alptot,rspace;
        int idm1,idm2,idm3,jdir,iobj,kobj,kcount,ncal[3],nfeas,mscal,ncobj,
            kount,icount,igood1,igood2,igood3,igood4,ibest,iii,nlnc,jgoto,ispace[2];
            
        
   public:// added by cherif
        inline const int GetNDV()const{return ndv ;}   // Number of decision variables
        inline const int GetNCON()const{return ncon ;} // Number of constraints
        
};
#endif
