// Square Cylinder Turbulence Validation Test
// This tests our turbulence models with a simple square obstacle
// Uses rectangular boundary conditions to avoid curved geometry complexity

simdata {
    // Domain size - channel with square obstacle
    domainX     80;       // Streamwise (long enough for wake development)
    domainY     40;       // Cross-stream (wide enough for blockage effects)
    domainZ     20;       // Spanwise (sufficient for 3D effects)
    dx          0.001;    // Grid spacing

    // Flow parameters that satisfy physics checker
    Uref        0.08;     // Reference velocity (inlet velocity)
    Lref        0.008;    // Reference length (cylinder width = 8*dx)
    Re          6.4;      // Reynolds number = Uref*Lref/nu

    // Simulation control
    timesteps   300;      // Sufficient for flow development
    nu          1.0e-4;   // Kinematic viscosity
    rho         1.0;      // Density
    maxPermittedVel 0.12; // Maximum velocity limit

    dump;  // Output all computed parameters
}

// Test our Smagorinsky turbulence model
turbulence {
    model               smagorinsky;      // Use our Smagorinsky model
    smagorinskyConstant 0.18;            // Standard Smagorinsky constant
    filterWidth         2.0;             // Filter width = 2*dx
    strainRateMethod    nonequilibrium;  // Non-equilibrium method
}

// Channel flow with square cylinder obstacle
init {
    // Inlet (west) - uniform velocity profile
    west {
        vel_in {
            y_L     1..'domainY'-1;
            z_L     1..'domainZ'-1;
            ux_L    0.08;  // Inlet velocity (creates Re ~ 640 based on cylinder size)
            uy_L    0.0;
            uz_L    0.0;
        }
    }
    
    // Outlet (east) - pressure outlet
    east {
        prs_nils {
            y_L     1..'domainY'-1;
            z_L     1..'domainZ'-1;
            prs_L   0.0;  // Zero gauge pressure
        }
    }
    
    // Channel walls (top and bottom) - no slip
    north {
        noslip {
            x_L     0..'domainX'+1;
            z_L     0..'domainZ'+1;
        }
    }

    south {
        noslip {
            x_L     0..'domainX'+1;
            z_L     0..'domainZ'+1;
        }
    }

    // Side walls (front and back) - slip
    top {
        freeslip {
            x_L     0..'domainX'+1;
            y_L     0..'domainY'+1;
        }
    }

    bottom {
        freeslip {
            x_L     0..'domainX'+1;
            y_L     0..'domainY'+1;
        }
    }
    
    // Square cylinder obstacle (8x8 cells, centered in cross-section)
    // Located at x=20-28, y=16-24, full span in z
    obstacle_square {
        noslip {
            x_L     20..28;
            y_L     16..24;
            z_L     1..'domainZ'-1;
        }
    }
}

// Output configuration for turbulence visualization
paraview {
    filename    square_cylinder_turb;
    xstart      0;
    xend        80;
    ystart      0;
    yend        40;
    zstart      10;
    zend        10;       // Mid-plane slice
    writevelocity  1;
    writepressure  1;
    writeeddyvisc  1;     // Critical: output eddy viscosity to verify turbulence
    writeflags     1;     // Show obstacle geometry
    interval       50;    // Output every 50 steps
    physical       1;     // Use physical units
}

// Additional slice for wake analysis
paraview_wake {
    filename    square_cylinder_wake;
    xstart      25;       // Start just downstream of cylinder
    xend        75;       // Extend into wake region
    ystart      0;
    yend        40;
    zstart      10;
    zend        10;       // Mid-plane slice
    writevelocity  1;
    writeeddyvisc  1;     // Focus on turbulent wake
    interval       100;   // Less frequent output
    physical       1;
}

// Monitor turbulence quantities and forces
simoutput {
    DRAG_COEFFICIENT {
        time 25;  // Output drag coefficient every 25 steps
        refArea   0.000064;  // Cylinder frontal area (8*dx * 8*dx)
        refLength 0.008;     // Cylinder width (8*dx)
    }
    TURBULENT_KINETIC_ENERGY {
        time 25;  // Output TKE every 25 steps
        region_x  30..70;    // Focus on wake region
        region_y  10..30;    // Around cylinder wake
        region_z  5..15;     // Mid-span region
    }
    EDDY_VISCOSITY_STATS {
        time 25;  // Output eddy viscosity statistics
        region_x  15..35;    // Around cylinder
        region_y  10..30;    // Cross-stream extent
        region_z  5..15;     // Mid-span region
    }
}

// Logging configuration
logging {
    logfile square_cylinder_turbulence.log;
    append  0;
}
