
// #include "Visitor.h"

// graph2.C
// ----------------------------------------------------------------
// COMP 290-001: Algorithm Library Design, <PERSON><PERSON>, 01/11/2000
// Graph example sketches basic idea for the flexible solution
// in graph3.C.

template <class Graph>
struct Node {
    typedef typename Graph::Edge Edge;
    Edge* edge; //Edge* edge2; //Edge* edge3; 
//     virtual void accept( Visitor* ) = 0;
//     virtual ~Node() {} 
    // .... maybe some more edges ....
};

template <class Graph>
struct Edge {
    typedef typename Graph::Node Node;
    Node* node;
};

template <
	   template <class G> class T_Node,  // 
           template <class G> class T_Edge   // 
	 >
struct Graph {
    typedef Graph< T_Node, T_Edge>       Self;
    typedef T_Node<Self>                 Node;
    typedef T_Edge<Self>                 Edge;
};

// to show the flexibility, here is a different Node class,
// adding a color field to the node class from above.
template <class Graph>
struct Colored_node : public Node<Graph> {
    int color;
};


// Using two graph types -- one with the Node and Edge from above, and 
// one with the Colored_node.

int main() {
    {
	// Declaring a graph type with Node and Edge from above:
	typedef Graph< Node, Edge> G;
	G::Node node;
	G::Edge edge;
	node.edge = &edge;
	edge.node = &node;
    }
    {
        // Declaring a graph type using the modified colored node:
        typedef Graph< Colored_node, Edge> G;
	G::Node node;
	G::Edge edge;
	node.edge = &edge;
	edge.node = &node;
	node.color = 3;
    }
}

// EOF //


