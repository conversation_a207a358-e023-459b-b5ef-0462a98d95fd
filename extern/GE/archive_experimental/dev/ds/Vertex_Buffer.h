#ifndef VERTEX_BUFFER_HH
#define VERTEX_BUFFER_HH

// =============================================================================
// includes 
// =============================================================================
#include <vector>
#include <list>
#include <deque>
#include <limits>
#include "Vertex_Format.h" // #include "GVertex.h"

#include "ContainerType.h" // TODO
// =============================================================================




//!  ===========================================================================
//! 	The Vertex Buffer
//!  ===========================================================================
// Now that we can generate the type of vertex that we want, 
// making a vertex buffer template that uses it is fairly straight-forward.
//
//
namespace ge
{
      template <  typename Usage, //tell the compiler that Usage(container) must be a type
// /* this is working fine* /        typename container_type = std::vector< typename Usage::vertex > //tell the compiler which container you like to to use
typename container_type = ContainerType <typename Usage::vertex ,boost::ptr_vector <typename Usage::vertex> > 
>
     struct vbuffer 
     {
        typedef typename Usage::vertex                  vertex;  // vertex type depend on th egiven template-parameter
        typedef typename container_type::iterator       vertex_iterator;
        typedef typename container_type::const_iterator const_vertex_iterator;

      public:

	inline void reserve(const int& nv)		{return m_vertices.reserve(nv);}
	inline std::size_t size(void)const		{return m_vertices.size();}
	// iterator
	inline vertex_iterator       begin(void)	{return m_vertices.begin();}
	inline vertex_iterator       end(void)  	{return m_vertices.end();}
	// const iterator
	inline const_vertex_iterator begin(void)const	{return m_vertices.begin();}
	inline const_vertex_iterator end(void)  const	{return m_vertices.end();}

	inline std::size_t push_back(const vertex& v);
	inline std::size_t insert   (const vertex& v);



	// TODO add access operator [] from ContainerType, its save in Debug mode:=) 	
	inline vertex& operator [] (const std::size_t& index)  { return m_vertices [index] ;}
	inline vertex  operator [] (const std::size_t& index) const { return m_vertices [index] ;}
	

	
//	inline void Print () const ;
      private:

	container_type m_vertices ;
  };



      template <typename U , class C>
      inline std::size_t vbuffer<U,C>::push_back(const vbuffer<U,C>::vertex& v )
      {
	  m_vertices.push_back(v) ;
	  return m_vertices.size() - 1 ;
      }
      
      // 
      template <typename U, class C>
      inline std::size_t vbuffer<U,C>::insert(const vbuffer<U,C>::vertex& v)
      {
	    for (unsigned int i=0 ; i< m_vertices.size() ; ++i)
		if ( ( m_vertices[i] ) == v )
		  return i ;
	    m_vertices.push_back (v);
	    // m_vertices[i].neigbor_t.push_back( FACES.size() );
	    return m_vertices.size() - 1 ;
      }

//       template <typename U, class C>
//       inline void vbuffer<U,C>::Print () const 
//       {
// 	vbuffer<U,C>::const_vertex_iterator it = m_vertices.begin() ;
// 	  for (; it !=  m_vertices.end() ; ++ it)
// 	    std::cout << *(it) << endl ;
//       }

}
#endif
