#include "DebInfo.h"

//#include "vtkObject.h" // for std STL stuff

#include <iostream>


using namespace std  ;







// =============================================================================
//
// Quadtree example
//
// =============================================================================


int main()
{
  std::cout << "====================================================== " <<"\n";
  std::cout << "////////////////////////////////////////////////////// \n";
  std::cout << "		Quadtree example \n"; 
  std::cout << "////////////////////////////////////////////////////// \n";
  std::cout << "====================================================== " <<"\n";  

  
  
  // Construct a small 2-d binary tree.
//   double center[2]      = { 5., 5. }; // center 
  double DomainSize [2] = { 10., 10. }; // domain size 
 
  octree<int,2> my_octree( DomainSize, 400 );
  
  octree<int,2>::iterator it;
  octree<int,2>::cursor curs( &my_octree ); 


  const double* center_befor = (my_octree).center() ;
  my_octree.root()->value() = 42;
  std::cout<< "	-> Number of leafs -> " << my_octree.size(true) << "\n";// 1 onyl root
  std::cout<< "	-> Center(x,y,z) = ("
	   << *&center_befor[0] << "," 
	   << *&center_befor[1] << "," 
	   << *&center_befor[2] << ")\n";
	   
   my_octree.root()->add_children();
// 
   *(*my_octree.root())[0] = 25;
//   *(*my_octree.root())[1] = 19;
//   *(*my_octree.root())[2] = 8;
//   *(*my_octree.root())[3] = 3;
//   
//   const double* center_after = (my_octree).center() ;
//   std::cout << "====================================================== " <<"\n";  
//   std::cout<< "After add Chikdren\n";
//   std::cout<< "	-> Number of leafs ->  "      <<my_octree.size(true) << "\n"; // root +  4 child
//   std::cout<< "	-> Number of nodes(total) -> "<<my_octree.size(false) << "\n"; // root +  4 child
//   std::cout<< "	-> Center(x,y,z) = ("
// 	   << *&center_after[0] << "," 
// 	   << *&center_after[1] << "," 
// 	   << *&center_after[2] << ")\n";
// 	   
// 	 
	   
  std::cout<< "test output operator \n"<<  my_octree  << std::endl;
	   
//     const double* bds =  ((it).center());


//   (*my_octree.root())[1].add_children();
//   (*my_octree.root())[3].add_children();
//   
//   std::cout << "====================================================== " <<"\n"; 
//   std::cout<< "After add Chikdren\n";
//   std::cout<< "	-> Number of leafs ->  "      <<my_octree.size(true) << "\n"; // 
//   std::cout<< "	-> Number of nodes(total) -> "<<my_octree.size(false) << "\n"; //
//   std::cout << "====================================================== " <<"\n";
// 
//   *(*my_octree.root())[1][0] = 38;
//   *(*my_octree.root())[1][1] =  5;
//   *(*my_octree.root())[1][2] = -19;
//   *(*my_octree.root())[1][3] =  1;
// 
// 
//   *(*my_octree.root())[3][0] = 15;
// 
// //  (*my_octree.root())[3].remove_children();
// 
// 
//   std::cout << "====================================================== " <<"\n";
//   std::cout << "Root is " << my_octree.root()->value() << "\n";
//   std::cout << "Child 0 is " << (*my_octree.root())[0].value() << "\n";
//   std::cout << "Child 1 is " << (*my_octree.root())[1].value() << "\n";
//   std::cout << "Child 2 is " << (*my_octree.root())[2].value() << "\n";
//   std::cout << "Child 3 is " << (*my_octree.root())[3].value() << "\n";
//   std::cout << "====================================================== " <<"\n";
// 
//   std::cout << "\n\n";
//   std::cout << "====================================================== " <<"\n";
//   std::cout << "Print only leaf nodes.\npreorder\n";
//   std::cout << "====================================================== " <<"\n";
//   // Now test an iterator
//   
//     OnlyLeafsNodesDepthFirstTraversal (my_octree) ;
//     
//   std::cout << "====================================================== " <<"\n";
//   
//   
// 
//   
//   
//   
//   std::cout << "\n\n";
//   std::cout << "====================================================== " <<"\n";
//   std::cout << "Print all nodes\npreorder\n";
//   std::cout << "====================================================== " <<"\n";
//   
//     AllNodesDepthFirstTraversal (my_octree) ;
//   
//   std::cout << "====================================================== " <<"\n";
//   
//   
//   
//   std::cout << "\n\n";
//   std::cout << "====================================================== " <<"\n";
//   std::cout << "Print only leaf nodes.\ninorder (symetric)\n";
//   std::cout << "it the opposit of wikipedia say\n";
//   std::cout << "traverse first right, then the parent, and after that the left\n";    
//   std::cout << "====================================================== " <<"\n";
//   
// 	OnlyLeafsNodesInorderSymetricDepthFirstTraversal ( my_octree ) ;
//   
//   std::cout << "====================================================== " <<"\n";
// 
//   
//   
//   
//   
//   std::cout << "\n\n";
//   std::cout << "====================================================== " <<"\n";
//   std::cout << "Print all nodes.\ninorder (symetric)\n";
// //   std::cout << "it the opposit of wikipedia say\n";
// //   std::cout << "traverse first right, then the parent, and after that the left\n";    
//   std::cout << "====================================================== " <<"\n";
//   
// 	AllNodesInorderSymetricDepthFirstTraversal ( my_octree );
// 	
//   std::cout << "====================================================== " <<"\n";
// 
// 
// 
// 
// 
//   std::cout << "====================================================== " <<"\n";
//   std::cout << "Now test the immediate family mode.\n";
//   std::cout << "Only Leaves nodes will be traversed\n";   
//   std::cout << "====================================================== " <<"\n";  
//   // Now test the "immediate family" mode.
//   it = my_octree.begin();
//   ++it;
//   it.immediate_family( true );
//   for ( ; it != my_octree.end(); ++it )
//   {
// //     const double* bds = it->center();
//       /*
//     const double* bds = it->center();
//     double he = it->size() / 2.;
//     */
//     std::cout
//       << "Node  " 
//       << " (" << (it.level()) << ") "
//       << " = " << it->value()
//       /*
//       << " [" << (bds[0] - he) << "->" << (bds[0] + he)
//       << ", " << (bds[1] - he) << "->" << (bds[1] + he) << "]"
//       */
//       << "\n";
//     }
// 
// 
//   std::cout << "\n\n";
//   std::cout << "====================================================== " <<"\n";
//   std::cout << "Now test the immediate family mode.\n";
//   std::cout << "All nodes will be traversed\n";   
//   std::cout << "====================================================== " <<"\n"; 
//   it = my_octree.begin( false );
//   ++it;
//   it.immediate_family( true );
//   for ( ; it != my_octree.end( false ); ++it )
//     {
//     
// //     const double* bds =  ((it).center());
// //     double he = it->size() / 2.;
//    
// //           << "Node  0x" << hex << (&*it)
//     std::cout
//       << "Node  "
//       << " (" << (it.level()) << ") "
//       << " = " << it->value()
//       /*
//       << " [" << (bds[0] - he) << "->" << (bds[0] + he)
//       << ", " << (bds[1] - he) << "->" << (bds[1] + he) << "]"
//       */
//       << "\n";
//     }
// 
//   std::cout << "\n\n";
// 
//   // Test octree cursors:
//   //  octree<int,2>::cursor curs( &my_octree );
//   curs.down( 0 );
//   curs.over( 1 );
//   std::cout << "Initial L2Node: " << "level " << curs.level() << " where " << curs.where() << " val " << curs->value() << "\n";
//   curs.axis_partner( 1 );
//   std::cout << "Axis 1 partner: " << "level " << curs.level() << " where " << curs.where() << " val " << curs->value() << "\n";
//   curs.over( 1 );
//   curs.axis_partner( 0 );
//   std::cout << "Axis 0 partner: " << "level " << curs.level() << " where " << curs.where() << " val " << curs->value() << "\n";
//   curs.over( 1 );
//   curs.down( 3 );
//   std::cout << "Down to level2: " << "level " << curs.level() << " where " << curs.where() << " val " << curs->value() << "\n";
// 
//   // Copy an iterator's position
//   curs = my_octree.begin();
//   std::cout << "level " << curs.level() << " where " << curs.where() << " val " << curs->value() << "\n";

  return 0;
}
