/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2.0.1                                 |
|   \\  /    A nd           | Web:      www.OpenFOAM.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       dictionary;
    location    "system";
    object      controlDict;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

application     icoFoam;

startFrom       startTime;

startTime       0;

stopAt          endTime;

endTime         .3;

deltaT          0.05;

writeControl    adjustableRunTime;

writeInterval   0.05;

purgeWrite      0;

writeFormat     ascii;

writePrecision  12;

writeCompression off;

timeFormat      general;

timePrecision   12;

runTimeModifiable true;


// ************************************************************************* //

functions
{

///////////////////////////////////////////////////////////////////////////

    minmaxdomain
    {
	type fieldMinMax;
	//type banana;

	functionObjectLibs ("libfieldFunctionObjects.so");

	enabled true;

	mode component;

	writeControl timeStep;
	writeInterval 1;

	log true;

	fields (p U);
    }

///////////////////////////////////////////////////////////////////////////

};
