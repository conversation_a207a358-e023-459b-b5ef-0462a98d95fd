// Turbulence Model Validation Test
// This parameter file specifically tests our turbulence models

simdata {
    domainX         32;         // Small domain for quick testing
    domainY         16;         // Small domain for quick testing
    domainZ         16;         // Small domain for quick testing
    dx              0.001;      // Cell size delta x
    Uref            0.01;       // Reference velocity
    timesteps       100;        // Short test run
    Lref            0.016;      // Reference length
    nu              1.0e-5;     // Viscosity
    rho             1.0;        // Density
    maxPermittedVel 0.02;       // Maximum velocity

    dump;  // Output all computed parameters
}

// Test our Smagorinsky turbulence model
turbulence {
    model               smagorinsky;      // Use our Smagorinsky model
    smagorinskyConstant 0.18;            // Standard Smagorinsky constant
}

init {
    // Simple inlet/outlet setup
    south {
        vel_in {
            x_L     1..'domainX';
            z_L     1..'domainZ';
            ux_L    0.0;
            uy_L    0.03;  // Small inlet velocity
            uz_L    0.0;
        }
    }
    
    north {
        prs_nils {
            x_L     1..'domainX';
            z_L     1..'domainZ';
            prs_L   0.0;
        }
    }
    
    // Side walls - no slip
    east {
        noslip {
            y_L     0..'domainY'+1;
            z_L     1..'domainZ';
        }
    }
    
    west {
        noslip {
            y_L     0..'domainY'+1;
            z_L     1..'domainZ';
        }
    }
    
    // Top and bottom - no slip
    top {
        noslip {
            x_L     0..'domainX'+1;
            y_L     0..'domainY'+1;
        }
    }
    
    bottom {
        noslip {
            x_L     0..'domainX'+1;
            y_L     0..'domainY'+1;
        }
    }
}

// Output to verify turbulence model is working
paraview {
    filename    turbulence_validation;
    xstart      0;
    xend        32;
    ystart      0;
    yend        16;
    zstart      8;
    zend        8;       // Single plane
    writevelocity  1;
    writepressure  1;
    writeeddyvisc  1;    // This should show non-zero values if turbulence model is active
    interval       50;   // Output every 50 steps
    physical       1;    // Use physical units
}

logging {
    logfile turbulence_validation.log;
    append  0;
}
