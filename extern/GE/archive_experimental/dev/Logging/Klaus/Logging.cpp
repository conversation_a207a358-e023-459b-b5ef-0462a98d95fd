
#include "Logging.h"

	// =====================================================================
	// Definition and initialization of the static member variables
	// =====================================================================
namespace ge
{
	std::string Logging::file_;
	
	
	
	// =====================================================================
	/*!\fn Logging::Logging()
	// \brief Constructor of the logging class.
	//
	// The constructor is used only once by the single instance of the logging class. It relies on
	// the fact, that the logging class has already been configured. The constructor opens the log
	// file and appends a file header with a time stamp.
	 */
	Logging::Logging()
	{
		char timeStr[50];  //Time string for the header line in the log file
		time_t t;
		tm* localTime;

		//Calculation of the local time
		time( &t );
		localTime = localtime( &t );

		//Construction of the filename and time string
		strftime( timeStr, 50, "%A, %d.%B %Y, %H:%M", localTime );
		const size_t length = strlen( timeStr );

		//Opening the file stream and and setting it to flush the buffer after each insertion
		log_.open( Logging::file_.c_str(), std::ofstream::out | std::ofstream::app );
		log_.setf( std::ios::unitbuf );
		log_.precision(10);

		//Writing the header line
		log_ << "\n\n--";
		#ifdef DEBUG
			log_ << " DEBUG ";
		#else
			log_ << " RELEASE ";
		#endif
	}
	// =====================================================================




	



	//======================================================================
	//
	//  UTILITY FUNCTIONS
	//
	//======================================================================

	// =====================================================================
	/*!\fn Logging& Logging::Instance()
	// \brief Access to the single instance of the logging class.
	//
	// \return The single instance of the logging class.
	// \exception std::runtime_error Logging class has not been configured.
	//
	// The 'Log' function directly commits the message string to file.
	 */
	Logging& Logging::Instance()
	{
		static std::auto_ptr<Logging> logger_;
		if( !logger_.get() )
		{
			if( file_.empty() )
			throw std::runtime_error( "Logging not configured!" );
			logger_ = std::auto_ptr<Logging>( new Logging() );
		}
		return *logger_.get();
	}
	// =====================================================================
	
	
	
	// =====================================================================
	/*!\fn Logging::~Logging()
	// \brief Destructor of the logging class.
	//
	// The destructor is used only once by the single instance of the logging class. The constructor
	// appends a footer in the log file and closes it.
	 */
	// =====================================================================
	Logging::~Logging()
	{
		char timeStr[50];  //Time string for the bottom line in the log file
		time_t t;
		tm* localTime;

		//Calculation of the local time
		time( &t );
		localTime = localtime( &t );

		//Construction of the filename and time string
		strftime( timeStr, 50, "%A, %d.%B %Y, %H:%M", localTime );
		const size_t length = strlen( timeStr );

		//Writing the bottom line
		log_ << "\n\n--LOG END , " << std::setw(length) << timeStr
			<< std::setw(65-length) << std::setfill('-') << "\n\n" << std::endl;

		//Closing the file stream
		log_.close();
	}
	// =====================================================================

	
	
	
}