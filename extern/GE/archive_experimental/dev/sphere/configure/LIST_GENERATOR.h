#ifndef LIST_GENERATOR_HH
#define LIST_GENERATOR_HH

#include "Configure.h"


// tmp constants
enum Ownership  {ext_ref, own_ref, cp};
enum Morphology {mono, poly};

enum CounterFlag {with_counter, no_counter};
enum TracingFlag {with_tracing, no_tracing};



//! =============================================================================
//!
//!			Automatate list generator 
//!
//! =============================================================================

template <
	    class       ElementType_,		  	// type (ie. int ..)
	    Ownership   ownership    = cp, 		// ext_ref | own_ref | cp
	    Morphology  morphology   = mono,		//
	    CounterFlag counter_flag = no_counter,	//
	    TracingFlag tracing_flag = no_tracing,	//
	    class       LengthType_  = int	        //
	  >
class LIST_GENERATOR
{
public:

  struct Config;  // forward declaration of the configuration repository

private:

  // define the constants used for type selection
  enum{
	  is_copy      = ownership==cp, // true ^ false or  (0 or 1 )
	  is_own_ref   = ownership==own_ref,
	  is_mono      = morphology==mono,
	  has_counter  = counter_flag==with_counter,
	  does_tracing = tracing_flag==with_tracing 
  };

  // select the components
  typedef typename
  IF<is_copy || is_own_ref,
     ElementDestroyer<ElementType_>,
     EmptyDestroyer<ElementType_>
  >::RET Destroyer_;

  typedef typename
  IF<is_mono,
     DynamicTypeChecker<ElementType_>,
     EmptyTypeChecker<ElementType_>
  >::RET TypeChecker_;

  typedef typename
  IF<is_copy,
     typename 
     IF<is_mono,
	MonomorphicCopier<ElementType_>,
	PolymorphicCopier<ElementType_> >::RET,
     EmptyCopier<ElementType_>
  >::RET Copier_;

  typedef typename
  IF<is_copy, const ElementType_, ElementType_>::RET ElementArgumentType_;

  // define the list type
  typedef PtrList<Config> BasicList;

  typedef typename
  IF<has_counter,LengthList<BasicList>,  BasicList >::RET   OptLengthList;

  typedef typename
  IF<does_tracing, TracedList<OptLengthList>,  OptLengthList >::RET List;

public:
    
    typedef List RET; // return the final list type

    struct Config     // define the configuration repository
    {
      typedef ElementType_           ElementType;
      typedef ElementArgumentType_   ElementArgumentType;
      typedef Copier_                Copier;
      typedef Destroyer_             Destroyer;
      typedef TypeChecker_           TypeChecker;
      typedef LengthType_            LengthType;
      typedef RET                    FinalListType;
    };
};

//=================================================================================
// 
// Example
// 
//=================================================================================

// Now we can specify a list configuration TracedIntList we saw earlier as follows:
typedef LIST_GENERATOR<int,cp,mono,no_counter,with_tracing>::RET      TracedIntList;

// If no tracing was required, we could simply write

typedef LIST_GENERATOR<int>::RET IntList;

#endif 
