//------------------------------------------------------------------------------
#ifndef UNIX
#pragma hdrstop
#include <condefs.h>
#endif

#include "src/conmin.h"
#include "main_analysis.h"
//------------------------------------------------------------------------------

#define np 4.0;


//------------------------------------------------------------------------------
#pragma argsused

int main()
{
	const int ndv=2, ncon=2, nacp1=3;

	//   Initialize
	Conmin ex1(ndv, ncon, nacp1);

	//   set parameters
	ex1.iprint 	= Debug4;
	ex1.info	= Cal_all;
	ex1.itmax	= 40;
	ex1.infog	= All;
	ex1.nfdg	= Fdiff;
	ex1.nside	= Bounded;
	ex1.icndir	= 0;
	ex1.nscal	= 0;
	ex1.fdch	= 0;
	ex1.fdchm	= 0.;
	ex1.ct		= 0.;
	ex1.ctmin	= 0.;
	ex1.ctl		= 0.;
	ex1.ctlmin 	= 0.;
	ex1.theta 	= 0.;
	ex1.phi 	= 0.;
	ex1.delfun 	= 0.;
	ex1.dabfun 	= 0.;
	ex1.linear_obj 	= Linear;
	ex1.itrm 	= 0;
	ex1.alphax	= 0.;
	ex1.abobj1	= 0;

	//   Initialize constraint identification vector, isc.
	for(int j=1; j<=ncon; j++){
		ex1.isc[j] = 0;
	}
	//   Initialize x-vector

	ex1.x[1] = 2.;
	ex1.x[2] = 2.;
	
	for(int j=1; j<= ndv; j++)
	{
		ex1.vub[j]= np;
 		ex1.vlb[j]= 1.0;
	}

	//   Solve optimization
	while( ex1.conmin() != Finished)
		ex1.obj = Ex1_Analysis( ex1.x, ex1.g );

	std::cout<<"\n";
	return 0;
}
