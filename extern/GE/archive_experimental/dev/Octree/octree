#ifndef __octree
#define __octree

#include <cfloat>
#include <stdexcept>
#include <vector>

// The following headers should not be included directly.

#include "octree_node.h"
#include "octree_path.h"
#include "octree_cursor.h"
#include "octree_iterator.h"
#include "octree.h"

#include "octree_node.cxx"
#include "octree_path.cxx"
#include "octree_cursor.cxx"
#include "octree_iterator.cxx"
#include "octree.cpp"



#endif // __octree_h
