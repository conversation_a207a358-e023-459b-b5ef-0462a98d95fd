#include <map>
#include <iostream>
#include <string>

// class Vec3f;
// class CmpVec
// {
// public:
// 
//   CmpVec(float _eps=FLT_MIN) : eps_(_eps) {}
// 
//   bool operator()( const Vec3f& _v0, const Vec3f& _v1 ) const
//   {
//     if (fabs(_v0[0] - _v1[0]) <= eps_) 
//     {
//       if (fabs(_v0[1] - _v1[1]) <= eps_) 
//       {
// 	return (_v0[2] < _v1[2] - eps_);
//       }
//       else return (_v0[1] < _v1[1] - eps_);
//     }
//     else return (_v0[0] < _v1[0] - eps_);
//   }
// 
// private:
//   float eps_;
// };


struct vertex
{
    double _x;
    double _y;
    double _z;
    
    coordinate(const double& x,const double& y,const double& z)
    : _x( x )
    , _y( y )
    , _z( z )
    {}
};

struct VertexCompare
{
  bool operator()( const coordinate& a ,const coordinate& b )const
    {
        return ( a._x == b._x ) ? ( a._y < b._y ) : ( a._x < b._x );
   }
};

struct coordinatecompare
{
    /*inline*/ bool operator()(const coordinate& a,const coordinate& b)const{
        return ( a._x == b._x ) ? ( a._y < b._y ) : ( a._x < b._x );
    }
    
    bool operator()(const coordinate& a,const coordinate& b)const
    {
        return ( a._x == b._x ) ? ( a._y < b._y ) : ( a._x < b._x );
    }
    
//   bool operator()( const coordinate& _v0, const coordinate& _v1 ) const
//   {
//     if (fabs(_v0.x - _v1.x) <= eps_) 
//     {
//       if (fabs(_v0.y - _v1.y) <= eps_) 
//       {
// 	return (_v0.z < _v1.z- eps_);
//       }
//       else return (_v0.y < _v1.y - eps_);
//     }
//     else return (_v0.x < _v1.x - eps_);
//   }
    
    
    
};

int main() 
{
    typedef std::map<  coordinate , 
		        std::string,
		        coordinatecompare
		     > mymap_t;
    
    mymap_t mymap;

    mymap[ coordinate( 1.f , 2.f ) ] = std::string( "Hello!");
   
    mymap_t::const_iterator 
	    it = mymap.find( coordinate( 1.f , 2.f ) );

    if ( it != mymap.end() )
    {
        std::cout << it->second << std::endl;
	
    } else {
      
        std::cout << "not found" << std::endl;
    }

    return 0;
}
