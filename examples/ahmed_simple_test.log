

-- RELEASE -- SVN Revision 134 -- LOG BEGIN , Tuesday, 17.June 2025, 11:22---------------------------------


 INFO:    wa<PERSON><PERSON><PERSON><PERSON> called with parameter file: ahmed_body_simple_test.prm
 ERROR:   PhysicalCheck::ComputeData (on behalf of Domain::InitSimData):                  Re cannot be computed.
                                                                                          Please specify either Re directly or all missing values of one alternative as specified by the following expression:
                                                                                          (Note: '+' means AND, ';' means OR/Alternative)
                                                                                          Re;Uref+Lref;
                                                                                          More clearly, this means, you have to take one of these alternatives:

- Specify a value for Ure<PERSON> and Uref and Lref
 ERROR:   PhysicalCheck::CheckAndCompleteParam (on behalf of Domain::InitSimData): Error with parameter Re. Check Log!
