
#ifndef SSE_HH
#define SSE_HH
 
 
//  Examples of instrinsics below:
/*
    Intel & AMD:
    //==========
    vr = _mm_add_ps(va, vb);

    Cell Processor (SPU):
    //====================
    vr = spu_add(va, vb);

    Altivec:(a variant of PowerPC AltiVec.)
    //======================================    
    // AltiVec is a floating point and integer SIMD instruction set designed and owned by Apple, IBM    

    vr = vec_add(va, vb);

    
  Application :Returning results by value
 //======================================
 
    By observing the intrisics interface a vector library must imitate that interface to maximize performance. Therefore, you must return the results by value and not by reference, as such:

   typedef __m128 Vec4; 

    //correct
    inline Vec4 VAdd(Vec4 va, Vec4 vb)
    {
    return(_mm_add_ps(va, vb));
    }; 
	
 */

 
 
 #if defined(__MMX__) 
    #include <xmmintrin.h>
//     std::cout<<" NOTE::  SSE1 is activate \n";  
#elif defined(__SSE__)
    #include <xmmintrin.h>
     std::cout<<" NOTE::  SSE1 is activate \n";
#elif defined(__SSE2__)
    #include <emmintrin.h>
    std::cout<<" NOTE::  SSE2 is activate \n"; 
#elif defined(__SSE3__)
    #include <pmmintrin.h>
    std::cout<<" NOTE::  SSE3 is activate \n";
#else
    std::cout<<" NOTE::  don support SSE \n";
#endif
 
 
 
 
/*!
  @brief Fast mathematic functions using SSE-support (default sse2 is used)!
 
  @Note: this methods will ONLY work with a CPU with the SSE instruction set
*/


#if __INTEL_COMPILER

      float dot_product_intrin(float *a, float *b, int size ) ;
  
#endif

      float dot_product(float *a, float *b, int size) ;


      
      
      



#endif 
