#include "../configure/Configure.h"


// ==========================================================================
//! 				Example of configuration 
// ========================================================================== 
template< class T> class PtrList ;                  // forward declaration
template <class OptCounterList> class TracedList ;  // forward declaration

// 
// template< class T = PtrList <T> > 
// class TracedList ;
// 
struct TracedIntListConfig
{
  typedef int                             ElementType; // object type
  typedef const ElementType               ElementArgumentType;
  typedef MonomorphicCopier<ElementType>  Copier;      // PolymorphicCopier  | MonomorphicCopier | EmptyCopier
  typedef ElementDestroyer<ElementType>   Destroyer;
  typedef DynamicTypeChecker<ElementType> TypeChecker; // DynamicTypeChecker | EmptyTypeChecker
// with or without tracing.cplusplus.com/forum/general/1414/
//  typedef PtrList<TracedIntListConfig>     FinalListType; // wihtout tracing
   typedef TracedList< PtrList<TracedIntListConfig> >    FinalListType; // with tracing 
};
// 


int main ()
{
  
  
      return (0) ;
}

