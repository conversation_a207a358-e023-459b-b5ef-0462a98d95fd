

FUNCTION(VERSI<PERSON>_COMPARE OUTVARNAME LEFTMAJOR LEFTMINOR LEFTBUILD RIGHTMAJOR RIGHTMINOR RIGHTBUILD)


IF   (LEFTMAJOR LESS RIGHTMAJOR)
  SET (${OUTVARNAME} -1 PARENT_SCOPE)
ELSE (LEFTMAJOR LESS RIGHTMAJOR)
  IF  (LEFTMAJOR GREATER RIGHTMAJOR)
    SET (${OUTVARNAME} 1 PARENT_SCOPE)
  ELSE (LEFTMAJOR GREATER RIGHTMAJOR)
    IF   (LEFTMINOR LESS RIGHTMINOR)
      SET (${OUTVARNAME} -1 PARENT_SCOPE)
    ELSE (LEFTMINOR LESS RIGHTMINOR)
      IF   (<PERSON>EF<PERSON>INOR GREATER RIGHTMINOR)
        SET (${OUTVARNAME} 1 PARENT_SCOPE)
      ELSE (LEFTMINOR GREATER RIGHTMINOR)
        IF   (LEFTBUILD LESS RIGHTBUILD)
          SET (${OUTVARNAME} -1 PARENT_SCOPE)
        ELSE (LEFTBUILD LESS RIGHTBUILD)
          IF   (<PERSON>EF<PERSON><PERSON>UI<PERSON> GREATER RIGHTBUILD)
            SET (${OUTVARNAME} 1 PARENT_SCOPE)
          ELSE (LEFTBUILD GREATER RIGHTBUILD)
            SET (${OUTVARNAME} 0 PARENT_SCOPE)
          ENDIF(LEFTBUILD GREATER RIGHTBUILD)
        ENDIF(LEFTBUILD LESS RIGHTBUILD)
      ENDIF(LEFTMINOR GREATER RIGHTMINOR)
    ENDIF(LEFTMINOR LESS RIGHTMINOR)
  ENDIF(LEFTMAJOR GREATER RIGHTMAJOR)
ENDIF(LEFTMAJOR LESS RIGHTMAJOR)

ENDFUNCTION(VERSION_COMPARE) 

