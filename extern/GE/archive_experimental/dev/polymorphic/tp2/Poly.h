  
#ifndef POLY_HH
#define POLY_HH

namespace ObjPoly
{
      //========================================================================
      /*!
      *  @brief The base class defines a virtual destructor to allow deletion of
      *  derived objects using a pointer to a base object. In addition,
      *  it declares an abstract function clone() which allocates a copy
      *  of the object and an abstract function get_object() used to
      *  obtain a reference to the T object hold in the derived object.
      *  The derived class just implements these methods:
      */
      //========================================================================
    
      template <typename T>
      struct poly_base 
      {
		  virtual ~poly_base() {}          //! support derivation
		  virtual  poly_base* clone() = 0; //! copy derived object
		  virtual T& get_object()     = 0; //! stored in derived
      };

      
      //========================================================================
      /*!
      *  @brief Each poly_object<T, S> stores an object of type S to which a
      *         reference is returned by the get_object() method, i.e. S is
      *         derived from T. The stored object is initialized using copy
      *         construction. The whole purpose of this class system is the
      *         method clone() which creates a copy of the correct type without
      *         requiring such a method for the type T: This method just
      *         allocates a new copy of poly_object<T, S> initialized by copy
      *         construction from *this.
      */
      //========================================================================
      
      template <typename T, typename S>
      struct poly_object: public poly_base<T> 
      {
			//The stored object is initialized using copy construction.
			poly_object(S const& s): object(s) {}

			// clone 
			inline poly_base<T>* clone()/*const*/{
				return new poly_object(*this); 
			}

			inline T& get_object() { return object; }
        
		private:
			S object;
      };

    
      
      //========================================================================
      /*!
      * @brief These two classes are just auxiliary classes which are not used
      *        directly by the user. Instead, the user only uses the copying
      *        pointer:
      */
      //========================================================================
      
      template <typename T>
      struct poly
      {
			poly(): ptr(0) {}                 // no object by default
			~poly() { delete ptr; }           // release current object

			template <typename S>             // creation from prototype
			poly(S const& s):			ptr(new poly_object<T, S>(s)) {}
    
			poly(poly const& p):              // create a cloned object
					ptr(p.ptr->clone()) {}
      
			poly& operator= (poly const& p) { // assign cloned object
				poly_base<T>* tmp = p.ptr->clone();
				std::swap(ptr, tmp);
				delete tmp;
				return *this;
			}
        
		 //======================================================================
		 //!@brief Accessors to the hold object:
		 //======================================================================	
		 
		 T& operator*()  const { return ptr->get_object(); }
		 
		 T* operator->() const { return &(ptr->get_object()); }
      
	 private:
	
		 poly_base<T>* ptr;
      
	 };
      
      
      
    
      
      //========================================================================
      // operator less than for sroting objects
      //========================================================================
      
      template <typename T>
      bool operator< (poly<T> const& p1, poly<T> const& p2) 
      {
	  return *p1 < *p2;
      }

} // end namespace 
       



#endif
