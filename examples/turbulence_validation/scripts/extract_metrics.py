#!/usr/bin/env python3
"""
Advanced Post-Processing for Turbulence Validation
Extracts quantitative metrics from VTU files and simulation logs
"""

import os
import sys
import numpy as np
import pandas as pd
from pathlib import Path
import re
import json
from datetime import datetime

try:
    import vtk
    from vtk.util.numpy_support import vtk_to_numpy
    VTK_AVAILABLE = True
except ImportError:
    VTK_AVAILABLE = False
    print("Warning: VTK not available. Some features will be limited.")

class MetricsExtractor:
    def __init__(self, project_root):
        self.project_root = Path(project_root)
        self.results = {}
        
    def extract_from_log(self, log_file):
        """Extract metrics from simulation log file"""
        metrics = {}
        
        if not os.path.exists(log_file):
            return metrics
            
        try:
            with open(log_file, 'r') as f:
                content = f.read()
                
            # Extract turbulence model information
            if 'Smagorinsky model' in content:
                metrics['turbulence_model'] = 'Smagorinsky'
                # Extract Smagorinsky constant
                match = re.search(r'sim\.csmag\s*=\s*([\d\.]+)', content)
                if match:
                    metrics['smagorinsky_constant'] = float(match.group(1))
                    
            # Extract final kinetic energy
            lines = content.split('\n')
            for line in reversed(lines):
                if 'Ek :' in line:
                    match = re.search(r'Ek\s*:\s*([\d\.e\-\+]+)', line)
                    if match:
                        metrics['final_kinetic_energy'] = float(match.group(1))
                        break
                        
            # Extract maximum velocity
            for line in reversed(lines):
                if 'Max Velocity:' in line:
                    match = re.search(r'Length:\s*([\d\.e\-\+]+)', line)
                    if match:
                        metrics['max_velocity_magnitude'] = float(match.group(1))
                        break
                        
            # Extract simulation completion info
            if 'timesteps' in content:
                # Find total timesteps
                match = re.search(r'timesteps\s+(\d+)', content)
                if match:
                    metrics['total_timesteps'] = int(match.group(1))
                    
            # Check for successful completion
            if 'Writing 25 timesteps' in content or 'Writing' in content:
                metrics['simulation_status'] = 'completed'
            else:
                metrics['simulation_status'] = 'incomplete'
                
        except Exception as e:
            print(f"Warning: Could not extract metrics from {log_file}: {e}")
            
        return metrics
    
    def extract_from_vtu(self, vtu_files):
        """Extract metrics from VTU files using VTK"""
        if not VTK_AVAILABLE:
            print("VTK not available. Skipping VTU analysis.")
            return {}
            
        metrics = {}
        
        try:
            # Process the last VTU file (final timestep)
            if vtu_files:
                last_file = sorted(vtu_files)[-1]
                
                # Read VTU file
                reader = vtk.vtkXMLUnstructuredGridReader()
                reader.SetFileName(str(last_file))
                reader.Update()
                
                grid = reader.GetOutput()
                
                # Extract velocity field
                velocity_array = grid.GetPointData().GetArray('velocity')
                if velocity_array:
                    velocities = vtk_to_numpy(velocity_array)
                    
                    # Calculate velocity statistics
                    velocity_magnitudes = np.linalg.norm(velocities, axis=1)
                    metrics['mean_velocity_magnitude'] = float(np.mean(velocity_magnitudes))
                    metrics['max_velocity_magnitude_vtu'] = float(np.max(velocity_magnitudes))
                    metrics['velocity_std'] = float(np.std(velocity_magnitudes))
                    
                # Extract pressure field
                pressure_array = grid.GetPointData().GetArray('pressure')
                if pressure_array:
                    pressures = vtk_to_numpy(pressure_array)
                    metrics['mean_pressure'] = float(np.mean(pressures))
                    metrics['pressure_std'] = float(np.std(pressures))
                    metrics['pressure_range'] = float(np.max(pressures) - np.min(pressures))
                    
                # Extract eddy viscosity if available
                eddy_visc_array = grid.GetPointData().GetArray('eddy_viscosity')
                if eddy_visc_array:
                    eddy_visc = vtk_to_numpy(eddy_visc_array)
                    metrics['mean_eddy_viscosity'] = float(np.mean(eddy_visc))
                    metrics['max_eddy_viscosity'] = float(np.max(eddy_visc))
                    
        except Exception as e:
            print(f"Warning: Could not extract VTU metrics: {e}")
            
        return metrics
    
    def calculate_drag_coefficient(self, vtu_files, cylinder_center, cylinder_diameter):
        """Calculate drag coefficient from pressure and velocity fields"""
        if not VTK_AVAILABLE:
            return None
            
        try:
            # This is a simplified drag calculation
            # In practice, would need surface integration over cylinder boundary
            
            # For now, return a placeholder that could be implemented
            # with proper surface mesh and integration
            return {
                'drag_coefficient': None,
                'method': 'surface_integration_needed',
                'note': 'Requires surface mesh for accurate calculation'
            }
            
        except Exception as e:
            print(f"Warning: Could not calculate drag coefficient: {e}")
            return None
    
    def analyze_time_series(self, vtu_files):
        """Analyze time series data for frequency analysis (Strouhal number)"""
        if not VTK_AVAILABLE or len(vtu_files) < 10:
            return {}
            
        try:
            time_series_data = []
            
            for vtu_file in sorted(vtu_files):
                reader = vtk.vtkXMLUnstructuredGridReader()
                reader.SetFileName(str(vtu_file))
                reader.Update()
                
                grid = reader.GetOutput()
                
                # Extract velocity at a monitoring point (e.g., wake region)
                # This is simplified - would need proper point location
                velocity_array = grid.GetPointData().GetArray('velocity')
                if velocity_array:
                    velocities = vtk_to_numpy(velocity_array)
                    # Use mean velocity as a simple time series
                    mean_vel = np.mean(np.linalg.norm(velocities, axis=1))
                    time_series_data.append(mean_vel)
                    
            if len(time_series_data) > 10:
                # Simple frequency analysis
                fft = np.fft.fft(time_series_data)
                freqs = np.fft.fftfreq(len(time_series_data))
                
                # Find dominant frequency (excluding DC component)
                dominant_freq_idx = np.argmax(np.abs(fft[1:len(fft)//2])) + 1
                dominant_frequency = abs(freqs[dominant_freq_idx])
                
                return {
                    'dominant_frequency': float(dominant_frequency),
                    'time_series_length': len(time_series_data),
                    'method': 'simplified_fft'
                }
                
        except Exception as e:
            print(f"Warning: Could not analyze time series: {e}")
            
        return {}
    
    def process_case(self, case_name, case_dir):
        """Process a complete validation case"""
        case_path = self.project_root / case_dir
        
        print(f"Processing case: {case_name}")
        
        # Find log file
        log_files = list(case_path.glob("*.log"))
        if not log_files:
            log_files = list(case_path.glob("results/*.log"))
            
        # Find VTU files
        vtu_files = list(case_path.glob("*.vtu"))
        if not vtu_files:
            vtu_files = list(case_path.glob("results/*.vtu"))
            
        metrics = {
            'case_name': case_name,
            'timestamp': datetime.now().isoformat(),
            'log_files_found': len(log_files),
            'vtu_files_found': len(vtu_files)
        }
        
        # Extract from log file
        if log_files:
            log_metrics = self.extract_from_log(log_files[0])
            metrics.update(log_metrics)
            
        # Extract from VTU files
        if vtu_files:
            vtu_metrics = self.extract_from_vtu(vtu_files)
            metrics.update(vtu_metrics)
            
            # Time series analysis
            time_series_metrics = self.analyze_time_series(vtu_files)
            if time_series_metrics:
                metrics['time_series'] = time_series_metrics
                
        self.results[case_name] = metrics
        return metrics
    
    def compare_with_reference(self, case_name, reference_file):
        """Compare extracted metrics with reference data"""
        if case_name not in self.results:
            return None
            
        ref_path = self.project_root / "reference_data" / reference_file
        if not ref_path.exists():
            return {'status': 'no_reference', 'file': str(ref_path)}
            
        try:
            # Load reference data
            ref_data = pd.read_csv(ref_path, comment='#')
            
            # Extract reference values
            ref_values = {}
            for _, row in ref_data.iterrows():
                if 'parameter' in ref_data.columns:
                    param = row['parameter']
                    if param in ['drag_coefficient', 'strouhal_number']:
                        ref_values[param] = {
                            'value': row['value'],
                            'uncertainty': row.get('uncertainty', 0.1)
                        }
                        
            # Compare with simulation results
            comparison = {}
            sim_metrics = self.results[case_name]
            
            for param, ref_data in ref_values.items():
                if param in sim_metrics:
                    ref_val = ref_data['value']
                    sim_val = sim_metrics[param]
                    error = abs(sim_val - ref_val) / ref_val * 100
                    
                    comparison[param] = {
                        'reference': ref_val,
                        'simulation': sim_val,
                        'error_percent': error,
                        'uncertainty': ref_data['uncertainty'],
                        'status': 'pass' if error < 15 else 'fail'
                    }
                    
            return comparison
            
        except Exception as e:
            return {'status': 'error', 'message': str(e)}
    
    def generate_report(self, output_file=None):
        """Generate comprehensive metrics report"""
        if output_file is None:
            output_file = self.project_root / "validation_metrics_report.json"
            
        report = {
            'timestamp': datetime.now().isoformat(),
            'total_cases': len(self.results),
            'vtk_available': VTK_AVAILABLE,
            'cases': self.results
        }
        
        with open(output_file, 'w') as f:
            json.dump(report, f, indent=2)
            
        print(f"Metrics report saved to: {output_file}")
        return report

def main():
    """Main entry point"""
    if len(sys.argv) < 2:
        print("Usage: python extract_metrics.py <project_root> [case_name]")
        sys.exit(1)
        
    project_root = sys.argv[1]
    extractor = MetricsExtractor(project_root)
    
    if len(sys.argv) > 2:
        # Process specific case
        case_name = sys.argv[2]
        case_dir = case_name.replace('_', '/')
        extractor.process_case(case_name, case_dir)
    else:
        # Process all cases
        cases = [
            ('basic_validation', 'basic_validation'),
            ('square_cylinder', 'square_cylinder'),
            ('circular_cylinder', 'circular_cylinder')
        ]
        
        for case_name, case_dir in cases:
            try:
                extractor.process_case(case_name, case_dir)
            except Exception as e:
                print(f"Error processing {case_name}: {e}")
                
    # Generate report
    extractor.generate_report()

if __name__ == '__main__':
    main()
