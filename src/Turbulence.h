//=================================================================================================
/*!
//  \file Turbulence.h
//  \brief Header file for turbulence functions
//  \author Ankit <PERSON>
 */
//=================================================================================================


#ifndef _WALBERLA_TURBULENCE_H
#define _WALBERLA_TURBULENCE_H

#include "Definitions.h"
#include "PDFField.h"
#include "VelField.h"
#include "DensField.h"
#include "Vector3.h"
#include "Lattice.h"
#include "Logging.h"
#include <math.h>

#include "Tensor.h"






namespace walberla{

const static int* c[3] = { D3Q19::cx, D3Q19::cy, D3Q19::cz };


class Turbulence // : public boost::noncopyable
{
  public:

        inline static Real getLesOmega(const Real& omega,const Real& csmag,const Real& Qo,const Real C_D,const Real C_E ,Real& turb_ke,Real&  turb_epsilon);
	
        //Compute TKE following OF
        inline static Real TkE(const Matrix3<Real>& Sij);

        inline static void CalcPiTurbulence(
                const PDFField &pdf, const Vector3<Real>& vel,
                const Real& rho,const Real& omega,const Uint x,const Uint y,const Uint z,Matrix3<Real>& tau) ;

        inline static Real getTensorCoefNew(const PDFField &pdf,
                           const Uint& x,const Uint& y,const Uint& z,
                           const Vector3<Real>& vel,
                           const Real& rho,const Real& omega );

        //old stuff
        inline static void TurbCollideIncompressible(PDFField &pdf,const Uint x,const Uint y,const Uint z,const Real rho,const Vector3<Real> vel,const Real u_sqr_trm_,const Real omega);
        inline static void TurbStreamPull(const PDFField& src, PDFField& dst, const int& x,const int& y,const int& z );
        inline static void TurbStreamPush(const PDFField& src, PDFField& dst, const int& x,const int& y,const int& z );

    private:

	Turbulence (const Turbulence& ) ;
	void operator = (const Turbulence& ) ;
		
};
// TODO SSE version for each of these functions !!!




// =====================================================================
/*!
@fn Real Turbulence::getLesOmega
\brief calculate Omega using LES turbulence model 
\param  const Point3D<T>& a
\param  const Point3D<T>& b
\return Real  
\author Cherif.Mihoubi
*/
// =====================================================================
inline Real Turbulence::getLesOmega(const Real& omega,
                        const Real& csmag,
                        const Real& Qo,
                        const Real C_D,
                        const Real C_E ,
                        Real&  turb_ke,
                        Real&  turb_epsilon)
{
/*
        const Real tau = 1.0/omega;
        const Real nu = (2.0*tau-1.0) * (1.0/6.0);
        //        const Real Csqr = csmag*csmag;

        const Real Csqr = csmag;//*csmag ;

        const Real S = (-nu + sqrt( nu*nu + 18.0*Csqr*Qo )) / (6.0*Csqr);

        assert (S >=0 ) ;
        if ( S < 0 )
        {
            std::cerr << "Erro stress tensor in negative !!!\n" ;
            exit (1);
        }

        //turb_ke = S*S*  std::pow ( ( (csmag*csmag)/(C_D) ) , 2 ) ;
        turb_ke = S*S*  std::pow ( ( (csmag )/(C_D) ) , 2 ) ;

        turb_epsilon = C_E * std::pow ( (turb_ke),(3./2.0)  );

        const Real tau_total = 3.0*( nu + Csqr * S ) + 0.5 ;// the total tau

        return( 1.0/( tau_total) ); // total omega
  */

        const Real tau = 1.0/omega;
        const Real Csqr = csmag  ;//= csmag*csmag ;

        const Real tau_eff = 0.5*( sqrt ( tau*tau + 18.0*(Csqr)* Qo ) - tau);

        assert (S >=0 ) ;

        const Real nu = (2.0*tau-1.0) * (1.0/6.0);

      //const Real S = (-nu + sqrt( nu*nu + 18.0*Csqr*Qo )   ) / (6.0*Csqr);
        const Real S = (-nu + pow( nu*nu + 18.0*Csqr*Qo  , 0.5 )   ) / (6.0*Csqr);
        turb_ke = S*S*  std::pow ( ( (csmag*csmag)/(C_D) ) , 2.0 ) ;

        turb_epsilon = C_E * std::pow ( (turb_ke),(3.0/2.0)  );

        return( 1.0/( tau_eff) );

}



// ==========================================================================
// Compute TKE following OF implementation
// https://www.openfoam.com/documentation/guides/latest/doc/guide-turbulence-les-smagorinsky.html
// D is  \tensor{D} = \frac{1}{2} \left( \grad \u + \grad (\u)^T \right) 
// in OF,      
// volSymmTensorField D(symm(gradU));
// note: 
// D is actually Sij 
// ==========================================================================
/*
        //- Return SGS kinetic energy
        //  calculated from the given velocity gradient

*/
inline Real Turbulence::TkE(const Matrix3<Real>&  Sij)
{
    const Real a = 1.048 ;// Ce 
    
    // const Real trace= (1.0/3.0)* ( D(0,0) +  D(1,1) +  D(2,2)  ) ;

    // const Matrix3<Real> dev_D (
    //         D(0,0)-trace ,  D(0,1) ,   D(0,2) ,
    //         D(1,0) ,  D(1,1)-trace ,   D(1,2) , 
    //         D(2,0) ,  D(2,1) ,   D(2,2)-trace
    // ) ;

    Real trace;
    
    const Matrix3<Real> dev_D = Tensor::dev(Sij , trace) ; 

    const Real b = (2.0/3.0) * trace;

    const Real c = 2.0 * (0.094 ) * Tensor::double_dot ( Sij , dev_D ) ;

    return SQR  (  ( -b +  sqrt ( SQR(b) + 4.0 *a*c  ) )/( 2.0 *a )  )  ; 
}











// ==========================================================================
// 
// for WALE model
// 
// ==========================================================================
inline const Matrix3<Real> Sd (const Matrix3<Real>& gradU  )
{
    //const Matrix3<Real>  g2 =  gradU * gradU  ; // inner product 
        
    //const Matrix3<Real> tmp ( Tensor::dev ( Tensor::symmm ( g2 )  ) ) ; 

    return Matrix3<Real>  ( Tensor::dev ( Tensor::symmm ( gradU * gradU  )  ) );
}

inline Real Sd_msgSrt(const Matrix3<Real>&  gradU )
{
    return Tensor::magSqr ( Sd  ( gradU) ) ;
}










//********************************SMAGORINSKY TURBULENCE MODEL*****************************************************************

inline Real calc_feq(const Vector3<Real>& vel,
                     const  Real&  rho,
                     const int l,
                     const Real& u_sqr_trm_)
{
        const Real tmp_ =
                D3Q19::cx[l]*vel[X] +
                D3Q19::cy[l]*vel[Y] +
                D3Q19::cz[l]*vel[Z];
// Standard incompressible LBM equilibrium distribution
// f_eq = w_i * rho * (1 + 3*c_i*u + 4.5*(c_i*u)^2 - 1.5*u^2)
return ( D3Q19::w[l] * rho * ( 1.0 + 3.0*tmp_ + 4.5*tmp_*tmp_ - u_sqr_trm_ ) ); // incompressible, correct formulation

            // D3Q19::w[l]*( delta_rho +
             // D3Q19::w[l]*( rho+
//                 D3Q19::w[l]*rho (1.0 +
//                               3.0 *tmp_ -
//                               u_sqr_trm_ + // here, 1.5 is already included!!!
//                               4.5*tmp_*tmp_ ) );
}

inline Real calc_feqCompressible(const Vector3<Real>& vel,
                     const Real&  rho,
                     const int l,
                     const Real& u_sqr_trm_)
{
        const Real tmp_ =
                D3Q19::cx[l]*vel[X] +
                D3Q19::cy[l]*vel[Y] +
                D3Q19::cz[l]*vel[Z];
 return ( D3Q19::w[l]*rho *(1.0 - u_sqr_trm_ + 3.0 *tmp_ + 4.5*tmp_*tmp_  ) ); //compressible, coorect  i have check them
}



inline void Turbulence::TurbCollideIncompressible(PDFField &pdf,
            const Uint x,
            const Uint y,
            const Uint z,
            const Real rho,
            const Vector3<Real> vel,
            const Real u_sqr_trm_,
            const Real omega)
{

   for( unsigned int l=0; l<D3Q19::Cellsize; ++l )
   {
/*
         const Real tmp_ =  D3Q19::cx[l]*vel[X] + D3Q19::cy[l]*vel[Y] + D3Q19::cz[l]*vel[Z];
         pdf.SET_PDF(x,y,z,l,
                     ( 1.0-omega )* pdf.GET_PDF(x,y,z,l) +
                         rho*omega*D3Q19::w[l]*( 1.0 +  3.0*tmp_ - u_sqr_trm_  + 4.5*tmp_*tmp_ ));
*/

         const Real  f =  pdf.GET_PDF(x,y,z,l) ;
         const Real  feq = calc_feq( vel,rho , l, u_sqr_trm_);
         const Real  f_tmp  =  ( f - feq   ) * omega   ;
         const Real  f_plus =  ( f - f_tmp ) ;
         pdf.SET_PDF( x,y,z,l,f_plus );
   }

}
inline void Turbulence::TurbStreamPull( const PDFField& src, PDFField& dst, const int& x,const int& y,const int& z )
{
   for( unsigned int l=0; l<D3Q19::Cellsize; ++l ) {
      dst.SET_PDF(x,y,z,l,src.GET_PDF(x-D3Q19::cx[l],y-D3Q19::cy[l],z-D3Q19::cz[l],l));
   }
}

inline void Turbulence::TurbStreamPush( const PDFField& src, PDFField& dst, const int& x,const int& y,const int& z )
{
   for( unsigned int l=0; l<D3Q19::Cellsize; ++l ) {
      dst.SET_PDF(x+D3Q19::cx[l],y+D3Q19::cy[l],z+D3Q19::cz[l],l,src.GET_PDF(x,y,z,l));
   }
}






// ==========================================================================
// 
// 
// ==========================================================================
inline Real Calctau_EffNew (
                        const Real& delta_rho ,
                        const Vector3<Real>& vel,
                        const Real& u_sqr_trm_ ,
                        const Real& Omega,
                        Matrix3<Real>& piMatrix ,
                        const SimData& sim,
                        Uint x,Uint y, Uint z ,
                        const PDFField& src ,
                        Real& Qo
                        )
{

    double pi = 0.0 ;
    for (int a=0; a<3; a++)
    {
        for (int b=0; b<3; b++) {
               Real tmp = 0.0;
               for(int l=0; l<D3Q19::Cellsize; l++)
               {
                   if(c[a][l]==0.0 || c[b][l]==0.0)
                          continue;
                   const Real f    = src.GET_PDF(x,y,z,l) ;
                   const Real f_eq = calc_feq(vel,delta_rho,l,u_sqr_trm_) ;
                    tmp += c[a][l]*c[b][l]*( f - f_eq  ); // old implementation
                   //tmp += c[l][a]*c[l][b]*( f - f_eq  ); // only test , segmentation error

                   //  tmp += c[a][l]*c[b][l]*(pdf.GET_PDF(x,y,z,l)-calc_feq(vel,rho,l,u_sqr_trm_));
               }
               piMatrix (a,b) = tmp ;
               pi   +=   (tmp * tmp) ;
        }
    }

//    for(int l=0; l<D3Q19::Cellsize; l++)
//    {
//         double f   =  src.GET_PDF(x,y,z,l) ;
//         double feq =  calc_feq(vel,delta_rho,l,u_sqr_trm_) ;

//         piMatrix (E,E) =  piMatrix (E,E) + (f  - feq*D3Q19::cx[l]*D3Q19::cx[l]);
//         piMatrix (E,Y) =  piMatrix (E,Y) + (f  - feq*D3Q19::cx[l]*D3Q19::cy[l]);
//         piMatrix (E,T) =  piMatrix (E,T) + (f  - feq*D3Q19::cz[l]*D3Q19::cz[l]);
//         piMatrix (Y,E) =  piMatrix (E,Y) ;
//         piMatrix (Y,Y) =  piMatrix (Y,Y) + (f  - feq*D3Q19::cy[l]*D3Q19::cy[l]);
//         piMatrix (Y,T) =  piMatrix (Y,T) + (f  - feq*D3Q19::cy[l]*D3Q19::cz[l]);
//         piMatrix (T,E) =  piMatrix (E,T) ;
//         piMatrix (T,Y) =  piMatrix (Y,T) ;
//         piMatrix (T,T) =  piMatrix (T,T) + (f  - feq*D3Q19::cz[l]*D3Q19::cz[l]);
//    }


     // evaluate |S| (magnitude of the strain-rate)
//      const Real sigmaT =
//              pow( piMatrix (E,E),2)  +  //  Pi_xx^2
//              pow( piMatrix (Y,Y),2)  +  //  Pi_yy^2
//              pow( piMatrix (T,T),2)  +  //  Pi_zz^2
//              2.0 * (
//                      pow( piMatrix (E,Y),2) + // Pi_xy^2
//                      pow( piMatrix (Y,T),2) + // Pi_yz^2
//                      pow( piMatrix (E,T),2)   // Pi_xz^2
//                  ) ;

     Qo =  sqrt (  pi ); //
    // Qo =  sqrt ( sigmaT );

     const Real nu   =  sim.nu_L;

     const Real Csqr =  sim.csmag ;//= csmag*csmag ;

     //const Real tau_0  = 0.5;// (3.0 * nu + 0.50) ;

     const Real tau_0  =  (3.0 * nu + 0.50) ;

     // first try
     // const Real tau_t = 0.5 * (  sqrt  (   (tau_0*tau_0) + 18.0 *(Csqr)* Qo )  - tau_0) ;


     // second formulat :
     // book : Lattice Boltzmann Method and Its Applications in Engineering
     // page 346
     // or Equation 2.90, thesis:  Dropbox/thesis/validation/turbulence/Koda_Yusuke_veryNice.pdf
     const Real tau_t = 0.50 * ( tau_0 +  sqrt  (   (tau_0*tau_0) + 18.0 *(Csqr)* Qo )  ) ;

     // third formulat :
     // Equation 16, paper: Dropbox/thesis/validation/turbulence/dongpof2008a.pdf

 //     const Real tau_t =  0.5 + 3.0 *( sim.nu_L +  ( 1.0/6.0 ) * (  sqrt  (   (tau_0*tau_0) + 18.0 *(Csqr)* Qo )  - tau_0)  ) ;


     const Real tau_eff = tau_t  ;

//     if (x ==  5 && y == 5  && z == 5 )
//        std::cerr <<"\t  Calctau_EffNew ----> Q0 \t" <<  Qo
//               //  << " piMatrix :  \n" << piMatrix
//               //  << " Csqr\t" <<  Csqr
//                 << " tau_0\t" <<  tau_0
//                 << " tau_t\t" <<  tau_t
//                 << "\n" ;

     return tau_eff ;


     // Nils thesis
//     const Real S = ( sqrt( nu*nu + 18.0*Csqr*Qo ) -nu  ) / (6.0*Csqr);

//     assert (S >=0 ) ;
//     if ( S < 0 )
//     {
//         std::cerr << "Erro stress tensor in negative !!!\n" ;
//         exit (1);
//     }

//     //turb_ke = S*S*  std::pow ( ( (csmag*csmag)/(C_D) ) , 2 ) ;
//     //turb_ke = S*S*  std::pow ( ( (csmag )/(C_D) ) , 2 ) ;

//     //turb_epsilon = C_E * std::pow ( (turb_ke),(3./2.0)  );

//     const Real tau_total = 3.0*( nu + Csqr * S ) + 0.5 ;// the total tau

//     return(  tau_total );




}




inline Real Calctau_EffNewRegularizedLBM (
                        const Real& delta_rho ,
                        const Vector3<Real>& vel,
                        const Real& u_sqr_trm_ ,
                        const Real& Omega,
                        Matrix3<Real>& piMatrix ,
                        const SimData& sim,
                        Uint x,Uint y, Uint z ,
                        const PDFField& src ,
                        Real& Qo
                        )
{
     // evaluate |S| (magnitude of the strain-rate)
     const Real sigmaT =
             pow( piMatrix (0,0),2)  +  //  Pi_xx^2
             pow( piMatrix (1,1),2)  +  //  Pi_yy^2
             pow( piMatrix (2,2),2)  +  //  Pi_zz^2
             2.0 * (
                     pow( piMatrix (0,1),2) + // Pi_xy^2
                     pow( piMatrix (1,2),2) + // Pi_yz^2
                     pow( piMatrix (0,2),2)   // Pi_xz^2
                 ) ;

     Qo =  sqrt ( 2.0* sigmaT );

     const Real nu   =  sim.nu_L;// 0.00035  ;
     const Real Csqr =  sim.csmag ;//* csmag ;//= csmag*csmag ;
     const Real tau_0  = (3.0 * nu + 0.50) ;


     // second formulat :
     // book : Lattice Boltzmann Method and Its Applications in Engineering
     // page 346
     // or Equation 2.90, thesis:  Dropbox/thesis/validation/turbulence/Koda_Yusuke_veryNice.pdf
     const Real tau_t = 0.50 * ( tau_0 +  sqrt  (   (tau_0*tau_0) + 18.0 *(Csqr)* Qo )  ) ;
     const Real tau_eff = tau_t  ;
     return tau_eff ;
}





#ifdef STRESS_TENSOR_DEBUG

inline Real getTensorCoef (const PDFField &pdf,
                           const  Uint& x,
                           const  Uint& y,
                           const  Uint& z,
                           const  Vector3<Real>& vel,
                           const  Real& rho,
                           const Real& tau ,
                           Matrix3<Real>& Q ,
                           const Real& cs_mag,
                           Real& sMag ,
                           SimData& sim )
{
    Real tmp =0.0 ;
    //Matrix3<Real> Q (0.0);
    const Real u_sqr_trm_ = 1.5*( vel[X]*vel[X] + vel[Y]*vel[Y] + vel[Z]*vel[Z] ); // used in calculating the feq

    Q(0,0) = 0.0f;  Q(0,1) = 0.0f; Q(0,2) = 0.0f;
    Q(1,0) = 0.0f;  Q(1,1) = 0.0f; Q(1,2) = 0.0f;
    Q(2,0) = 0.0f;  Q(2,1) = 0.0f; Q(2,2) = 0.0f;



    for (int a=0; a<3; a++)
    {
        for (int b=0; b<3; b++) {
               Real tmp = 0.0;
               for(int l=0; l<D3Q19::Cellsize; l++)
               {
                   if(c[a][l]==0.0 || c[b][l]==0.0)
                          continue;
                   const Real f    = src.GET_PDF(x,y,z,l) ;
                   const Real f_eq = calc_feq(vel,delta_rho,l,u_sqr_trm_) ;
                    tmp += c[a][l]*c[b][l]*( f - f_eq  ); // old implementation
                   //tmp += c[l][a]*c[l][b]*( f - f_eq  ); // only test , segmentation error

                   //  tmp += c[a][l]*c[b][l]*(pdf.GET_PDF(x,y,z,l)-calc_feq(vel,rho,l,u_sqr_trm_));
               }
               Q (a,b) = tmp ;
        }
    }


//    for(int l=0; l<D3Q19::Cellsize; l++)
//    {
//         //
//         // pi(a,b) += c[a][l]*c[b][l]*(pdf.GET_PDF(x,y,z,l)-calc_feq(vel,rho,l,u_sqr_trm_));
//         const Real f_neq = (pdf.GET_PDF(x,y,z,l)-calc_feq(vel,rho,l,u_sqr_trm_));
//         Q(0,0) += cx[l]*cx[l]*f_neq;
//         Q(0,1) += cx[l]*cy[l]*f_neq;
//         Q(0,2) += cx[l]*cz[l]*f_neq;
//         //
//         Q(1,0) += cy[l]*cx[l]*f_neq;
//         Q(1,1) += cy[l]*cy[l]*f_neq;
//         Q(1,2) += cy[l]*cz[l]*f_neq;
//         //
//         Q(2,0) += cz[l]*cx[l]*f_neq;
//         Q(2,1) += cz[l]*cy[l]*f_neq;
//         Q(2,2) += cz[l]*cz[l]*f_neq;
//    }

    const Real S_mg =
            std::pow( Q (0,0),2) +
            std::pow( Q (1,1),2) +
            std::pow( Q (2,2),2) +
            2.0*std::pow( ( Q (0,1) + Q (1,0) ),2) +
            2.0*std::pow( ( Q (0,2) + Q (2,0) ),2) +
            2.0*std::pow( ( Q (1,2) + Q (2,1) ),2)
             ;// pi

    Q  *= (-3.0/ (2.0*tau ) ) ;


    sMag = std::sqrt ( 2.0 * S_mg  ) ;

    ASSERT (sMag >0  , "sMag should be positive");

    const Real Csqr = cs_mag;//*csmag ;

    const Real nuTpysical=  Csqr *sMag* (sim.dx *sim.dx  )/(sim.dt*sim.dt) ;
    const Real nu_lbm = nuTpysical * (sim.dt );
    return  3.0 * (  sim.nu_L  +  nu_lbm ) ;
}

#else // end STRESS_TENSOR_DEBUG

inline Real getTensorCoef (const PDFField &pdf,
                           const Uint& x,
                           const Uint& y,
                           const Uint& z,
                           const Vector3<Real>& vel,
                           const Real& rho,
                           const Real& tau_0 ,
                           Matrix3<Real>& Q ,//< -- The filtered strain rate tensor 
                           const Real& cs_mag,
                           Real& sMag ,
                           SimData& sim,
                           Uint t)
{
    Real tmp =0.0 ;
    //Matrix3<Real> Q (0.0);
    const Real u_sqr_trm_ = 1.50*( vel[X]*vel[X] + vel[Y]*vel[Y] + vel[Z]*vel[Z] ); // used in calculating the feq

    Q(0,0) = 0.0f;  Q(0,1) = 0.0f; Q(0,2) = 0.0f;
    Q(1,0) = 0.0f;  Q(1,1) = 0.0f; Q(1,2) = 0.0f;
    Q(2,0) = 0.0f;  Q(2,1) = 0.0f; Q(2,2) = 0.0f;

    const Real CS2=1.0/3.0;

    for(int l=0; l<D3Q19::Cellsize; l++)
    {
         // pi(a,b) += c[a][l]*c[b][l]*(pdf.GET_PDF(x,y,z,l)-calc_feq(vel,rho,l,u_sqr_trm_));
         const Real f_neq = (  pdf.GET_PDF(x,y,z,l) - calc_feq(vel,rho,l,u_sqr_trm_)   );

         Q(0,0) += (cx[l]*cx[l] - CS2  )*f_neq; // NORMAL Laminar
         //Q(0,0) +=  cx[l]*cx[l]*f_neq; // LES
         Q(0,1) +=  cx[l]*cy[l]*f_neq;
         Q(0,2) +=  cx[l]*cz[l]*f_neq;
         //
         Q(1,0) +=  cy[l]*cx[l]*f_neq;
         Q(1,1) += (cy[l]*cy[l]-CS2  )*f_neq;//Q(1,1) +=  cy[l]*cy[l]*f_neq;//
         Q(1,2) +=  cy[l]*cz[l]*f_neq;
         //
         Q(2,0) +=  cz[l]*cx[l]*f_neq;
         Q(2,1) +=  cz[l]*cy[l]*f_neq;
         Q(2,2) += (cz[l]*cz[l]-CS2  )*f_neq;//Q(2,2) +=  cz[l]*cz[l]*f_neq;//
    }



//    const Real nu_l = (2.0*tau_0 -1.0)/6.0 ;
//if (cs_mag == 0.0 )


        sMag = Tensor::mag ( Q ) ;



        const Real tau_turb = 0.5f*( std::sqrt ( tau_0*tau_0  + (18.0f *cs_mag* sMag)  ) - tau_0);



        // ============================================
        //
        //
        // Now , we convert Q (better say PI ) to Sij
        //
        //
        // ============================================

        // Q < -- The filtered strain rate tensor 
        Q *=  (-3.0/( 2.0 *rho *tau_0 ) ); // <--- Sij, and don't confuse it with velocity gradient.

        //Q *= -(2.0/sim.nu_L)*( 1.0 -  1.0/( 2.0 *tau_0 ) ) ; // <--- Sij, i don't know why  fact = 4 give nice results.



        // const Real tau_total = 3.0*( sim.nu_L +  cs_mag *S_mg  ) + tau_0 ;

//    if (t == 0 ){
//        S_mg += 0.001 ;
//    }

//#ifdef FINITE_DIFFERENCE
   //sMag = std::sqrt ( 2.0 * S_mg  ) ; // this should be the correct one
//sMag = std::sqrt ( S_mg  ) ; 

    ASSERT (sMag > 0  , "sMag should be possitive ");


   // if ( x==5 && y==5 && z == 5 )
   //     std::cerr << "  Qo (PI)  \n" <<  Q   << "\n";

   // return   0.50 *( sqrt ( (tau*tau) +  18.0 * 1.414213562 * cs_mag *sqrt (2.0* S_mg)  )   -  tau ) ;


    //const  Real tau_t = 0.5f*(pow(pow(tau,2.0f) + 18.0f*1.414213562 *(cs_mag)* sMag ,0.5f) - tau);
//    const Real nu = sim.nu_L;// const Real nu_L=   ((tau-0.50)/3.0) ;

//    const Real nu_t = (1./6.0) * ( sqrt ( (nu *nu) + 18 * cs_mag * sMag   ) ) ;
  //const Real nu_t = (1./6.0) * ( sqrt ( sim.nu_L *sim.nu_L + 18 * cs_mag * sMag   ) ) ;
//    return 3.0 * nu_t ;

    //const Real Csqr = cs_mag;//*csmag ;

//    // https://www10.cs.fau.de/publications/theses/2009/Rathgeber_BT_2009.pdf page 33, eq 2.12
//    const Real S = ( std::sqrt(  (nu*nu) + 18.0*Csqr*sMag )- nu) / (6.0*Csqr);


// non-optimized version, here the factor = (sim.dx *sim.dx  )/(sim.dt*sim.dt) used to convert sMag from LBM units to physical units!!!

// const Real nuTpysical=   (sim.dx *sim.dx  )/(sim.dt*sim.dt) ;


    //const Real nu_t = (1.0/6.0) * ( std::sqrt ( (tau_0*tau_0)  + (18.0/rho) * cs_mag * sMag   ) - tau_0 ) ;





//convert to physical units:
//    const Real nuTpysical=  Csqr *sMag* (sim.dx *sim.dx  )/(sim.dt*sim.dt) ;
//    const Real nu_lbm = nuTpysical * (sim.dt )   ;




//    assert (S >=0 ) ;

//    if ( S < 0 )
//    {
//        std::cerr << "Erro stress tensor in negative !!!\n" ;
//        exit (1);
//    }


    // fix me,
//    return  3.0 * (  sim.nu_L  +  nu_t )  ;

    //turb_ke = S*S*  std::pow ( ( (csmag*csmag)/(C_D) ) , 2 ) ;

    return tau_turb;
}




// =====================================================================
// 
// 
// 
// =====================================================================
inline Real getTensorCoefCompressible(const PDFField &pdf,const Uint& x,const Uint& y,const Uint& z,
			   const Vector3<Real>& vel,
                           const Real& rho,
                           const Real& tau ,
                           Matrix3<Real>& Q ,
                           const Real& cs_mag,
                           Real& sMag ,
                           SimData& sim,
                           Uint t)
{
    Real tmp =0.0 ;
    //Matrix3<Real> Q (0.0);
    const Real u_sqr_trm_ = 1.5*( vel[X]*vel[X] + vel[Y]*vel[Y] + vel[Z]*vel[Z] ); // used in calculating the feq

    Q(0,0) = 0.0f;  Q(0,1) = 0.0f; Q(0,2) = 0.0f;
    Q(1,0) = 0.0f;  Q(1,1) = 0.0f; Q(1,2) = 0.0f;
    Q(2,0) = 0.0f;  Q(2,1) = 0.0f; Q(2,2) = 0.0f;

    const Real CS2=1.0/3.0;

//    for(int l=0; l<D3Q19::Cellsize; l++)
//    {
//         // pi(a,b) += c[a][l]*c[b][l]*(pdf.GET_PDF(x,y,z,l)-calc_feq(vel,rho,l,u_sqr_trm_));
//         const Real f_neq = ( pdf.GET_PDF(x,y,z,l)-calc_feqCompressible( vel,rho,l,u_sqr_trm_) );
//         Q(0,0) += (cx[l]*cx[l] - CS2  )*f_neq; // NORMAL Laminar
//         //Q(0,0) +=  cx[l]*cx[l]*f_neq; // LES
//         Q(0,1) +=  cx[l]*cy[l]*f_neq;
//         Q(0,2) +=  cx[l]*cz[l]*f_neq;
//         //
//         Q(1,0) +=  cy[l]*cx[l]*f_neq;
//         Q(1,1) += (cy[l]*cy[l]-CS2  )*f_neq;//Q(1,1) +=  cy[l]*cy[l]*f_neq;//
//         Q(1,2) +=  cy[l]*cz[l]*f_neq;
//         //
//         Q(2,0) +=  cz[l]*cx[l]*f_neq;
//         Q(2,1) +=  cz[l]*cy[l]*f_neq;
//         Q(2,2) += (cz[l]*cz[l]-CS2  )*f_neq;//Q(2,2) +=  cz[l]*cz[l]*f_neq;//
//    }

            //const int* c[3] = { cx, cy, cz };

            Real pi = 0.0;

            for (int a=0; a<3; a++)
            {
                for (int b=0; b<3; b++) {
                       pi = 0.0;
                       for(int l=0; l<D3Q19::Cellsize; l++)
                       {
                             if(c[a][l]==0.0 || c[b][l]==0.0)
                                            continue;

                             pi += c[a][l]*c[b][l]*(pdf.GET_PDF(x,y,z,l)-calc_feq(vel,rho,l,u_sqr_trm_));
                             Q(a,b) += c[a][l]*c[b][l]*(pdf.GET_PDF(x,y,z,l)-calc_feq(vel,rho,l,u_sqr_trm_));

                       }
                       //Qo += pi*pi;
                }
            }

    Q *= -(2.0/sim.nu_L)*( 1.0 -  1.0/( 2.0 *tau ) ) ; // <--- Sij, i don't know why  fact = 4 give nice results.

   // Q *=  -3.0/( 2.0 *rho *tau ) ; // <--- Sij

    Real S_mg =
            2.0*std::pow( Q (0,0),2.0) +
            2.0*std::pow( Q (1,1),2.0) +
            2.0*std::pow( Q (2,2),2.0) +
            std::pow( ( Q (0,1) + Q (1,0) ),2.0) +
            std::pow( ( Q (0,2) + Q (2,0) ),2.0) +
            std::pow( ( Q (1,2) + Q (2,1) ),2.0)    ;// pi

    sMag = std::sqrt ( 2.0 * S_mg  ) ; // this should be the correct one

    ASSERT (sMag >0  , "sMag should be possitive ");

    const Real nu_t = (1.0/6.0) * ( std::sqrt ( (tau*tau)  + (18.0/rho) * cs_mag * sMag   ) - tau ) ;
    return  3.0 * (  sim.nu_L  +  nu_t )  ;
}


#endif



///*
//    static const int cx[MAX_COMM_DIR] =
//            // N	S	W	E	T	B  NW  NE	SW  SE  TN  TS  TW  TE  BN  BS  BW  BE NET NEB NWT NWB SET SEB SWT SWB
//        {	  0,  0, -1,  1,  0,  0, -1,  1,  -1,  1,  0,  0, -1,  1,  0,  0, -1,  1,  1,  1, -1, -1,  1,  1, -1, -1};
//        static const int cy[MAX_COMM_DIR] =
//            // N	S	W	E	T	B  NW  NE	SW  SE  TN  TS  TW  TE  BN  BS  BW  BE NET NEB NWT NWB SET SEB SWT SWB
//        {	  1, -1,  0,  0,  0,  0,  1,  1,  -1, -1,  1, -1,  0,  0,  1, -1,  0,  0,  1,  1,  1,  1, -1, -1, -1, -1};
//        static const int cz[MAX_COMM_DIR] =
//            // N	S	W	E	T	B  NW  NE	SW  SE  TN  TS  TW  TE  BN  BS  BW  BE NET NEB NWT NWB SET SEB SWT SWB
//        {	  0,  0,  0,  0,  1, -1,  0,  0,	0,  0,  1,  1,  1,  1, -1, -1, -1, -1,  1, -1,  1, -1,  1, -1,  1, -1};

//*/
//inline Real getTensorCoef (const PDFField &pdf,
//                           const Uint& x,
//                           const Uint& y,
//                           const Uint& z,
//                           const Vector3<Real>& vel,
//                           const Real& rho )
//{

//        Real Qo = 0.0;
//        const Real u_sqr_trm_ = 1.5*( vel[X]*vel[X] + vel[Y]*vel[Y] + vel[Z]*vel[Z] ); // used in calculating the feq

//        //const int* c[3] = { cx, cy, cz };

//        Matrix3<Real> piMatrix (0.0  ) ;
//        Real pi = 0.0;
//        /*
//        for (int a=0; a<3; a++)
//        {
//            for (int b=0; b<3; b++) {
//                   pi = 0.0;
//                   for(int l=0; l<D3Q19::Cellsize; l++)
//                   {
//                         if(c[a][l]==0.0 || c[b][l]==0.0)
//                                        continue;

//                         pi += c[a][l]*c[b][l]*(pdf.GET_PDF(x,y,z,l)-calc_feq(vel,rho,l,u_sqr_trm_));
//                   }
//                   Qo += pi*pi;
//            }
//        }

//       return sqrt(Qo) ;

//     //   second method, cherif

////

////    const Real u_sqr_trm_ = 1.5*( vel[X]*vel[X] + vel[Y]*vel[Y] + vel[Z]*vel[Z] ); // used in calculating the feq

////    for(int l=0; l<D3Q19::Cellsize; l++)
////    {
////         double f   =  pdf.GET_PDF(x,y,z,l) ;
////         double feq =  calc_feq(vel,rho,l,u_sqr_trm_) ;

////         piMatrix (0,0) =  piMatrix (0,0) + (f  - feq*D3Q19::cx[l]*D3Q19::cx[l]);
////         piMatrix (0,1) =  piMatrix (0,1) + (f  - feq*D3Q19::cx[l]*D3Q19::cy[l]);
////         piMatrix (0,2) =  piMatrix (0,2) + (f  - feq*D3Q19::cz[l]*D3Q19::cz[l]);
////         piMatrix (1,0) =  piMatrix (0,1) ;
////         piMatrix (1,1) =  piMatrix (1,1) + (f  - feq*D3Q19::cy[l]*D3Q19::cy[l]);
////         piMatrix (1,2) =  piMatrix (1,2) + (f  - feq*D3Q19::cy[l]*D3Q19::cz[l]);
////         piMatrix (2,0) =  piMatrix (0,2) ;
////         piMatrix (2,1) =  piMatrix (1,2) ;
////         piMatrix (2,2) =  piMatrix (2,2) + (f  - feq*D3Q19::cz[l]*D3Q19::cz[l]);
////    }

//    Real newQ =0.0 ;
//Real Qo = 0.0;
////        for (int a=0; a<3; a++)
////        {
////            for (int b=0; b<3; b++) {
////               newQ +=  (piMatrix (a,b) * piMatrix(b,a) ); ///FIXME piMatrix (a,b)^2 why this ?
////            }
////        }

////    // evaluate |S| (magnitude of the strain-rate)
////      double sigmaT = pow( piMatrix (0,0),2) + pow( piMatrix (0,1),2) + pow( piMatrix (0,2),2)
////                 +  pow( piMatrix (1,0),2) + pow( piMatrix (1,1),2) + pow( piMatrix (1,2),2)
////                 +  pow( piMatrix (2,0),2) + pow( piMatrix (2,1),2) + pow( piMatrix (2,2),2);

////      return  pow( sigmaT ,0.5);

//        Qo  = sqrt (newQ);
//        return  Qo;
//*/




////        Real newQ  ;
////        for (int a=0; a<3; a++)
////        {
////            for (int b=0; b<3; b++) {
////                   Real tmp = 0.0;
////                   for(int l=0; l<D3Q19::Cellsize; l++)
////                   {

////                         tmp += c[a][l]*c[b][l]*(pdf.GET_PDF(x,y,z,l)-calc_feq(vel,rho,l,u_sqr_trm_));
////                   }
////                   piMatrix (a,b) = tmp ;
////            }
////        }

////        for (int a=0; a<3; a++)
////        {
////            for (int b=0; b<3; b++) {
////               newQ +=  piMatrix (a,b) * piMatrix(b,a) ;
////            }
////        }

////        if ( Qo != newQ  )
////            std::cerr << "there  is difference between Qo != newQ \t newQ = " << newQ << "\t Qo = " << Qo  << "\n";


//        for (int a=0; a<3; a++)
//        {
//            for (int b=0; b<3; b++) {
//                   Real tmp = 0.0;
//                   for(int l=0; l<D3Q19::Cellsize; l++)
//                   {
//                       const Real f    = pdf.GET_PDF(x,y,z,l) ;
//                       const Real f_eq = calc_feq(vel, rho,l,u_sqr_trm_) ;
//                       tmp += c[a][l]*c[b][l]*( f - f_eq  );
//                       //  tmp += c[a][l]*c[b][l]*(pdf.GET_PDF(x,y,z,l)-calc_feq(vel,rho,l,u_sqr_trm_));
//                   }
//                   piMatrix (a,b) = tmp ;
//            }
//        }

//        Real newQ =0.0 ;

////        for (int a=0; a<3; a++)
////        {
////            for (int b=0; b<3; b++) {
////               newQ +=  (piMatrix (a,b) * piMatrix(b,a) ); ///FIXME piMatrix (a,b)^2 why this ?
////            }
////        }

//        newQ =  pow( piMatrix (0,0),2)  +  //  Pi_xx^2
//                pow( piMatrix (1,1),2)  +  //  Pi_yy^2
//                pow( piMatrix (2,2),2)  +  //  Pi_zz^2
//                2.0 * (
//                        pow( piMatrix (0,1),2) + // Pi_xy^2
//                        pow( piMatrix (1,2),2) + // Pi_yz^2
//                        pow( piMatrix (0,2),2)   // Pi_xz^2
//                    ) ;


//        Qo  = sqrt (newQ);



//        return sqrt(newQ);

//}





// =====================================================================
// 
// 
// 
// =====================================================================
inline Real Turbulence::getTensorCoefNew(const PDFField &pdf,
                           const Uint& x,const Uint& y,const Uint& z,
                           const Vector3<Real>& vel,
                           const Real& rho,const Real& omega)
{




        Matrix3<Real> piMatrix (0.0  ) ;

        Turbulence::CalcPiTurbulence(pdf , vel , rho , omega ,  x,y,z, piMatrix ) ;

        Real newQ =0.0 ;

        newQ =  pow( piMatrix (0,0),2)  +  //  Pi_xx^2
                pow( piMatrix (1,1),2)  +  //  Pi_yy^2
                pow( piMatrix (2,2),2)  +  //  Pi_zz^2
                2.0 * (
                        pow( piMatrix (0,1),2) + // Pi_xy^2
                        pow( piMatrix (1,2),2) + // Pi_yz^2
                        pow( piMatrix (0,2),2)   // Pi_xz^2
                    ) ;


        return sqrt(2.0*newQ);
}



// get LES \omega
//inline Real getLesOmega(const Real& omega, const Real& csmag, const Real& Qo)
//{
//	const Real tau = 1.0/omega;
//	const Real nu = (2.0*tau-1.0) * (1.0/6.0);
//    const Real Csqr = csmag*csmag ;
//    const Real S = (-nu + sqrt( nu*nu + 18.0*Csqr*Qo )) / (6.0*Csqr);
//        return( 1.0/( 3.0*( nu+Csqr*S ) + 0.5 ) );
//}


//inline Real getLesOmega(const Real& omega,
//                        const Real& csmag,
//                        const Real& Qo,
//                        const Real C_D,
//                        const Real C_E ,
//                        Real&  turb_ke,
//                        Real&  turb_epsilon,
//                        Real&  tau_total)
//{

//        const Real tau = 1.0/omega;
//        const Real Csqr = csmag  ;//= csmag*csmag ;

//        const Real tau_t = 0.5*(pow(pow(tau,2) + 18.0*(Csqr)* Qo ,0.5) - tau);

//        const Real tau_eff = tau + tau_t;

//        assert (S >=0 ) ;

//        const Real nu = (2.0*tau-1.0) * (1.0/6.0);

//        const Real S = (-nu + sqrt( nu*nu + 18.0*Csqr*Qo )   ) / (6.0*Csqr);
//       // const Real S = (-nu + pow( nu*nu + 18.0*Csqr*Qo  , 0.5 )   ) / (6.0*Csqr);
//        turb_ke = S*S*  std::pow ( ( (csmag*csmag)/(C_D) ) , 2 ) ;

//        turb_epsilon = C_E * std::pow ( (turb_ke),(3./2.0)  );

//        tau_total = tau_eff ;

//        return( 1.0/( tau_eff) );


//}


//**************************************DYNAMIC SMAGORINSKY MODEL********************************//
// set a stress field in starting of the model d

/* inline void setStressTensor(PDFField &pdf, const  Uint x,const  Uint y,const  Uint z, const Vector3<Real> vel, const  Real rho)
 {
         const int* c[3] = { cx, cy, cz };
         for( int a=0;a<3; a++)
                 for(int b=0; b<3; b++ )
                         for( int l=0; l<D3Q19::Cellsize; l++)
                         {
                         if(c[a][l]||c[b][l]==0.0) continue;
                                 Stress_temp[]
                         }

 }*/









// =====================================================================
// 
// 
// 
// =====================================================================
inline Real QReguLBM(const  Uint& a,
                     const  Uint& b,
                     int l)
{
    double cssqrt  ;
    if( a ==  b )
    {
          cssqrt =  1./3.0 ;
    }else {
          cssqrt = 0.0 ;
    }

    return   ( c[a][l]*c[b][l]  - cssqrt  );
}

// used only for regulariezed LBM

//inline Real getQTensorCoef (const PDFField & fNeq,
//                           const  Uint& x,const  Uint& y,const  Uint& z,
//                           const  Vector3<Real>& vel,
//                           const  Real& rho)
//{

//    Real Qo = 0.0;
//        const Real u_sqr_trm_ = 1.5*( vel[X]*vel[X] + vel[Y]*vel[Y] + vel[Z]*vel[Z] ); // used in calculating the feq

//        const int* c[3] = { cx, cy, cz };

//        Matrix3<Real> piMatrix (0.0  ) ;
//Real pi = 0.0;

//        for (int a=0; a<3; a++)
//        {
//            for (int b=0; b<3; b++) {
//                   pi = 0.0;
//                   for(int l=0; l<D3Q19::Cellsize; l++)
//                   {
//                         if(c[a][l]==0.0 || c[b][l]==0.0)
//                                        continue;

//                         pi += c[a][l]*c[b][l]*(pdf.GET_PDF(x,y,z,l)-calc_feq(vel,rho,l,u_sqr_trm_));
//                   }
//                   Qo += pi*pi;
//            }
//        }

//       return sqrt(Qo) ;
//}


// =============================================================
//
// cherif
//
// =============================================================
inline void Turbulence::CalcPiTurbulence(
        const PDFField &pdf, const Vector3<Real>& vel,
        const Real& rho, const Real& omega,
                    const  Uint x, const Uint y,const  Uint z,
                    Matrix3<Real>& tau)
{
   //This function is based on Artoli's Disseration Formula 3.22
   //Main diagonal is sum of all values the the given direction
      //
     //	   N = 1,     S = 2,    W = 3,    E = 4,    T = 5,    B = 6,  NW = 7,    NE = 8,  SW = 9,  SE = 10,    TN = 11,    TS = 12,   TW = 13,    TE = 14,      BN = 15,  BS = 16,  BW = 17,   BE = 18      };

   tau[S11] = pdf.GET_PDF(x,y,z,D3Q19::E)  + pdf.GET_PDF(x,y,z,D3Q19::W) + pdf.GET_PDF(x,y,z,D3Q19::NE)  +
              pdf.GET_PDF(x,y,z,D3Q19::SW) + pdf.GET_PDF(x,y,z,D3Q19::TE) + pdf.GET_PDF(x,y,z,D3Q19::BW) +
              pdf.GET_PDF(x,y,z,D3Q19::TW) + pdf.GET_PDF(x,y,z,D3Q19::BE) + pdf.GET_PDF(x,y,z,D3Q19::SE) +
              pdf.GET_PDF(x,y,z,D3Q19::NW);

   tau[S22] = pdf.GET_PDF(x,y,z,D3Q19::S) +  pdf.GET_PDF(x,y,z,D3Q19::N) + pdf.GET_PDF(x,y,z,D3Q19::TN) +
              pdf.GET_PDF(x,y,z,D3Q19::BS) + pdf.GET_PDF(x,y,z,D3Q19::NE) + pdf.GET_PDF(x,y,z,D3Q19::SW) +
              pdf.GET_PDF(x,y,z,D3Q19::BN) + pdf.GET_PDF(x,y,z,D3Q19::TS) + pdf.GET_PDF(x,y,z,D3Q19::SE) +
              pdf.GET_PDF(x,y,z,D3Q19::NW);

   tau[S33] = pdf.GET_PDF(x,y,z,D3Q19::T) +  pdf.GET_PDF(x,y,z,D3Q19::B) + pdf.GET_PDF(x,y,z,D3Q19::TN) +
              pdf.GET_PDF(x,y,z,D3Q19::BS) + pdf.GET_PDF(x,y,z,D3Q19::TE) + pdf.GET_PDF(x,y,z,D3Q19::BW) +
              pdf.GET_PDF(x,y,z,D3Q19::BN) + pdf.GET_PDF(x,y,z,D3Q19::TS) + pdf.GET_PDF(x,y,z,D3Q19::TW) +
              pdf.GET_PDF(x,y,z,D3Q19::BE);

   tau[S23] = pdf.GET_PDF(x,y,z,D3Q19::TN) + pdf.GET_PDF(x,y,z,D3Q19::BS) -
              pdf.GET_PDF(x,y,z,D3Q19::BN) - pdf.GET_PDF(x,y,z,D3Q19::TS);

   tau[S31] = pdf.GET_PDF(x,y,z,D3Q19::TE) + pdf.GET_PDF(x,y,z,D3Q19::BW) -
              pdf.GET_PDF(x,y,z,D3Q19::TW) - pdf.GET_PDF(x,y,z,D3Q19::BE);

   tau[S12] = pdf.GET_PDF(x,y,z,D3Q19::NE) + pdf.GET_PDF(x,y,z,D3Q19::SW) -
              pdf.GET_PDF(x,y,z,D3Q19::SE) - pdf.GET_PDF(x,y,z,D3Q19::NW);

//   const Real u_sqr_trm = 1.5*( vel[X]*vel[X] + vel[Y]*vel[Y] + vel[Z]*vel[Z] );
//   for(int l=1; l<D3Q19::Cellsize; ++l )
//   {
//      const Real tmp = D3Q19::cx[l]*vel[X] + D3Q19::cy[l]*vel[Y] + D3Q19::cz[l]*vel[Z];
//      const Real ttmp = rho - u_sqr_trm + 3.0*tmp + 4.5*tmp*tmp ;
//      tau[S11] -= D3Q19::w[l]*D3Q19::cx[l]*D3Q19::cx[l]*( ttmp );
//      tau[S22] -= D3Q19::w[l]*D3Q19::cy[l]*D3Q19::cy[l]*( ttmp );
//      tau[S33] -= D3Q19::w[l]*D3Q19::cz[l]*D3Q19::cz[l]*( ttmp );
//   }

}

//   tau[S22] = pdf.GET_PDF(x,y,z,D3Q19::S) +  pdf.GET_PDF(x,y,z,D3Q19::N) +
//              pdf.GET_PDF(x,y,z,D3Q19::TN) + pdf.GET_PDF(x,y,z,D3Q19::BS) +
//              pdf.GET_PDF(x,y,z,D3Q19::NE) + pdf.GET_PDF(x,y,z,D3Q19::SW) +
//              pdf.GET_PDF(x,y,z,D3Q19::BN) + pdf.GET_PDF(x,y,z,D3Q19::TS) +
//              pdf.GET_PDF(x,y,z,D3Q19::SE) + pdf.GET_PDF(x,y,z,D3Q19::NW) ;

//   tau[S33] =  pdf.GET_PDF(x,y,z,D3Q19::T) +  pdf.GET_PDF(x,y,z,D3Q19::B) +
//               pdf.GET_PDF(x,y,z,D3Q19::TN) + pdf.GET_PDF(x,y,z,D3Q19::BS) +
//               pdf.GET_PDF(x,y,z,D3Q19::TE) + pdf.GET_PDF(x,y,z,D3Q19::BW) +
//               pdf.GET_PDF(x,y,z,D3Q19::BN) + pdf.GET_PDF(x,y,z,D3Q19::TS) +
//               pdf.GET_PDF(x,y,z,D3Q19::TW) + pdf.GET_PDF(x,y,z,D3Q19::BE);

//   tau[S23] = pdf.GET_PDF(x,y,z,D3Q19::TN) + pdf.GET_PDF(x,y,z,D3Q19::BS) -
//              pdf.GET_PDF(x,y,z,D3Q19::BN) - pdf.GET_PDF(x,y,z,D3Q19::TS);


//   tau[S31] = pdf.GET_PDF(x,y,z,D3Q19::TE) + pdf.GET_PDF(x,y,z,D3Q19::BW) -
//              pdf.GET_PDF(x,y,z,D3Q19::TW) - pdf.GET_PDF(x,y,z,D3Q19::BE);
//   tau[S12] = pdf.GET_PDF(x,y,z,D3Q19::NE) + pdf.GET_PDF(x,y,z,D3Q19::SW) -
//              pdf.GET_PDF(x,y,z,D3Q19::SE) - pdf.GET_PDF(x,y,z,D3Q19::NW);






//}




// =====================================================================
// 
// 
// 
// =====================================================================
inline Real Calctau_Eff (
                        const Real& delta_rho ,
                        const Vector3<Real>& vel,
                        const Real& u_sqr_trm_ ,
                        const Real& Omega,
                        Matrix3<Real>& piMatrix ,
                        const SimData& sim,
                        Uint x,Uint y, Uint z ,
                        const PDFField& src ,
                        Real Qo
                        )
{
/*
     for( unsigned int l=0; l<D3Q19::Cellsize; ++l )
     {
          const double f   =  src.GET_PDF(x,y,z,l) ;
          double feq = calc_feq(vel, delta_rho ,l,u_sqr_trm_);
          piMatrix (0,0) =  piMatrix (0,0) + (f  - feq*D3Q19::cx[l]*D3Q19::cx[l]);
          piMatrix (0,1) =  piMatrix (0,1) + (f  - feq*D3Q19::cx[l]*D3Q19::cy[l]);
          piMatrix (0,2) =  piMatrix (0,2) + (f  - feq*D3Q19::cx[l]*D3Q19::cz[l]);
          piMatrix (1,0) =  piMatrix (0,1) ;
          piMatrix (1,1) =  piMatrix (1,1) + (f  - feq*D3Q19::cy[l]*D3Q19::cy[l]);
          piMatrix (1,2) =  piMatrix (1,2) + (f  - feq*D3Q19::cy[l]*D3Q19::cz[l]);
          piMatrix (2,0) =  piMatrix (0,2) ;
          piMatrix (2,1) =  piMatrix (1,2) ;
          piMatrix (2,2) =  piMatrix (2,2) + (f  - feq*D3Q19::cz[l]*D3Q19::cz[l]);
     }

     // evaluate |S| (magnitude of the strain-rate)
     const Real sigmaT =
                     pow( piMatrix (0,0),2) + pow( piMatrix (0,1),2) + pow( piMatrix (0,2),2)
                  +  pow( piMatrix (1,0),2) + pow( piMatrix (1,1),2) + pow( piMatrix (1,2),2)
                  +  pow( piMatrix (2,0),2) + pow( piMatrix (2,1),2) + pow( piMatrix (2,2),2);

     Qo =  sqrt ( sigmaT );
*/
    //Real Qo = 0.0;
    //const Real u_sqr_trm_ = 1.5*( vel[X]*vel[X] + vel[Y]*vel[Y] + vel[Z]*vel[Z] ); // used in calculating the feq
/* Best until now
 *         Real pi = 0.0;
    const int* c[3] = { cx, cy, cz };

        for (int a=0; a<3; a++)
        {
            for (int b=0; b<3; b++) {
                   pi = 0.0;
                   for(int l=0; l<D3Q19::Cellsize; l++)
                   {
                         if(c[a][l]==0.0 || c[b][l]==0.0)
                                continue;
                         const Real f    = src.GET_PDF(x,y,z,l) ;
                         const Real f_eq = calc_feq(vel,delta_rho,l,u_sqr_trm_) ;
                         pi += c[a][l]*c[b][l]*( f - f_eq  );
                   }
                   Qo += pi*pi;
            }
        }

       Qo = sqrt(Qo);
*/


        for (int a=0; a<3; a++)
        {
            for (int b=0; b<3; b++) {
                   Real tmp = 0.0;
                   for(int l=0; l<D3Q19::Cellsize; l++)
                   {
                       const Real f    = src.GET_PDF(x,y,z,l) ;
                       const Real f_eq = calc_feq(vel,delta_rho,l,u_sqr_trm_) ;
                       tmp += c[a][l]*c[b][l]*( f - f_eq  );
                       //  tmp += c[a][l]*c[b][l]*(pdf.GET_PDF(x,y,z,l)-calc_feq(vel,rho,l,u_sqr_trm_));
                   }
                   piMatrix (a,b) = tmp ;
            }
        }

        Real newQ =0.0 ;

//        for (int a=0; a<3; a++)
//        {
//            for (int b=0; b<3; b++) {
//               newQ +=  (piMatrix (a,b) * piMatrix(b,a) ); ///FIXME piMatrix (a,b)^2 why this ?
//            }
//        }

        newQ =  pow( piMatrix (0,0),2)  +  //  Pi_xx^2
                pow( piMatrix (1,1),2)  +  //  Pi_yy^2
                pow( piMatrix (2,2),2)  +  //  Pi_zz^2
                2.0 * (
                        pow( piMatrix (0,1),2) + // Pi_xy^2
                        pow( piMatrix (1,2),2) + // Pi_yz^2
                        pow( piMatrix (0,2),2)   // Pi_xz^2
                    ) ;


        Qo  = sqrt (2.0*newQ);
//     const Real tau  = 1.0/ Omega ;
//     const Real Csqr = csmag ;//* csmag ;//= csmag*csmag ;
//     const Real nu = (2.0*tau-1.0) * (1.0/6.0);
// //  const Real tau_eff = 0.5 + 3.0 * (0.00035 + (1./6.)*( pow(  pow(tau,2.0) + 18.0*(Csqr)* Qo ,0.5) - tau) );
//     const Real tau_eff = 0.5 + 3.0 * ( nu     + (1./6.)*( pow(  pow(tau,2.0) + 18.0*(Csqr)* Qo ,0.5) - tau) );

     const Real nu   =  sim.nu_L;// 0.00035  ;

     const Real Csqr =  sim.csmag ;//* csmag ;//= csmag*csmag ;

     const Real tau_0  = (3.0 * nu + 0.50) ;

     const Real tau_t = 0.5 * (  sqrt  (   (tau_0*tau_0) + 18.0*sqrt (2.0 )*(Csqr)* Qo )  - tau_0) ;

     //const Real tau_eff = tau_0 + tau_t  ;

     const Real tau_eff = tau_t  ;
     // const Real tau_eff = 0.5 + 3.0 * ( nu     + (1./6.)*( pow(  pow(tau,2.0) + 18.0*(Csqr)* Qo ,0.5) - tau) );


     return tau_eff ;
}




}

#endif
