#include <iostream>

// just forward declaration
template <class Config> class Vector ;


struct Precise
{ 
  typedef long double Element;
};


struct Compact
{ 
  typedef float Element;
};


template <int Size = 10, class Precision = Compact>
struct ComputeConfig
{ 
    struct Config
    {
      typedef typename Precision::Element Element;
      enum { size = Size };
      typedef Vector<Config> Vector_type;
    };
//  ...
// 
};

template <int condition, class A, class B>
struct If
{
     template <int N>
     struct Select {};
     struct Select<0> { typedef B RETURN; };
     struct Select<1> { typedef A RETURN; };
     typedef Select<condition>::RETURN RETURN;
};


template <class Config> 
class Vector 
{
  public: 
//     typedef 
};

typedef ComputeConfig<>::Config::Vector_type my_special_vector ;




// if the condition is true, return A, else return B
template <bool condition, class A, class B>
struct IF
{
     template <bool N>  struct Select        {typedef A RETURN; }; // by default 
     template <>        struct Select<false> { typedef B RETURN; }; // specialisation 
     
     typedef Select<condition>::RETURN RETURN;
};



int main ()
{
    my_special_vector  v ;
    
    return 0 ;
}
