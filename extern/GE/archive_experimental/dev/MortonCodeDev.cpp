/* =============================================================================
 *
 * 
 * File:   MortonCodeDev.cpp
 * Author: cherif
 *
 * Created on October 16, 2010, 4:56 PM
 * 
 *
 * =============================================================================
 */


#include <cstdlib>
#include <iostream>
//#include "Qnode/Qnode.h"
#include <string>
#include <limits.h> //CHAR_BIT

using namespace std;
// =============================================================================
// =============================================================================
string convBase(unsigned long v, long base)
{
	string digits = "0123456789abcdef";
	string result;
	if((base < 2) || (base > 16)) {
		result = "Error: base out of range.";
	}
	else {
		do {
			result = digits[v % base] + result;
			v /= base;
		}
		while(v);
	}
	return result;
}


// =============================================================================
/* Bit print of an integral expression*/
// =============================================================================
inline void Print (int c)
{
	//std::cout << "x = " << c << "   " ;
	int n = sizeof(int)*CHAR_BIT;
	int mask = 1 << (n-1);
	for (int i=1; i<=n; ++i)
  	{
		putchar( ((c & mask) == 0) ? '0' : '1' );
		c <<= 1;
		if (i% CHAR_BIT == 0 && i < n)
			putchar(' ') ;
	}
	std::cout << "\n" ;
}



#define Qt_N_LEVEL	16
#define Qt_ROOT_LEVEL	15	// = Qt_N_LEVEL - 1
#define Qt_max		32768   // = 2 ^ Qt_ROOT_LEVEL


//static  Qnode* my_arrayOfNodes ;

inline void Init () ;	   //  set of points-random
inline void BuildQtree () ;
inline void TraverseQtree (const float& x,const float& y ,const float& z) ;



inline void test (const int& pos1, const int& pos2)
{
	std::cout << "position(" << pos1 << " , "<< pos2 << ")\n";
	std::cout << "Binary:  " << convBase(pos1,2) << std::endl;
	std::cout << "Binary:  " << convBase(pos2,2) << std::endl;
	Print(pos1) ;
	Print(pos2) ;


	// computing Morton code,

	// interleave the two integers
	unsigned int z = 0; // z gets the resulting Morton Number.

	for (int i = 0; i < sizeof(pos1) * CHAR_BIT; i++) // unroll for more speed...
	{
		 z |= (pos1 & 1U << i) << i | (pos2 & 1U << i) << (i + 1);
	}

	std::cout << "\n" ;
	std::cout << "Binary:  " << convBase(z,2) << std::endl;
	Print (z) ;
}


/* =============================================================================
 *  main function
 ============================================================================ */

int main(int argc, char** argv)
{

	std::cout << "Starting testing\n" ;

	test (2 ,2 ) ;

	std::cout << "------------------------------------------------\n" ;
	return 0;
}



inline void Init () {

 
}

inline void BuildQtree () {
   
}


/* Takes a value and "spreads" the HIGH bits to lower slots to seperate them.
   ie, bit 31 stays at bit 31, bit 30 goes to bit 28, bit 29 goes to bit 25, etc.
   Anything below bit 21 just disappears. Useful for interleaving values
   for Morton codes.
 */
inline unsigned long spread3(unsigned long x)
{
  x=(0xF0000000&x) | ((0x0F000000&x)>>8) | (x>>16); // spread top 3 nibbles
  x=(0xC00C00C0&x) | ((0x30030030&x)>>4);
  x=(0x82082082&x) | ((0x41041041&x)>>2);
  return x;
}

inline unsigned long morton(unsigned long x, unsigned long y, unsigned long z)
{
  return spread3(x) | (spread3(y)>>1) | (spread3(z)>>2);
}





class Hilbert
{
 private:
	int nXDim,nYDim;

	int *pPointsX;
	int *pPointsY;
	int  nPoints;

	int curvesize(int ord) {	return (1<<ord) - 1; }

	static const int NORTH=0, EAST=1, SOUTH=2, WEST=3;
	int nX,nY;

 public:
	// constructor
	Hilbert(int dx,int dy)
	{
		nXDim  = dx;
		nYDim  = dy;
		int m  = dx>dy ? dx : dy; // max (dx,dy)

		int nOrder = 1;
		while ( (1<<nOrder) < m ) nOrder++;

		pPointsX = new int[dx*dy];
		pPointsY = new int[dx*dy];

	   
		nX = 0;
		nY = 0;
		nPoints = 0;

		hilbert(nOrder, NORTH, EAST, SOUTH, WEST);// member function 
	}

	~Hilbert()
	{
		if (pPointsX) delete []pPointsX;  pPointsX = NULL;
		if (pPointsY) delete []pPointsY;  pPointsY = NULL;
	}

	int *GetX() { return pPointsX; }
	int *GetY() { return pPointsY; }
	int Count() { return nPoints;  }



private:

	void move(int d)
	{
		// Move one unit in direction d from point lastpoint.
		switch (d)
		{
		  case NORTH: nY--;  break;
		  case EAST:  nX++;  break;
		  case SOUTH: nY++;  break;
		  case WEST:  nX--;  break;
		}
	}

	void hilbert (int i,	  // order of Hilbert curve to be drawn
                int front,  // direction at front
                int right,  // direction at right
                int behind, // direction at back
                int left)   // direction at left
	{
		if (i == 0)
		{
			if (nX<nXDim && nY<nYDim) // cut to image only
			{
				pPointsX[nPoints  ] = nX;
				pPointsY[nPoints++] = nY;
			}
		}
		else
		{
		  hilbert(i-1, left , behind, right , front );	move(right );
		  hilbert(i-1, front, right , behind, left  );	move(behind);
		  hilbert(i-1, front, right , behind, left  );	move(left  );
		  hilbert(i-1, right, front , left  , behind);
		}
	}
};


