/*
 *
 *
 *
 *
 */

#include "Voxel.h"
#include <iostream>
#include <boost/array.hpp>
#include "Voxel_Grid.h"


int main ()
{
	
	Voxel<int,2> mm0(boost::extents[3][4]);
	
	mm0[0][0] = 6; // ok
	
	Voxel<int,2> mm1( mm0 ); 

	std::cout<< "mm1 [0][0] " << mm1 [0][0]  << std::endl;
	
	
	#define N 3
    
	// Create a 3D array that is 3 x 4 x 2

	typedef boost::multi_array<double, N> array_type;
	
	typedef array_type::index index;
	
	array_type A(boost::extents[3][4][2]);

//	
	array_type *tmm ;
// 	tmm = new array_type ( boost::extents[3][4][2] ) ;
	tmm = new array_type ( boost::extents[3][4][2] , boost::fortran_storage_order ) ;
//		
		(*tmm) [2][3][1] = 2 ;
		std::cout << "================\n" ;
		std::cout << (*tmm) [2][3][1] << std::endl ;
		std::cout << "================\n" ;		
			
// 	Assign values to the elements
	int values = 0;
	for(index i = 0; i != 3; ++i) 
	  for(index j = 0; j != 4; ++j)
	    for(index k = 0; k != 2; ++k)
		A[i][j][k] = values++;

	// Verify values
	int verify = 0;
	for(index i = 0; i != 3; ++i) 
	    for(index j = 0; j != 4; ++j)
		for(index k = 0; k != 2; ++k)
			assert(A[i][j][k] == verify++);

	
	
	typedef boost::multi_array<double, 3> array_type;
	
	boost::array<array_type::index, 3> shape = { { 3, 4, 2 } };

	array_type A2(shape);

	
	Voxel_Grid  Domain(100, 100, 100 , 3) ;
		
	std::cout << "================\n" ;
	std::cout << "Domain(1,3,3)  : " << Domain(1,3,3) << std::endl ;
	std::cout << "================\n" ;  

	assert (Domain(1,3,3) == 3) ;
	
 	Domain(1,3,3) = 5 ;
	
	assert (Domain(1,3,3) == 5) ;
	std::cout << "////////////////\n" ;
	std::cout << "Domain(1,3,3)  : " << Domain(1,3,3) << std::endl ;
	std::cout << "////////////////\n" ;
	
	return (0) ;

}
 
