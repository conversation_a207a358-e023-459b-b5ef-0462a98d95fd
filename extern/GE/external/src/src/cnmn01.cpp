/*******************************************************************************
ConminC++, to solve constrained or unconstrained function minimization.
Copyright (C) 1999  <PERSON><PERSON> <PERSON><PERSON> Dunn

This program is free software; you can redistribute it and/or
modify it under the terms of the GNU General Public License
as published by the Free Software Foundation; either version 2
of the License, or (at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this program; if not, write to the Free Software
Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA  02111-1307, USA.
*******************************************************************************/

//---------------------------------------------------------------------------
#pragma hdrstop

#include "cnmn01.h"
#include "conmin.h"
#include <math.h>
#ifdef UNIX
#include <memory.h>
#else
#include <mem.h>
#endif


//---------------------------------------------------------------------------
#pragma package(smart_init)
void Conmin::cnmn01(int &jgoto, double *x, double *df, double *g, int *isc,
     int *ic, double **a, double *g1, double *vlb, double *vub, double *scal,
     double *c, int *ncal, double &dx, double &dx1, double &fi, double &xi,
     int &iii )
{
//     ROUTINE TO CALCULATE GRADIENT INFORMATION BY FINITE DIFFERENCE.
//     BY G. N. VANDERPLAATS                         JUNE, 1972.
//     NASA-AMES RESEARCH CENTER,  MOFFETT FIELD, CALIF.

//
//
//      revision history
//      v1.1, 5/1/95, sharon padula, use coding standards
//      v2.0, 6/1/98, jack dunn, converted to C++ class
//
double fdch1, x1;
int i1;
info_list  inf;

if(jgoto == 1) goto L10;
if(jgoto == 2) goto L70;
infog=All;
inf=info;
nac=0;
if(linear_obj == NonLinear && iter > 1) goto L10;
//
//                    GRADIENT OF LINEAR OBJECTIVE
//
if(nfdg == Combined)
  {
  jgoto=1;
  return;
  }

L10:
jgoto=0;
if(nfdg == Combined && ncon == 0) return;
if(ncon == 0) goto L40;
//
//       * * * DETERMINE WHICH CONSTRAINTS ARE ACTIVE OR VIOLATED * * *
//
for(int i=1; i<=ncon; i++)
  {
  if(g[i]<ct) continue;
  if(isc[i] > 0 && g[i]<ctl) continue;
  if(++nac>=n3) return;
  ic[nac]=i;
  }
if(nfdg == Combined && nac==0) return;
if((linear_obj == NonLinear && iter > 1) && nac == 0) return;
//
//                  STORE VALUES OF CONSTRAINTS IN G1
//
memcpy(&g1[1], &g[1], ncon*sizeof(double));

L40:
jgoto=0;
if(nac == 0 && nfdg == Combined) return;
//
//                            CALCULATE GRADIENTS
//
infog=Partial;
info=Cal_all;
fi=obj;
iii=0;

L50:
iii++;
xi=x[iii];
dx=fabs(fdch*xi);
fdch1=fdchm;
if(nscal != 0) fdch1=fdchm/scal[iii];
if(dx<fdch1) dx=fdch1;
x1=xi+dx;
if(nside == Unbounded) goto L60;
if(x1 > vub[iii]) dx=-dx;

L60:
dx1=1./dx;
x[iii]=xi+dx;
ncal[1]++;
//
//                         FUNCTION EVALUATION
//
jgoto=2;
return;

L70:
x[iii]=xi;
if(nfdg == Fdiff) df[iii]=dx1*(obj-fi);
if(nac == 0) goto L90;
//
//             DETERMINE GRADIENT COMPONENTS OF ACTIVE CONSTRAINTS
//
for(int j=1; j<=nac; j++)
  {
  i1=ic[j];
  a[iii][j] = dx1*(g[i1]-g1[i1]);
  }

L90:
if(iii< ndv) goto L50;
infog=All;
info=inf;
jgoto=0;
obj=fi;
if(ncon==0) return;
//
//             STORE CURRENT CONSTRAINT VALUES BACK IN G-VECTOR
//
memcpy(&g[1], &g1[1], ncon*sizeof(double));

return;
}
