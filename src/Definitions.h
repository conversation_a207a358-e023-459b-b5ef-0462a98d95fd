//=================================================================================================
/*!
//  \file Definitions.h
//  \brief Contains all data type and enum definitions
//  \author <PERSON>
 */
//=================================================================================================
/**
 * \mainpage Walberla
 * A widely applicable Lattice <PERSON> solver from Erlangen
 *
 * \section GoalSection Goals
 * - fluid in arbitrary geometries with complex boundary conditions like periodic, acceleration, freeslip, a.s.o., as needed by medical applications like blood flow in vessels
 * - moving particles in a fluid, including the calculation of the forces that occur between the particles and the fluid and therefore determine the movement of particles caused by the influence of the fluid
 * - fluid and free surfaces, which includes both bubbles and atmosphere, resulting in scenarios that enable simulation of squirting drops, foams, a.s.o.
 * - ionised fluid reacting to electrical fields induced by charges or charged spheres in the fluid
 * \image html ./png/walberla.png
 *
 * \section Comp_and_running_Section Compiling and running waLBerla
 *
 * \subsection General_section General
 * The general directory setup of the waLBerla project is described in the file ./HowTo/project_structure.txt. A description of Protoype 0.0 can be found in http://www10.informatik.uni-erlangen.de/Publications/TechnicalReports/TechRep07-4.pdf .
 * The orientation in the waLBerla program is as follows:
 * - x=0	-> defines west border
 * - x=max -> defines east border
 * - y=0	-> defines south border
 * - y=max -> defines north border
 * - z=0	-> defines bottom border
 * - z=max -> defines top border
 * \subsection Prep_flags_subsection Preprocessor flags
 * The waLBerla project is based on CMake and can be configured with the following preprocessor flags:
 * - LOGGING=0..4: Sets the verbosity of the logging level. Choose 0 for no logging and 4 for intensive logging. \attention A high logging level slows down the execution!
 *
 * - BENCHMARKING: If this flag is choosen, the program is benchmarked. In the output file and on standardoutput the total CPU and wallclock times are printed. Additionallaly, the MLUPS and MFLUPS values are written.
 * - OPTIMIZED: Sets the sweep to an optimeized one.
 * - NDEBUG: Unsets debugging. No checks are done for array access and value mismatches. \attention If NDEBUG is set the execution will speed up, but no checks are done!
 *
 * \subsection First_steps_subsection First steps
 * If you want to run waLBerla for the first time, type ccmake . in the base directory, modify the settings of CMake and generate a makefile.
 * Then type make and build the project. For generating the documentation files type make doc.
 * After compiling the binary is put to the directory ./bin and can be run with ./bin/solver Parameter_File. An example parameter file can be found in example_parameter_file.prm .
 * You can find more information to compile in the file ./HowTo/setup.txt. More information to CMake can be found in http://www.insightsoftwareconsortium.org/wiki/index.php/CMake_Tutorial .
 *
 * \subsection Libraries_Used Libraries used
 * waLBerla uses the following libraries. For documentation see the appropriate link targets:
 * - pe: Physical Engine by Klaus Iglberger. <a href="../../extern/pe/doc/html/index.html">Goto documentation</a>
 *
 * \subsection Coding_Style Coding style
 * - Names of directories begin with small letters
 * - Filenames are equal to the class names they contain
 * - The first letter of a class name is upper case, the rest lower case. If the class name contains a word in between, this word starts again upper case. Example: LikeThis
 * - Names of functions are treated equivalent to class names.
 * - Variables and parameters follow the convention of class names, but first letter is lower case. Example: likeThis
 * - Member varaible names are like variables, but have an additional underscore at the end. Example: likeThis_
 * - Macros are completely written upper case. Words are separated with underscore. Example: LIKE_THIS
 * - For floating point values, the data type Real is used.
 *
 * \subsection Coding_Coventions Coding conventions
 * - No tabs are used.
 * - Indenting is done via three spaces. Indenting for example in loops, if, namespace, etc.
 * - The namespace walberla is used.
 */


/**
// \example Paraview.par
 * This is an example of an paraview block with, which can be added to the parameter file
 */

/**
// \example SimOutput.par
 * This is an example of an sim output block with, which can be added to the parameter file
 */

/**
// \example SimData.par
 * This is an example of an sim data block, which can be added to the parameter file
 */

/**
 * \example Patches.par
 * This is an example of a patch block with 2x3x4 patches in direction x,y,z, which can be added to the parameter file
 */

/**
 * \example InitBoundaries.par
 * This is an example of an init block for describing the initialization of the boundaries
 */

/**
 * \example InitPeriodicBoundaries.par
 * This is an example of an init block for describing the initialization of periodic boundaries
 */

/**
 * \example InitInner.par
 * This is an example of how to specify objects inside the domain
 */

/**
 * \example InitFilename.par
 * This is an example of an init block, which can be added to the parameter file and initializes the domain with a given file
 */

/**
 * \example InitScenario.par
 * This is an example of an init block, which can be added to the parameter file and initializes the domain with a scenario
 */

/**
 * \example InitLidDrivenCavity.par
 * This is an example of an init block for describing a Lid-Driven Cavity scenario
 */

/**
 * \example InitCanal.par
 * This is an example of an init block for describing a Canal scenario
 */

/**
 * \example Logging.par
 * This is an example of a logging block to configure the log output
 */

/**
 * \example MonitorCell.par
 * This is an example of a monitorCell block to monitor a certain cell
 */

/**
 * \example example_parameter_file.prm
 * This is an example parameter file.
 */

#ifndef DEFINITIONS_HH
#define DEFINITIONS_HH

//#define BOOST_FILESYSTEM_NO_LIB 
//#define BOOST_FILESYSTEM_NO_DEPRECATED 
//#define BOOST_FILESYSTEM_VERSION 3

#if defined(_MSC_VER) && (_MSC_VER >= 1400)

	// Disables a 'deprecated' warning for some standard library functions. This warning
	// is emitted when you use some perfectly conforming library functions in a perfectly
	// correct way, and also by some of Microsoft's own standard library code. For more
	// information about this particular warning, see
	// http://msdn.microsoft.com/en-us/library/ttcz0bys(VS.80).aspx
#  pragma warning(disable:4996)

#endif








#ifdef WALBERLA_USE_MPI
  #include <mpi.h>
#endif

#include <cctype>
#include "Logging.h"
#include <string>
#include <cmath>
#include <map>
#include <iostream>
#include <sstream>
#include <assert.h>

#ifndef NO_PE
  #include <pe/core.h>
  #include <pe/util.h>
  #include <pe/povray.h>
#endif

#include <vector>
//#include "Vector3.h"

#ifdef USE_STL_GE
  // only if GE is activated!!!
   ///FIXME  this is tmp solution, please remove next line in the final version
   #define DEBUG_FORCE_SWEEP_GE
   #define FLUDE_BORDERCELLES_EXPORT



  #ifdef DEBUG
    #define DEBUG_PRESSCOEFF_EXP
    #define DEBUG_FORCE_SWEEP_GE
    #define FLUDE_BORDERCELLES_EXPORT
  #endif
  // include std-hashtables.
  #include <tr1/unordered_set> // hash table
  #include <util/Utility.h>
#endif




// =============================================================================
// Typdefs
// =============================================================================
namespace walberla
{
	class CalcPatch;
	class PatchEntry;
	class Domain;
	class PovRayVis;

	typedef double Real;
	typedef unsigned int Flag; 
	typedef unsigned int Uint; 
	typedef unsigned char byte;
	typedef CalcPatch* PatchID;
	typedef PatchEntry* PatchEntryID;
	typedef CalcPatch* CalcPatchID;
	typedef std::vector<CalcPatchID> CalcVector;
    //typedef unsigned int State;	/*! A State is a combination of application 'Apps'*/
    typedef long unsigned int State;	/*! A State is a combination of application 'Apps' cherif*/
	typedef bool (walberla::Domain::*SweepFP)(CalcPatchID patch,const State, const Uint t, Uint iteration);
   // typedef bool (walberla::Domain::*SweepFP2)(CalcPatchID patch,const State, const Uint t, Uint iteration,const double& ,const double&,const double& , const double&);
	typedef std::map<Uint, std::vector<unsigned char> > MPI_BufferMap;
	typedef std::vector<unsigned char> MPI_BufferVector;
	typedef std::vector<PatchEntry>::iterator PatchIter;
	
	//tmp varaible, to delete later
	typedef std::vector<double> Probound; /// processor bounds Cherif

#ifdef NO_PE
	typedef std::map<Uint,Uint> ObstacleMap;
	typedef std::map<Uint,Uint>::iterator ObstacleIter;
	typedef std::vector<Uint> ObstacleVector;
	typedef std::vector<Uint>::iterator ObstacleVectorIterator;
#else
	typedef std::map<Uint,pe::UnionID> ObstacleMap;
	typedef std::map<Uint,pe::UnionID>::iterator ObstacleIter;
	typedef std::vector<pe::UnionID> ObstacleVector;
	typedef std::vector<pe::UnionID>::iterator ObstacleVectorIterator;
#endif

}




namespace walberla
{
	// =============================================================================
	// Povray Definitions
	// =============================================================================
	enum PlaneType{xy,xz,yz};

	/*! \brief Stores a plane. The PlaneType specifies the normal and the D is the D
	 * \brief parameter in the plane eq and it is the distance from the origin(in this
	 * \brief case)*/
	struct VelPlane{
		PlaneType type;
		Uint D;
	};

	class Macro {
		private:
			std::string macro_;

		public:
			Macro(std::string macro) {
				macro_ = macro;
			}
			Macro() {
				macro_ = "";
			}

			inline void print( std::ostream& os) const {
				if(macro_.compare(""))
					os << macro_ << "()\n";
			}

			inline void print( std::ostream& os, const char* tab) const {
				if(macro_.compare(""))
					os <<std::string(tab)<< macro_ <<"\n";
			}

			inline void print( std::ostream& os,const std::string& param) const {
				if(macro_.compare(""))
					os << macro_<<"("<<param<<")\n";
			}

			inline void print( std::ostream& os,const std::string& param, const char* tab) const {
				if(macro_.compare(""))
					os <<std::string(tab)<<macro_<<"("<<param<<")\n";
			}

	};

	class DeclaredInterior {
		private:
			std::string interior_;

		public:
			DeclaredInterior(const std::string& interior) {
				interior_ = interior;
			}
			DeclaredInterior() {
				interior_ = "";
			}
			inline void print( std::ostream& os) const {
				if(interior_.compare(""))
					os << "interior{\n\t"<<interior_<<"\n}\n";
			}

			inline void print( std::ostream& os, const char* tab) const {
				if(interior_.compare(""))
					os <<std::string(tab)<< "interior{\n\t"<<interior_<<"\n}\n";
			}

		};

	struct Photons {
			Real target_;
			bool reflection_;
			bool refraction_;
			bool collect_;

			Photons(const bool& collect,const bool& reflection,const bool& refraction,const Real& target=1.0) {
				target_ = target;
				reflection_ = reflection;
				refraction_ = refraction;
				collect_ = collect;
			}
			Photons() {
				// default POVRAY values (object will not influence any photons)
				target_=1.0;
				reflection_=false;
				refraction_=false;
				collect_=true;
			}
			inline void print( std::ostream& os) const {
					os << "photons{\n"
						<< "\ttarget "<<target_<< "\n"
						<< "\treflection "<< (reflection_? "on\n":"off\n")
						<< "\trefraction "<< (refraction_? "on\n":"off\n")
						<< "\tcollect "<< (collect_? "on\n":"off\n")
						<< "}\n";
			}

		};

	/*! \brief Stores visulaization information. First the appereance of an
	 * \brief object, second information if the path of an object should be visualized
	 * \brief or if force vector should be visulaized for an object.*/
	struct PovVisType{

		PovVisType():track(false),show_force(false),hollow(false),no_shadow(false),
		//texture( pe::povray::Texture::Texture( pe::povray::ColorPigment( "color rgb <1,1,0>" ))) //old version
		texture( pe::povray::PlainTexture(
		 pe::povray::ColorPigment( 1.0, 1.0, 0.0 ),
			pe::povray::Finish(
				pe::povray::Ambient( 0.1 ),
				pe::povray::Diffuse( 0.6 ),
				pe::povray::Phong( 0.9 ),
				pe::povray::Reflection( 0.05 )
			)
		 )
		),
		interior(),
		photons(),
		macro()
		{}

		bool track,
			  show_force;
		bool hollow;
		bool no_shadow;
		pe::povray::Texture texture;
		DeclaredInterior interior;
		Photons photons;
		Macro macro;
	};

	/*! \brief Stores visulaization information. First the position of the force,
	  \brief next the force vector, the norm of the force and a parameter to determine the
	  \brief size of the geometry*/
	struct PovVisForce{

		pe::Vec3 pos,
			force;
		Real magnitude,
			  size;
	};

	/*! \brief Stores visulaization information. First the position of the velocity,
	  \brief next the velocity vector and the norm of the velocity*/
	struct PovVisVel{

		pe::Vec3 pos,
			vel;
		Real magnitude;
	};



	/*!\brief Vector that stores the visulaization parameters for each object
	  \brief Access index is the id of the object */
	typedef std::vector<Uint> ObjectTypeVec;
	/*!\brief Vector that stores the string for cameras*/
	typedef std::vector<std::string> HeaderVec;
	//typedef std::vector< std::vector<VelPlane> > CamPlaneVecVec; // no longer needed!
	/*!\brief Vector that stores strings*/
	typedef std::vector<PovRayVis*> PovVec;
	/*! \brief Stores the visualization types for povray, like materials*/
	typedef std::vector<PovVisType> PovVisTypeVec;
	/*! \brief Stores the visualization information for the forces*/
	typedef std::vector<PovVisForce> PovVisForceVec;
	typedef std::vector<PovVisVel> PovVisVelVec;
	/*! \brief Stores the positions for the track visualization*/
	typedef std::vector<pe::Vec3> TrackVec;
	/*! \brief Stores the radii for spheres of the track visualization*/
	typedef std::vector<Real> TrackRadiiVec;
	//**End Povray Definitions***************************************************************************

#define PE_PROCESS (mpi_.numprocs-1)


	//*************************************************************************************************
	/*!\fn std::string ReplaceSubStr(std::string src, std::string find, std::string alt)
	// \brief Returns a string that equals \p src except that all occurences of \p find are replaced with \p alt.
	//
	// \param src The string which is operated on.
	// \param find The string which is searched in \p src and replaced by \p alt.
	// \param alt The alternative string which is used to replace \p find.
	// \return Modified string
	 */
	inline std::string ReplaceSubStr(std::string src, std::string find, std::string alt) {
		while (src.find(find)!=std::string::npos) {
			src = src.replace(src.find(find),find.length(),alt);
		}
		return src;
	}

	//*************************************************************************************************
	/*!\fn std::string ConvertToLowerCase( std::string s )
	// \brief Conversion of the character in \p s to lower case characters.
	//
	// \param s Reference to the string that gets converted.
	// \return The converted string.
	 */
	static inline std::string ConvertToLowerCase( std::string s )
	{
		for( std::string::size_type i=0; i<s.size(); ++i ) {
			s[i]=(std::tolower( s[i] ));
		}
		return s;
	}

	//*************************************************************************************************
	/*!\fn std::string ConvertToUpperCase( std::string s )
	// \brief Conversion of the character in \p s to upper case characters.
	//
	// \param s Reference to the string that gets converted.
	// \return The converted string.
	 */
	static inline std::string ConvertToUpperCase( std::string s )
	{
		for( std::string::size_type i=0; i<s.size(); ++i ) {
			s[i]=(std::toupper( s[i] ));
		}
		return s;
	}


	/*! \brief enum with different FLAG values. Flags are One-Hot-Coded (see binary representation).
	//	There are also flag groups for testing of several values with a single AND operation.
	//	CAUTION: If you add a new flag value here, please do so in functions FlagToStr and StrToFlag, too!
	*/
	enum FLAGS {					 // Hexadecimal -- Binary
		NO_FLAG		 	=  0,		//  0x00000000 -- 0000 0000 0000 0000 0000 0000 0000 0000

		NEAR_OBST		=  1,		//  0x00000001 -- 0000 0000 0000 0000 0000 0000 0000 0001
		LIQUID			=  2,		//  0x00000002 -- 0000 0000 0000 0000 0000 0000 0000 0010
		GAS			=  4,		//  0x00000004 -- 0000 0000 0000 0000 0000 0000 0000 0100
		INTERFACE		=  8,		//  0x00000008 -- 0000 0000 0000 0000 0000 0000 0000 1000
		FLUID			= 14,		//  0x0000000E -- 0000 0000 0000 0000 0000 0000 0000 1110

		GHOST			=  16,		//  0x00000010 -- 0000 0000 0000 0000 0000 0000 0001 0000
		PERIODIC		=  32,		//  0x00000020 -- 0000 0000 0000 0000 0000 0000 0010 0000

		NOSLIP			= 256,		//  0x00000100 -- 0000 0000 0000 0000 0000 0001 0000 0000
        ACC             = 512,		//  0x00000200 -- 0000 0000 0000 0000 0000 0010 0000 0000
		REACT			= 512,		//  0x00000200 -- 0000 0000 0000 0000 0000 0010 0000 0000
		VEL_IN			= 1024,		//  0x00000400 -- 0000 0000 0000 0000 0000 0100 0000 0000
		MI_IN			= 1024,		//  0x00000400 -- 0000 0000 0000 0000 0000 0100 0000 0000
		PRS_NILS		= 2048,		//  0x00000800 -- 0000 0000 0000 0000 0000 1000 0000 0000
		MI_OUT			= 2048,		//  0x00000800 -- 0000 0000 0000 0000 0000 1000 0000 0000
		BOUZIDI		  	= 4096,		//  0x00001000 -- 0000 0000 0000 0000 0001 0000 0000 0000
		FREESLIP		= 8192,		//  0x00002000 -- 0000 0000 0000 0000 0010 0000 0000 0000
		PARTSLIP		= 16384,	//  0x00004000 -- 0000 0000 0000 0000 0100 0000 0000 0000
        	ZERO_PRS_GRAD  		= 32768,	//  0x00008000 -- 0000 0000 0000 0000 1000 0000 0000 0000
		PRS_GRAD		= 65536,	//  0x00010000 -- 0000 0000 0000 0001 0000 0000 0000 0000
		PRS_COMP		= 131072,	//  0x00020000 -- 0000 0000 0000 0010 0000 0000 0000 0000
		MASS_IN		  	= 262144,	//  0x00040000 -- 0000 0000 0000 0100 0000 0000 0000 0000
		OBST			= 524032,	//  0x0007FF00 -- 0000 0000 0000 0111 1111 1111 0000 0000 
		ANYSLIP		  	= 28928,	//  0x00007100 -- 0000 0000 0000 0000 0111 0001 0000 0000

		TREATED		  	= 2097152,	//  0x00200000 -- 0000 0000 0010 0000 0000 0000 0000 0000
		OLD_OBST		= 4194304,	//  0x00400000 -- 0000 0000 0100 0000 0000 0000 0000 0000
		UNDEFINED		= 8388608,	//  0x00800000 -- 0000 0000 1000 0000 0000 0000 0000 0000
        TMP_FLOODFILL   = 16777216,     //  0x10000000 -- 0000 0001 0000 0000 0000 0000 0000 0000 //needed for GE-tool, add by Cherif
		ALL_FLAGS		= 4294967295U   //  0xFFFFFFFF -- 1111 1111 1111 1111 1111 1111 1111 1111
	};
	//*************************************************************************************************
	/*!\fn std::string FlagToStr(Flag flag)
	// \brief Returns a string that names the boundary conditions which are set in \p flag.
	//
	// \param flag A flag value to be interpreted
	// \return String with Flag names
	 */
	inline std::string FlagToStr(const Flag& flag) {
		std::string str="<";
		if (flag==0)			 str+="NONE+";
		if (flag & NEAR_OBST) str+="NEAR_OBST+";
		if (flag & LIQUID)	 str+="LIQUID+";
		if (flag & GAS)		 str+="GAS+";
		if (flag & FLUID)	  str+="FLUID+";
		if (flag & INTERFACE) str+="INTERFACE+";
		if (flag & GHOST)	  str+="GHOST+";
		if (flag & PERIODIC)  str+="PERIODIC+";
		if (flag & NOSLIP)	 str+="NOSLIP+";
		if (flag & ACC)		 str+="ACC+";
		if (flag & VEL_IN)	 str+="VEL_IN+";
		if (flag & PRS_NILS)  str+="PRS_NILS+";
		if (flag & BOUZIDI)	str+="BOUZIDI+";
		if (flag & FREESLIP)  str+="FREESLIP+";
		if (flag & PARTSLIP)  str+="PARTSLIP+";
		if (flag & ZERO_PRS_GRAD)str+="ZERO_PRS_GRAD+";
		if (flag & PRS_GRAD)  str+="PRS_GRAD+";
		if (flag & PRS_COMP)  str+="PRS_COMP+";
		if (flag & MASS_IN)	str+="MASS_IN+";
		if (flag & OBST)		str+="OBST+";
		if (flag & UNDEFINED) str+="UNDEFINED+";
		if (flag & TREATED)	str+="TREATED+";
		if (flag & OLD_OBST)  str+="OLD_OBST+";
		if (str.substr(str.length()-1)=="+") str=str.substr(0,str.length()-1);
		return str+">";
	}
	//*************************************************************************************************
	/*!\fn Flag StrToFlag(std::string flagStr)
	// \brief Returns a flag value that represents all named flags in the string.
	// The string contains names as stated by the function FlagToStr, several names can be concatenated by "+".
	//
	// \param flag A string value to be interpreted
	// \return Flag value
	 */
	inline Flag StrToFlag(const std::string& flagStr) {
		Flag flag = 0;
		std::string str=flagStr;
		if (str.substr(0,1)=="<") str=str.substr(1);
		if (str.substr(str.length()-1)==">") str=str.substr(0,str.length()-1);
		str="+"+ConvertToUpperCase(str)+"+";
		if (str.find("+NEAR_OBST+")!=std::string::npos)	  flag+=NEAR_OBST;
		if (str.find("+LIQUID+")!=std::string::npos)		  flag+=LIQUID;
		if (str.find("+GAS+")!=std::string::npos)			  flag+=GAS;
		if (str.find("+FLUID+")!=std::string::npos)			flag+=FLUID;
		if (str.find("+INTERFACE+")!=std::string::npos)	  flag+=INTERFACE;
		if (str.find("+GHOST+")!=std::string::npos)			flag+=GHOST;
		if (str.find("+PERIODIC+")!=std::string::npos)		flag+=PERIODIC;
		if (str.find("+NOSLIP+")!=std::string::npos)		  flag+=NOSLIP;
		if (str.find("+ACC+")!=std::string::npos)			  flag+=ACC;
		if (str.find("+VEL_IN+")!=std::string::npos)		  flag+=VEL_IN;
		if (str.find("+PRS_NILS+")!=std::string::npos)		flag+=PRS_NILS;
		if (str.find("+BOUZIDI+")!=std::string::npos)		 flag+=BOUZIDI;
		if (str.find("+FREESLIP+")!=std::string::npos)		flag+=FREESLIP;
		if (str.find("+PARTSLIP+")!=std::string::npos)		flag+=PARTSLIP;
		if (str.find("+ZERO_PRS_GRAD+")!=std::string::npos) flag+=ZERO_PRS_GRAD;
		if (str.find("+PRS_GRAD+")!=std::string::npos)		flag+=PRS_GRAD;
		if (str.find("+PRS_COMP+")!=std::string::npos)		flag+=PRS_COMP;
		if (str.find("+MASS_IN+")!=std::string::npos)		 flag+=MASS_IN;
		if (str.find("+OBST+")!=std::string::npos)			 flag+=OBST;
		if (str.find("+UNDEFINED+")!=std::string::npos)	  flag+=UNDEFINED;
		if (str.find("+TREATED+")!=std::string::npos)		 flag+=TREATED;
		if (str.find("+OLD_OBST+")!=std::string::npos)		flag+=OLD_OBST;
		return flag;
	}







	//Enumeration to access the velocity vector
	enum {
		X = 0,
		Y = 1,
		Z = 2
	};


	//Enumeration to access the shear stress tensor
	enum {
		S11 = 0,
		S12 = 1,
		S13 = 2,
		S21 = 3,
		S22 = 4,
		S23 = 5,
		S31 = 6,
		S32 = 7,
		S33 = 8
	};

	namespace comm{

		extern Uint communicateAllPDFDirs; //!< currently used to switch functionality in PDFField-Border methods whether to communicate all or only selected PDFs. Will be moved somewhere else when waLBerla library will be built.


		enum Directions{
			DIR_D3Q19=19,
			DIR_D3Q27=27,
			MAXIMUM_COMM_DIR=27
		};


		enum CommDir{

			N = 0,				//!< North
			S = 1,				//!< South
			W = 2,				//!< West
			E = 3,				//!< East
			T = 4,				//!< Top
			B = 5,				//!< Bottom
			NW = 6,			  //!< North-West
			NE = 7,			  //!< North-East
			SW = 8,			  //!< South-West
			SE = 9,			 //!< South-East
			TN = 10,			 //!< Top-North
			TS = 11,			 //!< Top-South
			TW = 12,			 //!< Top-West
			TE = 13,			 //!< Top-East
			BN = 14,			 //!< Bottom-North
			BS = 15,			 //!< Bottom-South
			BW = 16,			 //!< Bottom-West
			BE = 17,			 //!< Bottom-East
			MAX_COMM_DIR_D3Q19=17,
			NET = 18,			//!< North-East-Top
			NEB = 19,			//!< North-East-Bottom
			NWT = 20,			//!< North-West-Top
			NWB = 21,			//!< North-West-Bottom
			SET = 22,			//!< North-East-Top
			SEB = 23,			//!< North-East-Bottom
			SWT = 24,			//!< North-West-Top
                        SWB = 25,			//!< North-West-Bottom
			MAX_COMM_DIR_D3Q27=25,
			MAX_COMM_DIR=26
		};

		static const std::string commDirToString[] = {"N", "S", "W", "E", "T", "B","NW","NE","SW","SE","TN","TS","TW","TE","BN","BS","BW","BE", "NET", "NEB", "NWT", "NWB", "SET", "SEB", "SWT", "SWB"};

		enum CommTypes{
                        EMPTY				= 1,
                        FIELDS				= 2,
                        PE				= 3,
                        VAN				= 4,
                        BUBBLEUPDATE			= 5,	//!< for FREE_SURFACE, contains only diffdata
			BUBBLEUPDATECHAINUP	= 6,	//!< for FREE_SURFACE, diffdata for chain communication scheme, upstream
			BUBBLEUPDATECHAINDOWN = 7,	//!< for FREE_SURFACE, diffdata for chain communication scheme, dwonstream
			WHOLEBUBBLE			  = 8,	//!< for FREE_SURFACE, contains whole bubble data
			MERGEBUBBLEA			 = 9,	//!< for FREE_SURFACE, contains whole bubble data and information, which bubble is replaced by which
			MERGEBUBBLEB			 = 10,  //!< for FREE_SURFACE, contains volume diff data and whole patch and merge list
			MERGEBUBBLEC			 = 11	//!< for FREE_SURFACE, contains update information for merge list
		};

/*
                                               // N	  S	  W	  E	  T	  B  NW  NE	SW  SE  TN  TS  TW  TE  BN  BS  BW  BE NET NEB NWT NWB SET SEB SWT SWB
        static const int cx[MAX_COMM_DIR] = {	  0,  0, -1,  1,  0,  0, -1,  1,  -1,  1,  0,  0, -1,  1,  0,  0, -1,  1,  1,  1, -1, -1,  1,  1, -1, -1};
        static const int cy[MAX_COMM_DIR] = {	  1, -1,  0,  0,  0,  0,  1,  1,  -1, -1,  1, -1,  0,  0,  1, -1,  0,  0,  1,  1,  1,  1, -1, -1, -1, -1};
        static const int cz[MAX_COMM_DIR] = {	  0,  0,  0,  0,  1, -1,  0,  0,   0,  0,  1,  1,  1,  1, -1, -1, -1, -1,  1, -1,  1, -1,  1, -1,  1, -1};

*/


		static const int cx[MAX_COMM_DIR] =

		{	  0,  0, -1,  1,  0,  0, -1,  1,  -1,  1,  0,  0, -1,  1,  0,  0, -1,  1,  1,  1, -1, -1,  1,  1, -1, -1};
		static const int cy[MAX_COMM_DIR] =
			// N	S	W	E	T	B  NW  NE	SW  SE  TN  TS  TW  TE  BN  BS  BW  BE NET NEB NWT NWB SET SEB SWT SWB
		{	  1, -1,  0,  0,  0,  0,  1,  1,  -1, -1,  1, -1,  0,  0,  1, -1,  0,  0,  1,  1,  1,  1, -1, -1, -1, -1};
		static const int cz[MAX_COMM_DIR] =
			// N	S	W	E	T	B  NW  NE	SW  SE  TN  TS  TW  TE  BN  BS  BW  BE NET NEB NWT NWB SET SEB SWT SWB
		{	  0,  0,  0,  0,  1, -1,  0,  0,	0,  0,  1,  1,  1,  1, -1, -1, -1, -1,  1, -1,  1, -1,  1, -1,  1, -1};
		static const CommDir finv[MAX_COMM_DIR]=
			// N	S	W	E	T	B  NW  NE	SW  SE  TN  TS  TW  TE  BN  BS  BW  BE NET NEB NWT NWB SET SEB SWT SWB
		{	  S,  N,  E,  W,  B,  T, SE, SW,  NE, NW, BS, BN, BE, BW, TS, TN, TE, TW,SWB,SWT,SEB,SET,NWB,NWT,NEB,NET};
		static const CommDir half_dir[MAX_COMM_DIR/2]=
			// S  W  B  SW  SE  BS  BN  BW  BE  SWB  SWT  SEB  SET
		{	  N, E, T, NE, NW, TN, TS, TE, TW, NET, NEB, NWT, NWB  };

		inline void operator++(CommDir &inc){
			inc=static_cast<CommDir>(inc+1);
		}

                inline CommDir operator~(const CommDir& inv){
			return finv[inv];
		}

                // cherif

               //Cx {	  0,  0, -1,  1,  0,  0, -1,  1,  -1,  1,  0,  0, -1,  1,  0,  0, -1,  1,  1,  1, -1, -1,  1,  1, -1, -1};
               //Cy {	  1, -1,  0,  0,  0,  0,  1,  1,  -1, -1,  1, -1,  0,  0,  1, -1,  0,  0,  1,  1,  1,  1, -1, -1, -1, -1};

            //Cx *Cy
               //Cy {	  1, -1,  0,  0,  0,  0,  1,  1,  -1, -1,  1, -1,  0,  0,  1, -1,  0,  0,  1,  1,  1,  1, -1, -1, -1, -1};
               //Cz {	  0,  0,  0,  0,  1, -1,  0,  0,   0,  0,  1,  1,  1,  1, -1, -1, -1, -1,  1, -1,  1, -1,  1, -1,  1, -1}
            //Cy *Cz {	  0,  0,  0,  0,  0,  0,  0,  0,   0,  0,  1, -1,  0,  0, -1,  1,  0,  0,  1, -1,  1, -1, -1,  1,  1,  1}


                const int ep[6][MAX_COMM_DIR] = {
                    {	  0,  0,  1,  1,  0,  0,  1,  1,   1,  1,  0,  0,  1,  1,  0,  0,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1}, // ex * ex
                    {	  1,  1,  0,  0,  0,  0,  1,  1,   1,  1,  1,  1,  0,  0,  1,  1,  0,  0,  1,  1,  1,  1,  1,  1,  1,  1}, // ey * ey
                    {	  0,  0,  0,  0,  1,  1,  0,  0,   0,  0,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1}, // ez * ez
                    {	  0,  0,  0 , 0,  0,  0, -1,  1,   1, -1,  0,  0,  0,  0,  0,  0,  0,  0,  1,  1, -1, -1, -1, -1,  1,  1}, // ex * ey
                    {	  0,  0,  0,  0,  0,  0,  0,  0,   0,  0,  0,  0, -1,  1,  0,  0,  1, -1,  1, -1, -1,  1,  1, -1, -1,  1}, // ex * ez
                    {	  0,  0,  0,  0,  0,  0,  0,  0,   0,  0,  1, -1,  0,  0, -1,  1,  0,  0,  1, -1,  1, -1, -1,  1,  1,  1}  // ey * ez
                };
	}



    // cherif
//    const int* c[3] = { comm::cx, comm::cy, comm::cz };



	enum COMPRESSION {
		NO_COMPRESS = 0,
		FAST_COMPRESS_FILTERED = 1,
		FAST_COMPRESS_HUFFMAN  = 2,
		BEST_COMPRESS_FILTERED = 3,
		BEST_COMPRESS_HUFFMAN  = 4
	};







	//*************************************************************************************************
	/*!\fn void ByteSwap(unsigned char * b, int n)
	// \brief Swaps bytes for Paraview output method.
	//
	// \param b input variable which sould be set to other byte order.
	// \param n length in bytes.
	 */
	inline void ByteSwap(unsigned char * b, const int& n)
	{
		register int i = 0;
		register int j = n-1;
		while (i<j)
		{
			std::swap(b[i], b[j]);
			i++, j--;
		}
	}

	//*************************************************************************************************
	/*!\fn bool IsNumericallyEqual(Real a, Real b)
	// \brief Compares \p a and \p b for equality from a numerical sight (respects a round-off error of 10e-9)
	//
	// \param a variable that is to compare with \p b
	// \param b variable that is to compare with \p a
	// \param bound optional parameter. Accuracy for which the comparison is done. Defaults to 10e-9
	// \return Boolean value, true if \p a "equals" \p b
	 */
	inline bool IsNumericallyEqual(const Real& a,const Real& b,const Real& bound=10e-9) {
		if ( fabs(a - b) < bound ) 
			return true;
		else
			return false;
	}




	//*************************************************************************************************
	/*!\fn void DivideFilenamePath( const std::string& input, std::string filename, std::string path)
	// \brief Divides the filename and the path for the output.
	// \param filename The name.
	// \return void
	 */
	inline void DivideFilenamePath( const std::string& input, std::string& filename, std::string& path)
	{
		std::string temp_string;

		//Find slash or backslash
		std::string::size_type pos_slash = input.rfind("/"); // The position of slash
		if(pos_slash==std::string::npos)
			pos_slash = input.rfind("\\"); // or backslash for windows

		//Slash or backslash found -> path given in name
		if(pos_slash!=std::string::npos)
		{
			//LOG_INFO4("OutputContainer.h::SetFilename: Found slash or backslash at pos "+Convert<std::string>(pos_slash)+"\n");

			temp_string.assign(input,pos_slash+1,input.length());
			filename = temp_string;
			temp_string.assign(input,0,pos_slash+1);
			path = temp_string;
		}
		else //No path given in the name
		{
			//LOG_INFO4("OutputContainer.h::SetFilename: No slash or backslash found\n");

			//LOG_INFO4("No path given in name\n");
			filename = input;
			path = "";
		}
		//LOG_INFO4("OutputContainer.h::SetFilename: Filename is: "+filename+"\n");
		//LOG_INFO4("OutputContainer.h::SetFilename: Path is: "+path+"\n");

	}

  

	//**Struct PatchUID***************************************************************************
	/*!\brief Stores the 3D id of a patch and provides comparity operators and some support functions for MPI communication
	 */

	class PatchUID{

		public:

			inline PatchUID():xIndex(0),yIndex(0),zIndex(0){}
			inline PatchUID(const int& x,const int& y,const int& z):xIndex(x),yIndex(y),zIndex(z){}


			//*************************************************************************************************
			/*!\fn PatchUID(MPI_BufferVector::iterator &dataIt)
			// \brief Construtor to create a PatchUID from information of a MPI_BufferVector. Note: Moves the iterator forward.
			// \param dataIt An iterator pointing to the current position in a MPI_BufferVector
			// \return void
			 */
			inline PatchUID(MPI_BufferVector::iterator &dataIt) {
				Uint *pos = (Uint*)&(*dataIt);
				xIndex = pos[0];
				yIndex = pos[1];
				zIndex = pos[2];
				dataIt += 3*sizeof(Uint)/sizeof(char);
			}

			//*************************************************************************************************
			/*!\fn PatchUID(void* startAddress)
			// \brief Construtor to create a PatchUID from an arbitrary buffer. Note: does not change the pointer.
			// \param startAddress Pointer pointing to the startAddress in the buffer where the PatchUID begins
			// \return void
			 */
			inline PatchUID(void* startAddress) {
				Uint *pos = (Uint*) startAddress;
				xIndex = pos[0];
				yIndex = pos[1];
				zIndex = pos[2];
			}

			//*************************************************************************************************
			/*!\fn bool operator==( const PatchUID &eq )const
			// \brief Equality operator
			// \return true, if PatchUID is equal
			 */
			inline bool operator==( const PatchUID &eq )const{
				return (xIndex==eq.xIndex && yIndex==eq.yIndex && zIndex==eq.zIndex);
			}

			//*************************************************************************************************
			/*!\fn bool operator!=( const PatchUID &eq )const
			// \brief Inequality operator
			// \return true, if PatchUID is not equal
			 */
			inline bool operator!=( const PatchUID &eq )const{
				return (xIndex!=eq.xIndex || yIndex!=eq.yIndex || zIndex!=eq.zIndex);
			}

			//*************************************************************************************************
			/*!\fn bool operator<( const PatchUID &eq )const
			// \brief Comparison operator for PatchUID. Introduces hierarchy according to which a PatchID is
			// lower when it is more east or more north or at least more bottom
			// \return true, if PatchUID is less
			 */
			inline bool operator<( const PatchUID &eq )const{
				return (zIndex<eq.zIndex) ||
					(zIndex==eq.zIndex && yIndex<eq.yIndex) ||
					(zIndex==eq.zIndex && yIndex==eq.yIndex && xIndex<eq.xIndex);
			}

			//*************************************************************************************************
			/*!\fn bool operator>( const PatchUID &eq )const
			// \brief Comparison operator for PatchUID. Uses other comparison operators in order to avoid
			// conflicts in hierarchy
			// \return true, if PatchUID is larger
			 */
			inline bool operator>( const PatchUID &eq )const{
				return (!operator<(eq)) && (!operator==(eq));
				//return (xIndex>eq.xIndex) ||
				//		 (xIndex==eq.xIndex && yIndex>eq.yIndex) ||
				//		 (xIndex==eq.xIndex && yIndex==eq.yIndex && zIndex>eq.zIndex);
			}

			//*************************************************************************************************
			/*!\fn PatchUID GetNeighborUID( comm::dir d ) const
			// \brief Resizes the MPI_BufferVector buffer and fills it with the data of this PatchUID.
			// \param buffer The MPI_BufferVector to be resized and filled.
			// \return void
			 */
			inline PatchUID GetNeighborUID(const comm::CommDir& d ) const {
				return PatchUID(xIndex+comm::cx[d],yIndex+comm::cy[d],zIndex+comm::cz[d]);
			}

			//*************************************************************************************************
			/*!\fn void FillBuffer( MPI_BufferVector &buffer )
			// \brief Resizes the MPI_BufferVector buffer and fills it with the data of this PatchUID.
			// \param buffer The MPI_BufferVector to be resized and filled.
			// \return void
			 */
			inline void FillBuffer( MPI_BufferVector &buffer ) {
				Uint vSize=buffer.size();
				buffer.resize(vSize+3*sizeof(int));
				int *pos = (int*)&((&buffer.front())[vSize]);
				pos[0] = xIndex;
				pos[1] = yIndex;
				pos[2] = zIndex;
			}

			//*************************************************************************************************
			/*!\fn void WriteToBuffer( void* startAddress )
			// \brief Writes the data of this PatchUID to a buffer, starting at startAddress.
			// \param startAddress The address in the buffer where the first byte of PatchUID data will be.
			// \return void
			 */
			inline void WriteToBuffer( void* startAddress ) const {
				int *pos = (int*) startAddress;
				pos[0] = xIndex;
				pos[1] = yIndex;
				pos[2] = zIndex;
			}

			int xIndex,
				 yIndex,
				 zIndex;


	};

	//*************************************************************************************************
	/*!\fn std::ostream& operator<<( std::ostream& os, const PatchUID& v )
	// \brief Global output operator for PatchUID.
	//
	// \param os Reference to the output stream.
	// \param v Reference to a constant PatchUID.
	// \return Reference to the output stream.
	 */
	inline std::ostream& operator<<( std::ostream& os, const PatchUID& v )
	{
		return os << "(" << v.xIndex << "," << v.yIndex << "," << v.zIndex << ")";
	}




	//*************************************************************************************************

	/*!\brief Stores a reference to a patch and a local coordinate
	 */
	struct PatchCoordinate {

		PatchCoordinate(){}
		PatchCoordinate(Uint *data):pX(data[0]),pY(data[1]),pZ(data[2]),x(data[3]),y(data[4]),z(data[5]){}

		Uint pX;			  //!< x index of patch in patchGrid_ (0..xNumPatches_-1)
		Uint pY;			  //!< y index of patch in patchGrid_ (0..yNumPatches_-1)
		Uint pZ;			  //!< z index of patch in patchGrid_ (0..zNumPatches_-1)
		Uint x;				//!< x coordinate in the patch (0..xSize)
		Uint y;				//!< y coordinate in the patch (0..ySize)
		Uint z;				//!< z coordinate in the patch (0..zSize)

		//*************************************************************************************************
		/*!\fn bool operator==( const PatchCoordinate &eq )const
		// \brief Equality operator
		// \return true, if eq is equal to this
		 */
		inline bool operator==( const PatchCoordinate &eq )const{
			return (pX==eq.pX && pY==eq.pY && pZ==eq.pZ && x==eq.x && y==eq.y && z==eq.z);
		}
	private :
		//PatchCoordinate(const PatchCoordinate& );
		//void operator = (const PatchCoordinate& );
	};

	inline std::ostream& operator << (std::ostream& out, const PatchCoordinate& Coord)
	{
	    out << "Patch	(pX,pY,pZ) :  (" << Coord.pX << ","<< Coord.pY << ","<< Coord.pZ << ")\n" ;
	    out << "	(x, y ,z ) :  (" << Coord.x << "," << Coord.y  << ","<< Coord.z  << ")\n" ;
	    return out ;
	}


	class PDFField;
	class DensField;
	class VelField;


	namespace mix{


		struct BCCell{

			Uint i,j,k;
			Uint d;

		};

		typedef std::map<Uint, std::vector<BCCell> > BCMap;
		typedef std::vector<PDFField*> PdfIDVec;
		typedef std::vector<DensField*> DensIDVec;
		typedef std::vector<VelField*> VelIDVec;


		struct SpecieStruct{

			Real M,
				  M_L,
				  invM_L,
				  y_I,
				  rho_I,
				  rho_I_L,
				  n_I,
				  n_I_L,
				  D,
				  D_L,
				  lambda_D_L,
				  xi,
				  x_I,
				  mom_R,
				  mom_R_L;
			Real total_mom_In_L,
				  total_mom_R_L;
		};

		static const Real pre[19]={
			-0.5,
			1.5,1.5,1.5,1.5,1.5,1.5,
			-1.5,-1.5,-1.5,-1.5,-1.5,-1.5,-1.5,-1.5,-1.5,-1.5,-1.5,-1.5
		};


		static const Real pre_xi[19]={1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0};

		static const Real M[19*19]={
			1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1 ,
			0,0,0,-1,1,0,0,-1,1,-1,1,0,0,-1,1,0,0,-1,1 ,
			0,1,-1,0,0,0,0,1,1,-1,-1,1,-1,0,0,1,-1,0,0 ,
			0,0,0,0,0,1,-1,0,0,0,0,1,1,1,1,-1,-1,-1,-1 ,
			0,0,0,1,1,0,0,1,1,1,1,0,0,1,1,0,0,1,1 ,
			0,1,1,0,0,0,0,1,1,1,1,1,1,0,0,1,1,0,0 ,
			0,0,0,0,0,1,1,0,0,0,0,1,1,1,1,1,1,1,1 ,
			0,0,0,0,0,0,0,-1,1,1,-1,0,0,0,0,0,0,0,0 ,
			0,0,0,0,0,0,0,0,0,0,0,0,0,-1,1,0,0,1,-1 ,
			0,0,0,0,0,0,0,0,0,0,0,1,-1,0,0,-1,1,0,0 ,
			0,0,0,0,0,0,0,1,1,-1,-1,0,0,0,0,0,0,0,0 ,
			0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,0,0,-1,-1 ,
			0,0,0,0,0,0,0,0,0,0,0,1,1,0,0,-1,-1,0,0 ,
			0,0,0,0,0,0,0,-1,1,-1,1,0,0,0,0,0,0,0,0 ,
			0,0,0,0,0,0,0,0,0,0,0,1,-1,0,0,1,-1,0,0 ,
			0,0,0,0,0,0,0,0,0,0,0,0,0,-1,1,0,0,-1,1 ,
			0,0,0,0,0,0,0,1,1,1,1,0,0,0,0,0,0,0,0 ,
			0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,0,0,1,1 ,
			0,0,0,0,0,0,0,0,0,0,0,1,1,0,0,1,1,0,0
		} ;

		static const Real invM[19*19]={
			1,0,0,0,-1,-1,-1,0,0,0,0,0,0,0,0,0,1,1,1 ,
			0,0,1.0/2.0,0,0,1.0/2.0,0,0,0,0,-1.0/2.0,0,0,0,-1.0/2.0,0,-1.0/2.0,0,-1.0/2.0 ,
			0,0,-1.0/2.0,0,0,1.0/2.0,0,0,0,0,1.0/2.0,0,0,0,1.0/2.0,0,-1.0/2.0,0,-1.0/2.0 ,
			0,-1.0/2.0,0,0,1.0/2.0,0,0,0,0,0,0,0,0,1.0/2.0,0,1.0/2.0,-1.0/2.0,-1.0/2.0,0 ,
			0,1.0/2.0,0,0,1.0/2.0,0,0,0,0,0,0,0,0,-1.0/2.0,0,-1.0/2.0,-1.0/2.0,-1.0/2.0,0 ,
			0,0,0,1.0/2.0,0,0,1.0/2.0,0,0,0,0,-1.0/2.0,-1.0/2.0,0,0,0,0,-1.0/2.0,-1.0/2.0 ,
			0,0,0,-1.0/2.0,0,0,1.0/2.0,0,0,0,0,1.0/2.0,1.0/2.0,0,0,0,0,-1.0/2.0,-1.0/2.0 ,
			0,0,0,0,0,0,0,-1.0/4.0,0,0,1.0/4.0,0,0,-1.0/4.0,0,0,1.0/4.0,0,0 ,
			0,0,0,0,0,0,0,1.0/4.0,0,0,1.0/4.0,0,0,1.0/4.0,0,0,1.0/4.0,0,0 ,
			0,0,0,0,0,0,0,1.0/4.0,0,0,-1.0/4.0,0,0,-1.0/4.0,0,0,1.0/4.0,0,0 ,
			0,0,0,0,0,0,0,-1.0/4.0,0,0,-1.0/4.0,0,0,1.0/4.0,0,0,1.0/4.0,0,0 ,
			0,0,0,0,0,0,0,0,0,1.0/4.0,0,0,1.0/4.0,0,1.0/4.0,0,0,0,1.0/4.0 ,
			0,0,0,0,0,0,0,0,0,-1.0/4.0,0,0,1.0/4.0,0,-1.0/4.0,0,0,0,1.0/4.0 ,
			0,0,0,0,0,0,0,0,-1.0/4.0,0,0,1.0/4.0,0,0,0,-1.0/4.0,0,1.0/4.0,0 ,
			0,0,0,0,0,0,0,0,1.0/4.0,0,0,1.0/4.0,0,0,0,1.0/4.0,0,1.0/4.0,0 ,
			0,0,0,0,0,0,0,0,0,-1.0/4.0,0,0,-1.0/4.0,0,1.0/4.0,0,0,0,1.0/4.0 ,
			0,0,0,0,0,0,0,0,0,1.0/4.0,0,0,-1.0/4.0,0,-1.0/4.0,0,0,0,1.0/4.0 ,
			0,0,0,0,0,0,0,0,1.0/4.0,0,0,-1.0/4.0,0,0,0,-1.0/4.0,0,1.0/4.0,0 ,
			0,0,0,0,0,0,0,0,-1.0/4.0,0,0,-1.0/4.0,0,0,0,1.0/4.0,0,1.0/4.0,0
		};
	}

}


namespace walberla
{

	 //**Struct Cell***************************************************************************
	/*!\brief Stores three unsigned ints for the position of the cell
	 */
	struct Cell
	{
		Uint x;
		Uint y;
		Uint z;
		#ifdef USE_STL_GE
#ifdef DEBUG_FORCE_SWEEP_GE
		Real Force_term[3] ;
#endif
		Cell(const Uint& inX,const Uint& inY,const Uint& inZ)
		  : x(inX), y(inY), z(inZ)
#ifdef DEBUG_FORCE_SWEEP_GE
		{Force_term[0] = Force_term[1] =  Force_term[2] =0.0;  }
#else
		  {}
#endif


		// =============================================================
		//
		// =============================================================
		inline bool operator==(Cell const& other) const
		{
		  return (x == other.x  &&
			y == other.y  &&
			z == other.z    ) ;
		}


		// =============================================================
		//
		// =============================================================
		inline bool operator!=(Cell const& Q) const{
			return ( (x!=Q.x) || (y!=Q.y) || (z!=Q.z) ) ;
		}


		// =============================================================
		//
		// =============================================================
		inline bool operator < (Cell const& p) const
		{
			if ( y == p.y )
				if ( x == p.x )
				return (x < p.x);
				else
				return (z < p.z);
			else
			return (y < p.y);
		}

		// =============================================================
		//
		// =============================================================
		//! greater than <=> it is not less than or equal
		inline const bool operator > (Cell const& p) const {
			return !( (*this) <  p ||
				(*this) == p );
		}


		// =============================================================
		//
		// =============================================================
		inline const bool operator <= (Cell const& p) const {
			return ((*this) < p || (*this == p));
		}

		// =============================================================
		//
		//!  greater-than or equal <=> it is NOT less than the other
		// =============================================================
		inline const bool operator >= (Cell const& p) const {
			return !((*this) < p);
		}
		// =============================================================

#ifdef DEBUG_FORCE_SWEEP_GE
		// get/set the force term, used only for debugging
		inline Real& operator [](const int& index) {
			assert (index >=0 && index <=3);
			return Force_term [index];
		}

		inline const Real& operator [](const int& index) const{
			assert ( index >=0 && index <= 3);
			return Force_term [index];
		}
#endif
		#else
		Cell(const Uint& inX, const Uint& inY,const Uint& inZ)
		  : x(inX), y(inY), z(inZ)
		 {}
		#endif
		Cell(){};
	};

	



	
	//**Struct BoundaryCell***************************************************************************
	/*!\brief Stores three unsigned ints for the position of the cell and the
	 * \brief flag value of the cell.
	 */
	struct BoundaryCell{
		BoundaryCell(){}
		BoundaryCell(const Uint& X,const Uint& Y,const Uint& Z,Flag s):x(X),y(Y),z(Z),state(s){}
		Uint x;
		Uint y;
		Uint z;
		Flag state;
	};


   
	#ifdef USE_STL_GE
	struct BzCell : public Cell
	{

		explicit BzCell(const Uint& x_,const Uint& y_,const Uint& z_,
			const Uint& dir,const float& value ,
			const bool&fixed_ = false )
		  : Cell (x_,y_,z_)
		{
			for (int i=0 ; i <18 ; i++ )
				q_distances[i] = 0.0f ;
			q_distances[dir]= value;
			m_fixed		= fixed_ ;
		}

		// access ref
		inline const float& q(const comm::CommDir& dir)const
		{
			assert ( dir >= 0 ) ;
			assert ( dir < 18 ) ;
			return q_distances[dir];
		}
		//modifier access 
		inline float& q(const comm::CommDir& dir)
		{
			assert ( dir > 0 ) ;
			assert ( dir < 18 ) ;
			return q_distances[dir];
		}
		
		inline const float& operator[](const comm::CommDir& dir)const
		{
			assert ( dir >= 0 ) ;
			assert ( dir < 18 ) ;
			return q_distances[dir];
		}
		//modifier access 
		inline float& operator[](const comm::CommDir& dir)
		{
			assert ( dir > 0 ) ;
			assert ( dir < 18 ) ;
			return q_distances[dir];
		}
	  private:
		float q_distances[18];
		float m_fixed ;
	};
	#endif



	#ifdef USE_STL_GE
	inline std::ostream& operator<<(std::ostream& out,const Cell& my_cell)
	{
		out << "Cell<x,,y,z> : <"<<my_cell.x<<","<<my_cell.y<<","<<my_cell.z<<">\n";
		return out ;
	}

	inline std::ostream& operator<<(std::ostream& out,const BzCell& my_cell)
	{
		out << "Cell<x,,y,z> : <"<<my_cell.x<<","<<my_cell.y<<","<<my_cell.z<<">\n";
		std::cout << " q [  N  ] : " << my_cell [  comm::N  ] <<"\n"; 
		std::cout << " q [  S  ] : " << my_cell [  comm::S  ] <<"\n"; 
		std::cout << " q [  W  ] : " << my_cell [  comm::W  ] <<"\n";  
		std::cout << " q [  E  ] : " << my_cell [  comm::E  ] <<"\n"; 
		std::cout << " q [  T  ] : " << my_cell [  comm::T  ] <<"\n";  
		std::cout << " q [  B  ] : " << my_cell [  comm::B  ] <<"\n";
		std::cout << " q [  NW ] : " << my_cell [  comm::NW ] <<"\n";   
		std::cout << " q [  NE ] : " << my_cell [  comm::NE ] <<"\n";  
		std::cout << " q [  SW ] : " << my_cell [  comm::SW ] <<"\n"; 
		std::cout << " q [  SE ] : " << my_cell [  comm::SE ] <<"\n";  
		std::cout << " q [  TN ] : " << my_cell [  comm::TN ] <<"\n";
		std::cout << " q [  TS ] : " << my_cell [  comm::TS ] <<"\n";   
		std::cout << " q [  TW ] : " << my_cell [  comm::TW ] <<"\n";      
		std::cout << " q [  TE ] : " << my_cell [  comm::TE ] <<"\n";    
		std::cout << " q [  BN ] : " << my_cell [  comm::BN ] <<"\n"; 
		std::cout << " q [  BS ] : " << my_cell [  comm::BS ] <<"\n";     
		std::cout << " q [  BW ] : " << my_cell [  comm::BW ] <<"\n";     
		std::cout << " q [  BE ] : " << my_cell [  comm::BE ] <<"\n";   
		return out ;   
	      }
	   #endif

}





#define LOG_E(msg){std::stringstream ss;ss<<msg;std::cerr<<"ERROR: "<<msg<<std::endl;Logging::Instance().LogError(ss.str());}
#define THROW_LOG_E(msg){std::stringstream ss;ss<<msg;std::cerr<<msg<<std::endl;Logging::Instance().LogError(ss.str());throw std::runtime_error(ss.str()+"\n");}

//=================================================================================================
//
//  DEBUG MACROS
//
//=================================================================================================

#ifndef NDEBUG
#define _DEBUG 1
#else
#define _DEBUG 0
#endif

#if _DEBUG
#define CHECK(a,x,y,z,file,line) CheckValue((a),(x),(y),(z),(file),(line))
#else
#define CHECK(a,x,y,z,file,line) if(0) CheckValue((a),(x),(y),(z),(file),(line))
#endif

#ifdef WALBERLA_USE_MPI
#if _DEBUG
#define ASSERT(expr, msg) if(!(expr)){LOG_ERROR("ASSERT! "+std::string(msg));MPI_Abort(MPI_COMM_WORLD,0);}
#else
#define ASSERT(expr, msg) if(false && !(expr)){LOG_ERROR("ASSERT! "+std::string(msg));MPI_Abort(MPI_COMM_WORLD,0);}
#endif
#else
#if _DEBUG
#define ASSERT(expr, msg) if(!(expr)){LOG_ERROR("ASSERT! "+std::string(msg));assert(expr);}
#else
#define ASSERT(expr, msg) if(false && !(expr)){LOG_ERROR("ASSERT! "+std::string(msg));assert(expr);}
#endif
#endif


#if TIMING
#define START_TIMING( var ) Timing::Start( (var) )
#else
#define START_TIMING( var ) if(0) Timing::Start( (var) )
#endif

#if TIMING
#define STOP_TIMING( var ) Timing::End( (var) )
#else
#define STOP_TIMING( var ) if(0) Timing::End( (var) )
#endif

#if BENCHMARKING
#define START_MFLUPS( var ) Timing::Start( (var) )
#else
#define START_MFLUPS( var ) if(0) Timing::Start( (var) )
#endif

#if BENCHMARKING
#define STOP_MFLUPS( var ) Timing::End( (var) )
#else
#define STOP_MFLUPS( var ) if(0) Timing::End( (var) )
#endif

#if (_DEBUG||LOGGING_MONITOR)
#define GET_FLAG(i,j,k) MonitoredGet(i,j,k,__FILE__,__LINE__)
#else
#define GET_FLAG(i,j,k) Get(i,j,k)
#endif


#if (_DEBUG||LOGGING_MONITOR)
#define GET_PATCH3(i,j,k) GetMonitoredPatch(i,j,k,__FILE__,__LINE__)
#else
#define GET_PATCH3(i,j,k) GetPatch(i,j,k)
#endif

#if (_DEBUG||LOGGING_MONITOR)
#define GET_PATCH1(i) GetMonitoredPatch(i,__FILE__,__LINE__)
#else
#define GET_PATCH1(i) GetPatch(i)
#endif

#if (_DEBUG||LOGGING_MONITOR)
#define GET_PATCH() GetMonitoredPatch(__FILE__,__LINE__)
#else
#define GET_PATCH() GetPatch()
#endif

#if (_DEBUG||LOGGING_MONITOR)
#define SET_FLAG(i,j,k,val) MonitoredSet(i,j,k,val,__FILE__,__LINE__)
#else
#define SET_FLAG(i,j,k,val) Set(i,j,k,val)
#endif

#if (_DEBUG||LOGGING_MONITOR)
#define REMOVE_FLAG(i,j,k,val) MonitoredRemove(i,j,k,val,__FILE__,__LINE__)
#else
#define REMOVE_FLAG(i,j,k,val) Remove(i,j,k,val)
#endif

#if (_DEBUG||LOGGING_MONITOR)
#define IS_FLAG(i,j,k,val) MonitoredIs(i,j,k,val,__FILE__,__LINE__)
#else
#define IS_FLAG(i,j,k,val) Is(i,j,k,val)
#endif


#if (_DEBUG||LOGGING_MONITOR)
#define ADD_FLAG(i,j,k,val) MonitoredAdd(i,j,k,val,__FILE__,__LINE__)
#else
#define ADD_FLAG(i,j,k,val) Add(i,j,k,val)
#endif

#if (_DEBUG||LOGGING_MONITOR)
#define GET_PDF(x,y,z,l) MonitoredGet((x),(y),(z),(l),__FILE__,__LINE__)
#else
#define GET_PDF(x,y,z,l) Get((x),(y),(z),(l))
#endif

#if (_DEBUG||LOGGING_MONITOR)
#define SET_PDF(x,y,z,l,value) MonitoredSet((x),(y),(z),(l),(value),__FILE__,__LINE__)
#else
#define SET_PDF(x,y,z,l,value) Set((x),(y),(z),(l),(value))
#endif

#if (_DEBUG||LOGGING_MONITOR)
#define ADD_PDF(x,y,z,l,value) MonitoredAdd((x),(y),(z),(l),(value),__FILE__,__LINE__)
#else
#define ADD_PDF(x,y,z,l,value) Add((x),(y),(z),(l),(value))
#endif

#if (_DEBUG||LOGGING_MONITOR)
#define GET_VEL(x,y,z,l) MonitoredGet((x),(y),(z),(l),__FILE__,__LINE__)
#else
#define GET_VEL(x,y,z,l) Get((x),(y),(z),(l))
#endif

#if (_DEBUG||LOGGING_MONITOR)
#define GET_VEL_VEC(x,y,z,d) MonitoredGet((x),(y),(z),(d),__FILE__,__LINE__)
#else
#define GET_VEL_VEC(x,y,z,d) Get((x),(y),(z),(d))
#endif

#if (_DEBUG||LOGGING_MONITOR)
#define SET_VEL_VEC(x,y,z,value) MonitoredSet((x),(y),(z),(value),__FILE__,__LINE__)
#else
#define SET_VEL_VEC(x,y,z,value) Set((x),(y),(z),(value))
#endif

#if (_DEBUG||LOGGING_MONITOR)
#define SET_VEL(x,y,z,v_x,v_y,v_z) MonitoredSet((x),(y),(z),(v_x),(v_y),(v_z),__FILE__,__LINE__)
#else
#define SET_VEL(x,y,z,v_x,v_y,v_z) Set((x),(y),(z),(v_x),(v_y),(v_z))
#endif

#if (_DEBUG||LOGGING_MONITOR)
#define GET_DENS(x,y,z) MonitoredGet((x),(y),(z),__FILE__,__LINE__)
#else
#define GET_DENS(x,y,z) Get((x),(y),(z))
#endif

#if (_DEBUG||LOGGING_MONITOR)
#define SET_DENS(x,y,z,value) MonitoredSet((x),(y),(z),(value),__FILE__,__LINE__)
#else
#define SET_DENS(x,y,z,value) Set((x),(y),(z),(value))
#endif

#if (_DEBUG||LOGGING_MONITOR)
#define GET_SCALAR(x,y,z) MonitoredGet((x),(y),(z),__FILE__,__LINE__)
#else
#define GET_SCALAR(x,y,z) Get((x),(y),(z))
#endif

#if (_DEBUG||LOGGING_MONITOR)
#define SET_SCALAR(x,y,z,value) MonitoredSet((x),(y),(z),(value),__FILE__,__LINE__)
#else
#define SET_SCALAR(x,y,z,value) Set((x),(y),(z),(value))
#endif

#if (_DEBUG||LOGGING_MONITOR)
#define GET_VECTOR(x,y,z,l) MonitoredGet((x),(y),(z),(l),__FILE__,__LINE__)
#else
#define GET_VECTOR(x,y,z,l) Get((x),(y),(z),(l))
#endif

#if (_DEBUG||LOGGING_MONITOR)
#define GET_VECTOR_VEC(x,y,z,d) MonitoredGet((x),(y),(z),(d),__FILE__,__LINE__)
#else
#define GET_VECTOR_VEC(x,y,z,d) Get((x),(y),(z),(d))
#endif

#if (_DEBUG||LOGGING_MONITOR)
#define SET_VECTOR_VEC(x,y,z,value) MonitoredSet((x),(y),(z),(value),__FILE__,__LINE__)
#else
#define SET_VECTOR_VEC(x,y,z,value) Set((x),(y),(z),(value)) 
#endif

#if (_DEBUG||LOGGING_MONITOR)
#define SET_VECTOR(x,y,z,v_x,v_y,v_z) MonitoredSet((x),(y),(z),(v_x),(v_y),(v_z),__FILE__,__LINE__)
#else
#define SET_VECTOR(x,y,z,v_x,v_y,v_z) Set((x),(y),(z),(v_x),(v_y),(v_z))
#endif

#if _DEBUG
#define ACCESS_PERM(Field,Section,Read,Write) Field.ConfigureAccessPermissions(Section,Read,Write);
#else
#define ACCESS_PERM(Field,Section,Read,Write) if(0) Field.ConfigureAccessPermissions(Section,Read,Write);
#endif



//=================================================================================================
//
//  LOGGING MACROS
//
//=================================================================================================

#define LOG_ERROR(msg) {std::stringstream ss;ss<<msg;std::cerr<<walberla_LIGHTRED<<"ERROR: "<<ss.str()<<walberla_OLDCOLOR<<std::endl;Logging::Instance().LogError(ss.str());}
#define LOG_INFO(msg) {std::stringstream ss;ss<<msg;std::cerr<<"INFO: "<<ss.str()<<std::endl;Logging::Instance().LogInfo(ss.str());}
#define THROW_LOG_ERROR(msg){LOG_ERROR(msg);throw std::runtime_error(Convert<std::string>(msg)+"\n");}

#ifndef LOGGING
#define LOGGING 0
#endif

#ifndef NWARNING
#define LOG_WARNING(msg) {std::cerr<<walberla_BROWN<<" WARNING: "<<msg<<walberla_OLDCOLOR<<std::endl;Logging::Instance().LogWarning(msg);}
#else
#define LOG_WARNING(msg) if(0){std::cerr<<walberla_BROWN<<" WARNING: "<<msg<<walberla_OLDCOLOR<<std::endl;Logging::Instance().LogWarning(msg);}
#endif

#if LOGGING >= 1
#define LOG_INFO1(msg) Logging::Instance().LogInfo(msg)
#else
#define LOG_INFO1(msg) if(0)Logging::Instance().LogInfo(msg)
#endif

#if LOGGING >= 2
#define LOG_INFO2(msg) Logging::Instance().LogInfo(msg)
#else
#define LOG_INFO2(msg) if(0)Logging::Instance().LogInfo(msg)
#endif

#if LOGGING >= 3
#define LOG_INFO3(msg) Logging::Instance().LogInfo(msg)
#else
#define LOG_INFO3(msg) if(0)Logging::Instance().LogInfo(msg)
#endif

#if LOGGING >= 4
#define LOG_INFO4(msg) Logging::Instance().LogInfo(msg)
#else
#define LOG_INFO4(msg) if(0)Logging::Instance().LogInfo(msg)
#endif

#if _DEBUG
#define LOG_CHECK(expr, msg) if(!(expr)){LOG_WARNING(msg);}
#else
#define LOG_CHECK(expr, msg) if(false && !(expr)){LOG_WARNING(msg);}
#endif

#ifdef LOGGING_MONITOR
#define LOG_MONITOR(msg) Logging::Instance().LogInfo(msg)
#else
#define LOG_MONITOR(msg) if(0)Logging::Instance().LogInfo(msg)
#endif

#ifdef LOGGING_MONITOR
#define IF_MONITOR_CELL(patch,x,y,z) if (patch.IsMonitoredCell(x,y,z))
#else
#define IF_MONITOR_CELL(patch,x,y,z) if (0)
#endif


//=================================================================================================
//
//  OTHER USEFUL MACROS
//
//=================================================================================================

#define STR(v)Convert<std::string>(v)
#define STRB(v)Convert<std::string>(v)+" "
#define STRPOS(patch,x,y,z) std::string("(x,y,z)=(")+STR(x)+","+STR(y)+","+STR(z)+")/["+STR(x+patch.GetXStart())+","+STR(y+patch.GetYStart())+","+STR(z+patch.GetZStart())+std::string("]")

#define IFDEF(ARG,TRU,FA) if (ARG){TRU;}else{FA;};

#define ByteSwap_PV(x) ByteSwap((unsigned char *) &x,sizeof(x))

#define SQR(a) ((a)*(a))
#ifndef WIN32
#define walberla_RED			  "\033[0;31m"
#define walberla_LIGHTRED		"\033[1;31m"
#define walberla_BROWN			"\033[0;33m"
#define walberla_YELLOW		  "\033[1;33m"
#define walberla_OLDCOLOR		"\033[0m"
#else
#define walberla_RED			  ""
#define walberla_LIGHTRED		""
#define walberla_BROWN			""
#define walberla_YELLOW		  ""
#define walberla_OLDCOLOR		""
#endif




//=================================================================================================
//
//  COMPILE-TIME ASSERTION
//
//=================================================================================================

//*************************************************************************************************
/*! \cond WALBERLA_INTERNAL */
/*!\brief Compile-time assertion
// \ingroup core
//
// Helper template class for the compile-time assertion. Based on the compile-time constant
// expression used for the template instantiation, either the undefined basic template or the
// specialization is selected. If the undefined basic template is selected, a compilation
// error is created.
 */
template< bool > struct WALBERLA_COMPILE_TIME_ASSERTION_FAILED;
template<> struct WALBERLA_COMPILE_TIME_ASSERTION_FAILED<true> {};
/*! \endcond */
//*************************************************************************************************


//! Compile-time assertion macro
/*! In case of an invalid compile-time expression, an compilation error is created. */
#define STATIC_ASSERT(expr) { WALBERLA_COMPILE_TIME_ASSERTION_FAILED< (expr) != 0 >(); }

namespace walberla{
	//*************************************************************************************************
	/*!\fn inline bool ExtractParameterCoordinates(std::string parameter, std::string &first, std::string &second, std::string &third)
	// \brief Parses \p parameter which is in form of <x,y,z> and splits it into the three parts separated by
	//		  the commas. x is written to \p first, y to \p second, z to \p third.
	// \param parameter The string to be parsed.
	// \param first The container for the first part of \p parameter.
	// \param second The container for the second part of \p parameter.
	// \param third The container for the third part of \p parameter.
	// \return True if conversion is valid, false if conversion failed (i.e. due to wrong parameter format)
	//
	 */
	inline bool ExtractParameterCoordinates(std::string parameter, std::string &first, std::string &second, std::string &third) {
		std::string::size_type comma1, comma2;
		if (parameter[0]!='<') {
			LOG_ERROR("!Domain.h::ExtractParameterCoordinates: Malformed parameter '" + parameter + "'.\n");
			return false;
		}
		if (parameter[parameter.size()-1]!='>') {
			LOG_ERROR("!!Domain.h::ExtractParameterCoordinates: Malformed parameter '" + parameter + "'.\n");
			return false;
		}
		parameter = parameter.substr(1,parameter.size()-2);
		comma1 = parameter.find(",");
		if (comma1 == std::string::npos) {
			LOG_ERROR("!!!Domain.h::ExtractParameterCoordinates: Malformed parameter '" + parameter + "'.\n");
			return false;
		}
		comma2 = parameter.rfind(",");
		if (comma2 == std::string::npos) {
			LOG_ERROR("!!!!Domain.h::ExtractParameterCoordinates: Malformed parameter '" + parameter + "'.\n");
			return false;
		}
		first = parameter.substr(0,comma1);
		second = parameter.substr(comma1+1,comma2-comma1-1);
		third = parameter.substr(comma2+1);
		return true;
	}


#ifndef NO_PE
	/*!\fn inline bool Domain::GetCoordsFromString(std::string parameter, pe::Vec3& coords)
	// \brief Parses \p parameter which is in form of <x,y,z> and writes it into a three dimensional
	//		  vector \p coords.
	// \param parameter The string to be parsed.
	// \param coords The vector that the data shall be written to
	// \return True if conversion is valid, false if conversion failed (i.e. due to wrong parameter format)
	//
	 */
	inline bool GetCoordsFromString(std::string parameter, pe::Vec3& coords)
	{
		bool ret;
		std::string s1,s2,s3;
		std::stringstream s;
		ret = ExtractParameterCoordinates(parameter,s1,s2,s3);
		if (!ret)
		{
			return ret;
		}
		s.str(s1);
		s.clear();
		if (s >> coords[0]){}
		else{
			return false;
		}
		s.str(s2);
		s.clear();
		if (s >> coords[1]){}
		else{
			return false;
		}
		s.str(s3);
		s.clear();
		if (s >> coords[2]){}
		else{
			return false;
		}

		return true;
	}
#endif
}


	#ifdef USE_STL_GE

	// =======================================================================================
	// needed for hash table container
	// Add by Cherif.Mihoubi
	// =======================================================================================
	namespace std
	{
		namespace tr1
		{
			// normal cell type
			template<>
			struct hash<class walberla::Cell> {
				inline std::size_t operator()( walberla::Cell const &other)const
				{
					//std::cout<<"Hash function is called\n" ;
					return  (other.x * LARGE_PRIME_A  ^
						other.y * LARGE_PRIME_B  ^
						other.z * LARGE_PRIME_C    ) % Hsize ;
				}
			};

			// second order bcs-cell type
			template<>
			struct hash<class walberla::BzCell> {
				inline std::size_t operator()( walberla::BzCell const &other)const
				{
					//std::cout<<"Hash function is called\n" ;
					return  (other.x * LARGE_PRIME_A  ^
						other.y * LARGE_PRIME_B  ^
						other.z * LARGE_PRIME_C    ) % Hsize ;
				}
			};


			// hash PatchUID
			template<>
			struct hash<class walberla::PatchUID> {
				inline std::size_t operator()( walberla::PatchUID const &other)const
				{
					//std::cout<<"Hash function is called\n" ;
					return  (other.xIndex * LARGE_PRIME_A  ^
						other.yIndex * LARGE_PRIME_B  ^
						other.zIndex * LARGE_PRIME_C    ) % Hsize ;
				}
			};
		}
	}
	// =======================================================================================
	#endif


#endif
