#include <iostream>
#include <list>
// =============================================================================
/*!
//  \brief Nested class technique can be used to implemete nice c++ iterator
//  \brief (const_iterator) as its explained by this example.
// \author Cherif.Mihoubi
*/
// =============================================================================
class X
{
  private:
    std::list<int> _list;
  public :
    class iterator ;
    class const_iterator ;
  public:
    // =========================================================================
    // public member functions 
    // =========================================================================
    inline iterator begin();
    inline void push_back(const int &);//Inserts element after the last  
    
  public:
	  // ===================================================================
	  // nested class definition 
	  // ===================================================================
	  class iterator
	  {
	    public:
		// =============================================================
		// Constructor 
		// =============================================================
		iterator(const std::list<int>::iterator& _i   =std::list<int>::iterator(),
			 const std::list<int>::iterator& _last=std::list<int>::iterator()
			) : i(_i), last(_last)
			{}

		// =============================================================
		int& operator* () const{ return *i; }	
		// =============================================================
	    private:
		std::list<int>::iterator i   ;
		std::list<int>::iterator last;
	  };
	  // ===================================================================
  private :
	int m_num ;
};



inline X::iterator X::begin()
{
  return iterator (_list.begin(), _list.end() );
}


// TODO return std::size_t = size()-1
inline void X::push_back(const int & in)//Inserts element after the last 
{
  _list.push_back (in);
}

// =============================================================================
// main function
// =============================================================================
int main ()
{
    X *m_x =  new X( ) ; // container 
    X::iterator it ;	 // container iterator, you can also extend by 
			 // const_iterator 
     

    m_x -> push_back (2)   ; 
    m_x -> push_back (20)  ;
    m_x -> push_back (200) ;
    
    it = m_x -> begin() ;
    

    std::cout<< *it << std::endl ;
    
    
    return (0) ;
}
// =============================================================================
