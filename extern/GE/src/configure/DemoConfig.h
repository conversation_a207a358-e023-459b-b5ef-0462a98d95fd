// =============================================================================
/*!
//  \file    DemoConfig.h
//  \brief   Header/Source file for the DemoConfig class
//  \author  cherif.Mihoubi
//  \email  <EMAIL>
//
//  Created on 2010-10-31, 2010 , 20:24:11
//
*/
// =============================================================================

#ifndef DemoConfig_HH
#define DemoConfig_HH

// =============================================================================
/*
//	This program is free software; you can redistribute it and/or
//	modify it under the terms of the GNU General Public License
//	as published by the Free Software Foundation; either version 2
//	of the License, or (at your option) any later version.
//
//	This program is distributed in the hope that it will be useful,
//	but WITHOUT ANY WARRANTY; without even the implied warranty of
//	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
//	GNU General Public License for more details.
//
//	You should have received a copy of the GNU General Public License
//	along with this program; if not, write to the Free Software
//	Foundation, Inc., 51 Franklin Street, Fifth Floor,
//	Boston, MA  02110-1301, USA.
//
//	
//	Copyright (C) 2010, Cherif <<EMAIL>>
*/
// =============================================================================



// =============================================================================
// includes
// =============================================================================
#include <iostream>
#include "../accel/World.h" 
#include "../accel/Sector.h"
#include "../accel/Sphere.h"  //#include "../accel/TriShapeBase.h"
#include "../util/DataLayout.h"
// vconfiguration
#include "TriObjectsConf.h"
#include "ObjectsConf.h"
#include "PolygonsConf.h"
// =============================================================================



// =============================================================================
// Details:
// =============================================================================
/*
 -
 |	VertexType    :  Point3D<float | double>
 |	TriangleType  :  VertexType
 |	ListType      :  SeqencialContainer | associatives ...//WHICH CONTAINER,
 |			 they can differt from each other:
 |	                 example -hash_table for VerticesType, and std::vector
 |	                 for TriangleType important they should share the same 
 |	                 interface functions, like 
 |	                 Add(ElementType) , ... Remove, find ..
 |
 |	PolgoneList   :  ListType && TriangleType 
 |	ObjectList    :  ListType && PolgoneList
 |
 |	Sector        :  ObjectList (maybe also && PolygonsList) && SectorsList
 |	World         :  SectorsList , maybe add to wold (ObjectList)
 |
 ._____ config
 
 
*/
// =============================================================================

      // template<typenam ElementType>
      // struct SampleConfig
      // {
      //       // =====================================
      //       // ==  Basic configuration technique  ==
      //       // =====================================
      //       //
      //       // 1. Polygone|obejct type (object,triangle) <=> FinalObjectType
      //       //   	      <=> Copier  destructor , and checker 
      //       //   	      <=> logger
      //       //   	      ...
      //       //              TODO use satandart configure
      //       // 2. (objects/polygone)-List type	  <=> FinalPolgoneList
      //       // 					  <=> FinalObjectListType
      //       //	        		
      //       //
      //       // 3. Sector,
      //       // 	 LengthType ;  // int , short or long
      //       //        dimensions ;  // SectorXSize, SectorYSize, SectorZSize
      //       //
      //       // 4. World 
      // } ;




// =============================================================================
// some configure sample
// =============================================================================

struct DefaultConfig 
{
	 //1. config first the basic type
	 typedef PolygonsConf<int> FinalPolygonsListType  ;
	 //  TODO rename PolygonType to PolygonsType in Sector.h class
	 typedef FinalPolygonsListType::polygonsType 	 PolygonType ;//triangle_type
	 typedef FinalPolygonsListType::polygonsList 	 PolygonList ;//triangles-container Type 

         //typedef ObjectsConf<animal>		FinalObjectListType ; // ObjectBase *
         //typedef FinalObjectListType::objectType 	ObjectType ;
         //typedef FinalObjectListType::objectsList	ObjectList ;

	 // TODO rename FinalObjectListType to SpheresObjectListType
	 typedef TriObjectsConf< TriShapeBase<Sphere> >  FinalObjectListType;//ObjectBase *
	 typedef FinalObjectListType::objectType 	 ObjectType ;
	 typedef FinalObjectListType::objectsList	 ObjectList ;

	 typedef int LengthType ; // int , short or long

	 // 3. Sectors size ,
	 enum { SectorXSize = 4 , SectorYSize = 4 ,SectorZSize = 4 }; // dimension

	 typedef Sector<DefaultConfig>			SectorType;

	 // 4. World
	 typedef DataLayout<SectorType,1>		GridType ;
	 typedef World<DefaultConfig>::FinalWorld	WordType ;
};


// ===========================================================================
  
#endif // DemoConfig_HH
