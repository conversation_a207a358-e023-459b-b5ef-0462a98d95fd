#include <iostream>
#include "HashTable.h"


class CPoint{
	Point (int a, int b):x(a),y(b){}
private:
	int x, y;
};

// =============================================================================
/*			main function 				       */
// =============================================================================

int main ()
{
    CHashtable<std::string, CPoint*>      ht_Points (NULL) ;
    
    ht_Points.Add ("Point1", new CPoint(10, 20) );
    ht_Points.Add ("Point2", new CPoint(20, 30) );
    ht_Points.Add ("Point3", new CPoint(30, 40) );

    CPoint* p1 = ht_Points.GetValue("Punkt1");
    CPoint* p2 = ht_Points.GetValue("Punkt2");
    CPoint* p3 = ht_Points.GetValue("Punkt3");

//     ASSERTMSG(ht_Points.GetValue("Punkt4") == NULL);
//     CHashtable<int, CRect> ht_Rect(CRect(-1,-1,-1,-1));
//     ht_Rect.Add(1, CRect(p1->x, p1->y, p1->x + 10, p1->y + 10));
//     ht_Rect.Add(2, CRect(p2->x, p2->y, p2->x + 10, p2->y + 10));

//     ASSERTMSG(ht_Rect.GetValue(1).left == p1->x);
//     ASSERTMSG(ht_Rect.GetValue(2).right == p2->x + 10);
//     ASSERTMSG(
//         ht_Rect.GetValue(3).left == -1  &&
//         ht_Rect.GetValue(3).top == -1   &&
//         ht_Rect.GetValue(3).right == -1 &&
//         ht_Rect.GetValue(3).bottom == -1
//     );

	delete p1;   delete p2;  delete p3;
	
	return 0;
}



