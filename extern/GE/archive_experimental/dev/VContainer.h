//==============================================================================
/*!
//  \file VContainer.h
//  \brief Header/Source file for the VContainer class
//  \author Cherif <PERSON>
*/
//==============================================================================

#ifndef VCONTAINER_HH
#define VCONTAINER_HH

#include <iostream>


//==============================================================================
/*!
//  \namespace ge
//  \class VContainer<Configure,Compare>
//  \brief container based on vector 
//  \param Configure configuration template parameter 
//  \param Compare by default = std::less<typename Configure::ElementType>
*/
//==============================================================================

/*!
// =============================================================================
// example of configuration 
// =============================================================================

struct ConfigSample{
    typedef int   ElementType ;
    typedef const ElementType         ElementArg ;  
    typedef EmptyCopier<ElementArg>   Copier;
    typedef std::less  <ElementType>  Compare ; 
    typedef std::vector<ElementType>  ContainerType ;
};


namespace ge 
{
	template< class Configure,
		  class Compare = std::less<typename Configure::ElementType> >
class VContainer : public Configure::ContainerType
{
   public: // types
	// import
	typedef typename Configure::ElementType ElementType ;
	typedef typename Configure::ElementArg  ElementArg  ;
	typedef typename Configure::Copier      Copier ;
	typedef typename Configure::ContainerType       ContainerType ;
	// additional 	
  	typedef typename ContainerType::size_type    	size_type ;
	typedef typename ContainerType::iterator        iterator  ;
	typedef typename ContainerType::const_iterator  const_iterator;
	typedef typename ContainerType::difference_type difference_type;
	typedef typename ContainerType::reference       reference     ;
	typedef typename ContainerType::const_reference const_reference;
	
	
	//==============================================================
	//!constructors 
	VContainer() {}  // default
		
	VContainer(const size_type& size,
		   const ElementType& value= ElementType()  )
		  :ContainerType (size,value) {}
		
	VContainer(iterator first,iterator last)
		  :ContainerType (first,last){}

   public:
	inline void add( Point* c )          { push_back( c );    }
	inline void remove( iterator where ) { erase( where ); }
	
	inline iterator        begin()       { return begin() ;}
	inline const_iterator  begin() const { return begin() ;}
	inline iterator        end() 	   { return end() ;}
	inline const_iterator  end()   const { return end() ;}
	inline std::size_t     size() 	   { return size() ;}
      
	// element access
	      //!@note  don't throw any BoundsViolation Exception
	      T& operator[]( size_type index );
	const T& operator[]( size_type index ) const;// Returns a reference to the n'th element
	      //!@note  throw any BoundsViolation Exception if  index > size or negative
	      T& at( size_type index );
	const T& at( size_type index ) const;// Returns a reference to the n'th element
};


// =============================================================================
*/


//==============================================================================
// VContainer Class Definition
//==============================================================================
namespace ge 
{
	template< class Configure,
		  class Compare = std::less<typename Configure::ElementType> >
	class VContainer : public Configure::ContainerType
	{
	    public: 
		typedef typename Configure::ElementType         ElementType ;
		typedef typename Configure::ElementArg          ElementArg  ;
		typedef typename Configure::Copier              Copier ;
		typedef typename Configure::ContainerType       ContainerType ;

	    //typedef typename ContainerType::value_type      value_type;
		typedef typename ContainerType::size_type    	size_type ;
		typedef typename ContainerType::iterator        iterator  ;
		typedef typename ContainerType::const_iterator  const_iterator;
		typedef typename ContainerType::difference_type difference_type;
		typedef typename ContainerType::reference       reference     ;
		typedef typename ContainerType::const_reference const_reference;
		
		// Export Final type ->
		typedef  VContainer<Configure, Compare>         VContainerFinal;
		
		//==============================================================
		//!constructors 
		VContainer() {}  // default
		
		VContainer(const size_type& size,
			   const ElementType& value= ElementType()  )
			  :ContainerType (size,value) {}
		
		
		VContainer(iterator first,iterator last)
			  :ContainerType (first,last){}
			
			
		//! VContainer by default inherited all things from std::vector, 
		//! but on can implement save container (debug mode)
		//! to check and all these stuff
		//#ifdef DEBUG  
		inline reference operator [] ( difference_type index )
		{
			assert(index >= 0 && 
			       index < static_cast<difference_type >
			       (this->size() ) ) ; // end assert
			return ContainerType::operator[](index) ;
		}
			      
		inline const_reference operator [] (difference_type index) const
		{
			assert(index >= 0 &&
			       index < static_cast<difference_type >
			       (this->size() ) ) ; // end assert
			return ContainerType::operator[](index) ;
		}
		//#endif


         	public: // adaptor functions only for Add, remove and Find
		
		
		// TODO use BaseLogger class better    
		inline void Add  (const ElementType& e)
		{
			std::cout<< "e -> "
				 <<  e  
				 << " Copier::copy( e ) ) ; "
				 <<  *Copier::copy(e)
				 <<  std::endl;
			insert (* Copier::copy( e ) );//TODO its not Elegant
			//
			// push_back(* Copier::copy( e ) ) ;
		}
			
		//!@brief Adaptator function, search an element in the container
		//!@param ElementType element
		//!@return if find it, return index position int the container	
		inline int Find (const ElementType& element)
		{
		  return search (element);
		}
		
		//==============================================================
		//!@fn Remove
		//!@brief remove element or range of elements
		//!@param position Iterator pointing to a single element
		//!*   to be removed from the vector.
		inline iterator Remove (iterator where)
		{
		  return erase (where);
		}
			  
			  
		//==============================================================
		//!@fn Remove
		//!@brief remove element or range of elements  
		//!@param or range of elements ([first,last)).or
		//!*   a range of elements ([first,last)).
		
		inline iterator Remove (iterator first, iterator last )
		{
		  return erase (first,last);
		} 
		//==============================================================
			
	  private:
		inline void insert (const ElementType& element) ;// in sorted order
		inline int  search (const ElementType& element) ;// binary-search
		
		// TODO merge, not yet implemented
		//inline void merge(const VContainer<ElementType>& v ) ;
	};// end class VContainer





	//======================================================================
	// VContainer Class Methods
	//======================================================================
	
	
	
	//======================================================================
	//!@brief adds a new object at the end of the vector and reorganizes 
	//!@brief in ascending order. (--, - , + , ++ , +++ , ++++)
	//======================================================================
	template <class Configure, class Compare>
	inline void VContainer<Configure, Compare>::insert(const VContainer<Configure, Compare>::ElementType& element)
	{
		typename VContainer<Configure, Compare>::iterator tmp, position ;
		push_back  ( element ) ; // push to  the end 	
		position = (*this).end () ; 
		position -- ; // last position
		while (position-- >  (*this).begin() ) //TODO use Comapre
		{
			if (element < *position){
			  tmp = position ;
			  * (++ tmp) = *position ; 
			  * position = element ;
			}
			else break ;
		}
	}
	
	
	
	//======================================================================
	//!@brief binary search algo, complexity  o(log (time) )
	//!@brief Method search() seeks an object obj in the vector
	//! using the binary search algorithms:
	//! The object obj is first compared to the element in the middle of the 
	//! vector. If obj is smaller than the "middle" element, it must belong 
	//! to the left half or else to the right half of the vector. We repeat 
	//! this process comparing obj with the "middle" element in the section
	//! where it is known to be, repeatedly halving the size of the interval
	//! until the interval consists of a single point, which is where obj
	//! belongs.
	//! This algorithm has logarithmic time and thus is very fast.
	//======================================================================

	template <class Configure,class Compare>
	inline int VContainer<Configure,Compare>::search(const VContainer<Configure, Compare>::ElementType& obj)
	{
		int first = 0, last = (*this).end() - (*this).begin() - 1, mid;
		while( first < last )
		{
			mid = (first + last + 1)/ 2; // Search the left half,
			if( obj < (*this)[mid] )
				last = mid - 1;
			else
				first = mid;      // the right half.
		}

		if ( obj == (*this)[first] )  // found ?
			return first;         // yes
		else
			return (*this).size();// not found
	}//======================================================================
	
}// end namespace ge

#endif
