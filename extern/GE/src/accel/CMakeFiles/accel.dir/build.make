# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/thesis/walberla-e7fa8985e052/extern/GE

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/thesis/walberla-e7fa8985e052/extern/GE

# Include any dependencies generated for this target.
include src/accel/CMakeFiles/accel.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include src/accel/CMakeFiles/accel.dir/compiler_depend.make

# Include the progress variables for this target.
include src/accel/CMakeFiles/accel.dir/progress.make

# Include the compile flags for this target's objects.
include src/accel/CMakeFiles/accel.dir/flags.make

src/accel/CMakeFiles/accel.dir/Sector.cpp.o: src/accel/CMakeFiles/accel.dir/flags.make
src/accel/CMakeFiles/accel.dir/Sector.cpp.o: src/accel/Sector.cpp
src/accel/CMakeFiles/accel.dir/Sector.cpp.o: src/accel/CMakeFiles/accel.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/thesis/walberla-e7fa8985e052/extern/GE/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object src/accel/CMakeFiles/accel.dir/Sector.cpp.o"
	cd /home/<USER>/thesis/walberla-e7fa8985e052/extern/GE/src/accel && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/accel/CMakeFiles/accel.dir/Sector.cpp.o -MF CMakeFiles/accel.dir/Sector.cpp.o.d -o CMakeFiles/accel.dir/Sector.cpp.o -c /home/<USER>/thesis/walberla-e7fa8985e052/extern/GE/src/accel/Sector.cpp

src/accel/CMakeFiles/accel.dir/Sector.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/accel.dir/Sector.cpp.i"
	cd /home/<USER>/thesis/walberla-e7fa8985e052/extern/GE/src/accel && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/thesis/walberla-e7fa8985e052/extern/GE/src/accel/Sector.cpp > CMakeFiles/accel.dir/Sector.cpp.i

src/accel/CMakeFiles/accel.dir/Sector.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/accel.dir/Sector.cpp.s"
	cd /home/<USER>/thesis/walberla-e7fa8985e052/extern/GE/src/accel && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/thesis/walberla-e7fa8985e052/extern/GE/src/accel/Sector.cpp -o CMakeFiles/accel.dir/Sector.cpp.s

# Object files for target accel
accel_OBJECTS = \
"CMakeFiles/accel.dir/Sector.cpp.o"

# External object files for target accel
accel_EXTERNAL_OBJECTS =

src/accel/libaccel.a: src/accel/CMakeFiles/accel.dir/Sector.cpp.o
src/accel/libaccel.a: src/accel/CMakeFiles/accel.dir/build.make
src/accel/libaccel.a: src/accel/CMakeFiles/accel.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/thesis/walberla-e7fa8985e052/extern/GE/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX static library libaccel.a"
	cd /home/<USER>/thesis/walberla-e7fa8985e052/extern/GE/src/accel && $(CMAKE_COMMAND) -P CMakeFiles/accel.dir/cmake_clean_target.cmake
	cd /home/<USER>/thesis/walberla-e7fa8985e052/extern/GE/src/accel && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/accel.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
src/accel/CMakeFiles/accel.dir/build: src/accel/libaccel.a
.PHONY : src/accel/CMakeFiles/accel.dir/build

src/accel/CMakeFiles/accel.dir/clean:
	cd /home/<USER>/thesis/walberla-e7fa8985e052/extern/GE/src/accel && $(CMAKE_COMMAND) -P CMakeFiles/accel.dir/cmake_clean.cmake
.PHONY : src/accel/CMakeFiles/accel.dir/clean

src/accel/CMakeFiles/accel.dir/depend:
	cd /home/<USER>/thesis/walberla-e7fa8985e052/extern/GE && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/thesis/walberla-e7fa8985e052/extern/GE /home/<USER>/thesis/walberla-e7fa8985e052/extern/GE/src/accel /home/<USER>/thesis/walberla-e7fa8985e052/extern/GE /home/<USER>/thesis/walberla-e7fa8985e052/extern/GE/src/accel /home/<USER>/thesis/walberla-e7fa8985e052/extern/GE/src/accel/CMakeFiles/accel.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : src/accel/CMakeFiles/accel.dir/depend

