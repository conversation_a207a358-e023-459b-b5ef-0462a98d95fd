/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2.0.1                                 |
|   \\  /    A nd           | Web:      www.OpenFOAM.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       volScalarField;
    object      p;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 2 -2 0 0 0 0];

//internalField   uniform 0;

internalField   #codeStream
{
    codeInclude
    #{
        #include "fvCFD.H"
    #};

    codeOptions
    #{
        -I$(LIB_SRC)/finiteVolume/lnInclude \
        -I$(LIB_SRC)/meshTools/lnInclude
    #};

    //libs needed to visualize BC in paraview
    codeLibs
    #{
        -lmeshTools \
	-lfiniteVolume
    #};

    code
    #{
        const IOdictionary& d = static_cast<const IOdictionary&>(dict);
        const fvMesh& mesh = refCast<const fvMesh>(d.db());
        scalarField p(mesh.nCells(), 0.);

        forAll(p, i)
        {
            const scalar x = mesh.C()[i][0];
            const scalar y = mesh.C()[i][1];

            p[i] = -0.25*(Foam::cos(2*x)+Foam::cos(2*y));
        }

        writeEntry(os, "", p);
    #};
};


boundaryField
{
    upperBoundary
    {
        type            cyclic;
    }

    lowerBoundary
    {
        type            cyclic;
    }
    
    leftBoundary
    {
        type            cyclic;
    }

    rightBoundary
    {
        type            cyclic;
    }

    frontAndBack    
    {
        type            empty;
    }
}

// ************************************************************************* //
