//  This example shows how to generate and manipulate texture coordinates.
//  A random cloud of points is generated and then triangulated with 
//  vtkDelaunay3D. Since these points do not have texture coordinates,
//  we generate them with vtkTextureMapToCylinder.


#include "vtkPointSource.h"
#include "vtkDelaunay3D.h"
#include "vtkTextureMapToCylinder.h"
#include "vtkTransformTextureCoords.h"

#include "vtkDataSetMapper.h"
#include "vtkBMPReader.h"
#include "vtkTexture.h"
#include "vtkActor.h"
#include "vtkRenderer.h"
#include "vtkRenderWindow.h"
#include "vtkRenderWindowInteractor.h"
#include "vtkCamera.h"

//
// GenerateTextureCoords.cpp file 
// 

int main(int argc, char *argv[])
{
      //  Begin by generating 25 random points in the unit sphere.
      // 
      vtkPointSource *sphere = vtkPointSource::New() ; 
	sphere->SetNumberOfPoints(25) ;

      //  Triangulate the points with vtkDelaunay3D. This generates a convex hull
      //  of tetrahedron.
      // 
      vtkDelaunay3D *del = vtkDelaunay3D::New() ; 
	del -> SetInputConnection(sphere->GetOutputPort () ) ;
	del -> SetTolerance(0.01) ;
	  
      //  The triangulation has texture coordinates generated so we can map
      //  a texture onto it.
      // 
      vtkTextureMapToCylinder *tmapper = vtkTextureMapToCylinder::New() ; 
	tmapper-> SetInputConnection( del->GetOutputPort () );
	tmapper-> PreventSeamOn() ;

      //  We scale the texture coordinate to get some repeat patterns.
      vtkTransformTextureCoords *xform = vtkTransformTextureCoords::New() ; 
	xform ->SetInputConnection ( tmapper -> GetOutputPort () ) ;
	xform -> SetScale( 4, 4 ,1) ;

      //  vtkDataSetMapper internally uses a vtkGeometryFilter to extract the
      //  surface from the triangulation. The output (which is vtkPolyData) is
      //  then passed to an internal vtkPolyDataMapper which does the
      //  rendering.
      vtkDataSetMapper *mapper =  vtkDataSetMapper::New() ;
	mapper -> SetInputConnection (xform -> GetOutputPort () ) ;

      //  A texture is loaded using an image reader. Textures are simply images.
      //  The texture is eventually associated with an actor.
      // 
      vtkBMPReader *bmpReader = vtkBMPReader::New() ;
//        /homes/staff/mihoubi/vtk/VTKData
	bmpReader -> SetFileName ("/homes/staff/mihoubi/vtk/VTKData/Data/masonry.bmp") ;
	
      vtkTexture *atext = vtkTexture::New() ;
	atext ->SetInputConnection ( bmpReader-> GetOutputPort() ) ;
	atext ->InterpolateOn() ;
	
      vtkActor *triangulation = vtkActor::New() ;
	triangulation ->SetMapper (mapper);
	triangulation ->SetTexture(atext );

      //  Create the standard rendering stuff.
      vtkRenderer *ren1 = vtkRenderer::New() ;
      
      vtkRenderWindow *renWin = vtkRenderWindow::New() ;
      
	  renWin ->AddRenderer (ren1) ;
	  
      vtkRenderWindowInteractor *iren = vtkRenderWindowInteractor::New() ;
	  iren ->SetRenderWindow(renWin );

      //  Add the actors to the renderer, set the background and size
      // 
      ren1-> AddActor (triangulation );
      ren1 ->SetBackground (1 ,  1  ,  1 ) ;
      
      renWin ->SetSize (800 ,800 ) ;
      renWin ->Render() ;

      // render the image
      // prevent the tk window from showing up then start the event loop
      for (int i = 0; i < 360; ++i)
      {
	  renWin->Render(); // render the image
	  ren1->GetActiveCamera()->Azimuth(1);// rotate the active camera by one degree
      }

      // free the memory
      renWin->Delete() ;
      ren1->Delete() ;
      iren->Delete() ;
      
}

