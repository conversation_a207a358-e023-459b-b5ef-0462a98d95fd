
// Init block that initializes a Canal with inflow at south and outflow at north.
// Inflow velocity is 0.02 (lattices per timestep).

init
{
  north{                    
    prs_nils{               
        x_L  1..'domainX';   
        z_L  1..'domainZ';
        prs_L 0.0;
    }
  }
  
  south{
    vel_in{
        x_L      1..'domainX';
        z_L      1..'domainZ';
        ux_L   0.0;
        uy_L   0.02;
        uz_L   0.0; 
    }
  }
  
  east{
    noslip{
        y_L  0..'domainY'+1;
        z_L  1..'domainZ';
    }
  }
  
  west{
    noslip{
        y_L  0..'domainY'+1;
        z_L  1..'domainZ';
    }
  }
  
  top{
    noslip{
        x_L    0..'domainX'+1;
        y_L    0..'domainY'+1;
    }
  }
  
  bottom{
    noslip{
        x_L  0..'domainX'+1;
        y_L  0..'domainY'+1;
    }
  }  
}
