//If the 'paraview' block is not given, no paraview output is written

paraview 
{
  filename Output; //Name of the output file. MUST be given
  mode ASCII;      //Either ASCII or BINARY. If not given, then mode is ASCII
  xstart 0;        //Stating point in x-direction. If not given, then we start at 0 
  xend 10;         //Last point in x-direction. If not given, then we finish at xSize of the domain
  ystart 0;        //Stating point in y-direction. If not given, then we start at 0 
  yend 10;         //Last point in y-direction. If not given, then we finish at ySize of the domain
  zstart 0;        //Stating point in z-direction. If not given, then we start at 0 
  zend 10;         //Last point in z-direction. If not given, then we finish at zSize of the domain
  tstart 0;        //Timesteps the output is started. If not given, then we start at timestep 0
  tend 5;          //Timesteps the last output is written. If not given, then we finish at last timestep
  spacing 1;       //Output is written in every  spacing timestep
  writedensity 1;  //If writedensity!=0 or writedensity is not given, then the density is written. 
  writevelocity 1; //If writevelocity!=0 or writevelocity is not given, then the velocity is written. 
  writeflags 1;    //If writeflags!=0 or writeflags is not given, then the flags are written. 
  writepressure 1; //If writepressure!=0 or writepressure is not given, then the pressure is written.
  physical 1;      //If physical==1, then all values are written in physical units, else in lattice units.
}
