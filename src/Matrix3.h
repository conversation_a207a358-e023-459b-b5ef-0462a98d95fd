//=================================================================================================
/*!
//  \file Matrix3.h
//  \brief Implementation of a 3x3 matrix
//  \author <PERSON>
*/
//=================================================================================================

#ifndef _WALBERLA_MATRIX3_H_
#define _WALBERLA_MATRIX3_H_


//*************************************************************************************************
// Includes
//*************************************************************************************************

#include <algorithm>
#include <cmath>
#include <iostream>
#include <limits>
#include "Definitions.h"
#include "WalberlaMath.h"
#include "MathTrait.h"
#include "Vector3.h"


//*************************************************************************************************
// Definitions
//*************************************************************************************************

//! High-order return value.
/*! Abbreviation for the evaluation of the higher-order data type in a numerical operation. */
#define HIGH typename MathTrait<Type,Other>::High


namespace walberla {

//=================================================================================================
//
//  CLASS DEFINITION
//
//=================================================================================================

//*************************************************************************************************
/*!\brief Efficient, generic implementation of a 3x3 matrix.
// \ingroup core
//
// The Matrix3 class is the representation of a 3x3 matrix with a total of 9 statically allocated
// elements of arbitrary type. The naming convention of the elements is as following:

                          \f[\left(\begin{array}{*{3}{c}}
                          xx & xy & xz \\
                          yx & yy & yz \\
                          zx & zy & zz \\
                          \end{array}\right)\f]\n

// These elements can be accessed directly with the 1D subscript operator or with the 2D function
// operator. The numbering of the matrix elements is

                          \f[\left(\begin{array}{*{3}{c}}
                          0 & 1 & 2 \\
                          3 & 4 & 5 \\
                          6 & 7 & 8 \\
                          \end{array}\right)\f]
*/
template< typename Type >
class Matrix3
{
 private:
   //**Friend declarations*************************************************************************
   /*! \cond WALBERLA_INTERNAL */
   template< typename Other > friend class Matrix3;
   /*! \endcond */
   //**********************************************************************************************

 public:
   //**Constructors********************************************************************************
   explicit inline Matrix3();
   explicit inline Matrix3( Type init );
   explicit inline Matrix3( Type xx, Type xy, Type xz, Type yx, Type yy, Type yz, Type zx, Type zy, Type zz );
   explicit inline Matrix3( const Type* init );

   template< typename Axis, typename Angle >
   explicit Matrix3( Vector3<Axis> axis, Angle angle );

   inline Matrix3( const Matrix3& m );

   template< typename Other >
   inline Matrix3( const Matrix3<Other>& m );
   //**********************************************************************************************

   //**Destructor**********************************************************************************
   // No explicitly declared destructor.
   //**********************************************************************************************

   //**Operators***********************************************************************************
   /*!\name Operators */
   //@{
                              inline Matrix3&    operator= ( Type set );
                              inline Matrix3&    operator= ( const Matrix3& set );
   template< typename Other > inline Matrix3&    operator= ( const Matrix3<Other>& set );
   template< typename Other > inline bool        operator==( const Matrix3<Other>& rhs )      const;
   template< typename Other > inline bool        operator!=( const Matrix3<Other>& rhs )      const;
                              inline Type&       operator[]( unsigned int index );
                              inline const Type& operator[]( unsigned int index )             const;
                              inline Type&       operator()( unsigned int i, unsigned int j );
                              inline const Type& operator()( unsigned int i, unsigned int j ) const;
   //@}
   //**********************************************************************************************

   //**Arithmetic operators************************************************************************
   /*!\name Arithmetic operators
   // \brief The return type of the arithmetic operators depends on the involved data types of
   // \brief the matices. HIGH denotes the more significant data type of the arithmetic operation
   // \brief (for further detail see the MathTrait class description).
   */
   //@{
   template< typename Other > inline Matrix3&            operator+=( const Matrix3<Other>& rhs );
   template< typename Other > inline Matrix3&            operator-=( const Matrix3<Other>& rhs );
   template< typename Other > inline Matrix3&            operator*=( Other rhs );
   template< typename Other > inline Matrix3&            operator*=( const Matrix3<Other>& rhs );
   template< typename Other > inline Matrix3&            operator/=( Other rhs );
   template< typename Other > inline const Matrix3<HIGH> operator+ ( const Matrix3<Other>& rhs ) const;
   template< typename Other > inline const Matrix3<HIGH> operator- ( const Matrix3<Other>& rhs ) const;
   template< typename Other > inline const Matrix3<HIGH> operator* ( Other rhs )                 const;
   template< typename Other > inline const Matrix3<HIGH> operator/ ( Other rhs )                 const;
   template< typename Other > inline const Vector3<HIGH> operator* ( const Vector3<Other>& rhs ) const;
   template< typename Other > inline const Matrix3<HIGH> operator* ( const Matrix3<Other>& rhs ) const;
   //@}
   //**********************************************************************************************

   //**Utility functions***************************************************************************
   /*!\name Utility functions
   // \brief The return type of the utility functions depends on the involved data types of the
   // \brief matices. HIGH denotes the more significant data type of the utility operations
   // \brief (for further detail see the MathTrait class description).
   */
   //@{
                              inline Type                getDeterminant()                           const;
                              inline Matrix3&            transpose();
                              inline const Matrix3       getTranspose()                             const;
                              inline Matrix3&            invert();
                              inline const Matrix3       getInverse()                               const;
   template< typename Other > inline const Vector3<HIGH> multTranspose( const Vector3<Other>& rhs ) const;
   template< typename Other > inline const Matrix3<HIGH> rotate( const Matrix3<Other>& m )          const;
   template< typename Other > inline const Matrix3<HIGH> diagRotate( const Matrix3<Other>& m )      const;
                              inline bool                isSingular()                               const;
                              inline bool                isSymmetric()                              const;
                              inline const Matrix3       getCholesky()                              const;
   template< typename Other > inline const Vector3<HIGH> solve( const Vector3<Other> &rhs )         const;
   //@}
   //**********************************************************************************************

   //**Euler rotations*****************************************************************************
   //! Order of the Euler rotation
   /*! This codes are needed for the EulerAngles function in order to calculate the Euler angles
       for a specific combination of rotations. */
   enum EulerRotation {
      XYZs =  0,  //!< Rotation order x, y, z in a static frame.
      ZYXr =  1,  //!< Rotation order z, y, x in a rotating frame.
      XYXs =  2,  //!< Rotation order x, y, x in a static frame.
      XYXr =  3,  //!< Rotation order x, y, z in a rotating frame.
      XZYs =  4,  //!< Rotation order x, z, y in a static frame.
      YZXr =  5,  //!< Rotation order y, z, x in a rotating frame.
      XZXs =  6,  //!< Rotation order x, z, x in a static frame.
      XZXr =  7,  //!< Rotation order x, z, x in a rotating frame.
      YZXs =  8,  //!< Rotation order y, z, x in a static frame.
      XZYr =  9,  //!< Rotation order x, z, y in a rotating frame.
      YZYs = 10,  //!< Rotation order y, z, y in a static frame.
      YZYr = 11,  //!< Rotation order y, z, y in a rotating frame.
      YXZs = 12,  //!< Rotation order y, x, z in a static frame.
      ZXYr = 13,  //!< Rotation order z, x, y in a rotating frame.
      YXYs = 14,  //!< Rotation order y, x, y in a static frame.
      YXYr = 15,  //!< Rotation order y, x, y in a rotating frame.
      ZXYs = 16,  //!< Rotation order z, x, y in a static frame.
      YXZr = 17,  //!< Rotation order y, x, z in a rotating frame.
      ZXZs = 18,  //!< Rotation order z, x, z in a static frame.
      ZXZr = 19,  //!< Rotation order z, x, z in a rotating frame.
      ZYXs = 20,  //!< Rotation order z, y, x in a static frame.
      XYZr = 21,  //!< Rotation order x, y, z in a rotating frame.
      ZYZs = 22,  //!< Rotation order z, y, z in a static frame.
      ZYZr = 23   //!< Rotation order z, y, z in a rotating frame.
   };

   /*!\name Euler rotations
   // \brief For the classification of the Euler rotation, the following characteristics are
   // \brief defined:\n
   // \brief  - Inner axis: the inner axis is the axis of the first rotation matrix multiplied
   // \brief    to a vector.
   // \brief  - Parity: the parity is even, if the inner axis X is followed by the middle axis
   // \brief    Y, or Y is followed by Z, or Z is followed by X; otherwise parity is odd.
   // \brief  - Repetition: repetition tells, if the first and last axes are the same or different.
   // \brief  - Frame: the frame refers to the frame from which the Euler angles are calculated.
   // \brief
   // \brief Altogether, there are 24 possible Euler rotations. The possibilities are consisting
   // \brief of the choice of the inner axis (X,Y or Z), the parity (even or odd), repetition
   // \brief (yes or no) and the frame (static or rotating). E.g., an Euler order of XYZs stands
   // \brief for the rotation order of x-, y- and z-axis in a static frame (inner axis: X, parity:
   // \brief even, repetition: no, frame: static), whereas YXYr stands for the rotation order y-,
   // \brief x- and y-axis in a rotating frame ( inner axis: Y, parity: odd, repetition: yes,
   // \brief frame: rotating).
   */
   //@{
          const Vector3<Type> getEulerAngles( EulerRotation order ) const;
   inline const Vector3<Type> getEulerAnglesXYZ()                   const;
   //@}
   //**********************************************************************************************

 private:
   //**Member variables****************************************************************************
   /*!\name Member variables */
   //@{
   Type v_[9];  //!< The nine statically allocated matrix elements.
                /*!< Access to the matrix elements is gained via the subscript or function call
                     operator. The order of the elements is
                     \f[\left(\begin{array}{*{3}{c}}
                     0 & 1 & 2 \\
                     3 & 4 & 5 \\
                     6 & 7 & 8 \\
                     \end{array}\right)\f] */
   //@}
   //**********************************************************************************************
};
//*************************************************************************************************




//=================================================================================================
//
//  CONSTRUCTORS
//
//=================================================================================================

//*************************************************************************************************
/*!\fn Matrix3<Type>::Matrix3()
// \brief The default constructor for Matrix3.
//
// The diagonal matrix elements are initialized with 1, all other elements are initialized
// with 0.
*/
template< typename Type >
inline Matrix3<Type>::Matrix3()
{
   v_[0] = v_[4] = v_[8] = Type(1);
   v_[1] = v_[2] = v_[3] = v_[5] = v_[6] = v_[7] = Type(0);
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn Matrix3<Type>::Matrix3( Type init )
// \brief Constructor for a homogenous initialization of all elements.
//
// \param init Initial value for all matrix elements.
*/
template< typename Type >
inline Matrix3<Type>::Matrix3( Type init )
{
   v_[0] = init; v_[1] = init; v_[2] = init;
   v_[3] = init; v_[4] = init; v_[5] = init;
   v_[6] = init; v_[7] = init; v_[8] = init;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn Matrix3<Type>::Matrix3( Type xx, Type xy, Type xz, Type yx, Type yy, Type yz, Type zx, Type zy, Type zz )
// \brief Constructor for a direct initialization of all matrix elements
//
// \param xx The initial value for the xx-component.
// \param xy The initial value for the xy-component.
// \param xz The initial value for the xz-component.
// \param yx The initial value for the yx-component.
// \param yy The initial value for the yy-component.
// \param yz The initial value for the yz-component.
// \param zx The initial value for the zx-component.
// \param zy The initial value for the zy-component.
// \param zz The initial value for the zz-component.
*/
template< typename Type >
inline Matrix3<Type>::Matrix3( Type xx, Type xy, Type xz,
                               Type yx, Type yy, Type yz,
                               Type zx, Type zy, Type zz )
{
   v_[0] = xx; v_[1] = xy; v_[2] = xz;
   v_[3] = yx; v_[4] = yy; v_[5] = yz;
   v_[6] = zx; v_[7] = zy; v_[8] = zz;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn Matrix3<Type>::Matrix3( const Type* init )
// \brief Constructor for an array initializer
//
// \param init Pointer to the initialization array.
//
// The array is assumed to have at least nine valid elements.
*/
template< typename Type >
inline Matrix3<Type>::Matrix3( const Type* init )
{
   v_[0] = init[0];
   v_[1] = init[1];
   v_[2] = init[2];
   v_[3] = init[3];
   v_[4] = init[4];
   v_[5] = init[5];
   v_[6] = init[6];
   v_[7] = init[7];
   v_[8] = init[8];
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn Matrix3<Type>::Matrix3( Vector3<Axis> axis, Angle angle )
// \brief Rotation matrix constructor.
//
// \param axis The rotation axis.
// \param angle The rotation angle.
//
// This constructor is only defined for floating point vectors. The attempt to use this
// constructor for vectors of integral data type results in a compile time error.
*/
template< typename Type >
template< typename Axis, typename Angle >
Matrix3<Type>::Matrix3( Vector3<Axis> axis, Angle angle )
{
   STATIC_ASSERT( !std::numeric_limits<Type>::is_integer  );
   STATIC_ASSERT( !std::numeric_limits<Axis>::is_integer  );
   STATIC_ASSERT( !std::numeric_limits<Angle>::is_integer );

   const Angle sina( std::sin(angle) );
   const Angle cosa( std::cos(angle) );
   const Angle tmp( Angle(1)-cosa );

   axis.normalize();

   v_[0] = cosa + axis[0]*axis[0]*tmp;
   v_[1] = axis[0]*axis[1]*tmp - axis[2]*sina;
   v_[2] = axis[0]*axis[2]*tmp + axis[1]*sina;
   v_[3] = axis[1]*axis[0]*tmp + axis[2]*sina;
   v_[4] = cosa + axis[1]*axis[1]*tmp;
   v_[5] = axis[1]*axis[2]*tmp - axis[0]*sina;
   v_[6] = axis[2]*axis[0]*tmp - axis[1]*sina;
   v_[7] = axis[2]*axis[1]*tmp + axis[0]*sina;
   v_[8] = cosa + axis[2]*axis[2]*tmp;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn Matrix3<Type>::Matrix3( const Matrix3& m )
// \brief The copy constructor for Matrix3.
//
// \param m Matrix to be copied.
//
// The copy constructor is explicitly defined in order to enable/facilitate NRV optimization.
*/
template< typename Type >
inline Matrix3<Type>::Matrix3( const Matrix3& m )
{
   v_[0] = m.v_[0];
   v_[1] = m.v_[1];
   v_[2] = m.v_[2];
   v_[3] = m.v_[3];
   v_[4] = m.v_[4];
   v_[5] = m.v_[5];
   v_[6] = m.v_[6];
   v_[7] = m.v_[7];
   v_[8] = m.v_[8];
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn Matrix3<Type>::Matrix3( const Matrix3<Other>& m )
// \brief Conversion constructor from different Matrix3 instances.
//
// \param m Matrix to be copied.
*/
template< typename Type >
template< typename Other >
inline Matrix3<Type>::Matrix3( const Matrix3<Other>& m )
{
   v_[0] = m.v_[0];
   v_[1] = m.v_[1];
   v_[2] = m.v_[2];
   v_[3] = m.v_[3];
   v_[4] = m.v_[4];
   v_[5] = m.v_[5];
   v_[6] = m.v_[6];
   v_[7] = m.v_[7];
   v_[8] = m.v_[8];
}
//*************************************************************************************************




//=================================================================================================
//
//  OPERATORS
//
//=================================================================================================

//*************************************************************************************************
/*!\fn Matrix3<Type>& Matrix3<Type>::operator=( Type set )
// \brief Homogenous assignment to all matrix elements.
//
// \param set Scalar value to be assigned to all matrix elements.
// \return Reference to the assigned matrix.
*/
template< typename Type >
inline Matrix3<Type>& Matrix3<Type>::operator=( Type set )
{
   v_[0] = set;
   v_[1] = set;
   v_[2] = set;
   v_[3] = set;
   v_[4] = set;
   v_[5] = set;
   v_[6] = set;
   v_[7] = set;
   v_[8] = set;
   return *this;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn Matrix3<Type>& Matrix3<Type>::operator=( const Matrix3& set )
// \brief Copy assignment operator for Matrix3.
//
// \param set Matrix to be copied.
// \return Reference to the assigned matrix.
//
// Explicit definition of a copy assignment operator for performance reasons.
*/
template< typename Type >
inline Matrix3<Type>& Matrix3<Type>::operator=( const Matrix3& set )
{
   // This implementation is faster than the synthesized default copy assignment operator and
   // faster than an implementation with the C library function 'memcpy' in combination with a
   // protection against self-assignment. Additionally, this version goes without a protection
   // against self-assignment.
   v_[0] = set.v_[0];
   v_[1] = set.v_[1];
   v_[2] = set.v_[2];
   v_[3] = set.v_[3];
   v_[4] = set.v_[4];
   v_[5] = set.v_[5];
   v_[6] = set.v_[6];
   v_[7] = set.v_[7];
   v_[8] = set.v_[8];
   return *this;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn Matrix3<Type>& Matrix3<Type>::operator=( const Matrix3<Other>& set )
// \brief Assignment operator for different Matrix3 instances.
//
// \param set Matrix to be copied.
// \return Reference to the assigned matrix.
*/
template< typename Type >
template< typename Other >
inline Matrix3<Type>& Matrix3<Type>::operator=( const Matrix3<Other>& set )
{
   // This implementation is faster than the synthesized default copy assignment operator and
   // faster than an implementation with the C library function 'memcpy' in combination with a
   // protection against self-assignment. Additionally, this version goes without a protection
   // against self-assignment.
   v_[0] = set.v_[0];
   v_[1] = set.v_[1];
   v_[2] = set.v_[2];
   v_[3] = set.v_[3];
   v_[4] = set.v_[4];
   v_[5] = set.v_[5];
   v_[6] = set.v_[6];
   v_[7] = set.v_[7];
   v_[8] = set.v_[8];
   return *this;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn bool Matrix3<Type>::operator==( const Matrix3<Other>& rhs ) const
// \brief Equality operator for the comparison of two matrices.
//
// \param rhs The right-hand-side matrix for the comparison.
// \return bool
*/
template< typename Type >
template< typename Other >
inline bool Matrix3<Type>::operator==( const Matrix3<Other>& rhs ) const
{
   // In order to compare the vector and the scalar value, the data values of the lower-order
   // data type are converted to the higher-order data type.
   if( !equal( v_[0], rhs.v_[0] ) ||
       !equal( v_[1], rhs.v_[1] ) ||
       !equal( v_[2], rhs.v_[2] ) ||
       !equal( v_[3], rhs.v_[3] ) ||
       !equal( v_[4], rhs.v_[4] ) ||
       !equal( v_[5], rhs.v_[5] ) ||
       !equal( v_[6], rhs.v_[6] ) ||
       !equal( v_[7], rhs.v_[7] ) ||
       !equal( v_[8], rhs.v_[8] ) )
      return false;
   else return true;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn bool Matrix3<Type>::operator!=( const Matrix3<Other>& rhs ) const
// \brief Inequality operator for the comparison of two matrices.
//
// \param rhs The right-hand-side matrix for the comparison.
// \return bool
*/
template< typename Type >
template< typename Other >
inline bool Matrix3<Type>::operator!=( const Matrix3<Other>& rhs ) const
{
   // In order to compare the vector and the scalar value, the data values of the lower-order
   // data type are converted to the higher-order data type.
   if( !equal( v_[0], rhs.v_[0] ) ||
       !equal( v_[1], rhs.v_[1] ) ||
       !equal( v_[2], rhs.v_[2] ) ||
       !equal( v_[3], rhs.v_[3] ) ||
       !equal( v_[4], rhs.v_[4] ) ||
       !equal( v_[5], rhs.v_[5] ) ||
       !equal( v_[6], rhs.v_[6] ) ||
       !equal( v_[7], rhs.v_[7] ) ||
       !equal( v_[8], rhs.v_[8] ) )
      return true;
   else return false;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn Type& Matrix3<Type>::operator[]( unsigned int index )
// \brief 1D-access to the matrix elements.
//
// \param index Access index. The index has to be in the range \f$[0..8]\f$.
// \return Reference to the accessed value.
*/
template< typename Type >
inline Type& Matrix3<Type>::operator[]( unsigned int index )
{
   ASSERT( index < 9 ,"Invalid matrix access index" );
   return v_[index];
}
//**********************************************************************************************


//*************************************************************************************************
/*!\fn const Type& Matrix3<Type>::operator[]( unsigned int index ) const
// \brief 1D-access to the matrix elements.
//
// \param index Access index. The index has to be in the range \f$[0..8]\f$.
// \return Reference-to-const to the accessed value.
*/
template< typename Type >
inline const Type& Matrix3<Type>::operator[]( unsigned int index ) const
{
   ASSERT( index < 9 ,"Invalid matrix access index" );
   return v_[index];
}
//**********************************************************************************************


//*************************************************************************************************
/*!\fn Type& Matrix3<Type>::operator()( unsigned int i, unsigned int j )
// \brief 2D-access to the matrix elements.
//
// \param i Access index for the row. The index has to be in the range [0..2].
// \param j Access index for the column. The index has to be in the range [0..2].
// \return Reference to the accessed value.
*/
template< typename Type >
inline Type& Matrix3<Type>::operator()( unsigned int i, unsigned int j )
{
   ASSERT( i<3 && j<3,"Invalid matrix access index" );
   return v_[i*3+j];
}
//**********************************************************************************************


//*************************************************************************************************
/*!\fn const Type& Matrix3<Type>::operator()( unsigned int i, unsigned int j ) const
// \brief 2D-access to the matrix elements.
//
// \param i Access index for the row. The index has to be in the range [0..2].
// \param j Access index for the column. The index has to be in the range [0..2].
// \return Reference-to-const to the accessed value.
*/
template< typename Type >
inline const Type& Matrix3<Type>::operator()( unsigned int i, unsigned int j ) const
{
   ASSERT( i<3 && j<3 ,"Invalid matrix access index" );
   return v_[i*3+j];
}
//**********************************************************************************************




//=================================================================================================
//
//  ARITHMETIC OPERATORS
//
//=================================================================================================

//*************************************************************************************************
/*!\fn Matrix3<Type>& Matrix3<Type>::operator+=( const Matrix3<Other>& rhs )
// \brief Addition assignment operator for the addition of two matrices (\f$ A+=B \f$).
//
// \param rhs The right-hand-side matrix to be added to the matrix.
// \return Reference to the matrix.
*/
template< typename Type >
template< typename Other >
inline Matrix3<Type>& Matrix3<Type>::operator+=( const Matrix3<Other>& rhs )
{
   v_[0] += rhs.v_[0];
   v_[1] += rhs.v_[1];
   v_[2] += rhs.v_[2];
   v_[3] += rhs.v_[3];
   v_[4] += rhs.v_[4];
   v_[5] += rhs.v_[5];
   v_[6] += rhs.v_[6];
   v_[7] += rhs.v_[7];
   v_[8] += rhs.v_[8];
   return *this;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn Matrix3<Type>& Matrix3<Type>::operator-=( const Matrix3<Other>& rhs )
// \brief Subtraction assignment operator for the subtraction of two matrices (\f$ A-=B \f$).
//
// \param rhs The right-hand-side matrix to be subtracted from the matrix.
// \return Reference to the matrix.
*/
template< typename Type >
template< typename Other >
inline Matrix3<Type>& Matrix3<Type>::operator-=( const Matrix3<Other>& rhs )
{
   v_[0] -= rhs.v_[0];
   v_[1] -= rhs.v_[1];
   v_[2] -= rhs.v_[2];
   v_[3] -= rhs.v_[3];
   v_[4] -= rhs.v_[4];
   v_[5] -= rhs.v_[5];
   v_[6] -= rhs.v_[6];
   v_[7] -= rhs.v_[7];
   v_[8] -= rhs.v_[8];
   return *this;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn Matrix3<Type>& Matrix3<Type>::operator*=( Other rhs )
// \brief Multiplication assignment operator for the multiplication between a matrix and
// \brief a scalar value (\f$ A*=s \f$).
//
// \param rhs The right-hand-side scalar value for the multiplication.
// \return Reference to the matrix.
*/
template< typename Type >
template< typename Other >
inline Matrix3<Type>& Matrix3<Type>::operator*=( Other rhs )
{
   v_[0] *= rhs;
   v_[1] *= rhs;
   v_[2] *= rhs;
   v_[3] *= rhs;
   v_[4] *= rhs;
   v_[5] *= rhs;
   v_[6] *= rhs;
   v_[7] *= rhs;
   v_[8] *= rhs;
   return *this;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn Matrix3<Type>& Matrix3<Type>::operator*=( const Matrix3<Other>& rhs )
// \brief Multiplication assignment operator for the multiplication between two matrices
// \brief (\f$ A*=B \f$).
//
// \param rhs The right-hand-side matrix for the multiplication.
// \return Reference to the matrix.
*/
template< typename Type >
template< typename Other >
inline Matrix3<Type>& Matrix3<Type>::operator*=( const Matrix3<Other>& rhs )
{
   // Creating a temporary due to data dependencies
   Matrix3 tmp( v_[0]*rhs.v_[0] + v_[1]*rhs.v_[3] + v_[2]*rhs.v_[6],
                v_[0]*rhs.v_[1] + v_[1]*rhs.v_[4] + v_[2]*rhs.v_[7],
                v_[0]*rhs.v_[2] + v_[1]*rhs.v_[5] + v_[2]*rhs.v_[8],
                v_[3]*rhs.v_[0] + v_[4]*rhs.v_[3] + v_[5]*rhs.v_[6],
                v_[3]*rhs.v_[1] + v_[4]*rhs.v_[4] + v_[5]*rhs.v_[7],
                v_[3]*rhs.v_[2] + v_[4]*rhs.v_[5] + v_[5]*rhs.v_[8],
                v_[6]*rhs.v_[0] + v_[7]*rhs.v_[3] + v_[8]*rhs.v_[6],
                v_[6]*rhs.v_[1] + v_[7]*rhs.v_[4] + v_[8]*rhs.v_[7],
                v_[6]*rhs.v_[2] + v_[7]*rhs.v_[5] + v_[8]*rhs.v_[8] );

   return this->operator=( tmp );
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn Matrix3<Type>& Matrix3<Type>::operator/=( Other rhs )
// \brief Division assignment operator for the division of a matrix by a scalar value (\f$ A/=s \f$).
//
// \param rhs The right-hand-side scalar value for the division.
// \return Reference to the matrix.
*/
template< typename Type >
template< typename Other >
inline Matrix3<Type>& Matrix3<Type>::operator/=( Other rhs )
{
   v_[0] /= rhs;
   v_[1] /= rhs;
   v_[2] /= rhs;
   v_[3] /= rhs;
   v_[4] /= rhs;
   v_[5] /= rhs;
   v_[6] /= rhs;
   v_[7] /= rhs;
   v_[8] /= rhs;
   return *this;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn const Matrix3<HIGH> Matrix3<Type>::operator+( const Matrix3<Other>& rhs ) const
// \brief Addition operator for the addition of two matrices (\f$ A=B+C \f$).
//
// \param rhs The right-hand-side matrix to be added to the matrix.
// \return The sum of the two matrices.
*/
template< typename Type >
template< typename Other >
inline const Matrix3<HIGH> Matrix3<Type>::operator+( const Matrix3<Other>& rhs ) const
{
   return Matrix3<HIGH>( v_[0] + rhs.v_[0],
                         v_[1] + rhs.v_[1],
                         v_[2] + rhs.v_[2],
                         v_[3] + rhs.v_[3],
                         v_[4] + rhs.v_[4],
                         v_[5] + rhs.v_[5],
                         v_[6] + rhs.v_[6],
                         v_[7] + rhs.v_[7],
                         v_[8] + rhs.v_[8] );
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn const Matrix3<HIGH> Matrix3<Type>::operator-( const Matrix3<Other>& rhs ) const
// \brief Subtraction operator for the subtraction of two matrices (\f$ A=B-C \f$).
//
// \param rhs The right-hand-side matrix to be subtracted from the matrix.
// \return The difference of the two matrices.
*/
template< typename Type >
template< typename Other >
inline const Matrix3<HIGH> Matrix3<Type>::operator-( const Matrix3<Other>& rhs ) const
{
   return Matrix3<HIGH>( v_[0] - rhs.v_[0],
                         v_[1] - rhs.v_[1],
                         v_[2] - rhs.v_[2],
                         v_[3] - rhs.v_[3],
                         v_[4] - rhs.v_[4],
                         v_[5] - rhs.v_[5],
                         v_[6] - rhs.v_[6],
                         v_[7] - rhs.v_[7],
                         v_[8] - rhs.v_[8] );
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn const Matrix3<HIGH> Matrix3<Type>::operator*( Other rhs ) const
// \brief Multiplication operator for the multiplication of a matrix and a scalar value
// \brief (\f$ A=B*s \f$).
//
// \param rhs The right-hand-side scalar value for the multiplication.
// \return The scaled result matrix.
*/
template< typename Type >
template< typename Other >
inline const Matrix3<HIGH> Matrix3<Type>::operator*( Other rhs ) const
{
   return Matrix3<HIGH>( v_[0]*rhs, v_[1]*rhs, v_[2]*rhs,
                         v_[3]*rhs, v_[4]*rhs, v_[5]*rhs,
                         v_[6]*rhs, v_[7]*rhs, v_[8]*rhs );
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn const Matrix3<HIGH> Matrix3<Type>::operator/( Other rhs ) const
// \brief Division operator for the division of a matrix by a scalar value (\f$ A=B/s \f$).
//
// \param rhs The right-hand-side scalar value for the division.
// \return The scaled result matrix.
*/
template< typename Type >
template< typename Other >
inline const Matrix3<HIGH> Matrix3<Type>::operator/( Other rhs ) const
{
   return Matrix3<HIGH>( v_[0]/rhs, v_[1]/rhs, v_[2]/rhs,
                         v_[3]/rhs, v_[4]/rhs, v_[5]/rhs,
                         v_[6]/rhs, v_[7]/rhs, v_[8]/rhs );
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn const Vector3<HIGH> Matrix3<Type>::operator*( const Vector3<Other>& rhs ) const
// \brief Multiplication operator for the multiplication of a matrix and a vector
// \brief (\f$ \vec{a}=B*\vec{c} \f$).
//
// \param rhs The right-hand-side vector for the multiplication.
// \return The resulting vector.
*/
template< typename Type >
template< typename Other >
inline const Vector3<HIGH> Matrix3<Type>::operator*( const Vector3<Other>& rhs ) const
{
   return Vector3<HIGH>( v_[0]*rhs[0] + v_[1]*rhs[1] + v_[2]*rhs[2],
                         v_[3]*rhs[0] + v_[4]*rhs[1] + v_[5]*rhs[2],
                         v_[6]*rhs[0] + v_[7]*rhs[1] + v_[8]*rhs[2] );
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn const Matrix3<HIGH> Matrix3<Type>::operator*( const Matrix3<Other>& rhs ) const
// \brief Multiplication operator for the multiplication of two matrices (\f$ A=B*C \f$).
//
// \param rhs The right-hand-side matrix for the multiplication.
// \return The resulting matrix.
*/
template< typename Type >
template< typename Other >
inline const Matrix3<HIGH> Matrix3<Type>::operator*( const Matrix3<Other>& rhs ) const
{
   return Matrix3<HIGH>( v_[0]*rhs.v_[0] + v_[1]*rhs.v_[3] + v_[2]*rhs.v_[6],
                         v_[0]*rhs.v_[1] + v_[1]*rhs.v_[4] + v_[2]*rhs.v_[7],
                         v_[0]*rhs.v_[2] + v_[1]*rhs.v_[5] + v_[2]*rhs.v_[8],
                         v_[3]*rhs.v_[0] + v_[4]*rhs.v_[3] + v_[5]*rhs.v_[6],
                         v_[3]*rhs.v_[1] + v_[4]*rhs.v_[4] + v_[5]*rhs.v_[7],
                         v_[3]*rhs.v_[2] + v_[4]*rhs.v_[5] + v_[5]*rhs.v_[8],
                         v_[6]*rhs.v_[0] + v_[7]*rhs.v_[3] + v_[8]*rhs.v_[6],
                         v_[6]*rhs.v_[1] + v_[7]*rhs.v_[4] + v_[8]*rhs.v_[7],
                         v_[6]*rhs.v_[2] + v_[7]*rhs.v_[5] + v_[8]*rhs.v_[8] );
}
//*************************************************************************************************




//=================================================================================================
//
//  UTILITY FUNCTIONS
//
//=================================================================================================

//*************************************************************************************************
/*!\fn Type Matrix3<Type>::getDeterminant() const
// \brief Calculation of the determinant of the matrix.
//
// \return The determinant of the matrix.
*/
template< typename Type >
inline Type Matrix3<Type>::getDeterminant() const
{
   return v_[0]*v_[4]*v_[8] + v_[1]*v_[5]*v_[6] + v_[2]*v_[3]*v_[7] -
          v_[6]*v_[4]*v_[2] - v_[7]*v_[5]*v_[0] - v_[8]*v_[3]*v_[1];
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn Matrix3<Type>& Matrix3<Type>::transpose()
// \brief Transposing the matrix.
//
// \return Reference to the transposed matrix.
*/
template< typename Type >
inline Matrix3<Type>& Matrix3<Type>::transpose()
{
   std::swap( v_[1], v_[3] );
   std::swap( v_[2], v_[6] );
   std::swap( v_[5], v_[7] );
   return *this;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn const Matrix3<Type> Matrix3<Type>::getTranspose() const
// \brief Calculation of the transpose of the matrix.
//
// \return The transpose of the matrix.
*/
template< typename Type >
inline const Matrix3<Type> Matrix3<Type>::getTranspose() const
{
   return Matrix3( v_[0], v_[3], v_[6], v_[1], v_[4], v_[7], v_[2], v_[5], v_[8] );
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn Matrix3<Type>& Matrix3<Type>::invert()
// \brief Inverting the matrix.
//
// \return Reference to the inverted matrix.
//
// The calculation is performed with the matrix inversion by Cramer. This function is only
// defined for matrices of floating point type. The attempt to use this function with matrices
// of integral data types will result in a compile time error.
*/
template< typename Type >
inline Matrix3<Type>& Matrix3<Type>::invert()
{
   STATIC_ASSERT( !std::numeric_limits<Type>::is_integer );

   STATIC_ASSERT( !std::numeric_limits<Type>::is_integer );

   Type det = v_[0] * ( ( v_[4] * v_[8] ) - ( v_[7] * v_[5] ) ) +
              v_[1] * ( ( v_[5] * v_[6] ) - ( v_[8] * v_[3] ) ) +
              v_[2] * ( ( v_[3] * v_[7] ) - ( v_[4] * v_[6] ) );

   ASSERT( det != Type(0),"Matrix is singular and cannot be inverted" );

   det = Type(1) / det;

   // Creating a temporary due to data dependencies
   return *this = Matrix3( det * ( ( v_[4]*v_[8] ) - ( v_[5]*v_[7] ) ),
                           det * ( ( v_[7]*v_[2] ) - ( v_[8]*v_[1] ) ),
                           det * ( ( v_[1]*v_[5] ) - ( v_[2]*v_[4] ) ),
                           det * ( ( v_[5]*v_[6] ) - ( v_[3]*v_[8] ) ),
                           det * ( ( v_[8]*v_[0] ) - ( v_[6]*v_[2] ) ),
                           det * ( ( v_[2]*v_[3] ) - ( v_[0]*v_[5] ) ),
                           det * ( ( v_[3]*v_[7] ) - ( v_[4]*v_[6] ) ),
                           det * ( ( v_[6]*v_[1] ) - ( v_[7]*v_[0] ) ),
                           det * ( ( v_[0]*v_[4] ) - ( v_[1]*v_[3] ) ) );
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn const Matrix3<Type> Matrix3<Type>::getInverse() const
// \brief Calculation of the inverse of the matrix.
//
// \return The inverse of the matrix.
//
// The calculation is performed with the matrix inversion by Cramer. This function is only
// defined for matrices of floating point type. The attempt to use this function with matrices
// of integral data types will result in a compile time error.
*/
template< typename Type >
inline const Matrix3<Type> Matrix3<Type>::getInverse() const
{
   STATIC_ASSERT( !std::numeric_limits<Type>::is_integer );

   Type det = v_[0] * ( ( v_[4] * v_[8] ) - ( v_[7] * v_[5] ) ) +
              v_[1] * ( ( v_[5] * v_[6] ) - ( v_[8] * v_[3] ) ) +
              v_[2] * ( ( v_[3] * v_[7] ) - ( v_[4] * v_[6] ) );

   ASSERT( det != Type(0),"Matrix is singular and cannot be inverted" );

   det = Type(1) / det;

   return Matrix3( det * ( ( v_[4]*v_[8] ) - ( v_[5]*v_[7] ) ),
                   det * ( ( v_[7]*v_[2] ) - ( v_[8]*v_[1] ) ),
                   det * ( ( v_[1]*v_[5] ) - ( v_[2]*v_[4] ) ),
                   det * ( ( v_[5]*v_[6] ) - ( v_[3]*v_[8] ) ),
                   det * ( ( v_[8]*v_[0] ) - ( v_[6]*v_[2] ) ),
                   det * ( ( v_[2]*v_[3] ) - ( v_[0]*v_[5] ) ),
                   det * ( ( v_[3]*v_[7] ) - ( v_[4]*v_[6] ) ),
                   det * ( ( v_[6]*v_[1] ) - ( v_[7]*v_[0] ) ),
                   det * ( ( v_[0]*v_[4] ) - ( v_[1]*v_[3] ) ) );
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn const Vector3<HIGH> Matrix3<Type>::multTranspose( const Vector3<Other>& rhs ) const
// \brief Multiplication of the transpose of the matrix and a vector (\f$ \vec{a}=B^T*\vec{c} \f$).
//
// \param rhs The right-hand-side vector for the multiplication.
// \return The resulting vector.
*/
template< typename Type >
template< typename Other >
inline const Vector3<HIGH> Matrix3<Type>::multTranspose( const Vector3<Other>& rhs ) const
{
   return Vector3<HIGH>( v_[0]*rhs[0] + v_[3]*rhs[1] + v_[6]*rhs[2],
                         v_[1]*rhs[0] + v_[4]*rhs[1] + v_[7]*rhs[2],
                         v_[2]*rhs[0] + v_[5]*rhs[1] + v_[8]*rhs[2] );
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn const Matrix3<HIGH> Matrix3<Type>::rotate( const Matrix3<Other>& m ) const
// \brief Rotation of a matrix M (\f$ ROT=R*M*R^{-1} \f$).
//
// \param m The matrix to be rotated.
// \return The rotated matrix.
//
// This function is only defined for matrices of floating point type. The attempt to use this
// function with matrices of integral data type will result in a compile time error.
*/
template< typename Type >
template< typename Other >
inline const Matrix3<HIGH> Matrix3<Type>::rotate( const Matrix3<Other>& m ) const
{
   STATIC_ASSERT( !std::numeric_limits<Type>::is_integer  );
   STATIC_ASSERT( !std::numeric_limits<Other>::is_integer );

   //--Multiplication in two steps (number of FLOP = 90, 1 additional temporary matrix)------------

   // Precalculation of tmp = m * R(-1)
   const Matrix3<HIGH> tmp( m.v_[0]*v_[0] + m.v_[1]*v_[1] + m.v_[2]*v_[2],
                            m.v_[0]*v_[3] + m.v_[1]*v_[4] + m.v_[2]*v_[5],
                            m.v_[0]*v_[6] + m.v_[1]*v_[7] + m.v_[2]*v_[8],
                            m.v_[3]*v_[0] + m.v_[4]*v_[1] + m.v_[5]*v_[2],
                            m.v_[3]*v_[3] + m.v_[4]*v_[4] + m.v_[5]*v_[5],
                            m.v_[3]*v_[6] + m.v_[4]*v_[7] + m.v_[5]*v_[8],
                            m.v_[6]*v_[0] + m.v_[7]*v_[1] + m.v_[8]*v_[2],
                            m.v_[6]*v_[3] + m.v_[7]*v_[4] + m.v_[8]*v_[5],
                            m.v_[6]*v_[6] + m.v_[7]*v_[7] + m.v_[8]*v_[8] );

   // Calculating ROT = R * tmp
   return Matrix3<HIGH>( v_[0]*tmp.v_[0] + v_[1]*tmp.v_[3] + v_[2]*tmp.v_[6],
                         v_[0]*tmp.v_[1] + v_[1]*tmp.v_[4] + v_[2]*tmp.v_[7],
                         v_[0]*tmp.v_[2] + v_[1]*tmp.v_[5] + v_[2]*tmp.v_[8],
                         v_[3]*tmp.v_[0] + v_[4]*tmp.v_[3] + v_[5]*tmp.v_[6],
                         v_[3]*tmp.v_[1] + v_[4]*tmp.v_[4] + v_[5]*tmp.v_[7],
                         v_[3]*tmp.v_[2] + v_[4]*tmp.v_[5] + v_[5]*tmp.v_[8],
                         v_[6]*tmp.v_[0] + v_[7]*tmp.v_[3] + v_[8]*tmp.v_[6],
                         v_[6]*tmp.v_[1] + v_[7]*tmp.v_[4] + v_[8]*tmp.v_[7],
                         v_[6]*tmp.v_[2] + v_[7]*tmp.v_[5] + v_[8]*tmp.v_[8] );

   //--Multiplication in one step (number of FLOP = 180, no additional temporary matrix)-----------
   /*
   return Matrix3<HIGH>( m.v_[0]*v_[0]*v_[0] + m.v_[4]*v_[1]*v_[1] + m.v_[8]*v_[2]*v_[2] + v_[0]*v_[1]*( m.v_[1]+m.v_[3] ) + v_[0]*v_[2]*( m.v_[2]+m.v_[6] ) + v_[1]*v_[2]*( m.v_[5]+m.v_[7] ),
                         v_[0]*( m.v_[0]*v_[3] + m.v_[1]*v_[4] + m.v_[2]*v_[5] ) + v_[1]*( m.v_[3]*v_[3] + m.v_[4]*v_[4] + m.v_[5]*v_[5] ) + v_[2]*( m.v_[6]*v_[3] + m.v_[7]*v_[4] + m.v_[8]*v_[5] ),
                         v_[0]*( m.v_[0]*v_[6] + m.v_[1]*v_[7] + m.v_[2]*v_[8] ) + v_[1]*( m.v_[3]*v_[6] + m.v_[4]*v_[7] + m.v_[5]*v_[8] ) + v_[2]*( m.v_[6]*v_[6] + m.v_[7]*v_[7] + m.v_[8]*v_[8] ),
                         v_[3]*( m.v_[0]*v_[0] + m.v_[1]*v_[1] + m.v_[2]*v_[2] ) + v_[4]*( m.v_[3]*v_[0] + m.v_[4]*v_[1] + m.v_[5]*v_[2] ) + v_[5]*( m.v_[6]*v_[0] + m.v_[7]*v_[1] + m.v_[8]*v_[2] ),
                         m.v_[0]*v_[3]*v_[3] + m.v_[4]*v_[4]*v_[4] + m.v_[8]*v_[5]*v_[5] + v_[3]*v_[4]*( m.v_[1]+m.v_[3] ) + v_[3]*v_[5]*( m.v_[2]+m.v_[6] ) + v_[4]*v_[5]*( m.v_[5]+m.v_[7] ),
                         v_[3]*( m.v_[0]*v_[6] + m.v_[1]*v_[7] + m.v_[2]*v_[8] ) + v_[4]*( m.v_[3]*v_[6] + m.v_[4]*v_[7] + m.v_[5]*v_[8] ) + v_[5]*( m.v_[6]*v_[6] + m.v_[7]*v_[7] + m.v_[8]*v_[8] ),
                         v_[6]*( m.v_[0]*v_[0] + m.v_[1]*v_[1] + m.v_[2]*v_[2] ) + v_[7]*( m.v_[3]*v_[0] + m.v_[4]*v_[1] + m.v_[5]*v_[2] ) + v_[8]*( m.v_[6]*v_[0] + m.v_[7]*v_[1] + m.v_[8]*v_[2] ),
                         v_[6]*( m.v_[0]*v_[3] + m.v_[1]*v_[4] + m.v_[2]*v_[5] ) + v_[7]*( m.v_[3]*v_[3] + m.v_[4]*v_[4] + m.v_[5]*v_[5] ) + v_[8]*( m.v_[6]*v_[3] + m.v_[7]*v_[4] + m.v_[8]*v_[5] ),
                         m.v_[0]*v_[6]*v_[6] + m.v_[4]*v_[7]*v_[7] + m.v_[8]*v_[8]*v_[8] + v_[6]*v_[7]*( m.v_[1]+m.v_[3] ) + v_[6]*v_[8]*( m.v_[2]+m.v_[6] ) + v_[7]*v_[8]*( m.v_[5]+m.v_[7] ) );
   */
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn const Matrix3<HIGH> Matrix3<Type>::diagRotate( const Matrix3<Other>& m ) const
// \brief Rotation of a diagonal matrix M (\f$ ROT=R*M*R^{-1} \f$).
//
// \param m The diagonal matrix to be rotated.
// \return The rotated matrix.
//
// The DiagRotate function is a special case of the Rotate function. The matrix is assumed to
// be a diagonal matrix, which reduces the number of floating point operations of the rotation.
// This function is only defined for matrices of floating point type. The attempt to use this
// function with matrices of integral data type will result in a compile time error.
*/
template< typename Type >
template< typename Other >
inline const Matrix3<HIGH> Matrix3<Type>::diagRotate( const Matrix3<Other>& m ) const
{
   STATIC_ASSERT( !std::numeric_limits<Type>::is_integer  );
   STATIC_ASSERT( !std::numeric_limits<Other>::is_integer );

   // Precalculating tmp = m * R(-1)
   const Matrix3<HIGH> tmp( m.v_[0]*v_[0], m.v_[0]*v_[3], m.v_[0]*v_[6],
                            m.v_[4]*v_[1], m.v_[4]*v_[4], m.v_[4]*v_[7],
                            m.v_[8]*v_[2], m.v_[8]*v_[5], m.v_[8]*v_[8] );

   // Calculating ROT = R * tmp
   return Matrix3<HIGH>( v_[0]*tmp.v_[0] + v_[1]*tmp.v_[3] + v_[2]*tmp.v_[6],
                         v_[0]*tmp.v_[1] + v_[1]*tmp.v_[4] + v_[2]*tmp.v_[7],
                         v_[0]*tmp.v_[2] + v_[1]*tmp.v_[5] + v_[2]*tmp.v_[8],
                         v_[3]*tmp.v_[0] + v_[4]*tmp.v_[3] + v_[5]*tmp.v_[6],
                         v_[3]*tmp.v_[1] + v_[4]*tmp.v_[4] + v_[5]*tmp.v_[7],
                         v_[3]*tmp.v_[2] + v_[4]*tmp.v_[5] + v_[5]*tmp.v_[8],
                         v_[6]*tmp.v_[0] + v_[7]*tmp.v_[3] + v_[8]*tmp.v_[6],
                         v_[6]*tmp.v_[1] + v_[7]*tmp.v_[4] + v_[8]*tmp.v_[7],
                         v_[6]*tmp.v_[2] + v_[7]*tmp.v_[5] + v_[8]*tmp.v_[8] );
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn bool Matrix3<Type>::isSingular() const
// \brief Singularity check for the matrix (det=0).
//
// \return \a true if the matrix is singular, \a false if not.
*/
template< typename Type >
inline bool Matrix3<Type>::isSingular() const
{
   if( getDeterminant() == Type(0) )
      return true;
   else return false;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn bool Matrix3<Type>::isSymmetric() const
// \brief Checks if the matrix is symmetric.
//
// \return \a true if the matrix is symmetric, \a false if not.
*/
template< typename Type >
inline bool Matrix3<Type>::isSymmetric() const
{
   if( !equal( v_[1], v_[3] ) || !equal( v_[2], v_[6] ) || !equal( v_[5], v_[7] ) )
      return false;
   else return true;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn const Matrix3<Type> Matrix3<Type>::getCholesky() const
// \brief Cholesky decomposition of the matrix (\f$ A = LR \f$).
//
// \return The decomposed matrix \f$ L \f$.
//
// This function is only defined for matrices of floating point type. The attempt to use this
// function with matrices of integral data type will result in a compile time error.
*/
template< typename Type >
inline const Matrix3<Type> Matrix3<Type>::getCholesky() const
{
   STATIC_ASSERT( !std::numeric_limits<Type>::is_integer );

   Matrix3 tmp( Type(0) );

   ASSERT( !isSingular() ,"Matrix is singular and cannot be decomposed" );

   Type sum( 0 );

   for( int k=0; k<3; ++k ) {
      for( int p=0; p<k; ++p ) {
         sum += tmp.v_[k*3+p] * tmp.v_[k*3+p];
      }
      tmp.v_[k*4] = std::sqrt( v_[k*4]-sum );
      sum = Type(0);
      for( int i=(k+1); i<3; ++i ) {
         for( int p=0; p<k; ++p ) {
            sum += tmp.v_[k*3+p] * tmp.v_[i*3+p];
         }
         tmp.v_[i*3+k] = (v_[i*3+k]-sum) / tmp.v_[k*4];
         sum = Type(0);
      }
   }

   return tmp;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn const Vector3<HIGH> Matrix3<Type>::solve( const Vector3<Other> &rhs ) const
// \brief Solving the linear system of equations \f$A*x=b\f$ with the decomposed matrix
// \brief \f$LR = A\f$.
//
// \param rhs The right-hand-side of the linear system of equations.
// \return The solution of the linear system of equations.
//
// This function is only defined for matrices of floating point type. The attempt to use this
// function with matrices of integral data type will result in a compile time error.
*/
template< typename Type >
template< typename Other >
inline const Vector3<HIGH> Matrix3<Type>::solve( const Vector3<Other> &rhs ) const
{
   STATIC_ASSERT( !std::numeric_limits<Type>::is_integer  );
   STATIC_ASSERT( !std::numeric_limits<Other>::is_integer );

   Vector3<HIGH> tmp1, tmp2;
   HIGH sum;

   // Solving the equation L*y = b
   for( int i=0; i<3; ++i ) {
      sum = rhs[i];
      for( int j=0; j<i; ++j ) {
         sum -= v_[i*3+j] * tmp1[j];
      }
      tmp1[i] = sum / v_[i*4];
   }

   // Solving the equation R*x = y
   for( int i=2; i>=0; --i ) {
      sum = tmp1[i];
      for( int j=2; j>i ; --j ) {
         sum -= v_[j*3+i] * tmp2[j];
      }
      tmp2[i] = sum / v_[i*4];
   }

   return tmp2;
}
//*************************************************************************************************




//=================================================================================================
//
//  EULER ROTATIONS
//
//=================================================================================================

//*************************************************************************************************
/*!\fn const Vector3<Type> Matrix3<Type>::getEulerAnglesXYZ() const
// \brief Calculation of the Euler angles (in radian measure).
//
// \return The Euler angles for a rotation order of x, y, z.
//
// The Euler angles are calculated for a rotation order of x-, y- and z-axis. This function
// is only defined for matrices of floating point type. The attempt to use this function with
// matrices of integral data type will result in a compile time error.
*/
template< typename Type >
inline const Vector3<Type> Matrix3<Type>::getEulerAnglesXYZ() const
{
   STATIC_ASSERT( !std::numeric_limits<Type>::is_integer );

   const Type cy( std::sqrt( v_[0]*v_[0] + v_[3]*v_[3] ) );

   if( cy > Type(16)*std::numeric_limits<Type>::epsilon() ) {
      return Vector3<Type>( std::atan2( v_[7], v_[8] ), std::atan2( -v_[6], cy ), std::atan2( v_[3], v_[0] ) );
   }
   else {
      return Vector3<Type>( std::atan2( -v_[5], v_[4] ), std::atan2( -v_[6], cy ), Type(0) );
   }
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn const Vector3<Type> Matrix3<Type>::getEulerAngles( EulerRotation order ) const
// \brief Calculation of the Euler angles for a specific rotation order.
//
// \param order The specific rotation order.
// \return The specific Euler angles.
//
// This function is only defined for matrices of floating point type. The attempt to use this
// function with matrices of integral data type will result in a compile time error.
*/
template< typename Type >
const Vector3<Type> Matrix3<Type>::getEulerAngles( EulerRotation order ) const
{
   STATIC_ASSERT( !std::numeric_limits<Type>::is_integer );

   static const unsigned int eulSafe[4] = { 0, 1, 2, 0 };
   static const unsigned int eulNext[4] = { 1, 2, 0, 1 };

   Vector3<Type> ea;

   // Unpacking the euler order
   const unsigned int frame( order&1 );
   const unsigned int repetition( (order&2)>>1 );
   const unsigned int parity( (order&4)>>2 );
   const unsigned int i( eulSafe[(order&24)>>3] );
   const unsigned int j( eulNext[i+parity] );
   const unsigned int k( eulNext[i+1-parity] );

   // Treatment of rotations with repetition
   if( repetition ) {
      const Type sy( std::sqrt( v_[i*3+j]*v_[i*3+j] + v_[i*3+k]*v_[i*3+k] ) );
      if( sy > Type(16)*std::numeric_limits<Type>::epsilon() ) {
         ea[0] = std::atan2( v_[i*3+j], v_[i*3+k] );
         ea[1] = std::atan2( sy, v_[i*3+i] );
         ea[2] = std::atan2( v_[j*3+i], -v_[k*3+i] );
      }
      else {
         ea[0] = std::atan2( -v_[j*3+k], v_[j*3+j] );
         ea[1] = std::atan2( sy, v_[i*3+i] );
         ea[2] = Type(0);
      }
   }

   // Treatment of rotations without repetition
   else {
      const Type cy( std::sqrt( v_[i*3+i]*v_[i*3+i] + v_[j*3+i]*v_[j*3+i] ) );
      if( cy > Type(16)*std::numeric_limits<Type>::epsilon() ) {
         ea[0] = std::atan2( v_[k*3+j], v_[k*3+k] );
         ea[1] = std::atan2( -v_[k*3+i], cy );
         ea[2] = std::atan2( v_[j*3+i], v_[i*3+i] );
      }
      else {
         ea[0] = std::atan2( -v_[j*3+k], v_[j*3+j] );
         ea[1] = std::atan2( -v_[k*3+i], cy );
         ea[2] = Type(0);
      }
   }

   // Treatment of an odd partity
   if( parity ) {
      ea[0] = -ea[0];
      ea[1] = -ea[1];
      ea[2] = -ea[2];
   }

   // Treatment of a rotating frame
   if( frame ) {
      Real tmp = ea[0];
      ea[0] = ea[2];
      ea[2] = tmp;
   }

   return ea;
}
//*************************************************************************************************




//=================================================================================================
//
//  GLOBAL OPERATORS
//
//=================================================================================================

//*************************************************************************************************
/*!\name Matrix3 operators */
//@{
template< typename Type, typename Other >
inline const Matrix3<HIGH> operator*( Other scalar, const Matrix3<Type>& matrix );

template< typename Type >
std::ostream& operator<<( std::ostream& os, const Matrix3<Type>& m );

template< typename Type >
inline bool isnan( const Matrix3<Type>& m );

template< typename Type >
inline const Matrix3<Type> abs( const Matrix3<Type>& m );

template< typename Type >
inline const Matrix3<Type> fabs( const Matrix3<Type>& m );
//@}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn const Matrix3<Type> operator*( Other scalar, const Matrix3<Type>& matrix )
// \brief Multiplication operator for the multiplication of a scalar value and a matrix
// \brief (\f$ A=s*B \f$).
//
// \param scalar The left-hand-side scalar value for the multiplication.
// \param matrix The right-hand-side matrix for the multiplication.
// \return The scaled result matrix.
*/
template< typename Type, typename Other >
inline const Matrix3<HIGH> operator*( Other scalar, const Matrix3<Type>& matrix )
{
   return matrix*scalar;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn std::ostream& operator<<( std::ostream& os, const Matrix3<Type>& m )
// \brief Global output operator for 3x3 matrices.
//
// \param os Reference to the output stream.
// \param m Reference to a constant matrix object.
// \return Reference to the output stream.
*/
template< typename Type >
std::ostream& operator<<( std::ostream& os, const Matrix3<Type>& m )
{
   return os << " ( " << m[0] << " , " << m[1] << " , " << m[2] << " )\n"
             << " ( " << m[3] << " , " << m[4] << " , " << m[5] << " )\n"
             << " ( " << m[6] << " , " << m[7] << " , " << m[8] << " )\n";
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn bool isnan( const Matrix3<Type>& m )
// \brief Checks the given matrix for not-a-number elements.
//
// \param m The matrix to be checked for not-a-number elements.
// \return \a true if at least one element of the matrix is not-a-number, \a false otherwise.
*/
template< typename Type >
inline bool isnan( const Matrix3<Type>& m )
{
   if( std::isnan( m[0] ) || std::isnan( m[1] ) || std::isnan( m[2] ) ||
       std::isnan( m[3] ) || std::isnan( m[4] ) || std::isnan( m[5] ) ||
       std::isnan( m[6] ) || std::isnan( m[7] ) || std::isnan( m[8] ) )
      return true;
   else return false;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn const Matrix3<Type> abs( const Matrix3<Type>& m )
// \brief Returns a matrix containing the absolute values of each single element of \a m.
//
// \param m The integral input matrix.
// \return The absolute value of each single element of \a m.
//
// The \a abs function calculates the absolute value of each element of the input matrix
// \a m. This function can only be applied to matrices of integral data type. For floating
// point matrices, the pe::fabs( const Matrix3& ) function can be used.
*/
template< typename Type >
inline const Matrix3<Type> abs( const Matrix3<Type>& m )
{
   STATIC_ASSERT( std::numeric_limits<Type>::is_integer );
   return Matrix3<Type>( std::abs(m[0]), std::abs(m[1]), std::abs(m[2]),
                         std::abs(m[3]), std::abs(m[4]), std::abs(m[5]),
                         std::abs(m[6]), std::abs(m[7]), std::abs(m[8]) );
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn const Matrix3<Type> fabs( const Matrix3<Type>& m )
// \brief Returns a matrix containing the absolute values of each single element of \a m.
//
// \param m The floating point input matrix.
// \return The absolute value of each single element of \a m.
//
// The \a fabs function calculates the absolute value of each element of the input matrix
// \a m. This function can only be applied to  floating point matrices. For matrices of
// integral data type, the pe::abs( const Matrix3& ) function can be used.
*/
template< typename Type >
inline const Matrix3<Type> fabs( const Matrix3<Type>& m )
{
   STATIC_ASSERT( !std::numeric_limits<Type>::is_integer );
   return Matrix3<Type>( std::fabs(m[0]), std::fabs(m[1]), std::fabs(m[2]),
                         std::fabs(m[3]), std::fabs(m[4]), std::fabs(m[5]),
                         std::fabs(m[6]), std::fabs(m[7]), std::fabs(m[8]) );
}
//*************************************************************************************************

} // namespace walberla

#undef HIGH

#endif
