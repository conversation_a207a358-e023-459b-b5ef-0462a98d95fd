#ifndef TEST_HH
#define TEST_HH

#include  <boost/utility.hpp>

namespace ge
{
	// =============================================================================
	// class dynamic which is responsible for Rigid Dynamics simulation.
	// =============================================================================
	template<typename object_type, template<typename S> class Derived >
	struct Kinematics  : boost::noncopyable 
	{
		// typedefs :
		typedef typename object_type::real 		real ;
		typedef typename object_type::vector_type	vector_type ;
		typedef typename object_type::point_type	point_type ;
		
		// constructor 
		explicit Kinematics(object_type& obj ) : m_object (obj) {}
		inline void Update(const real& dt){
			(static_cast <Derived <object_type> const&> (*this) ).Update (dt) ;
		}
	private:
		object_type& m_object;
	};

	// =====================================================================
	// 
	// =====================================================================
	template < typename object_type>
	struct UniformRectilinear : Kinematics<object_type,UniformRectilinear >
	{
		// typedefs :
		typedef typename object_type::real 		real ;
		typedef typename object_type::vector_type	vector_type ;
		typedef typename object_type::point_type	point_type ;
		
		explicit UniformRectilinear(object_type& obj_, const vector_type& speed )
			: Kinematics < object_type , ge::UniformRectilinear > (obj_)
			, m_speed(speed){}
			
		inline void Update(const real& dt);	
	  private :
		const vector_type& m_speed;
	};

	// =====================================================================
	template < typename object_type>
	inline void UniformRectilinear<object_type>::Update
	  (const typename UniformRectilinear<object_type>::real& dt)
	{
		typedef typename UniformRectilinear<object_type>::vector_type vector_type;
		const vector_type move_to  = m_speed * dt ;
		this->m_object.GetCOM () += move_to;
		this->m_object.UpdateTrianglesMeshPositionOnlyTranslation (move_to);
		// moves the cells too 
		this->m_object.UpdateTheMappedInsideCells(move_to);
	}
	// =====================================================================
	
	
	
	
	
}// end namespace ge

#endif


