
#include  <iostream>

int main()
{
    const int NX=4;
    const int NY=2;

//     const double c[9][2] = { // particle velocities 
//             {0, 0}, // zero 
//             {1, 0}, {0, 1}, // east, north 
//             {-1, 0}, {0, -1}, // west, south 
//             {1, 1}, {-1, 1}, // north-east, north-west 
//             {-1, -1}, {1, -1} // south-west, south-east 
//     };


    
//     for (int i =0 ; i < N ; i ++)
//     {
//         std::cout << (i%2) + 1 << "\t" ;
// 
// 
//     }
    
//     for( int i = 0, j = 0;   j/2 < N ; ++i, j+=2 )
//     {
//         std::cout <<  i  <<  "  " << j/2 <<  "\n" ;
//     }
    
    for (int j =0 ;j < NY ; j ++)
    {
        int jj =  0.5*( ( (j+ 1 )% 2) + j -1 ) ; // test filter indice

        for (int i =0 ; i < NX ; i ++)
        {
                int ii =  0.5*( ( (i+ 1 )% 2) + i -1 ) ; // test filter indice
                // std::cout <<  ii  << "\t" ;
//                 if ( i % 2  &&  j % 2  )
                {
                    std::cout << "( ii, jj ):\t(" <<  ii  << "," <<  jj  
                    << ")\t Neigbohrs--> (i , i+1 , j , j+1)\t (" 
                              <<  ((i+ 1 )% 2) + i -1  << " , " << ((i+ 1 )% 2) + i   << " , " 
                              <<  ((j+ 1 )% 2) + j -1  << " , " << ((j+ 1 )% 2) + j   << ")\n" ;
                }
        }
    }
    
    
    std::cout <<"\n" ;
    
}

