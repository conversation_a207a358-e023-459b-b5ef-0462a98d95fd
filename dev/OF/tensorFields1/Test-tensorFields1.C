/*---------------------------------------------------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     |
    \\  /    A nd           | www.openfoam.com
     \\/     M anipulation  |
-------------------------------------------------------------------------------
    Copyright (C) 2011 OpenFOAM Foundation
    Copyright (C) 2019-2020 OpenCFD Ltd.
-------------------------------------------------------------------------------
License
    This file is part of OpenFOAM.

    OpenFOAM is free software: you can redistribute it and/or modify it
    under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    OpenFOAM is distributed in the hope that it will be useful, but WITHOUT
    ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
    FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
    for more details.

    You should have received a copy of the GNU General Public License
    along with OpenFOAM.  If not, see <http://www.gnu.org/licenses/>.

Application
    Test-tensorFields1

\*---------------------------------------------------------------------------*/

#include "tensorField.H"
#include "Random.H"
#include "fvCFD.H"

using namespace Foam;

vectorField randomVectorField(label size)
{
    Random rnd;

    vectorField vf(size);

    forAll(vf, i)
    {
        for (direction cmpt=0; cmpt < vector::nComponents; ++cmpt)
        {
            vf[i][cmpt] = rnd.position<label>(0, 100);
        }
    }

    return vf;
}

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

int main(int argc, char *argv[])
{
    // vectorField
    {
        Info<< nl << "vectorField" << nl;

        vectorField vf1(randomVectorField(4));
        //FixedList<scalarField, 3> cmpts(scalarField(vf1.size()));

	//volSymmTensorField  gradU = fvc::grad( vf1) ;
	
        //Info<< nl  << 
	//	" velocity grad tensor => " << 
	//	fvc::grad( vf1)  << nl;
	
	//volSymmTensorField D(symm(  fvc::grad( vf1)  )); 

        //Info<< nl  << " D tensor => " <<  symm(  fvc::grad( vf1)  )   << nl;

    }


    Info<< nl << "End\n" << nl;

    return 0;
}


// ************************************************************************* //
