#include "createRDeltaT.H"

Info<< "Reading field p\n" << endl;
volScalarField p
(
    IOobject
    (
        "p",
        runTime.timeName(),
        mesh,
        IOobject::MUST_READ,
        IOobject::AUTO_WRITE
    ),
    mesh
);

Info<< "Reading field U\n" << endl;
volVectorField U
(
    IOobject
    (
        "U",
        runTime.timeName(),
        mesh,
        IOobject::MUST_READ,
        IOobject::AUTO_WRITE
    ),
    mesh
);

dimensionedScalar unit("unit", dimensionSet(0,1,0,0,0,0,0),1.0);
volScalarField xx = mesh.C().component(0)/unit;
volScalarField yy = mesh.C().component(1)/unit;
volScalarField zz = mesh.C().component(2)/unit;
U.replace(0,2.0/::sqrt(3.0)*Foam::sin(xx)*Foam::cos(yy)*Foam::cos(zz));
U.replace(1,-2.0/::sqrt(3.0)*Foam::cos(xx)*Foam::sin(yy)*Foam::cos(zz));
U.replace(2,0.0);
U.write();



#include "createPhi.H"


label pRefCell = 0;
scalar pRefValue = 0.0;
setRefCell(p, pimple.dict(), pRefCell, pRefValue);
mesh.setFluxRequired(p.name());


singlePhaseTransportModel laminarTransport(U, phi);

autoPtr<incompressible::momentumTransportModel> turbulence
(
    incompressible::momentumTransportModel::New(U, phi, laminarTransport)
);

#include "createMRF.H"
#include "createFvModels.H"
#include "createFvConstraints.H"
