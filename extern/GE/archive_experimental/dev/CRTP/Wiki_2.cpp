// =============================================================================
// this code was copied from : http://en.wikipedia.org/wiki/Curiously_recurring_template_pattern
// 
//  basic idea for design the engine using CRTP pattern
// 
// 
// =============================================================================




// =============================================================================
// includes :
// =============================================================================
#include <iostream>

// =============================================================================
// Base class has a pure virtual function for cloning
// =============================================================================
class Shape {
public:
    virtual ~Shape() {}
    virtual Shape *clone() const = 0;
};
// =============================================================================
// This CRTP class implements clone() for Derived
// =============================================================================
template <typename T, template<typename S> class Derived> 
class Shape_CRTP: public Shape
{
public:
    inline Shape *clone() const {
        return new Derived<T>(static_cast<Derived<T> const&>(*this));
    }
};

// =============================================================================
// Every derived class inherits from Shape_CRTP instead of Shape
// =============================================================================
template<typename T>
class Square: public Shape_CRTP< T,Square >
{
    private :
	
  
};

// template<typename T>
// class Circle: public Shape_CRTP<Circle> {};


inline void PrintSizeOf(); // check the sizeof of these classes.

int main()
{
	PrintSizeOf();
	
	Square<int> *tmp = new Square<int> ;
	Shape *tmp2 = tmp->clone () ;

	Shape *m_square = new Square<int> ;
	return (0) ;
}

// =============================================================================
// add you need.
// =============================================================================
inline void PrintSizeOf()
{
	std::cout << "size of (Shape) : "  << sizeof (Shape)  << std::endl;
	std::cout << "size of (Square) : " << sizeof (Square<int>) << std::endl;
// 	std::cout << "size of (Shape_CRTP<Square> ) : " << sizeof (Shape_CRTP< Square <int> > ) << std::endl;
	
}
/*
 * 
 * Some resluts

size of (Shape) : 8
size of (Square) : 8
size of (Shape_CRTP<Square> ) : 8



*/



