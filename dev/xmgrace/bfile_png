#Obligatory descriptive comment
s0 line color 3
s1 length s0.length
s1.x = s0.x

s1.y = (0.00001/(0.2) )*x* (48- s0.x)

s1 line color 1 
s1 symbol 2 
s1 line type 0 
s1 symbol size 0.5
s1 symbol color 1 


s0 legend "LBM"
s1 legend "Exact solution"

title "LBM simulation"
xaxis label "x ( - )"
yaxis label "Velocity ( -  )"
autoscale 

PRINT TO "output.png"
HARDCOPY DEVICE "PNG"
PAGE SIZE 2560, 2048
DEVICE "PNG" FONT ANTIALIASING on
DEVICE "PNG" OP "transparent:off"
DEVICE "PNG" OP "compression:9"
PRINT


