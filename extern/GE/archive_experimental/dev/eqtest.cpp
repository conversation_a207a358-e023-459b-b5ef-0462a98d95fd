#include <iostream>
#include <string>

template <class T>
struct _cmp_ // do not edit
{
	// equality
    	inline static bool eq(T const& a,T const& b ) {return a == b ;}
   	// less than 
	inline static bool lt(T const& a,T const& b ) {return a <  b ;}
 
	// not equality
	inline static bool nq(T const& a,T const& b ) {return a != b ;}
	
	// greater than  
    	inline static bool gt(T const& a,T const& b ) {return a >  b ;}

	// greater than or equal to   
    	inline static bool ge(T const& a,T const& b ) {return a >= b ;}
   
	// less than or equal to
	inline static bool le(T const& a,T const& b ) {return a <= b ;}    
};


template <class T, class C = _cmp_<T> >
struct SortComp // :public unuary_function <> 
{
  //!@return 0 in case of quality,-1 is "a" is less than "b",
  //!        and +1 is "a" is great than "b"
  inline int operator ()(T const& a,T const& b)
  {
	if ( C::eq (a, b) )
	    return 0 ;
	else return ( C::lt  (a, b) ) ? -1 : 1  ;
  }
};


int main()
{
  
  SortComp < std::string > comparator ;

  std::cout << "compare<string,sting> () -> "      <<
		comparator ("chesdsadf", "cherif") << 
		std::endl ;
	    
  return  (0) ;
}


// template <class T, class C = Cmp<T> >
// int compare(const T & a , const T & b)
// {
//     for (int i = 0; i < str1.length() && i <str2.length() ; ++i )
//       
// 	if (! C::eq (str1[i], str2[i] ) )
// 	      return C::lt  (str1[i], str2[i] ) ? -1 : 1  ;
// 
// 	return  ( str1.length() - i <str2.length()  );
// }





