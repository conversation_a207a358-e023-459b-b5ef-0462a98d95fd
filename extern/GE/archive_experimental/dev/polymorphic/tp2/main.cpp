#include <iostream>
#include <iterator>
#include <vector>


#include <algorithm> /// sort 

#include "Consumer.h"
#include "Poly.h"

#include "Binary_deref_function.h"

using namespace std ;
using namespace ObjPoly;

template<class T> 
bool compare (poly<T> const& p1, poly<T> const& p2)
{
	return *p1 < *p2;
}


int main()
{

   std::vector< poly <customer > >  vec ;

   vec.push_back ( Company("LSS") )  ;
   vec.push_back ( Person("in") )  ;
   vec.push_back ( Person("<PERSON>eri<PERSON>") )  ;


   for (int i= 0 ; i < vec.size() ; ++ i)
      std::cout << vec[i]->name() << std::endl ;


   
   std::sort (vec.begin(), vec.end() ) ;
   
   std::cout <<"_______________\n After sorting \n";  
     for (int i= 0 ; i < vec.size() ; ++ i)
      std::cout << vec[i]->name() << std::endl ;
   
   
     
//      std::sort(vec.begin(), vec.end(),binary_deref_fct<poly>(compare));

     
   //std::ostream_iterator<customer>(std::cout, "\n") ;


   return 0 ;
}

    