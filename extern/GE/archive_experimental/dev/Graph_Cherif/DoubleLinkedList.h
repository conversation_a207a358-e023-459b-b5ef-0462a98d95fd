#include <iostream>
#include <list>
#include "Timing.h"



template <typename T>
class double_linked
{
   private:
	struct node;
	node* head; // first
	node* tail; // last

   public:

	double_linked() : head( NULL ), tail ( NULL ) {}

	template<int N>
	double_linked( T (&arr) [N]) : head( NULL ), tail ( NULL ){
		for( int i(0); i != N; ++i)
			push_back(arr[i]);
	}

	inline bool empty() const ;

	inline operator bool() const;

	inline void push_back(T);
	inline void push_front(T);
	inline T pop_back();
	inline T pop_front();

	~double_linked(){
		while(head)
		{
			node*    temp(head);
			head   = head->next;
			delete   temp;
		}
	}
};
  
  
  
  
  
	template <typename T>
	struct double_linked<T>::node
	{
		T data;
		node* prev;
		node* next;
		node(T t, node* p, node* n) : data(t), prev(p), next(n) {}
	};
  
	
	template <typename T>
	inline bool double_linked<T>::empty() const { return ( !head || !tail ); }


	template <typename T>
	inline double_linked<T>::operator bool() const {return !empty();}
	
	
// friend std::ostream operator<<(std::ostream & output,const double_linked&dL)
// {
//       output << "Link list Element :) |" << 
//        
// //        while( dL )    
// // 		output<< dL.pop_front() << " ";
//       output << " |"<<std::endl;
//       return output;
// 
//       }

       

      template <typename T>
      void double_linked<T>::push_back(T data)
      {
      tail = new node(data, tail, NULL);
      if( tail->prev )
	tail->prev->next = tail;
	if( empty() )
        head = tail;
      }

      template <typename T>

      void double_linked<T>::push_front(T data)
      {
	head = new node(data, NULL, head);
	if( head->next )
	head->next->prev = head;
	if( empty() )
	tail = head;
      }

       

      template<typename T>
      T double_linked<T>::pop_back()
      {
	if( empty() ) 
		  throw("double_linked : list empty");
	node* temp(tail);
	T data( tail->data );
	tail = tail->prev ;
	if( tail )
		  tail->next = NULL;
	else
		  head = NULL ;
	delete temp;
	return data;
      }

      template<typename T>
      T double_linked<T>::pop_front()
      {
	if( empty() )
		    throw("double_linked : list empty");
	node* temp(head);
	T data( head->data );
	head = head->next ;
	if( head )
		  head->prev = NULL;
	else
		  tail = NULL;
	delete temp;
	return data;
      }