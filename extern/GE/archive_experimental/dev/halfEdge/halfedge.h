#ifndef HALFEDGE_HH // -*- C++ -*-
#define HALFEDGE_HH



/************************************************************************

  Half-Edge primitive for representing manifold subdivisions.

  Copyright (C) 2004 <PERSON>.

 ************************************************************************/

#include <cassert>
#include <iostream>

template<class Vetex_type, class Face_type = int>
class Halfedge
{
public:
	typedef Halfedge*  Halfedge_ptr;
	typedef Vetex_type Vertex;
	typedef Face_type Face;
	enum   { NoFace =-1 };

private: // presentations 
	Halfedge_ptr	he_next, he_prev, he_oppos;
	Vertex		incd_vertex; // incident vertex 
	Face		lface; // ID for the face

public:
	// =====================================================================
	// default constructor
	// =====================================================================
	Halfedge(Vertex v, Face f=NoFace)
	  :he_next(NULL), 
	  he_prev(NULL), 
	  he_oppos(NULL), 
	  incd_vertex(v), 
	  lface(f) {}


	// =====================================================================
	// destructor 
	// =====================================================================
	~Halfedge() { he_next=he_prev=he_oppos=NULL; }



	// =====================================================================
	// Get Functions 
	// =====================================================================
	// @{
	inline Halfedge_ptr he_next() const { return he_next; }
	inline Halfedge_ptr he_prev() const { return he_prev; }
	inline Halfedge_ptr he_oppos()const { return he_oppos; }

	// Get he_opposit to each of the he_ constitues the courent he 
	inline Halfedge_ptr Onext() const { return he_prev()->he_oppos(); }
	inline Halfedge_ptr Oprev() const { return he_oppos()->he_next(); }
	inline Halfedge_ptr Dnext() const { return he_oppos()->he_prev(); }
	inline Halfedge_ptr Dprev() const { return he_next()->he_oppos(); }
	inline Halfedge_ptr Rnext() const { return Oprev()->he_oppos(); }
	inline Halfedge_ptr Rprev() const { return Dnext()->he_oppos(); }

	
	// access to incident vertexm, const vertion and modiefed access also
	inline Vertex& incd_vertex()	{ return incd_vertex; }
	inline Vertex  incd_vertex()const{ return incd_vertex; }

	//
	// we use he_next here, rather than he_oppos because he_next will
	// always be defined, whereas he_oppos may not be.
	inline Vertex& Dest()	   { return he_next()->incd_vertex(); }
	inline Vertex  Dest() const { return he_next()->incd_vertex(); }

	// Halfedges are oriented from e[0] -> e[1]
	inline Vertex&	operator[](int i)	{ return i ? Dest() : incd_vertex(); }
	inline Vertex	operator[](int i) const { return i ? Dest() : incd_vertex(); }

	// Each edge maintains an ID for the face on its left
	inline Face& Lface()	   { return lface; }
	inline Face  Lface() const { return lface; }
	// @}
public:
	// =====================================================================
	// 
	// =====================================================================
	template<class Polygon>
	static Halfedge_ptr createFace_type(Polygon& P, Face f = NoFace)
	{
		const int N = P.size();
		std::vector<Halfedge_ptr> edges(N);

		for(int i=0; i<N;   ++i)  edges[i] = new Halfedge(P[i], f);
		for(int i=0; i<N-1; ++i)  edges[i]->he_next = edges[i+1];
		for(int i=1; i<N;   ++i)  edges[i]->he_prev = edges[i-1];

		edges[N-1]->he_next = edges[0]  ;
		edges[0]->he_prev   = edges[N-1];
		return edges[0];
	}
	
	
	// =====================================================================
	// 
	// =====================================================================
	static void paste(Halfedge_ptr e1, Halfedge_ptr e2)
	{
		assert(e1->he_oppos() == NULL);
		assert(e2->he_oppos() == NULL);
		assert(e1->incd_vertex() == e2->Dest());
		assert(e2->incd_vertex() == e1->Dest());

		e1->he_oppos = e2;
		e2->he_oppos = e1;
	}
	
	// =====================================================================
	// 
	// =====================================================================
	static void cut(Halfedge_ptr e)
	{
		if( e->he_oppos )
		{
			e->he_oppos->he_oppos = NULL;
			e->he_oppos = NULL;
		}
	}
	// =====================================================================
};




	template<class T>
	inline std::ostream& operator<<(std::ostream& out, const Halfedge<T>& e)
	{
		  return out << e.incd_vertex() << " " << e.Dest(); 
	}

// HALFEDGE_INCLUDED
#endif
