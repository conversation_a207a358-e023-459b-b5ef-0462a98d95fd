#ifndef  CACHEALLIGN_HH
#define  CACHEALLIGN_HH



//========================================================================================
/*!@brief Cach allignemnt utility definition


   ensemble of functions for alignemnt memory, needed for fast mathematic functon 
   based on SSE vectorization
   
   @link http://msdn.microsoft.com/en-us/library/83ythb65.aspx#vclrfalignexamples
   We care about alignment for a single reason: Performance.

In modern hardware, memory can only be accessed on particular boundaries. Trying to read 
data from an unaligned memory address can result in two reads from main memory plus some 
logic to combine the data and present it to the user.

 For example, Intel's and AMD's SSE vector instructions operate on data aligned on 16-byte boundaries,
 so it is the programmer's responsibility to align the data properly before feeding it to the vector units.

In general, the more you interface with the hardware directly, the more careful you will 
have to be about the alignment of your data.   
   
*/

//========================================================================================


//========================================================================================
//include 
//========================================================================================
#include <iostream>
#include <stdio.h>
#include <assert.h>
#include <stdint.h> // uintptr_t

#if __MMX__ 
    #include <xmmintrin.h>
//     std::cout<<" NOTE::  SSE1 is activate \n"; 
#endif

#ifdef __SSE__ 
    #include <xmmintrin.h>
     std::cout<<" NOTE::  SSE1 is activate \n";
#endif     
     
#ifdef __SSE2__
    #include <emmintrin.h>
    std::cout<<" NOTE::  SSE2 is activate \n"; 
#endif

#ifdef __SSE3__
    #include <pmmintrin.h>
    std::cout<<" NOTE::  SSE3 is activate \n";
#endif


//========================================================================================
//! help functions
//========================================================================================
#define UINT_PTR uintptr_t
#define assert_16_byte_aligned(ptr)  	assert( (((UINT_PTR)(ptr))&15) == 0  )
#define ALIGN16( x )            	__declspec(align(32)) x
#define ALIGN4_INIT1( X, I )    	ALIGN16( static X[4] = { I, I, I, I } )


//========================================================================================
/*!@brief why? For example, when using SSE2 instructions, you may want to align your operands on a 16-byte boundary
*
* if you define a structure whose size is less than 32 bytes, you may want
* to align it to 32 bytes to ensure that objects of that structure type are efficiently cached
* @note  requirement !!! CACHE_LINE should be a multiple of 8 
* @example  #define CACHE_LINE  32 
*   
*/
//========================================================================================
#if defined (__INTEL_COMPILER) // also for VS(windows)
 
  #define TLS  __declspec(thread)
  
      #if defined (CACHE_LINE)
	  #define CACHE_ALIGN(L)   __declspec (align(CACHE_LINE)) 
      #else
	  #define CACHE_ALIGN(L)   __declspec (align(L))  
      #endif
      
#elif defined (__GNUG__)

  #define TLS __thread
  
   #if defined (CACHE_LINE)
      #define CACHE_ALIGN(L)  __attribute__((aligned(CACHE_LINE))) 
   #else
	  #define CACHE_ALIGN(L) __attribute__((aligned(L)))  
   #endif
#endif


/*!
    @example : \n

    struct CACHE_ALIGN(16) S1 { // cache align all instances of S1  \n
      int a, b, c, d;  \n
    }; \n
     
    struct S1 s1;   // s1 is 32-byte cache aligned  \n
    In the following example, sizeof(struct S2) will return 16, which is exactly the sum of the member sizes, 
    because that happens to be a multiple of the largest alignment requirement (a multiple of 8).
    
    
      	
    Look like a 'typo' on page two: "For example, the following code checks if some data is aligned on a 16-byte boundary: ((uintptr_t)&data & 0x1F)".

    To align on 16 byte boundary would be: ((uintptr_t)&data & 0x0F) == 0.
    
    
    #define assert_16_byte_aligned(data)  	assert( ((uintptr_t)&data & 0x0F) == 0 ) // cherif

*/



//! 
//! @brief TLS <==> Thread-local-storage (openMP)
//!   if you define an varaible using TLS macro, it will be local to the thread
//!   It's Not Always Nice To Share 


#endif

