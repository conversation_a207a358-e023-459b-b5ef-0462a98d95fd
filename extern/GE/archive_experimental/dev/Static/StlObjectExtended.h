// =============================================================================
/*!	\file  
// 	\brief 
// 	\author Cherif <PERSON>
// 	\email <EMAIL>
*/
// =============================================================================

#ifndef STLOBJECTEXTENDED_HH
#define STLOBJECTEXTENDED_HH

// =============================================================================
// includes
// =============================================================================
// #include "../util/FaceDataListContainer.h"
#include "../misc/FileDef.h"
#include "../util/ComputeInertia.h"// moment of inertia, //#include "../gmath/GMatrix33.h"
#include "../util/ComputeAreaUtillities.h"// compute area
#include <iomanip>

#include "../misc/Logger.h"
// =============================================================================





// TODO implement destructor
// =============================================================================
namespace ge
{
 template<typename config > // polygons_list
   class StlMeshTypeExtended
   {
    public:
	// =====================================================================
	// typedefs
	// =====================================================================
	typedef typename config::faces_conatiner_type	polygons_list ;
	typedef typename config::triangle_type		triangle_type;
	typedef typename polygons_list::iterator	iterator    ;
	typedef typename polygons_list::const_iterator	const_iterator ;

	typedef typename config::real			real   ;
	typedef typename config::point_type		point_type  ;
	typedef typename config::vector_type		vector_type ;
	typedef typename config::edge_type		edge_type;
	// additionally only for cut the mesh !!!
	typedef typename config::ray_type		ray_type ;
	typedef typename config::plane_type		plane_type ;
	typedef typename config::aabb_type		aabb_type ;	

	// =====================================================================
	typedef StlMeshTypeExtended<config> mesh_type ;

    public:
	// =====================================================================
	// constructor:
	// =====================================================================
	explicit StlMeshTypeExtended(const std::string& fn, const real& density_= 1.0, 
				const bool& object_dynam = false );


	// =====================================================================
	// GetFunctions:
	// =====================================================================
	// @{
	inline const std::string GetFileName()const {return m_file_name;}
	inline const bool& IsObjectIsDynamic()const {return m_dynamic ;}

	inline polygons_list&       GetPolyListMesh()	   {return m_mesh;}
	inline const polygons_list& GetPolyListMesh()const {return m_mesh;}

	inline bool& HaveBeenPreProc(){return m_IfHaveBeenPrepossed;}
	inline const bool& HaveBeenPreProc()const{return m_IfHaveBeenPrepossed;}

	// min max
	inline point_type&	   AbbMin()	 	{return m_pMin ;}
	inline const point_type&   AbbMin() const	{return m_pMin ;}
	inline point_type&	   AbbMax()	 	{return m_pMax ;}
	inline const point_type&   AbbMax() const	{return m_pMax ;}
	inline const point_type    AbbCenter()const	{return (m_pMax+m_pMin)*0.5 ;}
	
	inline bool IsInsideObjectBoundingBox(const point_type& )const;
	inline bool IsInsideObjectExtraBoundingBox(const point_type& )const;

	// length 
	inline real&	GetLengthX() {return m_length_x;}
	inline real&	GetLengthY() {return m_length_y;}
	inline real&	GetLengthZ() {return m_length_z;}
	inline const real& GetLengthX()const{return m_length_x;}
	inline const real& GetLengthY()const{return m_length_y;}
	inline const real& GetLengthZ()const{return m_length_z;}
	
	// number of triangles/faces
	inline const std::size_t size()const{return m_mesh.size();}
	
	inline Type&  GetType()	{return m_type;}
	inline const Type&  GetType()const{return m_type;}
	
	inline std::string GetHeaderFile()	{return m_header;}
	inline std::string GetHeaderFile()const	{return m_header;}

	
	// supposed that we have const density
	inline const void   SetDensity(const real& density_){ m_density = density_;}
	inline const real& GetDensity()const {return m_density ;}

	// the center of mass (COM)
	inline point_type&		GetCentroid()	   {return m_centroid;}
	inline const point_type&	GetCentroid()const {return m_centroid;}
	
	
	// note: you can get the total area :
	// a) call  ComputeArea ( (*this) )
	inline real&	   GetTotalArea(){return  m_area;} // the total area 
	inline const real& GetTotalArea()const{return  m_area;} // the total area 	
	
	
	// note: you can get the total volume :
	// a) call  ComputeVolume( (*this) )
	// b) optimized version by call the function :ComputeObjectInertiaMomentsAndCOM( (*this) ) once, 
	// which compute moment inertia, center of mass and total volume at once, 
	//
	
	// the total volume
	inline real& GetVolume()     {return m_volume;}
	inline const real& GetVolume()const{return m_volume;}
	
	// the total mass
	inline real& GetTotalMass() { return m_totalmass ;}
	inline const real& GetTotalMass()const {return m_totalmass ;}
	
	// linear velocity V
	inline const vector_type& GetVelocity()const {return m_velocity;} 
	inline const vector_type& GetAngularVelocity()const {return m_angelvelocity;}
	
	
	// Angular velocity w
	inline vector_type& GetVelocity(){return m_velocity;}
	inline vector_type& GetAngularVelocity(){return m_angelvelocity;}
	
	// inertia tensor 
	// note: the two functions bellow are setup only once, because'
	// m_IO a, and its inverse remain constant over the simulation,
	// 
	inline void SetI0(const GMatrix33<real>& I0_in){ m_I0 = I0_in ;}
	inline void SetI0_inverse(){ m_I0_inverse = *InverseOf(m_I0); }
	
	// return const and only const.
	inline const GMatrix33<real>& I0()const	    {return m_I0 ;}
	inline const GMatrix33<real>& I0_inv()const {return m_I0_inverse ;}
	// @}
	// =====================================================================


	// cut this Object with respect to given aabb, return only the  triangles-list
	// which are inside this aabb 
	// inline polygons_list& CutWithRespectTo(const aabb_type& abb ) ; // this 
	// inline const polygons_list& CutWithRespectTo(const aabb_type& ab, polygons_list& polgones) const ;

  private :

	// =====================================================================
	// representation  :
	// =====================================================================
	// a) arguments :
	std::string	m_file_name ;
	std::string	m_header;
	Type		m_type ; 	// binary or ascii
	real		m_density ; // supposed that we have const density
	bool 		m_IfHaveBeenPrepossed ; // tell if the object have been 
						// pre-proceeded or not.
	bool 	     	m_dynamic ;// specify if the object is dynamic or fix(static)
	// =====================================================================
	
	// mesh representation :
	polygons_list 	m_mesh ;
	// =====================================================================
	
	//b) computed member-data-values (from the geometry) ===================
	point_type 	m_pMin ;
	point_type 	m_pMax ;
	real		m_length_x, m_length_y, m_length_z ;
	real		m_area 	  ; // the total area 
	real		m_volume  ; // the total volume
	real		m_totalmass;// the total mass, computed based on the density and the volume
			    // you haven't specified as argument	
	

	// if the object is dynamic
	point_type	m_centroid; // the center of mass (COM)
	vector_type 	m_velocity     ; // linear velocity V
	vector_type 	m_angelvelocity; // angular  velocity W
	// =====================================================================

     public:
	//The following are moments of inertia/com, assume a constant density throughout the object.
	// they are constant over the simulation (Io and Io_in).
	GMatrix33<real>  m_I0 ;  	// Io_body -> inertia tensor
	GMatrix33<real>  m_I0_inverse ;	// Io_body_inverse -> inv inertia tensor
	// =====================================================================
	
     public:

	inline void GeomInfo (); // only for debugging, todelete alter
	inline void DynmaicInfo() const;

   private:
        // =====================================================================
        StlMeshTypeExtended(const StlMeshTypeExtended&); 		// Not implemented.
        void operator=(const StlMeshTypeExtended&);    // Not implemented
};





	// =====================================================================
	// constructor:
	// =====================================================================
	template<typename polygons_list_config >
	StlMeshTypeExtended<polygons_list_config>::StlMeshTypeExtended(
		const std::string& fn, 
		const typename StlMeshTypeExtended<polygons_list_config>::real& density_, 
		const bool& object_dynam )
	{
	      m_file_name = fn ;
	      this -> SetDensity (density_) ;
	      this -> m_dynamic = object_dynam ;
	      // by default the object is static 
	      // linear velocity V [m/s]
	      m_velocity.x() = 0.0 ;
	      m_velocity.y() = 0.0 ;
	      m_velocity.z() = 0.0 ;
	      // angular  velocity W [rad/s]
	      m_angelvelocity.x() = 0.0; 
	      m_angelvelocity.y() = 0.0;
	      m_angelvelocity.z() = 0.0;
	      m_IfHaveBeenPrepossed = false ;
	}


	// =====================================================================
	// 
	// =====================================================================
	template<typename polygons_list_config >
	inline void StlMeshTypeExtended<polygons_list_config>::GeomInfo ()
	{
		#ifndef GE_USE_MPI
		std::cout<<"// =============================================================================\n";
		std::cout<<"//	Geometry info \n";
		std::cout<<"// =============================================================================\n";
		std::cout<<"	Number of triangles:		"<< m_mesh.size()<<"\n";
		std::cout<<"	m_pMin:		 		"<< m_pMin << "\n";
		std::cout<<"	m_pMax:				"<< m_pMax << "\n";
		std::cout<<"	object->boundingBoxCenter:	"<< this->AbbCenter() << "\n";
		std::cout<<"	x_lenght:			"<< m_length_x << std::endl;// FIXEME
		std::cout<<"	y_lenght:			"<< m_length_y << std::endl;
		std::cout<<"	z_lenght:			"<< m_length_z << std::endl;
		std::cout<<"	total area:			"<< ComputeArea  ( (*this) ) << " [m^2]"<< std::endl;
		
		if ( ! IsObjectIsDynamic()  && ! m_IfHaveBeenPrepossed )
		{
			  ComputeObjectInertiaMomentsAndCOM (*this);
			  std::cout<<"	static object characteristic:\n " ;
			  std::cout<<"	volume:				"		 << this->GetVolume()  << " [m^3]\n";
			  std::cout<< setprecision (6)<<"	c.o.m. (center of mass): 	"<< this->GetCentroid()<< "\n";
			  std::cout<< fixed ;
			  std::cout<<"	the moment of inertia tensor I0/c.o.m  with origin at c.o.m. (const) :\n";
			  std::cout<< this->I0()     << "\n" ;
			  std::cout<<"	Inverse inertia tensor I0_inv/c.o.m with origin at c.o.m. (const) :\n";
			  std::cout<< this->I0_inv() << "\n" ;
		}	
			#ifdef DEBUG
			  std::cout<<"	First triangle\n";
			  std::cout<<  *m_mesh.begin() << "\n";
			#endif
		
		#else // parallel case 
		Log("// =============================================================================")
		Log("//	Geometry info ")
		Log("// =============================================================================")
		Log("	Number of triangles: 		" , m_mesh.size() )
		Log("	m_pMin:		 		" , m_pMin )
		Log("	m_pMax:				" , m_pMax )
		Log("	object->boundingBoxCenter:	" , this->AbbCenter() )
		Log("	x_lenght:			" , m_length_x  )// FIXEME
		Log("	y_lenght:			" , m_length_y  )
		Log("	z_lenght:			" , m_length_z  )
		Log("	total area [m^2]:		" , ComputeArea  ( (*this) ) /* " [m^2]" */ )
		
		if ( ! IsObjectIsDynamic()  && ! m_IfHaveBeenPrepossed )
		{
			  ComputeObjectInertiaMomentsAndCOM (*this);
			  Log("	static object characteristic:" )
			  Log ("	volume[m^3]:			" ,  this->GetVolume() ) //  << " [m^3]\n";
			  Log ("	c.o.m. (center of mass): 	" ,  this->GetCentroid( )   )
			  Log("	the moment of inertia tensor I0/c.o.m  with origin at c.o.m. (const) :")
			  Log( boost::lexical_cast<std::string> ( this->I0() )  )
			  Log("	Inverse inertia tensor I0_inv/c.o.m with origin at c.o.m. (const) :")
			  Log( boost::lexical_cast<std::string> ( this->I0_inv() ) ) 
		}
		#ifdef DEBUG
		  Log("	First triangle")
		  Log( boost::lexical_cast<std::string> (*m_mesh.begin()    ) )
		#endif	
		Log("// =============================================================================")
		#endif
	
	}
	// =====================================================================







	// =====================================================================
	// 
	// =====================================================================      
	template<typename polygons_list_config >
	inline void StlMeshTypeExtended<polygons_list_config>::DynmaicInfo()const
	{
		#ifndef GE_USE_MPI
		std::cout<<"// =============================================================================\n";
		std::cout<<"//	more object characteristics -> for dynamic simulation\n";
		std::cout<<"// =============================================================================\n";
		std::cout<<"	volume:				"		 << this->GetVolume()     << " [m^3]\n";
		std::cout<<"	mass:				"		 << this->GetTotalMass()  << " [kg]\n";
		std::cout<<"	density:			"		 << this->GetDensity()    << " [kg/m^3]\n";
		std::cout<< setprecision (6)<<"	c.o.m. (center of mass): 	"<< this->GetCentroid()<< std::endl;
		std::cout<< fixed ;
		std::cout<<"	I0/c.o.m (Inertia tensor (with respect to the c.o.m). :\n";
		std::cout<< this->I0() 		<< "\n";
		std::cout<<"	I0_inv/c.o.m (inverse inertia tensor with origin at c.o.m.) :\n";
		std::cout<< this->I0_inv() 	<< "\n";
		std::cout<<"// =============================================================================\n";
		#else
		Log("// =============================================================================")
		Log("//	more object characteristics -> for dynamic simulation")
		Log("// =============================================================================")
		Log("	volume [m^3]:				"		, this->GetVolume()     ) //<< " [m^3]\n";
		Log("	mass [kg]:				"		, this->GetTotalMass()  ) //<< " [kg]\n";
		Log("	density [kg/m^3]:			"		, this->GetDensity()    ) //<< " [kg/m^3]\n";
		Log("	I0/c.o.m (Inertia tensor (with respect to the c.o.m). :")
		Log(boost::lexical_cast<std::string> ( this->I0()     )) 
		Log("	I0_inv/c.o.m (inverse inertia tensor with origin at c.o.m.) :")
		Log(boost::lexical_cast<std::string> (this->I0_inv()  )) 
		Log("// =============================================================================")
		#endif
	}
	// =====================================================================
	
	
	



	// =====================================================================
	// 
	// =====================================================================
	template<typename polygons_list_config >
	inline bool StlMeshTypeExtended<polygons_list_config>::IsInsideObjectBoundingBox(const typename StlMeshTypeExtended<polygons_list_config>::point_type& P)const
	{
		// check the overlap in each axis. 
		if (	( P.X()  >= m_pMin .X()  ) && ( P.X() <= m_pMax .X()  ) &&
			( P.Y()  >= m_pMin .Y()  ) && ( P.Y() <= m_pMax .Y()  ) &&
			( P.Z()  >= m_pMin .Z()  ) && ( P.Z() <= m_pMax .Z()  )
		      )
		{
		      return true ;
		}
		else {
		      return false ;  
		}
	}
	// =====================================================================
	
	
	
	

	// =====================================================================
	// 
	// =====================================================================
	// Extra Bounding box mean  (m_pMin and m_pMax ) -+ 2 
	template<typename polygons_list_config >
	inline bool StlMeshTypeExtended<polygons_list_config>::IsInsideObjectExtraBoundingBox(const typename StlMeshTypeExtended<polygons_list_config>::point_type& P)const
	{
		// check the overlap in each axis, m_pMin
		if (	( P.X()  >= (m_pMin .X() - 2. )  ) && ( P.X() <= ( m_pMax .X() + 2. ) ) &&
			( P.Y()  >= (m_pMin .Y() - 2. )  ) && ( P.Y() <= ( m_pMax .Y() + 2. ) ) &&
			( P.Z()  >= (m_pMin .Z() - 2. )  ) && ( P.Z() <= ( m_pMax .Z() + 2. ) )
		      )
		{  
		  return true ;
		}
		else {
		       return false ;  
		}
	}
	// =====================================================================


}


#endif
