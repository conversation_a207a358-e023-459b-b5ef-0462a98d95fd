//=================================================================================================
/*!
//  \file TurbulenceModelFactory.cpp
//  \brief Factory implementation for creating turbulence models
*/
//=================================================================================================

#include "TurbulenceModel.h"
#include "models/SmagorinskyModel.h"
#include "models/DynamicSmagorinskyModel.h"
#include "models/WALEModel.h"
#include "models/VremanModel.h"
#include "../Logging.h"
#include <algorithm>
#include <cctype>

namespace walberla {
namespace turbulence {

std::unique_ptr<TurbulenceModel> TurbulenceModelFactory::create(
    TurbulenceModel::ModelType type,
    const SimData& sim)
{
    std::unique_ptr<TurbulenceModel> model;
    
    switch(type)
    {
        case TurbulenceModel::SMAGORINSKY:
        {
            if(sim.compressible) {
                model = std::make_unique<CompressibleSmagorinskyModel>(sim.csmag);
            } else {
                model = std::make_unique<SmagorinskyModel>(sim.csmag);
            }
            break;
        }
        
        case TurbulenceModel::DYNAMIC_SMAGORINSKY:
        {
            model = std::make_unique<DynamicSmagorinskyModel>();
            break;
        }
        
        case TurbulenceModel::WALE:
        {
            model = std::make_unique<WALEModel>();
            break;
        }
        
        case TurbulenceModel::VREMAN:
        {
            if(sim.compressible) {
                model = std::make_unique<CompressibleVremanModel>();
            } else {
                model = std::make_unique<VremanModel>();
            }
            break;
        }
        
        case TurbulenceModel::MIXED_SCALE:
        {
            LOG_ERROR("Mixed scale model not yet implemented");
            return nullptr;
        }
        
        case TurbulenceModel::NONE:
        default:
        {
            LOG_WARNING("No turbulence model selected");
            return nullptr;
        }
    }
    
    // Initialize the model with simulation parameters
    if(model) {
        model->initialize(sim);
        
        // Set strain rate method based on simulation preferences
        if(sim.useFiniteDifference) {
            model->setStrainRateMethod(TurbulenceModel::FINITE_DIFF);
        } else {
            model->setStrainRateMethod(TurbulenceModel::NON_EQUILIBRIUM);
        }
    }
    
    return model;
}

std::unique_ptr<TurbulenceModel> TurbulenceModelFactory::create(
    const std::string& modelName,
    const SimData& sim)
{
    // Convert to lowercase for case-insensitive comparison
    std::string lowerName = modelName;
    std::transform(lowerName.begin(), lowerName.end(), lowerName.begin(),
                   [](unsigned char c){ return std::tolower(c); });
    
    TurbulenceModel::ModelType type = TurbulenceModel::NONE;
    
    if(lowerName == "smagorinsky" || lowerName == "smag") {
        type = TurbulenceModel::SMAGORINSKY;
    }
    else if(lowerName == "dynamic" || lowerName == "dynamic_smagorinsky" || 
            lowerName == "dynamicsmagorinsky") {
        type = TurbulenceModel::DYNAMIC_SMAGORINSKY;
    }
    else if(lowerName == "wale") {
        type = TurbulenceModel::WALE;
    }
    else if(lowerName == "vreman") {
        type = TurbulenceModel::VREMAN;
    }
    else if(lowerName == "mixed" || lowerName == "mixed_scale") {
        type = TurbulenceModel::MIXED_SCALE;
    }
    else if(lowerName == "none" || lowerName == "laminar") {
        type = TurbulenceModel::NONE;
    }
    else {
        LOG_ERROR("Unknown turbulence model: " << modelName);
        LOG_INFO("Available models: Smagorinsky, Dynamic_Smagorinsky, WALE, Vreman, None");
        return nullptr;
    }
    
    return create(type, sim);
}

} // namespace turbulence
} // namespace walberla