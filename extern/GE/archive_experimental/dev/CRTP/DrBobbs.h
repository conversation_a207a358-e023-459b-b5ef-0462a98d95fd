template<typename T>
class AbstractStack {
public:
	typedef Item T;
	virtual ~AbstractStack() { }
	virtual void Push(std::auto_ptr<Item> x) = 0;
	virtual std::auto_ptr<Item> Pop() = 0;
	virtual bool IsEmpty() = 0;
};

// Listing 2 ===================================================================

template<typename T>
class ConcreteStack : AbstractStack
{
public:
	~ConcreteStack() { while (!IsEmpty()) Pop(); }
	// =====================================================================
	/*inline*/ void Push(std::auto_ptr<Item> x){m.push_back(x.release());}
	// =====================================================================
	/*inline*/ std::auto_ptr<Item> Pop()
	{
		std::auto_ptr<Item> x = m.top();
		m.pop_back();
		return m;
	}
	// =====================================================================
	/*inline*/ bool IsEmpty() { return m.IsEmpty(); }
private:
	std::vector<Item*> m;
};

// Listing 3 ===================================================================

template<typename T, int N>
class FixedStack : AbstractStack
{
public:
	// =====================================================================
	// construct&destructor
	// =====================================================================
	FixedStack() : cur(0) { }
	~FixedStack() { while (!IsEmpty()) Pop(); }
	// =====================================================================
	inline void Push(std::auto_ptr<Item> x) { m.push_back(x.release()); }
	// =====================================================================
	inline std::auto_ptr<Item> Pop()
	{
		assert(cur > 0); 
		std::auto_ptr<Item> x = m[cur-1];
		m.pop_back();
		return m;
	}
	// =====================================================================
	inline bool IsEmpty() { return cur == 0; }
private:
	int cur;
	Item* m[N];
};

// Listing 4 ===================================================================

template<typename T>
class AbstractStackExtension : AbstractStack<T> {
public:
	inline void PushCopy(const Item& x) { Push(new Item(x)); }
	inline void MultiPop(int n) { while (n > 0) Pop(), --n; }
	inline void Clear() { while (!IsEmpty()) Pop(); }
	//...
};
	
// Listing 5 ===================================================================

template<typename T>
concept IterableConcept
{
	typedef Iterator;
	Iterator Begin();
	Iterator End();
}

// Listing 6 ===================================================================

template<typename IterableConcept>
struct IterableExtension : IterableConcept
{
	typedef typename IterableConcept::Iterator Iterator;
	typedef typename IterableConcept::Item Item;
	// =====================================================================
	template<typename FunctionT>
	inline void ForEach(FunctionT f)
	{
		Iterator first = IterableConcept::Begin(), last = IterableConcept::End();
		while (first != last) 
		      f(*first++);
	}
	// =====================================================================
	inline bool IsEmpty() {
		return IterableConcept::Begin() == IterableConcept::End();
	}
	// =====================================================================
	inline int Count() {
		int ret = 0;
		Iterator first = IterableConcept::Begin(), last = 
		IterableConcept::End();
		while (first != last) ++first, ++ret;
			return ret;
	}
};

// Listing 7 ===================================================================

template<typename T>
struct StackImpl
{
	typedef typename std::vector<T>::iterator Iterator;
	typedef T Item;
	// =====================================================================
	Iterator Begin() {
		return m.begin();
	}
	// =====================================================================
	Iterator End() {
		return m.end();
	}
	// =====================================================================
	void push(T n) { m.push_back(n); }
	// =====================================================================
	T pop() { 
		T ret = m.back();
		m.pop_back();
		return ret;
	}
private:
	std::vector<T> m;
};

template<typename T>
struct Stack : IterableExtension<StackImpl<T> > {
};
