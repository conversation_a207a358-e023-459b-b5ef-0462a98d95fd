#pragma once

#include "../../Definitions.h"
#include "core/math/Vector3.h"
#include "field/Field.h"
#include <cmath>

namespace walberla {
namespace curvedboundary {

// y+ adaptive wall function for curved boundaries
class AdaptiveWallFunction {
public:
    // Wall function types
    enum WallFunctionType {
        STANDARD,           // Standard log-law
        SPALDING,          // Spalding's law (smooth transition)
        REICHARDT,         // <PERSON><PERSON>'s law
        WERNER_WENGLE,     // Power law
        MUSKER             // <PERSON><PERSON><PERSON>'s law
    };
    
    // Wall treatment regions
    enum WallRegion {
        VISCOUS_SUBLAYER,   // y+ < 5
        BUFFER_LAYER,       // 5 < y+ < 30
        LOG_LAYER,          // y+ > 30
        AUTOMATIC           // Automatic detection
    };
    
    AdaptiveWallFunction(Real nu = 0.1, WallFunctionType type = SPALDING);
    
    // Compute wall shear stress from first cell value
    Real computeWallShearStress(
        Real u_parallel,      // Velocity parallel to wall
        Real y,              // Distance to wall
        Real rho = 1.0       // Density
    ) const;
    
    // Compute y+ value
    Real computeYPlus(
        Real y,              // Distance to wall
        Real u_tau           // Friction velocity
    ) const;
    
    // Compute friction velocity iteratively
    Real computeFrictionVelocity(
        Real u_parallel,
        Real y,
        Real rho = 1.0,
        Real tolerance = 1e-6,
        Uint maxIterations = 100
    ) const;
    
    // Get velocity from y+ (inverse wall function)
    Real getVelocityFromYPlus(
        Real yPlus,
        Real u_tau
    ) const;
    
    // Determine wall region based on y+
    WallRegion determineWallRegion(Real yPlus) const;
    
    // Apply wall function to boundary cell
    void applyWallFunction(
        Uint x, Uint y, Uint z,
        const Vector3<Real>& wallNormal,
        Real wallDistance,
        Field<Real,3>& velField,
        Field<Real,1>& densField,
        Field<Real,19>& pdfField
    );
    
    // Compute effective viscosity for wall modeling
    Real computeEffectiveViscosity(
        Real yPlus,
        Real molecularViscosity
    ) const;
    
    // Set wall function parameters
    void setKarmanConstant(Real kappa) { kappa_ = kappa; }
    void setLogLayerConstant(Real B) { B_ = B; }
    void setViscousSublayerThickness(Real ypThreshold) { ypThreshold_ = ypThreshold; }
    
    // Enable/disable adaptive treatment
    void enableAdaptiveTreatment(bool enable) { useAdaptive_ = enable; }
    
    // Get statistics for monitoring
    struct WallFunctionStats {
        Real averageYPlus;
        Real minYPlus;
        Real maxYPlus;
        Uint cellsInViscousSublayer;
        Uint cellsInBufferLayer;
        Uint cellsInLogLayer;
    };
    
    WallFunctionStats getStatistics() const { return stats_; }
    void resetStatistics() { stats_ = WallFunctionStats(); }
    
private:
    Real nu_;                    // Kinematic viscosity
    WallFunctionType type_;      // Wall function type
    bool useAdaptive_;           // Use adaptive treatment
    
    // Wall function constants
    Real kappa_ = 0.41;          // von Karman constant
    Real B_ = 5.2;               // Log layer constant
    Real E_ = 9.8;               // Alternative log layer constant
    Real ypThreshold_ = 11.225;  // y+ threshold for viscous sublayer
    
    // Werner-Wengle constants
    Real A_ww_ = 8.3;
    Real B_ww_ = 1.0/7.0;
    
    // Statistics
    mutable WallFunctionStats stats_;
    
    // Wall function implementations
    Real standardWallFunction(Real yPlus) const;
    Real spaldingWallFunction(Real yPlus) const;
    Real reichardtWallFunction(Real yPlus) const;
    Real wernerWengleWallFunction(Real yPlus) const;
    Real muskerWallFunction(Real yPlus) const;
    
    // Newton-Raphson solver for Spalding's law
    Real solveSpaldingYPlus(Real uPlus, Real tolerance, Uint maxIter) const;
    
    // Blending function for smooth transition
    Real blendingFunction(Real yPlus) const;
    
    // Update statistics
    void updateStatistics(Real yPlus) const;
};

// Enhanced wall model with pressure gradient effects
class EnhancedWallModel : public AdaptiveWallFunction {
public:
    EnhancedWallModel(Real nu = 0.1);
    
    // Apply wall model with pressure gradient
    void applyEnhancedWallModel(
        Uint x, Uint y, Uint z,
        const Vector3<Real>& wallNormal,
        Real wallDistance,
        const Vector3<Real>& pressureGradient,
        Field<Real,3>& velField,
        Field<Real,1>& densField,
        Field<Real,19>& pdfField
    );
    
    // Compute wall shear with pressure gradient correction
    Real computeWallShearWithPressureGradient(
        Real u_parallel,
        Real y,
        Real dpdx,           // Pressure gradient along wall
        Real rho = 1.0
    ) const;
    
    // Van Driest damping function
    Real vanDriestDamping(Real yPlus) const;
    
    // Pressure gradient parameter
    Real computePressureGradientParameter(
        Real dpdx,
        Real nu,
        Real u_tau
    ) const;
    
private:
    Real A_vd_ = 26.0;  // Van Driest constant
};

// Wall function manager for multiple walls
class WallFunctionManager {
public:
    WallFunctionManager();
    
    // Add wall with specific treatment
    void addWall(
        Uint wallID,
        std::shared_ptr<AdaptiveWallFunction> wallFunction
    );
    
    // Apply all wall functions
    void applyAllWallFunctions(
        const Field<Uint,1>& wallIDField,
        const Field<Real,3>& normalField,
        const Field<Real,1>& distanceField,
        Field<Real,3>& velField,
        Field<Real,1>& densField,
        Field<Real,19>& pdfField
    );
    
    // Get wall function for specific wall
    std::shared_ptr<AdaptiveWallFunction> getWallFunction(Uint wallID) const;
    
    // Generate y+ field for visualization
    void generateYPlusField(
        const Field<Uint,1>& wallIDField,
        const Field<Real,3>& normalField,
        const Field<Real,1>& distanceField,
        const Field<Real,3>& velField,
        const Field<Real,1>& densField,
        Field<Real,1>& yPlusField
    ) const;
    
private:
    std::map<Uint, std::shared_ptr<AdaptiveWallFunction>> wallFunctions_;
};

} // namespace curvedboundary
} // namespace walberla