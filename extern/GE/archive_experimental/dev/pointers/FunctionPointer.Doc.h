// Item 14 – Function Pointers
// - used traditioally for implementing callback functions
// -
void (*fp)(int);

extern int f( int );
extern void g( long );
extern void h( int );


//…
fp = f; // error! incompatible
fp = g; // error! incompatible
fp = 0; // set to null
fp = h; // OK
fp = &h; // OK, take address explicitly but not necessary

(*fp)(12); 	// explicit dereference but not necessary
fp(12); 	// implicit



// * A Callback Function *
extern void stopDropRoll();
inline void jumpIn() { /*… */}
//…
void (*fireaction)() = 0;
//…
if( !fatalist ) { //
      if( nearWater )
	  fireaction = jumpIn;
      else
	  fireaction = stopDropRoll;
}

if( ftemp >= 451 ) { // If there is a fire
  if( fireaction ) // .. and an action to execute
      fireaction(); // .. execute it
}

// It is also legal to take the address of an overloaded function:
void jumpIn() // gets selected
void jumpIn( bool canSwim );
// …
fireAction = jumpIn;