#ifndef IFELSE_HH
#define IFELSE_HH


	//!<- Turning integral values into templates
	template <int x> 
	struct Int_type{
		enum { value = x };
	};


	//! @struct  IfElse
	//! @brief   may be used to obtain one of two types, depending on a bool selector value.
	template <  bool selector,  typename FirstType, typename SecondType >
	struct IfElse {
		    typedef FirstType type;         /// default impl
	};


	template <  typename FirstType, typename SecondType >
	struct IfElse<false, FirstType, SecondType> /// special impl
	{
		    typedef SecondType type;
	};

// // Example how to used it
// -------------------------
// 
// 	template < typename Key   ,  typename Value ,  int Selector  >
// 	class Y 
// 	{
// 	  public :
// 	    // just example of use IfElse 
// 	    typedef typename IfElse <true , std::map <Key,Value> , std::map <Key,Value* > >::type MyFirsttype    ; // it make no sense, just for testing stuff
// 	    typedef typename IfElse <false, std::map <Key,Value> , std::map <Key,Value* > >::type MySecondttype ;
// 	    typedef typename IfElse <
// 				      Selector == 1 ,                 // if true, use first type, else use second one
// 				      std::map <Key,Value>, 	      // first type 
// 				      typename IfElse <		      // second type whic i  this case will <=> to second IfElse
// 						        Selector == 2 ,
// 						        std::map <Key,Value* >, ... bla 
// 							         	        ... > bla
// 						      >::type  
//
// 				    >::type myType ; 
// 	};
	
	
#endif
