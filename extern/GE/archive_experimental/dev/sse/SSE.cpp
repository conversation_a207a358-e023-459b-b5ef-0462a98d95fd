#include "SSE.h"



// #if __INTEL_COMPILER
//    #include <pmmintrin.h>
// #endif



//! wihtout using SIMD

float dot_product(float *a, float *b, int size)
{
	int sum=0;
	for(int  i=0; i<size; i++)
	{
	  sum += a[i]*b[i];
	}
	return sum;
}



//! Using SSE 

#if __INTEL_COMPILER

float dot_product_intrin(float *a, float *b, int size )
{
  float arr[4];
  float total;
  int i;
  
  __m128 num1, num2, num3, sum;
  
  sum= _mm_setzero_ps();  //sets sum to zero
  
  for(i=0; i< size ; i+=4)
  {
     num1 = _mm_loadu_ps(a+i);   
//loads unaligned array a into num1    
// num1= a[3] a[2] a[1] a[0]
     num2 = _mm_loadu_ps(b+i);   
    //loads unaligned array b into num2
    //num2= b[3]   b[2]   b[1] b[0]
    
     num3 = _mm_mul_ps(num1, num2); //performs multiplication
//     num3 =  a[3]*b[3] a[2]*b[2] a[1]*b[1] a[0]*b[0]
    
     num3 = _mm_hadd_ps(num3, num3); //performs horizontal addition
//num3= a[3]*b[3]+ a[2]*b[2] a[1]*b[1]+a[0]*b[0]  a[3]*b[3]+ a[2]*b[2] a[1]*b[1]+a[0]*b[0]

     sum = _mm_add_ps(sum, num3);  //performs vertical addition
  }
  
   sum= _mm_hadd_ps(sum, sum);
  
  _mm_store_ss(&total,sum);
  
  return total;
}
#endif




