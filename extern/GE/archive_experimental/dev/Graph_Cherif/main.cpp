// =============================================================================
// includes
// =============================================================================
#include <iostream>
#include <map>
#include <list>
// #include "vertex.h"
// #include "DoubleLinkedList.h"
#include "Graph.h"
#include "io.h"
// =============================================================================





int main() 
{

	// Map definieren, string ist Schlüssel und Zusatzwert ist int
	std::map<string,int> myMap;
	//...
	// Zusatzwert direkt setzen
	myMap["KEY"] = 10;
	// Zusatzwert auslesen
	int var = myMap["KEY"];



	// TODO reimplemnted and reconsider how to use std::map,
	// its wrong (u wast momory hier)
	// something like : std::map<Key, Data, Compare, Alloc>
	// http://www.bogotobogo.com/cplusplus/stl2.html
	typedef std::map< vertex , 
			  std::size_t,	// int
			  VertexCompare
			 > mymap_t;
	
	mymap_t mymap;

	mymap[vertex( 1.0 , 2.0 , 2.0 ) ] = 1 ;
	
	mymap_t::const_iterator	it = mymap.find( vertex( 1.0 , 2.0 , 2.0) );

	if ( it != mymap.end() )
	{
		std::cout << it->second << std::endl;
	
	} else {
	  
		std::cout << "not found" << std::endl;
	}
	
	
	
	
 
 
 
// Double link list 
		double wcTs1 , wcTe1, cpuTimeStart , cpuTimeEnd ;
		int N = 1000000 ;
	  int arr[1000000] ;
		for (int  i = 0 ;  i < N ; ++ i)
		  arr[i] = 2* i ;

		timing(&wcTs1,&cpuTimeStart) ;
		double_linked<int> dlist ( arr );
		timing(&wcTe1,&cpuTimeEnd);
		double runtime = wcTe1-wcTs1 ;
		std::cout<<"Walltime prediction hand implementation  = "<<runtime<<std::endl;
// 	  dlist.push_back ( 11 );
// 	  dlist.push_front( 100 );

	double wcTs2 , wcTe2 , cpuTimeStart2 , cpuTimeEnd2;
	timing(&wcTs2,&cpuTimeStart2) ;  
	std::list<int>  stdList ;
		for (int  i = 0 ;  i < N ; ++ i)
		stdList.push_back( 2* i );
	timing(&wcTs2,&cpuTimeStart2) ;
	
	std::cout<<"Walltime prediction stl implementation  = "<<runtime<<std::endl; 

	
// 	  while( dlist )
// 	  std::cout << dlist.pop_front() << " ";
// 	  std::cout <<std::endl;

// 	std::cout << "Double template :)"<<std::endl;
// 	  double Darr[] = {4.2, 6., 8., 32., 19.} ;
// 	  double_linked<double> dlist_D ( Darr );
// 	  while(dlist_D  )
// 	  std::cout << dlist_D.pop_front() << " ";
// 	  std::cout <<std::endl;


// Graph-class test 


 	Graph*	g= new Graph () ; 
	
		cout<< "g::size()  ==  " << g->size() << endl;

	
	cout<< " Read -binary file .... \n";
	
	Read_from_binary_file ("box.stl", *g);
		
	cout<< " End Read -binary file\n";
	
	
		cout<< "g::size()  ==  "  << g->size() << endl;	
	
	
		cout<< "g::print() .... \n " ;
	 
	g -> print() ;
	// =====================================================================
	/*
		cout << " End print()\n";
		
		vertex *root = new vertex (0.0 , 0.0 , 0.0) ;

		
		/// first use :: just for printing theh element of the graph
		
		cout << "Start BreadthFirstTraversal().....\n";	
		g->BreadthFirstTraversal(*root) ;
		
		cout << "End BreadthFirstTraversal()\n";	
			
	*/
	// =====================================================================
	
	delete g;
	return 0;
	
}











