// =============================================================================
// includes
// =============================================================================
#include <iostream>
#include <vector>
#include <functional>
#include <algorithm>
#include <cassert>
#include <boost/bind.hpp>
#include <boost/ref.hpp>
// =============================================================================


int f(int a, int b)
{
	return a + b;
}



struct F2
{
	int s;
	typedef void result_type;
	void operator()( int x ) {
		s += x;
	}
};



// =============================================================================
// main function
// =============================================================================
int main ()
{
	int x = 10 ;
	
	std::bind1st(std::ptr_fun(f), 5)(x);    // f(5, x)
	boost::bind(f, 5, _1)(x);		// f(5, x)

	std::cout << " --> std::bind1st(std::ptr_fun(f), 5)(x) 	= "<<
		std::bind1st(std::ptr_fun(f), 5)(x) << std::endl ;


	std::cout << " --> boost::bind(f, 5, _1)(x)			= "<<
		boost::bind(f, 5, _1)(x)  << std::endl ;
		

	F2 f2	= { 0 };
	int a[] = { 1, 2, 3 };
		
	std::for_each( a, a+3, bind( boost::ref(f2), _1 ) );
		
	assert( f2.s == 6 );

	return (0) ;
}