#include <iostream>
// =============================================================================
// Base class has a pure virtual function for cloning
// =============================================================================
template<typename config>
class Shape {
public:
	typedef typename config::real real ;
	
	virtual ~Shape() {}
	virtual Shape *clone() 	const = 0; // pure virt fct,I'm base class
	virtual real area ()	const = 0; // pure virt fct,I'm base class
};


// =============================================================================
// This CRTP class implements clone() for Derived
// =============================================================================
template <typename config, template<typename S> class Derived> 
class Shape_CRTP: public Shape <config>
{
    public:
	typedef typename config::real real ;
	
	inline Shape<config> *clone() const {
		return new Derived<config>(static_cast<Derived<config> const&>(*this));
	}
	inline real area ()const {
		return (static_cast <Derived <config> const&> (*this) ). area () ;
	}
};
// =============================================================================
// Every derived class inherits from Shape_CRTP instead of Shape
// =============================================================================
template<typename config>
struct Square: public Shape_CRTP< config,Square >
{
	typedef typename config::real real ;
	inline real area ()const {return m_hight * m_width ;}
	inline void Set(const real& h, const real& w){m_hight = h ;m_width = w;}
    private :
	real m_hight ;
	real m_width ;
};
// =============================================================================
#define pi 3.14
template<typename config>
struct Circle: public Shape_CRTP<config,Circle>
{
	typedef typename config::real real ;
	
	inline real area ()const {return m_ray * m_ray* pi;}
	inline void Set(const real& ray) {m_ray = ray;}
    private :
	real m_ray ;
};
// =============================================================================
// sample of configure pattern
// =============================================================================
struct ConfigSample
{
	typedef double real ;
};

// =============================================================================
inline void PrintSizeOf(); // check the sizeof of these classes.
// =============================================================================
template<typename config>
inline void PrintArea(/*const */ Shape<config>* my_shpes);
// =============================================================================
int main()
{
	PrintSizeOf();
	Square<ConfigSample> *tmp = new Square<ConfigSample> ;
	tmp-> Set (20.0,4.0) ;
	Shape<ConfigSample> *tmp2 = tmp->clone () ;
	Shape<ConfigSample> *m_square = new Square<ConfigSample> ;
	PrintArea (tmp) ;
	PrintArea (tmp2) ;
	PrintArea (m_square) ;

	Circle<ConfigSample> *m_circle = new Circle<ConfigSample> ;
	m_circle -> Set (2.) ;
	PrintArea (m_circle) ;
	return (0) ;
}
// =============================================================================
template<typename config>
inline void PrintArea(/*const */ Shape<config>* my_shpes)
{
	std::cout<< "area : " << my_shpes -> area () << std::endl ;
}
// =============================================================================
// add you need.
// =============================================================================
inline void PrintSizeOf()
{
	std::cout<<"size of (Shape) : " << sizeof (Shape<ConfigSample>)  << "\n";
	std::cout<<"size of (Square) : "<< sizeof (Square<ConfigSample>) << "\n";
//	std::cout<< "size of (Shape_CRTP<Square> ) : " 
//		 << sizeof (Shape_CRTP< Square <ConfigSample> > ) << "\n";
}


/*  Some results
size of (Shape) : 8
size of (Square) : 16
area : 80
area : 80
area : 12.56
*/

