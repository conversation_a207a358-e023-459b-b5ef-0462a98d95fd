//=================================================================================================
/*!
//  \file Vector3.h
//  \brief Header file for the implementation of a 3D vector
//  \author Klaus <PERSON>
*/
//=================================================================================================

#ifndef _WALBERLA_VECTOR3_H_
#define _WALBERLA_VECTOR3_H_


//*************************************************************************************************
// Includes
//*************************************************************************************************

#include "Definitions.h"
#include "SysFuncs.h"
#include <cmath>
#include <limits>
#include "WalberlaMath.h"
#include "MathTrait.h"
#include "SqrtTrait.h"
#include "Logging.h"
#include <iostream>


//*************************************************************************************************
// Definitions
//*************************************************************************************************

//! High-order return value.
/*! Abbreviation for the evaluation of the higher-order data type in a numerical operation. */
#define HIGH typename MathTrait<Type,Other>::High


namespace walberla {

//=================================================================================================
//
//  CLASS DEFINITION
//
//=================================================================================================

//*************************************************************************************************
/*!\brief Efficient, generic implementation of a 3-dimensional vector.
// \ingroup core
//
// The Vector3 class is the representation of a 3D vector with a total of 3 statically allocated
// elements of arbitrary type. The naming convention of the elements is as following:

                             \f[\left(\begin{array}{*{3}{c}}
                             x & y & z \\
                             \end{array}\right)\f]

// These elements can be accessed directly with the subscript operator. The numbering of the
// vector elements is

                             \f[\left(\begin{array}{*{3}{c}}
                             0 & 1 & 2 \\
                             \end{array}\right)\f]
*/
template< typename Type >
class Vector3
{
 private:
   //**Friend declarations*************************************************************************
   /*! \cond WALBERLAINTERNAL */
   template< typename Other > friend class Vector3;
   /*! \endcond */
   //**********************************************************************************************

 public:
   //**Type definitions****************************************************************************
   typedef typename SqrtTrait<Type>::Type Length;  //!< Vector length return type.
                                                   /*!< Return type of the Vector3<Type>::length
                                                        function. */
   //**********************************************************************************************

   //**Constructors********************************************************************************
   explicit inline Vector3();
   explicit inline Vector3( Type set );
   explicit inline Vector3( Type x, Type y, Type z );
   explicit inline Vector3( const Type* init );
            inline Vector3( const Vector3& v );

   template< typename Other >
   inline Vector3( const Vector3<Other>& v );
   //**********************************************************************************************

   //**Destructor**********************************************************************************
   // No explicitly declared destructor.
   //**********************************************************************************************

   //**Operators***********************************************************************************
   /*!\name Operators */
   //@{
                              inline Vector3&    operator= ( Type set );
                              inline Vector3&    operator= ( const Vector3& set );
   template< typename Other > inline Vector3&    operator= ( const Vector3<Other>& set );
   template< typename Other > inline bool        operator==( Other rhs )                 const;
   template< typename Other > inline bool        operator==( const Vector3<Other>& rhs ) const;
   template< typename Other > inline bool        operator!=( Other rhs )                 const;
   template< typename Other > inline bool        operator!=( const Vector3<Other>& rhs ) const;
                              inline Type&       operator[]( unsigned int index );
                              inline const Type& operator[]( unsigned int index )        const;
   //@}
   //**********************************************************************************************

   //**Arithmetic operators************************************************************************
   /*!\name Arithmetic operators
   // \brief The return type of the arithmetic operators depends on the involved data types of
   // \brief the vectors. HIGH denotes the more significant data type of the arithmetic operation
   // \brief (for further detail see the MathTrait class description).
   */
   //@{
                              inline const Vector3       operator-()                             const;
   template< typename Other > inline Vector3&            operator+=( const Vector3<Other>& rhs );
   template< typename Other > inline Vector3&            operator-=( const Vector3<Other>& rhs );
   template< typename Other > inline Vector3&            operator*=( Other rhs );
   template< typename Other > inline Vector3&            operator/=( Other rhs );
   template< typename Other > inline const Vector3<HIGH> operator+ ( const Vector3<Other>& rhs ) const;
   template< typename Other > inline const Vector3<HIGH> operator- ( const Vector3<Other>& rhs ) const;
   template< typename Other > inline const Vector3<HIGH> operator* ( Other rhs )                 const;
   template< typename Other > inline HIGH                operator* ( const Vector3<Other>& rhs ) const;
   template< typename Other > inline const Vector3<HIGH> operator/ ( Other rhs )                 const;
   template< typename Other > inline const Vector3<HIGH> cross( const Vector3<Other>& rhs )      const;
   template< typename Other > inline HIGH                dot( const Vector3<Other>& rhs )        const;
   //@}
   //**********************************************************************************************

   //**Utility functions***************************************************************************
   /*!\name Utility functions */
   //@{
   inline Length        length()        const;
   inline Type          sqrLength()     const;
   inline Vector3&      normalize();
   inline const Vector3 getNormalized() const;
   inline const Vector3 normalized() const;
   //@}
   //**********************************************************************************************

 private:
   //**Member variables****************************************************************************
   /*!\name Member variables */
   //@{
   Type v_[3];  //!< The three statically allocated vector elements.
                /*!< Access to the vector values is gained via the subscript operator.
                     The order of the elements is
                     \f[\left(\begin{array}{*{3}{c}}
                     0 & 1 & 2 \\
                     \end{array}\right)\f] */
   //@}
   //**********************************************************************************************
};
//*************************************************************************************************




//=================================================================================================
//
//  CONSTRUCTORS
//
//=================================================================================================

//*************************************************************************************************
/*!\fn Vector3<Type>::Vector3()
// \brief The default constructor for Vector3.
//
// All vector elements are initialized to the default value (i.e. 0 for integral data types).
*/
template< typename Type >
inline Vector3<Type>::Vector3()
{
   v_[0] = v_[1] = v_[2] = Type();
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn Vector3<Type>::Vector3( Type init )
// \brief Constructor for a homogenous initialization of all elements.
//
// \param init Initial value for all vector elements.
*/
template< typename Type >
inline Vector3<Type>::Vector3( Type init )
{
   v_[0] = v_[1] = v_[2] = init;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn Vector3<Type>::Vector3( Type x, Type y, Type z )
// \brief Constructor for a direct initialization of all vector elements.
//
// \param x The initial value for the x-component.
// \param y The initial value for the y-component.
// \param z The initial value for the z-component.
*/
template< typename Type >
inline Vector3<Type>::Vector3( Type x, Type y, Type z )
{
   v_[0] = x;
   v_[1] = y;
   v_[2] = z;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn Vector3<Type>::Vector3( const Type* init )
// \brief Constructor for an array initializer.
//
// \param init Pointer to the initialization array.
//
// The array is assumed to have at least three valid elements.
*/
template< typename Type >
inline Vector3<Type>::Vector3( const Type* init )
{
   v_[0] = init[0];
   v_[1] = init[1];
   v_[2] = init[2];
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn Vector3<Type>::Vector3( const Vector3& v )
// \brief The copy constructor for Vector3.
//
// \param v Vector to be copied.
//
// The copy constructor is explicitly defined in order to enable/facilitate NRV optimization.
*/
template< typename Type >
inline Vector3<Type>::Vector3( const Vector3& v )
{
   v_[0] = v.v_[0];
   v_[1] = v.v_[1];
   v_[2] = v.v_[2];
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn Vector3<Type>::Vector3( const Vector3<Other>& v )
// \brief Conversion constructor from different Vector3 instances.
//
// \param v Vector to be copied.
*/
template< typename Type >
template< typename Other >
inline Vector3<Type>::Vector3( const Vector3<Other>& v )
{
   v_[0] = v.v_[0];
   v_[1] = v.v_[1];
   v_[2] = v.v_[2];
}
//*************************************************************************************************




//=================================================================================================
//
//  OPERATORS
//
//=================================================================================================

//*************************************************************************************************
/*!\fn Vector3<Type>& Vector3<Type>::operator=( Type set )
// \brief Homogenous assignment to all vector elements.
//
// \param set Scalar value to be assigned to all vector elements.
// \return Reference to the assigned vector.
*/
template< typename Type >
inline Vector3<Type>& Vector3<Type>::operator=( Type set )
{
   v_[0] = v_[1] = v_[2] = set;
   return *this;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn Vector3<Type>& Vector3<Type>::operator=( const Vector3& set )
// \brief Copy assignment operator for Vector3.
//
// \param set Vector to be copied.
// \return Reference to the assigned vector.
//
// Explicit definition of a copy assignment operator for performance reasons.
*/
template< typename Type >
inline Vector3<Type>& Vector3<Type>::operator=( const Vector3& set )
{
   // This implementation is faster than the synthesized default copy assignment operator and
   // faster than an implementation with the C library function 'memcpy' in combination with a
   // protection against self-assignment. Additionally, this version goes without a protection
   // against self-assignment.
   v_[0] = set.v_[0];
   v_[1] = set.v_[1];
   v_[2] = set.v_[2];
   return *this;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn Vector3<Type>& Vector3<Type>::operator=( const Vector3<Other>& set )
// \brief Assignment operator for different Vector3 instances.
//
// \param set Vector to be copied.
// \return Reference to the assigned vector.
*/
template< typename Type >
template< typename Other >
inline Vector3<Type>& Vector3<Type>::operator=( const Vector3<Other>& set )
{
   // This implementation is faster than the synthesized default copy assignment operator and
   // faster than an implementation with the C library function 'memcpy' in combination with a
   // protection against self-assignment. Additionally, this version goes without a protection
   // against self-assignment.
   v_[0] = set.v_[0];
   v_[1] = set.v_[1];
   v_[2] = set.v_[2];
   return *this;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn bool Vector3<Type>::operator==( Other rhs ) const
// \brief Equality operator for the comparison of a vector and a scalar value.
//
// \param rhs The right-hand-side scalar value for the comparison.
// \return bool
//
// If all values of the vector are equal to the scalar value, the equality test returns true,
// otherwise false.
*/
template< typename Type >
template< typename Other >
inline bool Vector3<Type>::operator==( Other rhs ) const
{
   // In order to compare the vector and the scalar value, the data values of the lower-order
   // data type are converted to the higher-order data type within the equal function.
   if( !equal( v_[0], rhs ) || !equal( v_[1], rhs ) || !equal( v_[2], rhs ) )
      return false;
   else return true;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn bool Vector3<Type>::operator==( const Vector3<Other>& rhs ) const
// \brief Equality operator for the comparison of two vectors.
//
// \param rhs The right-hand-side vector for the comparison.
// \return bool
*/
template< typename Type >
template< typename Other >
inline bool Vector3<Type>::operator==( const Vector3<Other>& rhs ) const
{
   // In order to compare the two vectors, the data values of the lower-order data
   // type are converted to the higher-order data type within the equal function.
   if( !equal( v_[0], rhs.v_[0] ) || !equal( v_[1], rhs.v_[1] ) || !equal( v_[2], rhs.v_[2] ) )
      return false;
   else return true;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn bool Vector3<Type>::operator!=( Other rhs ) const
// \brief Inequality operator for the comparison of a vector and a scalar value.
//
// \param rhs The right-hand-side scalar value for the comparison.
// \return bool
//
// If one value of the vector is inequal to the scalar value, the inequality test returns true,
// otherwise false.
*/
template< typename Type >
template< typename Other >
inline bool Vector3<Type>::operator!=( Other rhs ) const
{
   // In order to compare the vector and the scalar value, the data values of the lower-order
   // data type are converted to the higher-order data type within the equal function.
   if( !equal( v_[0], rhs ) || !equal( v_[1], rhs ) || !equal( v_[2], rhs ) )
      return true;
   else return false;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn bool Vector3<Type>::operator!=( const Vector3<Other>& rhs ) const
// \brief Inequality operator for the comparison of two vectors.
//
// \param rhs The right-hand-side vector for the comparison.
// \return bool
*/
template< typename Type >
template< typename Other >
inline bool Vector3<Type>::operator!=( const Vector3<Other>& rhs ) const
{
   // In order to compare the two vectors, the data values of the lower-order data
   // type are converted to the higher-order data type within the equal function.
   if( !equal( v_[0], rhs.v_[0] ) || !equal( v_[1], rhs.v_[1] ) || !equal( v_[2], rhs.v_[2] ) )
      return true;
   else return false;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn Type& Vector3<Type>::operator[]( unsigned int index )
// \brief Subscript operator for the direct access to the vector elements.
//
// \param index Access index. The index has to be in the range \f$[0..2]\f$.
// \return Reference to the accessed value.
*/
template< typename Type >
inline Type& Vector3<Type>::operator[]( unsigned int index )
{
   ASSERT( index < 3 ,"Invalid vector access index" );
   return v_[index];
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn const Type& Vector3<Type>::operator[]( unsigned int index ) const
// \brief Subscript operator for the direct access to the vector elements.
//
// \param index Access index. The index has to be in the range \f$[0..2]\f$.
// \return Reference-to-const to the accessed value.
*/
template< typename Type >
inline const Type& Vector3<Type>::operator[]( unsigned int index ) const
{
   ASSERT( index < 3,"Invalid vector access index" );
   return v_[index];
}
//*************************************************************************************************




//=================================================================================================
//
//  ARITHMETIC OPERATORS
//
//=================================================================================================

//*************************************************************************************************
/*!\fn const Vector3<Type> Vector3<Type>::operator-() const
// \brief Unary minus operator for the inversion of a vector (\f$ \vec{a} = -\vec{b} \f$).
//
// \return The inverse of the vector.
*/
template< typename Type >
inline const Vector3<Type> Vector3<Type>::operator-() const
{
   return Vector3( -v_[0], -v_[1], -v_[2] );
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn Vector3<Type>& Vector3<Type>::operator+=( const Vector3<Other>& rhs )
// \brief Addition assignment operator for the addition of two vectors (\f$ \vec{a}+=\vec{b} \f$).
//
// \param rhs The right-hand-side vector to be added to the vector.
// \return Reference to the vector.
*/
template< typename Type >
template< typename Other >
inline Vector3<Type>& Vector3<Type>::operator+=( const Vector3<Other>& rhs )
{
   v_[0] += rhs.v_[0];
   v_[1] += rhs.v_[1];
   v_[2] += rhs.v_[2];
   return *this;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn Vector3<Type>& Vector3<Type>::operator-=( const Vector3<Other>& rhs )
// \brief Subtraction assignment operator for the subtraction of two vectors
// \brief (\f$ \vec{a}-=\vec{b} \f$).
//
// \param rhs The right-hand-side vector to be subtracted from the vector.
// \return Reference to the vector.
*/
template< typename Type >
template< typename Other >
inline Vector3<Type>& Vector3<Type>::operator-=( const Vector3<Other>& rhs )
{
   v_[0] -= rhs.v_[0];
   v_[1] -= rhs.v_[1];
   v_[2] -= rhs.v_[2];
   return *this;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn Vector3<Type>& Vector3<Type>::operator*=( Other rhs )
// \brief Multiplication assignment operator for the multiplication between a vector and
// \brief a scalar value (\f$ \vec{a}*=s \f$).
//
// \param rhs The right-hand-side scalar value for the multiplication.
// \return Reference to the vector.
*/
template< typename Type >
template< typename Other >
inline Vector3<Type>& Vector3<Type>::operator*=( Other rhs )
{
   v_[0] *= rhs;
   v_[1] *= rhs;
   v_[2] *= rhs;
   return *this;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn Vector3<Type>& Vector3<Type>::operator/=( Other rhs )
// \brief Division assignment operator for the division of a vector by a scalar value
// \brief (\f$ \vec{a}/=s \f$).
//
// \param rhs The right-hand-side scalar value for the division.
// \return Reference to the vector.
//
// \b Note: No check for 0 is applied.
*/
template< typename Type >
template< typename Other >
inline Vector3<Type>& Vector3<Type>::operator/=( Other rhs )
{
   // Depending on the two involved data types, an integer division is applied or a
   // floating point division is selected.
   if( std::numeric_limits<HIGH>::is_integer ) {
      v_[0] /= rhs;
      v_[1] /= rhs;
      v_[2] /= rhs;
      return *this;
   }
   else {
      const HIGH tmp( 1/static_cast<HIGH>( rhs ) );
      v_[0] = static_cast<Type>( static_cast<HIGH>( v_[0] ) * tmp );
      v_[1] = static_cast<Type>( static_cast<HIGH>( v_[1] ) * tmp );
      v_[2] = static_cast<Type>( static_cast<HIGH>( v_[2] ) * tmp );
      return *this;
   }
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn const Vector3<HIGH> Vector3<Type>::operator+( const Vector3<Other>& rhs ) const
// \brief Addition operator for the addition of two vectors (\f$ \vec{a}=\vec{b}+\vec{c} \f$).
//
// \param rhs The right-hand-side vector to be added to the vector.
// \return The sum of the two vectors.
//
// The operator returns a vector of the higher-order data type of the two involved vector
// data types (in fact the two template arguments \a Type and \a Other ).
*/
template< typename Type >
template< typename Other >
inline const Vector3<HIGH> Vector3<Type>::operator+( const Vector3<Other>& rhs ) const
{
   return Vector3<HIGH>( v_[0]+rhs.v_[0], v_[1]+rhs.v_[1], v_[2]+rhs.v_[2] );
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn const Vector3<HIGH> Vector3<Type>::operator-( const Vector3<Other>& rhs ) const
// \brief Subtraction operator for the subtraction of two vectors (\f$ \vec{a}=\vec{b}-\vec{c} \f$).
//
// \param rhs The right-hand-side vector to be subtracted from the vector.
// \return The difference of the two vectors.
//
// The operator returns a vector of the higher-order data type of the two involved vector
// data types (in fact the two template arguments \a Type and \a Other ).
*/
template< typename Type >
template< typename Other >
inline const Vector3<HIGH> Vector3<Type>::operator-( const Vector3<Other>& rhs ) const
{
   return Vector3<HIGH>( v_[0]-rhs.v_[0], v_[1]-rhs.v_[1], v_[2]-rhs.v_[2] );
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn const Vector3<HIGH> Vector3<Type>::operator*( Other rhs ) const
// \brief Multiplication operator for the multiplication of a vector and a scalar value
// \brief (\f$ \vec{a}=\vec{b}*s \f$).
//
// \param rhs The right-hand-side scalar value for the multiplication.
// \return The scaled result vector.
//
// The operator returns a vector of the higher-order data type of the two involved data types
// (in fact the two template arguments \a Type and \a Other ).
*/
template< typename Type >
template< typename Other >
inline const Vector3<HIGH> Vector3<Type>::operator*( Other rhs ) const
{
   return Vector3<HIGH>( v_[0]*rhs, v_[1]*rhs, v_[2]*rhs );
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn HIGH Vector3<Type>::operator*( const Vector3<Other>& rhs ) const
// \brief Multiplication operator for the scalar product (inner product) of two vectors
// \brief (\f$ s=\vec{a}*\vec{b} \f$).
//
// \param rhs The right-hand-side vector for the inner product.
// \return The scalar product.
//
// The operator returns a scalar value of the higher-order data type of the two involved data
// types (in fact the two template arguments \a Type and \a Other ).
*/
template< typename Type >
template< typename Other >
inline HIGH Vector3<Type>::operator*( const Vector3<Other>& rhs ) const
{
   return ( v_[0]*rhs.v_[0] + v_[1]*rhs.v_[1] + v_[2]*rhs.v_[2] );
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn const Vector3<HIGH> Vector3<Type>::operator/( Other rhs ) const
// \brief Division operator for the divison of a vector by a scalar value
// \brief (\f$ \vec{a}=\vec{b}/s \f$).
//
// \param rhs The right-hand-side scalar value for the division.
// \return The scaled result vector.
//
// The operator returns a vector of the higher-order data type of the two involved data types
// (in fact the two template arguments \a Type and \a Other ).\n
// \b Note: No check for 0 is applied.
*/
template< typename Type >
template< typename Other >
inline const Vector3<HIGH> Vector3<Type>::operator/( Other rhs ) const
{
   // Depending on the two involved data types, an integer division is applied or a
   // floating point division is selected.
   if( std::numeric_limits<HIGH>::is_integer ) {
      return Vector3<HIGH>( v_[0]/rhs, v_[1]/rhs, v_[2]/rhs );
   }
   else {
      const HIGH tmp( 1/static_cast<HIGH>( rhs ) );
      return Vector3<HIGH>( v_[0]*tmp, v_[1]*tmp, v_[2]*tmp );
   }
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn const Vector3<HIGH> Vector3<Type>::cross( const Vector3<Other>& rhs ) const
// \brief Cross product (outer product) of two vectors (\f$ \vec{a}=\vec{b}\times\vec{c} \f$).
//
// \param rhs The right-hand-side vector for the cross product.
// \return The cross product.
*/
template< typename Type >
template< typename Other >
inline const Vector3<HIGH> Vector3<Type>::cross( const Vector3<Other>& rhs ) const
{
   return Vector3<HIGH>( v_[1] * rhs.v_[2] - v_[2] * rhs.v_[1],
                         v_[2] * rhs.v_[0] - v_[0] * rhs.v_[2],
                         v_[0] * rhs.v_[1] - v_[1] * rhs.v_[0] );
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn HIGH Vector3<Type>::dot( const Vector3<Other>& rhs ) const
// \brief Dot product (inner product) of two vectors (\f$ s=\vec{a}\cdot\vec{b} \f$).
//
// \param rhs The right-hand-side vector for the dot product.
// \return The dot product.
*/
template< typename Type >
template< typename Other >
inline HIGH Vector3<Type>::dot( const Vector3<Other>& rhs ) const
{
   return ( v_[0]*rhs.v_[0] + v_[1]*rhs.v_[1] + v_[2]*rhs.v_[2] );
}
//*************************************************************************************************




//=================================================================================================
//
//  UTILITY FUNCTIONS
//
//=================================================================================================

//*************************************************************************************************
/*!\fn Length Vector3<Type>::length() const
// \brief Calculation of the vector length \f$|\vec{a}|\f$.
//
// \return The length of the vector.
//
// The return type of the length function depends on the actual type of the vector instance:
//
// <table border="0" cellspacing="0" cellpadding="1">
//    <tr>
//       <td width="250px"> \b Type </td>
//       <td width="100px"> \b Length </td>
//    </tr>
//    <tr>
//       <td>float</td>
//       <td>float</td>
//    </tr>
//    <tr>
//       <td>integral data types and double</td>
//       <td>double</td>
//    </tr>
//    <tr>
//       <td>long double</td>
//       <td>long double</td>
//    </tr>
// </table>
*/
template< typename Type >
inline typename SqrtTrait<Type>::Type Vector3<Type>::length() const
{
   return std::sqrt( v_[0]*v_[0] + v_[1]*v_[1] + v_[2]*v_[2] );
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn Type Vector3<Type>::sqrLength() const
// \brief Calculation of the vector square length \f$|\vec{a}|^2\f$.
//
// \return The square length of the vector.
*/
template< typename Type >
inline Type Vector3<Type>::sqrLength() const
{
   return ( v_[0]*v_[0] + v_[1]*v_[1] + v_[2]*v_[2] );
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn Vector3<Type>& Vector3<Type>::normalize()
// \brief Normalization of the vector (\f$|\vec{a}|=1\f$).
//
// \return Reference to the vector.
//
// Normalization of the vector to a length of 1. Keep in mind that a vector normalization of a
// vector of integer values will possibly result in a zero vector!
*/
template< typename Type >
inline Vector3<Type>& Vector3<Type>::normalize()
{
   const Length len( std::sqrt( v_[0]*v_[0] + v_[1]*v_[1] + v_[2]*v_[2] ) );

   if( len == Length(0.0) )
      return *this;

   const Length ilen( Length(1.0) / len );

   // In order to avoid warnings for integer data types explicit casts are
   // used to calculate the normalized vector elements.
   v_[0] = static_cast<Type>( static_cast<Length>( v_[0] ) * ilen );
   v_[1] = static_cast<Type>( static_cast<Length>( v_[1] ) * ilen );
   v_[2] = static_cast<Type>( static_cast<Length>( v_[2] ) * ilen );

   return *this;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn Vector3<Type>& Vector3<Type>::getNormalized() const
// \brief Calculation of the normalized vector (\f$|\vec{a}|=1\f$).
//
// \return The normalized vector.
//
// The function returns the normalized vector. Keep in mind that a vector normalization of a
// vector of integer values will possibly result in a zero vector!
*/
template< typename Type >
inline const Vector3<Type> Vector3<Type>::getNormalized() const
{
   const Length len( std::sqrt( (Length) (v_[0]*v_[0] + v_[1]*v_[1] + v_[2]*v_[2] ) ) );

   if( len == Length(0.0) )
      return *this;

   const Length ilen( Length(1.0) / len );

   // In order to avoid warnings for integer data types explicit casts are
   // used to calculate the normalized vector elements.
   return Vector3<Type>( static_cast<Type>( static_cast<Length>( v_[0] ) * ilen ),
                         static_cast<Type>( static_cast<Length>( v_[1] ) * ilen ),
                         static_cast<Type>( static_cast<Length>( v_[2] ) * ilen ) );
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn Vector3<Type>& Vector3<Type>::normalized() const
// \brief Alias for getNormalized() - returns the normalized vector (\f$|\vec{a}|=1\f$).
//
// \return The normalized vector.
//
// This method is an alias for getNormalized() to provide compatibility with other vector libraries.
*/
template< typename Type >
inline const Vector3<Type> Vector3<Type>::normalized() const
{
   return getNormalized();
}
//*************************************************************************************************




//=================================================================================================
//
//  GLOBAL OPERATORS
//
//=================================================================================================

//*************************************************************************************************
/*!\name Vector3 operators */
//@{

// The following overloads of the comparison operators are necessary to disambiguate
// comparisons between a scalar value and a vector.
template< typename Type > inline bool operator==( unsigned char  scalar, const Vector3<Type>& vec );
template< typename Type > inline bool operator==( char           scalar, const Vector3<Type>& vec );
template< typename Type > inline bool operator==( signed char    scalar, const Vector3<Type>& vec );
template< typename Type > inline bool operator==( wchar_t        scalar, const Vector3<Type>& vec );
template< typename Type > inline bool operator==( unsigned short scalar, const Vector3<Type>& vec );
template< typename Type > inline bool operator==( short          scalar, const Vector3<Type>& vec );
template< typename Type > inline bool operator==( unsigned int   scalar, const Vector3<Type>& vec );
template< typename Type > inline bool operator==( int            scalar, const Vector3<Type>& vec );
template< typename Type > inline bool operator==( unsigned long  scalar, const Vector3<Type>& vec );
template< typename Type > inline bool operator==( long           scalar, const Vector3<Type>& vec );
template< typename Type > inline bool operator==( float          scalar, const Vector3<Type>& vec );
template< typename Type > inline bool operator==( double         scalar, const Vector3<Type>& vec );
template< typename Type > inline bool operator==( long double    scalar, const Vector3<Type>& vec );

template< typename Type > inline bool operator!=( unsigned char  scalar, const Vector3<Type>& vec );
template< typename Type > inline bool operator!=( char           scalar, const Vector3<Type>& vec );
template< typename Type > inline bool operator!=( signed char    scalar, const Vector3<Type>& vec );
template< typename Type > inline bool operator!=( wchar_t        scalar, const Vector3<Type>& vec );
template< typename Type > inline bool operator!=( unsigned short scalar, const Vector3<Type>& vec );
template< typename Type > inline bool operator!=( short          scalar, const Vector3<Type>& vec );
template< typename Type > inline bool operator!=( unsigned int   scalar, const Vector3<Type>& vec );
template< typename Type > inline bool operator!=( int            scalar, const Vector3<Type>& vec );
template< typename Type > inline bool operator!=( unsigned long  scalar, const Vector3<Type>& vec );
template< typename Type > inline bool operator!=( long           scalar, const Vector3<Type>& vec );
template< typename Type > inline bool operator!=( float          scalar, const Vector3<Type>& vec );
template< typename Type > inline bool operator!=( double         scalar, const Vector3<Type>& vec );
template< typename Type > inline bool operator!=( long double    scalar, const Vector3<Type>& vec );

template< typename Type, typename Other >
inline const Vector3<HIGH> operator*( Other scalar, const Vector3<Type>& vec );

template< typename Type >
std::ostream& operator<<( std::ostream& os, const Vector3<Type>& v );

template< typename Type >
std::istream& operator>>( std::istream& is, Vector3<Type>& v );

template< typename Type >
inline bool isnan( const Vector3<Type>& v );

template< typename Type >
inline const Vector3<Type> abs( const Vector3<Type>& v );

template< typename Type >
inline const Vector3<Type> fabs( const Vector3<Type>& v );
//@}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn bool operator==( unsigned char scalar, const Vector3<Type>& vec )
// \brief Equality operator for the comparison of an unsigned char scalar and a vector.
//
// \param scalar The left-hand-side scalar value for the comparison.
// \param vec The right-hand-side vector for the comparison.
// \return bool
//
// If all values of the vector are equal to the scalar value, the equality test returns true,
// otherwise false.
*/
template< typename Type >
inline bool operator==( unsigned char scalar, const Vector3<Type>& vec )
{
   return vec == scalar;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn bool operator==( char scalar, const Vector3<Type>& vec )
// \brief Equality operator for the comparison of a char scalar and a vector.
//
// \param scalar The left-hand-side scalar value for the comparison.
// \param vec The right-hand-side vector for the comparison.
// \return bool
//
// If all values of the vector are equal to the scalar value, the equality test returns true,
// otherwise false.
*/
template< typename Type >
inline bool operator==( char scalar, const Vector3<Type>& vec )
{
   return vec == scalar;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn bool operator==( signed char scalar, const Vector3<Type>& vec )
// \brief Equality operator for the comparison of a signed char scalar and a vector.
//
// \param scalar The left-hand-side scalar value for the comparison.
// \param vec The right-hand-side vector for the comparison.
// \return bool
//
// If all values of the vector are equal to the scalar value, the equality test returns true,
// otherwise false.
*/
template< typename Type >
inline bool operator==( signed char scalar, const Vector3<Type>& vec )
{
   return vec == scalar;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn bool operator==( wchar_t scalar, const Vector3<Type>& vec )
// \brief Equality operator for the comparison of a wchar_t scalar and a vector.
//
// \param scalar The left-hand-side scalar value for the comparison.
// \param vec The right-hand-side vector for the comparison.
// \return bool
//
// If all values of the vector are equal to the scalar value, the equality test returns true,
// otherwise false.
*/
template< typename Type >
inline bool operator==( wchar_t scalar, const Vector3<Type>& vec )
{
   return vec == scalar;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn bool operator==( unsigned short scalar, const Vector3<Type>& vec )
// \brief Equality operator for the comparison of an unsigned short scalar and a vector.
//
// \param scalar The left-hand-side scalar value for the comparison.
// \param vec The right-hand-side vector for the comparison.
// \return bool
//
// If all values of the vector are equal to the scalar value, the equality test returns true,
// otherwise false.
*/
template< typename Type >
inline bool operator==( unsigned short scalar, const Vector3<Type>& vec )
{
   return vec == scalar;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn bool operator==( short scalar, const Vector3<Type>& vec )
// \brief Equality operator for the comparison of a short scalar and a vector.
//
// \param scalar The left-hand-side scalar value for the comparison.
// \param vec The right-hand-side vector for the comparison.
// \return bool
//
// If all values of the vector are equal to the scalar value, the equality test returns true,
// otherwise false.
*/
template< typename Type >
inline bool operator==( short scalar, const Vector3<Type>& vec )
{
   return vec == scalar;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn bool operator==( unsigned int scalar, const Vector3<Type>& vec )
// \brief Equality operator for the comparison of an unsigned int scalar and a vector.
//
// \param scalar The left-hand-side scalar value for the comparison.
// \param vec The right-hand-side vector for the comparison.
// \return bool
//
// If all values of the vector are equal to the scalar value, the equality test returns true,
// otherwise false.
*/
template< typename Type >
inline bool operator==( unsigned int scalar, const Vector3<Type>& vec )
{
   return vec == scalar;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn bool operator==( int scalar, const Vector3<Type>& vec )
// \brief Equality operator for the comparison of an int scalar and a vector.
//
// \param scalar The left-hand-side scalar value for the comparison.
// \param vec The right-hand-side vector for the comparison.
// \return bool
//
// If all values of the vector are equal to the scalar value, the equality test returns true,
// otherwise false.
*/
template< typename Type >
inline bool operator==( int scalar, const Vector3<Type>& vec )
{
   return vec == scalar;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn bool operator==( unsigned long scalar, const Vector3<Type>& vec )
// \brief Equality operator for the comparison of an unsigned long scalar and a vector.
//
// \param scalar The left-hand-side scalar value for the comparison.
// \param vec The right-hand-side vector for the comparison.
// \return bool
//
// If all values of the vector are equal to the scalar value, the equality test returns true,
// otherwise false.
*/
template< typename Type >
inline bool operator==( unsigned long scalar, const Vector3<Type>& vec )
{
   return vec == scalar;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn bool operator==( long scalar, const Vector3<Type>& vec )
// \brief Equality operator for the comparison of a long scalar and a vector.
//
// \param scalar The left-hand-side scalar value for the comparison.
// \param vec The right-hand-side vector for the comparison.
// \return bool
//
// If all values of the vector are equal to the scalar value, the equality test returns true,
// otherwise false.
*/
template< typename Type >
inline bool operator==( long scalar, const Vector3<Type>& vec )
{
   return vec == scalar;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn bool operator==( float scalar, const Vector3<Type>& vec )
// \brief Equality operator for the comparison of a float scalar and a vector.
//
// \param scalar The left-hand-side scalar value for the comparison.
// \param vec The right-hand-side vector for the comparison.
// \return bool
//
// If all values of the vector are equal to the scalar value, the equality test returns true,
// otherwise false.
*/
template< typename Type >
inline bool operator==( float scalar, const Vector3<Type>& vec )
{
   return vec == scalar;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn bool operator==( double scalar, const Vector3<Type>& vec )
// \brief Equality operator for the comparison of a double scalar and a vector.
//
// \param scalar The left-hand-side scalar value for the comparison.
// \param vec The right-hand-side vector for the comparison.
// \return bool
//
// If all values of the vector are equal to the scalar value, the equality test returns true,
// otherwise false.
*/
template< typename Type >
inline bool operator==( double scalar, const Vector3<Type>& vec )
{
   return vec == scalar;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn bool operator==( long double scalar, const Vector3<Type>& vec )
// \brief Equality operator for the comparison of a long double scalar and a vector.
//
// \param scalar The left-hand-side scalar value for the comparison.
// \param vec The right-hand-side vector for the comparison.
// \return bool
//
// If all values of the vector are equal to the scalar value, the equality test returns true,
// otherwise false.
*/
template< typename Type >
inline bool operator==( long double scalar, const Vector3<Type>& vec )
{
   return vec == scalar;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn bool operator!=( unsigned char scalar, const Vector3<Type>& vec )
// \brief Inequality operator for the comparison of an unsigned char scalar value and a vector.
//
// \param scalar The left-hand-side scalar value for the comparison.
// \param vec The right-hand-side vector for the comparision.
// \return bool
//
// If one value of the vector is inequal to the scalar value, the inequality test returns true,
// otherwise false.
*/
template< typename Type >
inline bool operator!=( unsigned char scalar, const Vector3<Type>& vec )
{
   return vec != scalar;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn bool operator!=( char scalar, const Vector3<Type>& vec )
// \brief Inequality operator for the comparison of a char scalar value and a vector.
//
// \param scalar The left-hand-side scalar value for the comparison.
// \param vec The right-hand-side vector for the comparision.
// \return bool
//
// If one value of the vector is inequal to the scalar value, the inequality test returns true,
// otherwise false.
*/
template< typename Type >
inline bool operator!=( char scalar, const Vector3<Type>& vec )
{
   return vec != scalar;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn bool operator!=( signed char scalar, const Vector3<Type>& vec )
// \brief Inequality operator for the comparison of a signed char scalar value and a vector.
//
// \param scalar The left-hand-side scalar value for the comparison.
// \param vec The right-hand-side vector for the comparision.
// \return bool
//
// If one value of the vector is inequal to the scalar value, the inequality test returns true,
// otherwise false.
*/
template< typename Type >
inline bool operator!=( signed char scalar, const Vector3<Type>& vec )
{
   return vec != scalar;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn bool operator!=( wchar_t scalar, const Vector3<Type>& vec )
// \brief Inequality operator for the comparison of a wchar_t scalar value and a vector.
//
// \param scalar The left-hand-side scalar value for the comparison.
// \param vec The right-hand-side vector for the comparision.
// \return bool
//
// If one value of the vector is inequal to the scalar value, the inequality test returns true,
// otherwise false.
*/
template< typename Type >
inline bool operator!=( wchar_t scalar, const Vector3<Type>& vec )
{
   return vec != scalar;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn bool operator!=( unsigned short scalar, const Vector3<Type>& vec )
// \brief Inequality operator for the comparison of an unsigned short scalar value and a vector.
//
// \param scalar The left-hand-side scalar value for the comparison.
// \param vec The right-hand-side vector for the comparision.
// \return bool
//
// If one value of the vector is inequal to the scalar value, the inequality test returns true,
// otherwise false.
*/
template< typename Type >
inline bool operator!=( unsigned short scalar, const Vector3<Type>& vec )
{
   return vec != scalar;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn bool operator!=( short scalar, const Vector3<Type>& vec )
// \brief Inequality operator for the comparison of a short scalar value and a vector.
//
// \param scalar The left-hand-side scalar value for the comparison.
// \param vec The right-hand-side vector for the comparision.
// \return bool
//
// If one value of the vector is inequal to the scalar value, the inequality test returns true,
// otherwise false.
*/
template< typename Type >
inline bool operator!=( short scalar, const Vector3<Type>& vec )
{
   return vec != scalar;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn bool operator!=( unsigned int scalar, const Vector3<Type>& vec )
// \brief Inequality operator for the comparison of an unsigned int scalar value and a vector.
//
// \param scalar The left-hand-side scalar value for the comparison.
// \param vec The right-hand-side vector for the comparision.
// \return bool
//
// If one value of the vector is inequal to the scalar value, the inequality test returns true,
// otherwise false.
*/
template< typename Type >
inline bool operator!=( unsigned int scalar, const Vector3<Type>& vec )
{
   return vec != scalar;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn bool operator!=( int scalar, const Vector3<Type>& vec )
// \brief Inequality operator for the comparison of an int scalar value and a vector.
//
// \param scalar The left-hand-side scalar value for the comparison.
// \param vec The right-hand-side vector for the comparision.
// \return bool
//
// If one value of the vector is inequal to the scalar value, the inequality test returns true,
// otherwise false.
*/
template< typename Type >
inline bool operator!=( int scalar, const Vector3<Type>& vec )
{
   return vec != scalar;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn bool operator!=( unsigned long scalar, const Vector3<Type>& vec )
// \brief Inequality operator for the comparison of an unsigned long scalar value and a vector.
//
// \param scalar The left-hand-side scalar value for the comparison.
// \param vec The right-hand-side vector for the comparision.
// \return bool
//
// If one value of the vector is inequal to the scalar value, the inequality test returns true,
// otherwise false.
*/
template< typename Type >
inline bool operator!=( unsigned long scalar, const Vector3<Type>& vec )
{
   return vec != scalar;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn bool operator!=( long scalar, const Vector3<Type>& vec )
// \brief Inequality operator for the comparison of a long scalar value and a vector.
//
// \param scalar The left-hand-side scalar value for the comparison.
// \param vec The right-hand-side vector for the comparision.
// \return bool
//
// If one value of the vector is inequal to the scalar value, the inequality test returns true,
// otherwise false.
*/
template< typename Type >
inline bool operator!=( long scalar, const Vector3<Type>& vec )
{
   return vec != scalar;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn bool operator!=( float scalar, const Vector3<Type>& vec )
// \brief Inequality operator for the comparison of a float scalar value and a vector.
//
// \param scalar The left-hand-side scalar value for the comparison.
// \param vec The right-hand-side vector for the comparision.
// \return bool
//
// If one value of the vector is inequal to the scalar value, the inequality test returns true,
// otherwise false.
*/
template< typename Type >
inline bool operator!=( float scalar, const Vector3<Type>& vec )
{
   return vec != scalar;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn bool operator!=( double scalar, const Vector3<Type>& vec )
// \brief Inequality operator for the comparison of a double scalar value and a vector.
//
// \param scalar The left-hand-side scalar value for the comparison.
// \param vec The right-hand-side vector for the comparision.
// \return bool
//
// If one value of the vector is inequal to the scalar value, the inequality test returns true,
// otherwise false.
*/
template< typename Type >
inline bool operator!=( double scalar, const Vector3<Type>& vec )
{
   return vec != scalar;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn bool operator!=( long double scalar, const Vector3<Type>& vec )
// \brief Inequality operator for the comparison of a long double scalar value and a vector.
//
// \param scalar The left-hand-side scalar value for the comparison.
// \param vec The right-hand-side vector for the comparision.
// \return bool
//
// If one value of the vector is inequal to the scalar value, the inequality test returns true,
// otherwise false.
*/
template< typename Type >
inline bool operator!=( long double scalar, const Vector3<Type>& vec )
{
   return vec != scalar;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn const Vector3<HIGH> operator*( Other scalar, const Vector3<Type>& vec )
// \brief Multiplication operator for the multiplication of a scalar value and a vector
// \brief (\f$ \vec{a}=s*\vec{b} \f$).
//
// \param scalar The left-hand-side scalar value for the multiplication.
// \param vec The right-hand-side vector for the multiplication.
// \return The scaled result vector.
*/
template< typename Type, typename Other >
inline const Vector3<HIGH> operator*( Other scalar, const Vector3<Type>& vec )
{
   return vec * scalar;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn std::ostream& operator<<( std::ostream& os, const Vector3<Type>& v )
// \brief Global output operator for 3-dimensional vectors.
//
// \param os Reference to the output stream.
// \param v Reference to a constant vector object.
// \return Reference to the output stream.
*/
template< typename Type >
std::ostream& operator<<( std::ostream& os, const Vector3<Type>& v )
{
   return os << "<" << v[0] << "," << v[1] << "," << v[2] << ">";
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn std::istream& operator>>( std::istream& is, Vector3<Type>& v )
// \brief Global input operator for 3-dimensional vectors.
//
// \param is Reference to the input stream.
// \param v Reference to a vector object.
// \return The input stream.
*/
template< typename Type >
std::istream& operator>>( std::istream& is, Vector3<Type>& v )
{
   if( !is ) return is;

   char bracket1, bracket2, comma1, comma2;
   Type x(0), y(0), z(0);
   const std::istream::pos_type pos( is.tellg() );
   const std::istream::fmtflags oldFlags( is.flags() );

   // Setting the 'skip whitespaces' flag
   is >> std::skipws;

   // Extracting the vector
   if( !(is >> bracket1 >> x >> comma1 >> y >> comma2 >> z >> bracket2) ||
       bracket1 != '<' || comma1 != ',' || comma2 != ',' || bracket2 != '>' ) {
      is.clear();
      is.seekg( pos );
      is.setstate( std::istream::failbit );
      is.flags( oldFlags );
      return is;
   }

   // Transfering the input to the vector values
   v[0] = x; v[1] = y; v[2] = z;

   // Resetting the flags
   is.flags( oldFlags );

   return is;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn bool isnan( const Vector3<Type>& v )
// \brief Checks the given vector for not-a-number elements.
//
// \param v The vector to be checked for not-a-number elements.
// \return \a true if at least one element of the vector is not-a-number, \a false otherwise.
*/
template< typename Type >
inline bool isnan( const Vector3<Type>& v )
{
   if( isnan( v[0] ) || isnan( v[1] ) || isnan( v[2] ) )
      return true;
   else return false;
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn const Vector3<Type> abs( const Vector3<Type>& v )
// \brief Returns a vector containing the absolute values of each single element of \a v.
//
// \param v The integral input vector.
// \return The absolute value of each single element of \a v.
//
// The \a abs function calculates the absolute value of each element of the input vector \a v.
// This function can only be applied to vectors of integral data type. For floating point vectors,
// the pe::fabs( const Vector3& ) function can be used.
*/
template< typename Type >
inline const Vector3<Type> abs( const Vector3<Type>& v )
{
   STATIC_ASSERT( std::numeric_limits<Type>::is_integer );
   return Vector3<Type>( std::abs(v[0]), std::abs(v[1]), std::abs(v[2]) );
}
//*************************************************************************************************


//*************************************************************************************************
/*!\fn const Vector3<Type> fabs( const Vector3<Type>& v )
// \brief Returns a vector containing the absolute values of each single element of \a v.
//
// \param v The floating point input vector.
// \return The absolute value of each single element of \a v.
//
// The \a fabs function calculates the absolute value of each element of the input vector \a v.
// This function can only be applied to floating point vectors. For vectors of integral data
// type, the pe::abs( const Vector3& ) function can be used.
*/
template< typename Type >
inline const Vector3<Type> fabs( const Vector3<Type>& v )
{
   STATIC_ASSERT( !std::numeric_limits<Type>::is_integer );
   return Vector3<Type>( std::fabs(v[0]), std::fabs(v[1]), std::fabs(v[2]) );
}
//*************************************************************************************************

} // namespace walberla

#undef HIGH

#endif
