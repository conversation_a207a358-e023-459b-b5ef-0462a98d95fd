\documentclass[12pt,a4paper]{report}
\usepackage{amsmath,amssymb,amsfonts,amsthm}
\usepackage{algorithm}
\usepackage{algorithmic}
\usepackage{listings}
\usepackage{graphicx}
\usepackage{subfigure}
\usepackage{color}
\usepackage{xcolor}
\usepackage{hyperref}
\usepackage{geometry}
\usepackage{tikz}
\usepackage{pgfplots}
\usepackage{bm}
\usepackage{cite}
\usepackage{physics}

\geometry{margin=1in}

% Define theorem environments
\newtheorem{theorem}{Theorem}
\newtheorem{lemma}{Lemma}
\newtheorem{proposition}{Proposition}
\newtheorem{definition}{Definition}
\newtheorem{remark}{Remark}

% Define colors for code listings
\definecolor{codegreen}{rgb}{0,0.6,0}
\definecolor{codegray}{rgb}{0.5,0.5,0.5}
\definecolor{codepurple}{rgb}{0.58,0,0.82}
\definecolor{backcolour}{rgb}{0.95,0.95,0.92}

% Code listing style
\lstdefinestyle{mystyle}{
    backgroundcolor=\color{backcolour},
    commentstyle=\color{codegreen},
    keywordstyle=\color{magenta},
    numberstyle=\tiny\color{codegray},
    stringstyle=\color{codepurple},
    basicstyle=\ttfamily\footnotesize,
    breakatwhitespace=false,
    breaklines=true,
    captionpos=b,
    keepspaces=true,
    numbers=left,
    numbersep=5pt,
    showspaces=false,
    showstringspaces=false,
    showtabs=false,
    tabsize=2,
    language=C++
}
\lstset{style=mystyle}

\begin{document}

\chapter{Advanced Turbulence Modeling in Lattice Boltzmann Methods}

\section{Introduction}

This chapter presents a comprehensive treatment of Large Eddy Simulation (LES) turbulence models integrated with the Lattice Boltzmann Method (LBM). We focus on the theoretical foundations, numerical implementation, and practical considerations for high-fidelity turbulent flow simulations. The integration of LES models with various collision operators (BGK, MRT, TRT) and advanced wall treatments represents a significant advancement in LBM-based computational fluid dynamics.

\section{Theoretical Foundation of LES in LBM}

\subsection{Filtered Lattice Boltzmann Equation}

The Large Eddy Simulation approach in LBM begins with the filtered lattice Boltzmann equation:

\begin{equation}
\overline{f_i}(\mathbf{x} + \mathbf{c}_i \Delta t, t + \Delta t) = \overline{f_i}(\mathbf{x}, t) + \overline{\Omega_i}(\mathbf{x}, t)
\end{equation}

where the overbar denotes spatial filtering and $\Omega_i$ is the collision operator. The filtered equilibrium distribution function is:

\begin{equation}
\overline{f_i^{eq}} = w_i \overline{\rho} \left[1 + \frac{\mathbf{c}_i \cdot \overline{\mathbf{u}}}{c_s^2} + \frac{(\mathbf{c}_i \cdot \overline{\mathbf{u}})^2}{2c_s^4} - \frac{\overline{\mathbf{u}} \cdot \overline{\mathbf{u}}}{2c_s^2}\right]
\end{equation}

The subgrid-scale (SGS) stress tensor arises from the nonlinear term:

\begin{equation}
\tau_{ij}^{SGS} = \overline{\rho u_i u_j} - \overline{\rho}\,\overline{u_i}\,\overline{u_j}
\end{equation}

\subsection{Chapman-Enskog Analysis with SGS Terms}

Through multi-scale Chapman-Enskog expansion with the presence of SGS stresses, we obtain the filtered Navier-Stokes equations:

\begin{align}
\frac{\partial \overline{\rho}}{\partial t} + \nabla \cdot (\overline{\rho}\overline{\mathbf{u}}) &= 0 \\
\frac{\partial (\overline{\rho}\overline{u_i})}{\partial t} + \frac{\partial (\overline{\rho}\overline{u_i}\overline{u_j})}{\partial x_j} &= -\frac{\partial \overline{p}}{\partial x_i} + \frac{\partial}{\partial x_j}\left[2\overline{\rho}\nu\overline{S_{ij}} - \tau_{ij}^{SGS}\right]
\end{align}

where $\overline{S_{ij}} = \frac{1}{2}\left(\frac{\partial \overline{u_i}}{\partial x_j} + \frac{\partial \overline{u_j}}{\partial x_i}\right)$ is the filtered strain rate tensor.

\subsection{Eddy Viscosity Hypothesis}

Following Boussinesq's hypothesis, the SGS stress tensor is modeled as:

\begin{equation}
\tau_{ij}^{SGS} - \frac{1}{3}\tau_{kk}^{SGS}\delta_{ij} = -2\overline{\rho}\nu_t\overline{S_{ij}}
\end{equation}

where $\nu_t$ is the turbulent eddy viscosity. This leads to an effective viscosity:

\begin{equation}
\nu_{eff} = \nu + \nu_t
\end{equation}

In LBM, this translates to an effective relaxation time:

\begin{equation}
\tau_{eff} = \tau_0 + \frac{\nu_t}{c_s^2\Delta t} = \frac{1}{2} + \frac{\nu_{eff}}{c_s^2\Delta t}
\end{equation}

\section{Smagorinsky Model}

\subsection{Standard Smagorinsky Model}

The standard Smagorinsky model, proposed by Smagorinsky (1963), relates the eddy viscosity to the local strain rate:

\begin{equation}
\nu_t = (C_s \Delta)^2 |\overline{S}|
\end{equation}

where $C_s$ is the Smagorinsky constant, $\Delta$ is the filter width, and $|\overline{S}|$ is the characteristic filtered strain rate:

\begin{equation}
|\overline{S}| = \sqrt{2\overline{S_{ij}}\overline{S_{ij}}} = \sqrt{2\sum_{i,j} \overline{S_{ij}}^2}
\end{equation}

\subsubsection{Strain Rate Computation in LBM}

In LBM, the strain rate tensor is computed from non-equilibrium moments:

\begin{equation}
\overline{S_{\alpha\beta}} = -\frac{1}{2\rho c_s^2\tau} \sum_i \Pi_i^{(1)neq} c_{i\alpha}c_{i\beta}
\end{equation}

where $\Pi_i^{(1)neq} = f_i - f_i^{eq}$ is the non-equilibrium part of the distribution function.

\begin{definition}[Non-equilibrium Stress Tensor]
The non-equilibrium stress tensor is defined as:
\begin{equation}
\Pi_{\alpha\beta}^{neq} = \sum_i (f_i - f_i^{eq}) c_{i\alpha} c_{i\beta}
\end{equation}
\end{definition}

\subsubsection{Implementation Details}

\begin{lstlisting}[caption={Smagorinsky model implementation},label={lst:smagorinsky_detailed}]
class SmagorinskyModel : public TurbulenceModel {
private:
    Real cs_;           // Smagorinsky constant
    Real filterWidth_;  // Filter width (typically 2*dx)
    
public:
    Real computeEddyViscosity(
        const Uint x, const Uint y, const Uint z,
        const PDFField& pdf,
        const Real& rho,
        const Vector3<Real>& vel) const override {
        
        // Compute non-equilibrium stress tensor
        Matrix3<Real> PiNeq = computeNonEqStressTensor(x, y, z, pdf, rho, vel);
        
        // Compute strain rate tensor from Pi^neq
        Real tau0 = 0.5 + nu0_ / (cs2_ * dt_);
        Matrix3<Real> S;
        
        for (int alpha = 0; alpha < 3; ++alpha) {
            for (int beta = 0; beta < 3; ++beta) {
                S(alpha, beta) = -PiNeq(alpha, beta) / (2.0 * rho * cs2_ * tau0);
            }
        }
        
        // Compute strain rate magnitude
        Real S_magnitude = 0.0;
        for (int i = 0; i < 3; ++i) {
            for (int j = 0; j < 3; ++j) {
                S_magnitude += S(i,j) * S(i,j);
            }
        }
        S_magnitude = sqrt(2.0 * S_magnitude);
        
        // Smagorinsky eddy viscosity
        return cs_ * cs_ * filterWidth_ * filterWidth_ * S_magnitude;
    }
    
    Matrix3<Real> computeNonEqStressTensor(
        const Uint x, const Uint y, const Uint z,
        const PDFField& pdf,
        const Real& rho,
        const Vector3<Real>& vel) const {
        
        Matrix3<Real> PiNeq(0.0);
        
        for (Uint i = 0; i < D3Q19::Cellsize; ++i) {
            // Compute equilibrium
            Real feq = computeEquilibrium(i, rho, vel);
            Real fneq = pdf.get(x, y, z, i) - feq;
            
            // Add contribution to stress tensor
            for (int alpha = 0; alpha < 3; ++alpha) {
                for (int beta = 0; beta < 3; ++beta) {
                    PiNeq(alpha, beta) += fneq * 
                        Real(D3Q19::c[i][alpha]) * 
                        Real(D3Q19::c[i][beta]);
                }
            }
        }
        
        return PiNeq;
    }
};
\end{lstlisting}

\subsection{Near-Wall Damping}

Near solid boundaries, the Smagorinsky constant must be damped to account for the reduced turbulent length scales:

\begin{equation}
C_s(y^+) = C_{s0} \left[1 - \exp\left(-\frac{y^+}{A^+}\right)\right]
\end{equation}

where $y^+ = y u_\tau/\nu$ is the dimensionless wall distance, $u_\tau = \sqrt{\tau_w/\rho}$ is the friction velocity, and $A^+ \approx 25$ is the van Driest constant.

\subsection{Compressible Extension}

For compressible flows, the Smagorinsky model is modified to include density variations:

\begin{equation}
\nu_t = (C_s \Delta)^2 \frac{\rho}{\overline{\rho}} |\overline{S}|
\end{equation}

with an additional dilatation term in the SGS stress:

\begin{equation}
\tau_{ij}^{SGS} = -2\overline{\rho}\nu_t\left(\overline{S_{ij}} - \frac{1}{3}\overline{S_{kk}}\delta_{ij}\right) + \frac{2}{3}\overline{\rho}k_{SGS}\delta_{ij}
\end{equation}

\section{Dynamic Smagorinsky Model}

\subsection{Germano Identity}

The dynamic procedure, introduced by Germano et al. (1991), eliminates the need for an empirical constant by using information from resolved scales. The Germano identity relates stresses at two filter levels:

\begin{equation}
\mathcal{L}_{ij} = \widehat{\overline{u_i u_j}} - \hat{\overline{u}}_i \hat{\overline{u}}_j = T_{ij} - \hat{\tau}_{ij}
\end{equation}

where the hat denotes test filtering at scale $\hat{\Delta} = 2\Delta$, and:
- $\mathcal{L}_{ij}$ is the resolved turbulent stress (Leonard stress)
- $T_{ij} = \widehat{\overline{\rho u_i u_j}} - \hat{\overline{\rho}}\hat{\overline{u}}_i\hat{\overline{u}}_j$ is the test-level SGS stress
- $\tau_{ij}$ is the grid-level SGS stress

\subsection{Dynamic Coefficient Computation}

Assuming scale similarity:
\begin{align}
\tau_{ij} - \frac{1}{3}\tau_{kk}\delta_{ij} &= -2C\Delta^2\overline{\rho}|\overline{S}|\overline{S_{ij}} \\
T_{ij} - \frac{1}{3}T_{kk}\delta_{ij} &= -2C\hat{\Delta}^2\hat{\overline{\rho}}|\widehat{\overline{S}}|\widehat{\overline{S_{ij}}}
\end{align}

Substituting into the Germano identity and solving for $C$ using least squares:

\begin{equation}
C = \frac{\langle \mathcal{L}_{ij}M_{ij} \rangle}{\langle M_{ij}M_{ij} \rangle}
\end{equation}

where:
\begin{equation}
M_{ij} = -2\left[\hat{\Delta}^2\hat{\overline{\rho}}|\widehat{\overline{S}}|\widehat{\overline{S_{ij}}} - \widehat{\Delta^2\overline{\rho}|\overline{S}|\overline{S_{ij}}}\right]
\end{equation}

\subsection{Stabilization Techniques}

\subsubsection{Clipping}
To prevent negative eddy viscosity:
\begin{equation}
C_{dyn} = \max(0, C), \quad \text{or} \quad C_{dyn} = \min(C_{max}, \max(0, C))
\end{equation}

\subsubsection{Averaging}
Spatial averaging over homogeneous directions:
\begin{equation}
C = \frac{\langle \mathcal{L}_{ij}M_{ij} \rangle_{xy}}{\langle M_{ij}M_{ij} \rangle_{xy}}
\end{equation}

\subsubsection{Lagrangian Averaging}
Time averaging along pathlines:
\begin{equation}
\mathcal{I}_{LM}(t) = \int_0^t \mathcal{L}_{ij}M_{ij} \exp\left[-\frac{t-t'}{\mathcal{T}}\right] dt'
\end{equation}

\subsection{Implementation}

\begin{lstlisting}[caption={Dynamic Smagorinsky implementation},label={lst:dynamic_smagorinsky}]
class DynamicSmagorinskyModel : public TurbulenceModel {
private:
    Real filterRatio_ = 2.0;  // Test to grid filter ratio
    Field<Real, 1> CdynField_;  // Dynamic coefficient field
    
public:
    void updateDynamicCoefficient(
        const VelField& velField,
        const DensField& densField) {
        
        // Apply test filter
        VelField testVel = applyTestFilter(velField);
        DensField testDens = applyTestFilter(densField);
        
        for (auto cell : domain) {
            // Compute strain rates at both levels
            Matrix3<Real> S = computeStrainRate(cell, velField);
            Matrix3<Real> S_test = computeStrainRate(cell, testVel);
            
            Real S_mag = computeMagnitude(S);
            Real S_test_mag = computeMagnitude(S_test);
            
            // Compute Leonard stress
            Matrix3<Real> L = computeLeonardStress(cell, velField, densField,
                                                  testVel, testDens);
            
            // Compute M tensor
            Matrix3<Real> M = computeMTensor(cell, S, S_mag, S_test, S_test_mag,
                                           densField, testDens);
            
            // Contract tensors
            Real LM = contractTensors(L, M);
            Real MM = contractTensors(M, M);
            
            // Compute dynamic coefficient with stabilization
            Real C = (MM > epsilon_) ? LM / MM : 0.0;
            C = stabilizeCoefficient(C);
            
            CdynField_.get(cell) = C;
        }
        
        // Apply averaging if needed
        if (useAveraging_) {
            applyAveraging(CdynField_);
        }
    }
    
    Real stabilizeCoefficient(Real C) const {
        // Clipping
        C = std::max(0.0, C);
        C = std::min(C, Cmax_);
        
        // Exponential averaging in time
        static Real C_avg = 0.0;
        const Real alpha = 0.1;  // Time averaging parameter
        C_avg = alpha * C + (1.0 - alpha) * C_avg;
        
        return C_avg;
    }
    
    Matrix3<Real> computeLeonardStress(
        const Cell& cell,
        const VelField& vel,
        const DensField& dens,
        const VelField& testVel,
        const DensField& testDens) const {
        
        Matrix3<Real> L;
        
        // Compute resolved Reynolds stress
        for (int i = 0; i < 3; ++i) {
            for (int j = 0; j < 3; ++j) {
                // Test-filtered product
                Real testProduct = testDens.get(cell) * 
                                  testVel.get(cell, i) * 
                                  testVel.get(cell, j);
                
                // Product of test-filtered quantities
                Real productTest = applyTestFilter(
                    dens.get(cell) * vel.get(cell, i) * vel.get(cell, j)
                );
                
                L(i,j) = productTest - testProduct;
            }
        }
        
        return L;
    }
};
\end{lstlisting}

\section{WALE Model}

\subsection{Mathematical Formulation}

The Wall-Adapting Local Eddy-viscosity (WALE) model by Nicoud and Ducros (1999) provides proper near-wall scaling without requiring explicit damping:

\begin{equation}
\nu_t = (C_w \Delta)^2 \frac{(S_{ij}^d S_{ij}^d)^{3/2}}{(\overline{S}_{ij}\overline{S}_{ij})^{5/2} + (S_{ij}^d S_{ij}^d)^{5/4}}
\end{equation}

where $C_w \approx 0.325$ and $S_{ij}^d$ is the traceless symmetric part of the square of the velocity gradient tensor:

\begin{equation}
S_{ij}^d = \frac{1}{2}(\bar{g}_{ij}^2 + \bar{g}_{ji}^2) - \frac{1}{3}\delta_{ij}\bar{g}_{kk}^2
\end{equation}

with $\bar{g}_{ij} = \partial \bar{u}_i / \partial x_j$ and $\bar{g}_{ij}^2 = \bar{g}_{ik}\bar{g}_{kj}$.

\subsection{Properties}

\begin{theorem}[Near-Wall Behavior]
The WALE model provides the correct asymptotic behavior near walls:
\begin{equation}
\nu_t = O(y^3) \quad \text{as} \quad y \to 0
\end{equation}
\end{theorem}

\begin{proof}
Near a wall with no-slip conditions, the velocity field behaves as:
\begin{equation}
u = ay + by^2 + O(y^3), \quad v = -ay^2/2 + O(y^3), \quad w = O(y^3)
\end{equation}

Computing $S_{ij}^d$ with these expressions shows that all components are $O(y)$, giving $(S_{ij}^d S_{ij}^d)^{3/2} = O(y^3)$. Since $\overline{S}_{ij} = O(1)$ near the wall, the denominator remains finite, yielding the desired scaling.
\end{proof}

\subsection{Implementation}

\begin{lstlisting}[caption={WALE model implementation},label={lst:wale}]
class WALEModel : public TurbulenceModel {
private:
    Real Cw_ = 0.325;
    
    Matrix3<Real> computeVelocityGradient(
        const Cell& cell,
        const VelField& velField) const {
        
        Matrix3<Real> gradU;
        
        // Central differences for interior cells
        for (int i = 0; i < 3; ++i) {
            for (int j = 0; j < 3; ++j) {
                Real dUidxj = 0.0;
                
                // Use isotropic central differences
                if (j == 0) {  // d/dx
                    dUidxj = 0.5 * (velField.get(cell.neighborE(), i) - 
                                   velField.get(cell.neighborW(), i));
                } else if (j == 1) {  // d/dy
                    dUidxj = 0.5 * (velField.get(cell.neighborN(), i) - 
                                   velField.get(cell.neighborS(), i));
                } else {  // d/dz
                    dUidxj = 0.5 * (velField.get(cell.neighborT(), i) - 
                                   velField.get(cell.neighborB(), i));
                }
                
                gradU(i, j) = dUidxj;
            }
        }
        
        return gradU;
    }
    
public:
    Real computeEddyViscosity(
        const Cell& cell,
        const VelField& velField,
        const DensField& densField) const override {
        
        // Compute velocity gradient tensor
        Matrix3<Real> g = computeVelocityGradient(cell, velField);
        
        // Compute g^2 = g_ik * g_kj
        Matrix3<Real> g2 = g * g;
        
        // Compute trace(g^2)
        Real trace_g2 = g2(0,0) + g2(1,1) + g2(2,2);
        
        // Compute S_ij^d
        Matrix3<Real> Sd;
        for (int i = 0; i < 3; ++i) {
            for (int j = 0; j < 3; ++j) {
                Sd(i,j) = 0.5 * (g2(i,j) + g2(j,i));
                if (i == j) {
                    Sd(i,j) -= trace_g2 / 3.0;
                }
            }
        }
        
        // Compute invariants
        Real Sd_Sd = 0.0;
        for (int i = 0; i < 3; ++i) {
            for (int j = 0; j < 3; ++j) {
                Sd_Sd += Sd(i,j) * Sd(i,j);
            }
        }
        
        // Compute strain rate magnitude
        Matrix3<Real> S = computeStrainRateTensor(g);
        Real S_S = 0.0;
        for (int i = 0; i < 3; ++i) {
            for (int j = 0; j < 3; ++j) {
                S_S += S(i,j) * S(i,j);
            }
        }
        
        // WALE eddy viscosity
        Real numerator = pow(Sd_Sd, 1.5);
        Real denominator = pow(S_S, 2.5) + pow(Sd_Sd, 1.25);
        
        if (denominator < 1e-10) {
            return 0.0;
        }
        
        return Cw_ * Cw_ * filterWidth_ * filterWidth_ * 
               numerator / denominator;
    }
};
\end{lstlisting}

\section{Vreman Model}

\subsection{Formulation}

The Vreman model (2004) is designed to be zero for certain laminar flows while remaining simple to implement:

\begin{equation}
\nu_t = c \sqrt{\frac{B_\beta}{\alpha_{ij}\alpha_{ij}}}
\end{equation}

where $c = 2.5 C_s^2 \approx 0.07$ and:

\begin{align}
\alpha_{ij} &= \frac{\partial u_j}{\partial x_i} = g_{ji} \\
\beta_{ij} &= \Delta_m^2 \alpha_{mi}\alpha_{mj} \\
B_\beta &= \beta_{11}\beta_{22} - \beta_{12}^2 + \beta_{11}\beta_{33} - \beta_{13}^2 + \beta_{22}\beta_{33} - \beta_{23}^2
\end{align}

\subsection{Properties}

\begin{proposition}[Vanishing for 2D Flows]
The Vreman model gives $\nu_t = 0$ for any two-dimensional flow in the $x$-$y$ plane.
\end{proposition}

\begin{proof}
For 2D flow, $u_3 = 0$ and $\partial/\partial x_3 = 0$. Thus:
\begin{equation}
\alpha_{i3} = \alpha_{3j} = 0 \quad \forall i,j
\end{equation}

This leads to $\beta_{13} = \beta_{23} = \beta_{33} = 0$, and consequently:
\begin{equation}
B_\beta = \beta_{11}\beta_{22} - \beta_{12}^2 = (\Delta_1^2\alpha_{11})(\Delta_2^2\alpha_{22}) - (\Delta_1\Delta_2\alpha_{12})^2 = 0
\end{equation}

for uniform grids where $\Delta_1 = \Delta_2$.
\end{proof}

\subsection{Implementation}

\begin{lstlisting}[caption={Vreman model implementation},label={lst:vreman}]
class VremanModel : public TurbulenceModel {
private:
    Real c_ = 0.07;  // Model constant
    
public:
    Real computeEddyViscosity(
        const Cell& cell,
        const VelField& velField) const override {
        
        // Compute velocity gradient tensor (transpose of g)
        Matrix3<Real> alpha = computeVelocityGradientTranspose(cell, velField);
        
        // Compute beta tensor
        Matrix3<Real> beta;
        for (int i = 0; i < 3; ++i) {
            for (int j = 0; j < 3; ++j) {
                beta(i,j) = 0.0;
                for (int m = 0; m < 3; ++m) {
                    Real Delta_m = (m == 0) ? dx_ : (m == 1) ? dy_ : dz_;
                    beta(i,j) += Delta_m * Delta_m * alpha(m,i) * alpha(m,j);
                }
            }
        }
        
        // Compute B_beta invariant
        Real B_beta = beta(0,0)*beta(1,1) - beta(0,1)*beta(0,1) +
                      beta(0,0)*beta(2,2) - beta(0,2)*beta(0,2) +
                      beta(1,1)*beta(2,2) - beta(1,2)*beta(1,2);
        
        // Compute alpha norm squared
        Real alpha_sq = 0.0;
        for (int i = 0; i < 3; ++i) {
            for (int j = 0; j < 3; ++j) {
                alpha_sq += alpha(i,j) * alpha(i,j);
            }
        }
        
        // Vreman eddy viscosity
        if (alpha_sq < 1e-10 || B_beta < 0.0) {
            return 0.0;
        }
        
        return c_ * sqrt(B_beta / alpha_sq);
    }
    
    // Specialized for non-uniform grids
    Real computeEddyViscosityNonUniform(
        const Cell& cell,
        const VelField& velField,
        const Vector3<Real>& gridSpacing) const {
        
        Matrix3<Real> alpha = computeVelocityGradientTranspose(cell, velField);
        Matrix3<Real> beta;
        
        // Account for non-uniform grid spacing
        for (int i = 0; i < 3; ++i) {
            for (int j = 0; j < 3; ++j) {
                beta(i,j) = 0.0;
                for (int m = 0; m < 3; ++m) {
                    beta(i,j) += gridSpacing[m] * gridSpacing[m] * 
                                alpha(m,i) * alpha(m,j);
                }
            }
        }
        
        // Rest of computation remains the same
        // ...
    }
};
\end{lstlisting}

\section{MRT-LES Integration}

\subsection{Multiple Relaxation Time Operator}

The MRT collision operator transforms distribution functions to moment space:

\begin{equation}
\mathbf{f}(\mathbf{x} + \mathbf{c}_i\Delta t, t + \Delta t) = \mathbf{f}(\mathbf{x}, t) - \mathbf{M}^{-1}\mathbf{S}[\mathbf{m} - \mathbf{m}^{eq}]
\end{equation}

where $\mathbf{M}$ is the transformation matrix, $\mathbf{m} = \mathbf{M}\mathbf{f}$ are the moments, and $\mathbf{S} = \text{diag}(s_0, s_1, ..., s_{18})$ is the relaxation matrix.

\subsection{D3Q19 Moment Basis}

For the D3Q19 lattice, the moment basis includes:

\begin{align}
m_0 &= \rho \quad \text{(density)} \\
m_{1,2,3} &= \rho u_{x,y,z} \quad \text{(momentum)} \\
m_4 &= e \quad \text{(energy)} \\
m_5 &= e_2 \quad \text{(energy square)} \\
m_{6,7,8} &= p_{x,y,z} \quad \text{(momentum flux)} \\
m_9 &= 3p_{xx} \quad \text{(normal stress)} \\
m_{10} &= p_{ww} \quad \text{(diagonal stress)} \\
m_{11} &= p_{xy} \quad \text{(shear stress)} \\
m_{12,13} &= p_{xz}, p_{yz} \quad \text{(off-diagonal stress)} \\
m_{14,15,16} &= m_{x,y,z} \quad \text{(third-order moments)} \\
m_{17,18} &= m_{xyz} \quad \text{(higher-order moments)}
\end{align}

\subsection{Turbulence-Modified Relaxation}

In MRT-LES, the relaxation parameters for shear modes are modified based on the effective viscosity:

\begin{equation}
s_{\nu,eff} = \frac{1}{\tau_{eff}} = \frac{1}{\tau_0 + \nu_t/(c_s^2\Delta t)}
\end{equation}

The modified relaxation matrix becomes:

\begin{equation}
\mathbf{S}_{turb} = \text{diag}(s_0, s_1, s_2, s_3, s_e, s_\epsilon, s_q, s_q, s_q, s_{\nu,eff}, s_\pi, s_{\nu,eff}, s_{\nu,eff}, s_{\nu,eff}, s_m, s_m, s_m, s_m, s_m)
\end{equation}

\subsection{Implementation}

\begin{lstlisting}[caption={MRT-LES implementation},label={lst:mrtles}]
class MRTTurbulence {
private:
    Matrix<Real, 19, 19> M_;      // Transformation matrix
    Matrix<Real, 19, 19> Minv_;   // Inverse transformation
    Vector<Real, 19> s_;          // Base relaxation rates
    
    void initializeD3Q19Matrices() {
        // Transformation matrix for D3Q19
        M_ = {
            // Row 0: density
            {1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1},
            // Rows 1-3: momentum
            {0, 1, -1, 0, 0, 0, 0, 1, -1, 1, -1, 1, -1, 1, -1, 0, 0, 0, 0},
            {0, 0, 0, 1, -1, 0, 0, 1, 1, -1, -1, 0, 0, 0, 0, 1, -1, 1, -1},
            {0, 0, 0, 0, 0, 1, -1, 0, 0, 0, 0, 1, 1, -1, -1, 1, 1, -1, -1},
            // Row 4: energy
            {-30, -11, -11, -11, -11, -11, -11, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8},
            // ... (remaining rows for higher moments)
        };
        
        // Compute inverse
        Minv_ = M_.inverse();
        
        // Initialize relaxation rates
        s_ = {1.0, 1.0, 1.0, 1.0, 1.3, 1.3, 1.0, 1.0, 1.0, 
              omega_, 1.2, omega_, omega_, omega_, 1.0, 1.0, 1.0, 1.0, 1.0};
    }
    
public:
    void collideWithTurbulence(
        PDFField& pdf,
        const Cell& cell,
        const Real& nu_t) {
        
        // Get PDFs at cell
        Vector<Real, 19> f;
        for (Uint i = 0; i < 19; ++i) {
            f[i] = pdf.get(cell, i);
        }
        
        // Transform to moment space
        Vector<Real, 19> m = M_ * f;
        
        // Compute equilibrium moments
        Real rho = m[0];
        Vector3<Real> u(m[1]/rho, m[2]/rho, m[3]/rho);
        Vector<Real, 19> meq = computeEquilibriumMoments(rho, u);
        
        // Modify relaxation for turbulence
        Vector<Real, 19> s_turb = s_;
        Real omega_eff = 1.0 / (0.5 + (nu_0_ + nu_t) / (cs2_ * dt_));
        
        // Update shear mode relaxation rates
        s_turb[9] = omega_eff;   // p_xx
        s_turb[11] = omega_eff;  // p_xy
        s_turb[12] = omega_eff;  // p_xz
        s_turb[13] = omega_eff;  // p_yz
        
        // Collision in moment space
        for (int i = 0; i < 19; ++i) {
            m[i] = m[i] - s_turb[i] * (m[i] - meq[i]);
        }
        
        // Transform back to velocity space
        f = Minv_ * m;
        
        // Store updated PDFs
        for (Uint i = 0; i < 19; ++i) {
            pdf.get(cell, i) = f[i];
        }
    }
    
    Vector<Real, 19> computeEquilibriumMoments(
        const Real& rho,
        const Vector3<Real>& u) {
        
        Vector<Real, 19> meq;
        
        Real u2 = u.squaredNorm();
        Real ux = u[0], uy = u[1], uz = u[2];
        
        // Density
        meq[0] = rho;
        
        // Momentum
        meq[1] = rho * ux;
        meq[2] = rho * uy;
        meq[3] = rho * uz;
        
        // Energy moments
        meq[4] = -11.0 * rho + 19.0 * rho * u2;
        meq[5] = omega_bulk_ * meq[4];  // Bulk viscosity
        
        // Momentum flux
        meq[6] = -2.0 * rho * ux;
        meq[7] = -2.0 * rho * uy;
        meq[8] = -2.0 * rho * uz;
        
        // Stress tensor components
        meq[9] = rho * (2.0*ux*ux - uy*uy - uz*uz);
        meq[10] = rho * (uy*uy - uz*uz);
        meq[11] = rho * ux * uy;
        meq[12] = rho * ux * uz;
        meq[13] = rho * uy * uz;
        
        // Higher order moments (set to zero for isothermal)
        for (int i = 14; i < 19; ++i) {
            meq[i] = 0.0;
        }
        
        return meq;
    }
};
\end{lstlisting}

\section{TRT-LES Integration}

\subsection{Two-Relaxation-Time Model}

The TRT model uses different relaxation rates for symmetric and anti-symmetric parts of the distribution:

\begin{align}
f_i^+ &= \frac{1}{2}(f_i + f_{\bar{i}}) \quad \text{(symmetric)} \\
f_i^- &= \frac{1}{2}(f_i - f_{\bar{i}}) \quad \text{(anti-symmetric)}
\end{align}

The collision operator becomes:

\begin{equation}
f_i^* = f_i - \omega^+(f_i^+ - f_i^{eq+}) - \omega^-(f_i^- - f_i^{eq-})
\end{equation}

\subsection{Magic Parameter}

The TRT model has a free parameter $\Lambda = (\tau^+ - 0.5)(\tau^- - 0.5)$ that controls numerical properties:

\begin{itemize}
\item $\Lambda = 1/4$: Optimal for boundary location independence
\item $\Lambda = 3/16$: Optimal for steady flow accuracy
\item $\Lambda = 1/12$: Cancels third-order advection error
\end{itemize}

\subsection{Turbulence Integration}

For LES, only the symmetric relaxation rate is modified:

\begin{equation}
\omega^+_{eff} = \frac{1}{\tau_0 + \nu_t/(c_s^2\Delta t)}
\end{equation}

while $\omega^-$ remains constant to maintain the magic parameter relationship.

\begin{lstlisting}[caption={TRT-LES implementation},label={lst:trtles_full}]
template<typename LatticeModel>
class TRTTurbulence {
private:
    Real omega_plus_;    // Symmetric relaxation
    Real omega_minus_;   // Anti-symmetric relaxation
    Real Lambda_;        // Magic parameter
    
public:
    TRTTurbulence(Real nu, Real Lambda = 3.0/16.0) 
        : Lambda_(Lambda) {
        // Compute base relaxation rates
        Real tau_plus = 3.0 * nu + 0.5;
        omega_plus_ = 1.0 / tau_plus;
        
        // Solve for omega_minus from magic parameter
        // Lambda = (tau_plus - 0.5)(tau_minus - 0.5)
        Real tau_minus = Lambda_ / (tau_plus - 0.5) + 0.5;
        omega_minus_ = 1.0 / tau_minus;
    }
    
    void collideWithTurbulence(
        PDFField& pdf,
        const Cell& cell,
        const Real& nu_t,
        const Real& rho,
        const Vector3<Real>& u) {
        
        // Compute effective symmetric relaxation
        Real nu_eff = nu_0_ + nu_t;
        Real tau_plus_eff = 3.0 * nu_eff + 0.5;
        Real omega_plus_eff = 1.0 / tau_plus_eff;
        
        // Maintain magic parameter by adjusting omega_minus
        Real tau_minus_eff = Lambda_ / (tau_plus_eff - 0.5) + 0.5;
        Real omega_minus_eff = 1.0 / tau_minus_eff;
        
        // Apply TRT collision
        for (Uint i = 0; i < LatticeModel::Cellsize; ++i) {
            Uint opp = LatticeModel::opposite[i];
            
            // Get distributions
            Real fi = pdf.get(cell, i);
            Real foppi = pdf.get(cell, opp);
            
            // Compute equilibria
            Real feqi = computeEquilibrium(i, rho, u);
            Real feqoppi = computeEquilibrium(opp, rho, u);
            
            // Split into symmetric and anti-symmetric
            Real fi_plus = 0.5 * (fi + foppi);
            Real fi_minus = 0.5 * (fi - foppi);
            Real feqi_plus = 0.5 * (feqi + feqoppi);
            Real feqi_minus = 0.5 * (feqi - feqoppi);
            
            // TRT collision with turbulence
            Real fi_star = fi - omega_plus_eff * (fi_plus - feqi_plus)
                              - omega_minus_eff * (fi_minus - feqi_minus);
            
            pdf.get(cell, i) = fi_star;
        }
    }
    
    // Special treatment for boundaries
    void applyBoundaryCorrection(
        PDFField& pdf,
        const Cell& cell,
        const Uint missingDir,
        const Real& q) {
        
        // TRT provides better stability for curved boundaries
        // due to controlled viscosity-independent errors
        
        Uint opp = LatticeModel::opposite[missingDir];
        
        // Reconstruct missing population using TRT symmetry
        Real fi_known = pdf.get(cell, opp);
        Real fi_eq = computeEquilibrium(missingDir, rho, u);
        Real foppi_eq = computeEquilibrium(opp, rho, u);
        
        // Use magic parameter relationship
        Real fi_missing = 2.0 * fi_eq - fi_known + 
                         (1.0 - omega_minus_) * (fi_eq - foppi_eq);
        
        pdf.get(cell, missingDir) = fi_missing;
    }
};
\end{lstlisting}

\section{Wall Models}

\subsection{Van Driest Damping Function}

The van Driest damping function modifies the mixing length near walls:

\begin{equation}
l_m = \kappa y \left[1 - \exp\left(-\frac{y^+}{A^+}\right)\right]
\end{equation}

where $\kappa = 0.41$ is the von Kármán constant and $A^+ = 26$ is the van Driest constant.

For LES, this translates to a damped eddy viscosity:

\begin{equation}
\nu_t = \nu_{t,\infty} \left[1 - \exp\left(-\frac{y^+}{A^+}\right)\right]^2
\end{equation}

\subsection{Spalding's Wall Function}

Spalding's law provides a single expression valid across all wall regions:

\begin{equation}
y^+ = u^+ + e^{-\kappa B}\left[e^{\kappa u^+} - 1 - \kappa u^+ - \frac{(\kappa u^+)^2}{2} - \frac{(\kappa u^+)^3}{6}\right]
\end{equation}

where $B = 5.2$. This requires iterative solution for $u^+$ given $y^+$.

\subsubsection{Newton-Raphson Solution}

\begin{lstlisting}[caption={Spalding wall function solver},label={lst:spalding}]
class SpaldingWallFunction : public WallModel {
private:
    Real kappa_ = 0.41;
    Real B_ = 5.2;
    Real E_ = exp(kappa_ * B_);
    
    Real spaldingFunction(Real uPlus) const {
        Real ku = kappa_ * uPlus;
        Real ku2 = ku * ku;
        Real ku3 = ku2 * ku;
        
        return uPlus + exp(-kappa_ * B_) * 
               (exp(ku) - 1.0 - ku - ku2/2.0 - ku3/6.0);
    }
    
    Real spaldingDerivative(Real uPlus) const {
        Real ku = kappa_ * uPlus;
        Real ku2 = ku * ku;
        
        return 1.0 + exp(-kappa_ * B_) * kappa_ * 
               (exp(ku) - 1.0 - ku - ku2/2.0);
    }
    
public:
    Real computeUPlus(Real yPlus) const override {
        // Initial guess
        Real uPlus;
        if (yPlus < 5.0) {
            uPlus = yPlus;  // Viscous sublayer
        } else if (yPlus < 30.0) {
            uPlus = yPlus / 1.2;  // Buffer layer
        } else {
            uPlus = log(E_ * yPlus) / kappa_;  // Log layer
        }
        
        // Newton-Raphson iteration
        const Real tol = 1e-6;
        const Uint maxIter = 20;
        
        for (Uint iter = 0; iter < maxIter; ++iter) {
            Real f = spaldingFunction(uPlus) - yPlus;
            Real df = spaldingDerivative(uPlus);
            
            Real delta = -f / df;
            uPlus += delta;
            
            if (abs(delta) < tol * abs(uPlus)) {
                break;
            }
        }
        
        return uPlus;
    }
    
    void applyWallModel(
        Cell& wallCell,
        const Real& wallDistance,
        const Vector3<Real>& wallNormal,
        VelField& velField,
        const Real& nu) override {
        
        // Get cell velocity
        Vector3<Real> vel = velField.get(wallCell);
        
        // Decompose into normal and tangential
        Real u_normal = vel * wallNormal;
        Vector3<Real> u_tangent = vel - wallNormal * u_normal;
        Real u_parallel = u_tangent.norm();
        
        if (u_parallel < 1e-10) return;
        
        // Iteratively compute friction velocity
        Real u_tau = computeFrictionVelocity(u_parallel, wallDistance, nu);
        
        // Compute y+ and u+
        Real yPlus = wallDistance * u_tau / nu;
        Real uPlus = computeUPlus(yPlus);
        
        // Reconstruct velocity
        Real u_parallel_corrected = uPlus * u_tau;
        u_tangent *= (u_parallel_corrected / u_parallel);
        
        // Update velocity field (zero normal component)
        velField.get(wallCell) = u_tangent;
    }
};
\end{lstlisting}

\subsection{Werner-Wengle Power Law}

The Werner-Wengle model uses a simple power law that avoids iteration:

\begin{equation}
u^+ = \begin{cases}
y^+ & \text{if } y^+ \leq y_c^+ \\
A(y^+)^B & \text{if } y^+ > y_c^+
\end{cases}
\end{equation}

where $y_c^+ = 11.81$, $A = 8.3$, and $B = 1/7$.

The wall shear stress is computed analytically:

\begin{equation}
\tau_w = \begin{cases}
\mu \frac{u}{y} & \text{if } y^+ \leq y_c^+ \\
\rho \left(\frac{u}{A^{1/B}y}\right)^{2/(1+B)} & \text{if } y^+ > y_c^+
\end{cases}
\end{equation}

\begin{lstlisting}[caption={Werner-Wengle implementation},label={lst:wernerwengle}]
class WernerWengleWallModel : public WallModel {
private:
    Real A_ = 8.3;
    Real B_ = 1.0/7.0;
    Real yc_plus_ = 11.81;
    
public:
    Real computeWallShearStress(
        Real u_parallel,
        Real y,
        Real nu,
        Real rho) const override {
        
        // Try viscous assumption first
        Real tau_w_visc = rho * nu * u_parallel / y;
        Real u_tau_visc = sqrt(tau_w_visc / rho);
        Real y_plus_visc = y * u_tau_visc / nu;
        
        if (y_plus_visc <= yc_plus_) {
            // Viscous sublayer
            return tau_w_visc;
        } else {
            // Power law region
            Real factor = pow(u_parallel / (A_ * pow(y, B_)), 
                             2.0 / (1.0 + B_));
            return rho * nu * factor;
        }
    }
    
    void applyIntegratedWallModel(
        Cell& wallCell,
        const Real& wallDistance,
        VelField& velField,
        PDFField& pdfField,
        const Real& nu) override {
        
        // Werner-Wengle allows analytical integration
        // over the first cell for improved accuracy
        
        Real y1 = 0.0;           // Wall position
        Real y2 = wallDistance;  // Cell center
        
        // Compute cell-averaged velocity
        Real u_avg;
        
        if (y2 <= yc_plus_ * nu / u_tau_guess) {
            // Entire cell in viscous sublayer
            u_avg = 0.5 * u_tau_guess * (y1 + y2) / nu;
        } else if (y1 >= yc_plus_ * nu / u_tau_guess) {
            // Entire cell in log layer
            Real y1_B = pow(y1, B_);
            Real y2_B = pow(y2, B_);
            u_avg = A_ * u_tau_guess / (B_ + 1.0) * 
                   (y2_B * y2 - y1_B * y1) / (y2 - y1);
        } else {
            // Cell spans both regions
            Real y_trans = yc_plus_ * nu / u_tau_guess;
            
            // Viscous part
            Real u_visc = 0.5 * u_tau_guess * y_trans / nu;
            Real weight_visc = (y_trans - y1) / (y2 - y1);
            
            // Log part
            Real y2_B = pow(y2, B_);
            Real yt_B = pow(y_trans, B_);
            Real u_log = A_ * u_tau_guess / (B_ + 1.0) * 
                        (y2_B * y2 - yt_B * y_trans) / (y2 - y_trans);
            Real weight_log = (y2 - y_trans) / (y2 - y1);
            
            u_avg = weight_visc * u_visc + weight_log * u_log;
        }
        
        // Update velocity field with averaged value
        // ...
    }
};
\end{lstlisting}

\section{Adaptive y+ Treatment}

\subsection{Automatic Wall Region Detection}

The adaptive wall treatment automatically switches between different formulations based on the local $y^+$ value:

\begin{equation}
u^+ = \begin{cases}
u^+_{visc}(y^+) & \text{if } y^+ < y_1^+ \\
w(y^+)u^+_{visc} + [1-w(y^+)]u^+_{log} & \text{if } y_1^+ \leq y^+ \leq y_2^+ \\
u^+_{log}(y^+) & \text{if } y^+ > y_2^+
\end{cases}
\end{equation}

where $w(y^+)$ is a blending function:

\begin{equation}
w(y^+) = \frac{1}{2}\left[1 + \cos\left(\pi\frac{y^+ - y_1^+}{y_2^+ - y_1^+}\right)\right]
\end{equation}

\subsection{Implementation}

\begin{lstlisting}[caption={Adaptive y+ wall treatment},label={lst:adaptive_yplus}]
class AdaptiveYPlusWallModel : public WallModel {
private:
    Real y1_plus_ = 5.0;    // Viscous sublayer limit
    Real y2_plus_ = 30.0;   // Log layer start
    
    Real blendingFunction(Real yPlus) const {
        if (yPlus <= y1_plus_) return 1.0;
        if (yPlus >= y2_plus_) return 0.0;
        
        Real xi = (yPlus - y1_plus_) / (y2_plus_ - y1_plus_);
        return 0.5 * (1.0 + cos(M_PI * xi));
    }
    
    Real viscousLawVelocity(Real yPlus) const {
        return yPlus;
    }
    
    Real logLawVelocity(Real yPlus) const {
        return (1.0/kappa_) * log(E_ * yPlus);
    }
    
public:
    Real computeUPlus(Real yPlus) const override {
        if (yPlus < y1_plus_) {
            // Pure viscous sublayer
            return viscousLawVelocity(yPlus);
        } else if (yPlus > y2_plus_) {
            // Pure log layer
            return logLawVelocity(yPlus);
        } else {
            // Blended region
            Real w = blendingFunction(yPlus);
            Real u_visc = viscousLawVelocity(yPlus);
            Real u_log = logLawVelocity(yPlus);
            return w * u_visc + (1.0 - w) * u_log;
        }
    }
    
    Real computeEddyViscosity(Real yPlus, Real nu_t_les) const {
        // Blend LES and RANS eddy viscosity
        Real nu_t_rans = computeRANSEddyViscosity(yPlus);
        
        if (yPlus < y1_plus_) {
            // No wall model in viscous sublayer
            return nu_t_les;
        } else if (yPlus > y2_plus_) {
            // Pure RANS in log layer
            return nu_t_rans;
        } else {
            // Blend LES and RANS
            Real w = blendingFunction(yPlus);
            return w * nu_t_les + (1.0 - w) * nu_t_rans;
        }
    }
    
    void adaptiveWallTreatment(
        Cell& cell,
        const Real& wallDistance,
        const Real& nu,
        const Real& nu_t_les,
        VelField& velField,
        TurbField& turbField) {
        
        // Compute local y+
        Real u_tau = computeFrictionVelocity(cell, wallDistance, nu);
        Real yPlus = wallDistance * u_tau / nu;
        
        // Determine wall region
        WallRegion region = determineWallRegion(yPlus);
        
        // Apply appropriate treatment
        switch(region) {
            case VISCOUS_SUBLAYER:
                // No wall model needed, use LES directly
                turbField.get(cell) = nu_t_les;
                break;
                
            case BUFFER_LAYER:
                // Blend wall model and LES
                applyBlendedTreatment(cell, yPlus, nu_t_les, 
                                     velField, turbField);
                break;
                
            case LOG_LAYER:
                // Full wall model
                applyWallModel(cell, wallDistance, velField);
                turbField.get(cell) = computeEddyViscosity(yPlus, 0.0);
                break;
        }
        
        // Store y+ for diagnostics
        yPlusField_.get(cell) = yPlus;
    }
};
\end{lstlisting}

\section{Summary and Best Practices}

\subsection{Model Selection Guidelines}

\begin{table}[h]
\centering
\begin{tabular}{|l|l|l|l|}
\hline
\textbf{Model} & \textbf{Advantages} & \textbf{Disadvantages} & \textbf{Use Cases} \\
\hline
Standard Smagorinsky & Simple, robust & Requires $C_s$ tuning & High Re, homogeneous \\
& Low computational cost & Too dissipative & turbulence \\
\hline
Dynamic Smagorinsky & No tuning required & Higher cost & General purpose \\
& Adapts to flow & Needs averaging & Complex geometries \\
\hline
WALE & Good near-wall behavior & More expensive & Wall-bounded flows \\
& No damping needed & Complex formulation & Heat transfer \\
\hline
Vreman & Zero for 2D flows & Less accurate & Transitional flows \\
& Robust, stable & Limited validation & Mixed 2D/3D regions \\
\hline
\end{tabular}
\caption{LES model comparison}
\end{table}

\subsection{Implementation Recommendations}

\begin{enumerate}
\item \textbf{Strain Rate Computation}: Use non-equilibrium moments for consistency with LBM
\item \textbf{Filter Width}: Set $\Delta = 2\Delta x$ for explicit filtering
\item \textbf{Wall Treatment}: Always use adaptive y+ for robustness
\item \textbf{Collision Operator}: MRT provides better stability than BGK for LES
\item \textbf{Time Averaging}: Essential for dynamic models in inhomogeneous flows
\end{enumerate}

\subsection{Numerical Considerations}

\begin{theorem}[Stability Constraint]
For MRT-LES, stability requires:
\begin{equation}
\tau_{eff} = \tau_0 + \frac{\nu_t}{c_s^2\Delta t} > 0.5
\end{equation}
which imposes a limit on the maximum eddy viscosity:
\begin{equation}
\nu_t < c_s^2\Delta t(\tau_0 - 0.5)
\end{equation}
\end{theorem}

This constraint is automatically satisfied by proper coefficient clipping in dynamic models.

\section{Conclusions}

This comprehensive treatment of LES turbulence modeling in LBM provides the theoretical foundation and practical implementation details necessary for accurate simulation of turbulent flows. The integration with multiple collision operators and advanced wall treatments enables high-fidelity simulations across a wide range of Reynolds numbers and flow configurations.

\end{document}