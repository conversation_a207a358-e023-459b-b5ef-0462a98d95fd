#include <iostream>
#include "Counter.h"

#include <cassert> 

#define _DEBUG_ 0  // 0 no debug info 
		   // 1 with debug info

template<typename lenght = int>
struct LengthTypeConfig {
  typedef lenght LenghType ;
};

template<>
struct LengthTypeConfig<long> {
  typedef long LenghType ;
};

template<>
struct LengthTypeConfig<short> {
  typedef long LenghType ;
};



struct IntLengthTypeConfig
{
  typedef int LenghType ;
} ;

struct LongLengthTypeConfig
{
  typedef long LenghType ;
} ;

struct ShortLengthTypeConfig
{
  typedef short LenghType ;
} ;




// first option
struct X : Counter <X,IntLengthTypeConfig >
{
//    X () ;

    ~X () {}
};
// second option 
struct Y : Counter <Y, LengthTypeConfig<int> >
{
//    X () ;

};





inline void Print (X* out)
{
  #if _DEBUG_
    std::cout <<"GetCountObjects_alive = "<<out->GetCountObjects_alive( ) ;
    std::cout <<"   GetCountObjects_created =  " << out->GetCountObjects_created() << std::endl ; 
  #endif
}

inline void Print (Y* out)
{
  #if _DEBUG_
    std::cout <<"GetCountObjects_alive = "<<out->GetCountObjects_alive( ) ;
    std::cout <<"   GetCountObjects_created =  " << out->GetCountObjects_created() << std::endl ; 
  #endif	
}



int main ()
{

	X *x_0  =  new X /*()*/ ;
	    assert ( x_0 ->GetCountObjects_alive  ( )  == 1 ) ;
	    assert ( x_0 ->GetCountObjects_created( )  == 1 ) ;
	
	X *x_1  =  new X/* ()*/ ;
	    assert ( x_0 ->GetCountObjects_alive  ( )  == 2 ) ;
	    assert ( x_0 ->GetCountObjects_created( )  == 2 ) ;	
	
	Print (x_0 ) ; 		// GetCountObjects_alive = 2   GetCountObjects_created =  2
	Print (x_1 ) ; 		// GetCountObjects_alive = 2   GetCountObjects_created =  2

	delete x_0  ;  
	    assert ( x_0 ->GetCountObjects_alive  ( )  == 1 ) ;
	    assert ( x_0 ->GetCountObjects_created( )  == 2 ) ;	
		    
	Print (x_1 ) ;		// GetCountObjects_alive = 1   GetCountObjects_created =  2
	
	X *x_2  =  new X ; 	// GetCountObjects_alive = 2   GetCountObjects_created =  3
	Print (x_2 ) ;
	    assert ( x_0 ->GetCountObjects_alive  ( )  == 2 ) ;
	    assert ( x_0 ->GetCountObjects_created( )  == 3 ) ;	

	    
	X *x_3  =  new X /*()*/ ;	
	Print (x_3 ) ;
	    assert ( x_0 ->GetCountObjects_alive  ( )  == 3 ) ;
	    assert ( x_0 ->GetCountObjects_created( )  == 4 ) ;


	delete x_0 ;
// 	    assert ( x_0 ->GetCountObjects_alive  ( )  == 2 ) ;
// 	    assert ( x_0 ->GetCountObjects_created( )  == 4 ) ;	
	
	
	delete x_1 ; 
// 	delete x_2 ;
// 	delete x_3 ;
	
	
	
	std::cout <<  std::endl << std::endl ;
//  Test the seconfd option 
	
	
	Y *y_0  =  new Y /*()*/ ;
	Y *y_1  =  new Y/* ()*/ ;
	
	
	Print (y_0 ) ; 		// GetCountObjects_alive = 2   GetCountObjects_created =  2
	Print (y_1 ) ; 		// GetCountObjects_alive = 2   GetCountObjects_created =  2
	
	delete y_0  ;  

	Print (y_1 ) ;		// GetCountObjects_alive = 1   GetCountObjects_created =  2
	
	Y *y_2  =  new Y ; 	// GetCountObjects_alive = 2   GetCountObjects_created =  3
	Print (y_2 ) ;

	
	return (0)  ;
}


