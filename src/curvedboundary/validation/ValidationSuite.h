#pragma once

#include "../../Definitions.h"
#include "../../Vector3.h"
#include "../../Domain.h"
#include "../CurvedBoundary.h"
#include "../mesh/TriangleMesh.h"
#include "../../turbulence/TurbulenceModel.h"
#include <memory>
#include <string>
#include <vector>

namespace walberla {
namespace curvedboundary {

// Base class for validation test cases
class ValidationCase {
public:
    struct ValidationResult {
        bool passed;
        Real relativeError;
        Real absoluteError;
        Real computedValue;
        Real referenceValue;
        std::string description;
        std::vector<std::pair<Real, Real>> convergenceData;  // (resolution, error)
    };
    
    virtual ~ValidationCase() = default;
    
    // Run validation test
    virtual ValidationResult run(
        Domain& domain,
        CurvedBoundary& curvedBC,
        std::shared_ptr<turbulence::TurbulenceModel> turbModel = nullptr
    ) = 0;
    
    // Get test case name
    virtual std::string getName() const = 0;
    
    // Get test case description
    virtual std::string getDescription() const = 0;
    
    // Set tolerance for validation
    void setTolerance(Real relTol, Real absTol) {
        relativeTolerance_ = relTol;
        absoluteTolerance_ = absTol;
    }
    
protected:
    Real relativeTolerance_ = 0.05;  // 5% relative error
    Real absoluteTolerance_ = 1e-3;   // Absolute error tolerance
    
    // Check if value is within tolerance
    bool checkTolerance(Real computed, Real reference, Real& relError, Real& absError) {
        absError = std::abs(computed - reference);
        relError = absError / std::abs(reference);
        
        return (relError <= relativeTolerance_ || absError <= absoluteTolerance_);
    }
};

// Sphere drag validation (Stokes flow and higher Re)
class SphereDragValidation : public ValidationCase {
public:
    SphereDragValidation(Real diameter, Real reynolds);
    
    ValidationResult run(
        Domain& domain,
        CurvedBoundary& curvedBC,
        std::shared_ptr<turbulence::TurbulenceModel> turbModel = nullptr
    ) override;
    
    std::string getName() const override { return "Sphere Drag"; }
    
    std::string getDescription() const override {
        return "Validates drag coefficient for flow around a sphere at Re=" + 
               std::to_string(reynolds_);
    }
    
private:
    Real diameter_;
    Real reynolds_;
    
    // Reference drag coefficient correlations
    Real getReferenceDrag(Real Re) const;
    
    // Create sphere mesh
    std::shared_ptr<TriangleMesh> createSphereMesh(
        const Vector3<Real>& center,
        Real radius,
        Uint resolution
    ) const;
};

// Cylinder flow validation (von Karman vortex street)
class CylinderFlowValidation : public ValidationCase {
public:
    CylinderFlowValidation(Real diameter, Real reynolds);
    
    ValidationResult run(
        Domain& domain,
        CurvedBoundary& curvedBC,
        std::shared_ptr<turbulence::TurbulenceModel> turbModel = nullptr
    ) override;
    
    std::string getName() const override { return "Cylinder Flow"; }
    
    std::string getDescription() const override {
        return "Validates Strouhal number and drag for cylinder at Re=" + 
               std::to_string(reynolds_);
    }
    
private:
    Real diameter_;
    Real reynolds_;
    
    // Reference values
    Real getReferenceStrouhal(Real Re) const;
    Real getReferenceDrag(Real Re) const;
    
    // Create cylinder mesh
    std::shared_ptr<TriangleMesh> createCylinderMesh(
        const Vector3<Real>& center,
        Real radius,
        Real height,
        Uint resolution
    ) const;
    
    // Compute Strouhal number from lift history
    Real computeStrouhalNumber(
        const std::vector<Real>& liftHistory,
        Real dt,
        Real diameter,
        Real velocity
    ) const;
};

// Flat plate boundary layer validation
class FlatPlateValidation : public ValidationCase {
public:
    FlatPlateValidation(Real length, Real reynolds);
    
    ValidationResult run(
        Domain& domain,
        CurvedBoundary& curvedBC,
        std::shared_ptr<turbulence::TurbulenceModel> turbModel = nullptr
    ) override;
    
    std::string getName() const override { return "Flat Plate"; }
    
    std::string getDescription() const override {
        return "Validates boundary layer profile and skin friction at Re=" + 
               std::to_string(reynolds_);
    }
    
private:
    Real length_;
    Real reynolds_;
    
    // Blasius solution for comparison
    Real getBlasiusCf(Real Rex) const;
    std::vector<Real> getBlasiusProfile(Real eta) const;
    
    // Create flat plate mesh
    std::shared_ptr<TriangleMesh> createPlateMesh(
        const Vector3<Real>& origin,
        Real length,
        Real width,
        Real thickness
    ) const;
    
    // Extract velocity profile
    std::vector<std::pair<Real, Real>> extractVelocityProfile(
        const Field<Real,3>& velField,
        Real x,
        Real z
    ) const;
};

// Ahmed body validation for automotive applications
class AhmedBodyValidation : public ValidationCase {
public:
    AhmedBodyValidation(Real slantAngle = 25.0);  // degrees
    
    ValidationResult run(
        Domain& domain,
        CurvedBoundary& curvedBC,
        std::shared_ptr<turbulence::TurbulenceModel> turbModel = nullptr
    ) override;
    
    std::string getName() const override { return "Ahmed Body"; }
    
    std::string getDescription() const override {
        return "Validates drag coefficient for Ahmed body with " + 
               std::to_string(slantAngle_) + " degree slant";
    }
    
private:
    Real slantAngle_;
    
    // Reference drag coefficients from experiments
    Real getReferenceDrag(Real angle) const;
    
    // Create Ahmed body mesh
    std::shared_ptr<TriangleMesh> createAhmedBodyMesh(
        const Vector3<Real>& center,
        Real scale = 1.0
    ) const;
};

// Moving sphere validation (added mass effects)
class MovingSphereValidation : public ValidationCase {
public:
    MovingSphereValidation(Real diameter, Real acceleration);
    
    ValidationResult run(
        Domain& domain,
        CurvedBoundary& curvedBC,
        std::shared_ptr<turbulence::TurbulenceModel> turbModel = nullptr
    ) override;
    
    std::string getName() const override { return "Moving Sphere"; }
    
    std::string getDescription() const override {
        return "Validates added mass coefficient for accelerating sphere";
    }
    
private:
    Real diameter_;
    Real acceleration_;
    
    // Theoretical added mass coefficient
    Real getReferenceAddedMass() const { return 0.5; }  // For sphere
};

// Complete validation suite
class ValidationSuite {
public:
    ValidationSuite();
    
    // Add a validation case
    void addCase(std::shared_ptr<ValidationCase> testCase) {
        cases_.push_back(testCase);
    }
    
    // Run all validation cases
    void runAll(
        Domain& domain,
        CurvedBoundary& curvedBC,
        std::shared_ptr<turbulence::TurbulenceModel> turbModel = nullptr
    );
    
    // Generate validation report
    void generateReport(const std::string& filename) const;
    
    // Check if all tests passed
    bool allPassed() const;
    
    // Get summary statistics
    struct SummaryStats {
        Uint totalTests;
        Uint passed;
        Uint failed;
        Real averageError;
        Real maxError;
    };
    SummaryStats getSummary() const;
    
private:
    std::vector<std::shared_ptr<ValidationCase>> cases_;
    std::vector<ValidationCase::ValidationResult> results_;
};

// Grid convergence study helper
class GridConvergenceStudy {
public:
    GridConvergenceStudy(std::shared_ptr<ValidationCase> testCase);
    
    // Run convergence study with different resolutions
    void run(
        const std::vector<Uint>& resolutions,
        Domain& domain,
        CurvedBoundary& curvedBC
    );
    
    // Compute order of convergence
    Real computeConvergenceOrder() const;
    
    // Richardson extrapolation
    Real richardsonExtrapolation() const;
    
    // Generate convergence plot data
    void exportPlotData(const std::string& filename) const;
    
private:
    std::shared_ptr<ValidationCase> testCase_;
    std::vector<std::pair<Real, Real>> results_;  // (grid spacing, value)
};

} // namespace curvedboundary
} // namespace walberla