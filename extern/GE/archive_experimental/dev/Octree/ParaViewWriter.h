#ifndef PARAVIEWWRITER_HH
#define PARAVIEWWRITER_HH

#include "octree"
#include <iostream>
#include <fstream>
#include <string>

// =============================================================================
//!@brief export the Octree to vtk-xml file format
//!@brief its designed to work in parallel 
// =============================================================================
template<typename T, int d>
class ParaViewWriter
{
  public:
	 ParaViewWriter(const char* fname, octree<T,d>& in ) 
	  :My_octree(in) , m_fname(fname) 
	  {}

	 // ====================================================================
	 // Prolog, and Epilog
	 // ====================================================================
	 inline void Prolog() ;
	 inline bool DrawGrid() ;
	 inline void Epilog() ;
	 // ====================================================================
private :        
	const char* 	m_fname ;
	std::fstream  	m_out ;
	octree<T,d>&    My_octree ;
};
// =============================================================================


	// constructor
	//	template <typename T, int d>
	//	ParaViewWriter<T,d>::ParaViewWriter(const char* fname){
	//		m_fname  =  fname  ;
	//		
	//	}

	template <typename T, int d>
	inline void ParaViewWriter<T,d>::Prolog()
	{
	     	m_out.open( m_fname , std::fstream::out);
		int size_ = 0 ;
		std::string name = "Made by cherif" ;

		int NumberOfCells   = 1 ;
		int NumberOfPoints  = 8 ;
		
		typename octree<T,d>::iterator it;
		
		if (m_out.is_open())
		{
		  
		  m_out <<"<?xml version=\"1.0\"?>\n";
		  m_out <<"<VTKFile type=\"UnstructuredGrid\" version=\"0.1\" byte_order=\"LittleEndian\">\n";
		  m_out <<"  <UnstructuredGrid>\n";		  
		  m_out <<"  	<Piece NumberOfPoints=\""<<NumberOfPoints<<"\" NumberOfCells=\""<< NumberOfCells << "\">\n";
		  m_out <<"  	   <Points>\n";
		  m_out <<"  		<DataArray type=\"Float32\" NumberOfComponents=\"3\" format=\"ascii\">\n";		  		  
		  
//  <VTKFile type="UnstructuredGrid" version="0.1" byte_order="LittleEndian">
//   <UnstructuredGrid>
//    <Piece NumberOfPoints="216" NumberOfCells="125">
//     <Points>
//      
		  
		  
		  
// 			m_out << "# vtk DataFile Version 3.1\n" ;
// 			m_out << name<< "\n" ;
// 			m_out << "ASCII\n";
// 			m_out << "DATASET UNSTRUCTURED_GRID\n";
// 			m_out << "POINTS " <<(size_+1)*8 <<" FLOAT\n";
		}
		else{
			std::cout << "Error opening file";
		}
	}

	template <typename T, int d>
 	inline void ParaViewWriter<T,d>::Epilog()
 	{
      		m_out.close() ;
	}



// =============================================================================

#endif 
