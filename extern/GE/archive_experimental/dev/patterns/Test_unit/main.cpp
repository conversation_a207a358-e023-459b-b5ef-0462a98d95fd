
#include <iostream>
#include "TestAdapter.h"


#include "Exception.h"
#include <boost/shared_ptr.hpp>
#include <vector>

#include "ExClasses.h" // some examples 


struct my_class
{
	my_class () {std::cout << "cdt  ... \n" ;}	
	inline void DO () {
		std::cout << "helo \n" ;
	} 
};


struct CWd // or class
{
    public:
      
    void Method1(){
	throw ErrorOpening (__FILE__,__LINE__,"CWd::Method1()");
      }
    
    void Method2(){
	throw ErrorOpening(__FILE__,__LINE__,"CWd::Method2()");
    }
    
    void Method3()
    {
	throw ErrorOpening(__FILE__,__LINE__,"CWd::Method3()");
    }
    
    int Method4()
    {
	throw ErrorOpening(__FILE__,__LINE__,"CWd::Method4()");
    }    
    
    ~ CWd (){
      cout << "CWd   -> Destructor " << endl;
    }
     
};

//------------------------------------------------------------------------------
/*!
  @fn Test **InitDeffClasses()
  \brief create instances for many completely-diffrent classes
  * in this example three class (Foo1 , Foo2 and Foo3)
  * each of theses classes is associated with one method wich you want to test it
  \param  
  \return the new is returned
  \author Cherif.Mihoubi
*/
//------------------------------------------------------------------------------
/*  */
//  
Test **InitDeffClasses()
{
  	Test **array = new Test *[3];
  	/* the old is below */
  	array[0] = new TestAdapter < Foo1 ,void> (new Foo1() , &Foo1::Method1);
  	array[1] = new TestAdapter < Foo2 ,void> (new Foo2() , &Foo2::Method2);
 	array[2] = new TestAdapter < Foo3 ,void> (new Foo3() , &Foo3::Method3);
  	return array;
}



// for one class and different classe::methods
// condition is each class have destructor implementation

template<typename T>
Test **InitDeffClasseMethods()
{
	Test **array = new Test *[3];
  	array[0] = new TestAdapter < T ,void > (new T()  , &T::Method1);   
  	array[1] = new TestAdapter < T ,void > (new T()  , &T::Method2);  
	array[2] = new TestAdapter < T ,void > (new T()  , &T::Method3);    
	
	return array;
}



// new idea, for each class, I create series of tests of all member functions
// of this class
// after that, i run these series "pipe"

template<typename T, int size  >
inline  Test **InitTestClasseMethods()
{
	assert (size > 0);
	Test **array = new Test *[  size ];
  	array[0] = new TestAdapter < T ,void > (new T()  , &T::DO )  ;
  	//array[1] = new TestAdapter < T ,void > (new T()  , &T::Method2);  
	//array[2] = new TestAdapter < T ,void > (new T()  , &T::Method3);    
	
	return array;
}




//==============================================================================
/*!
//  \brief Example how to create series of tests on any classes 
//
//  \author Cherif Mihoubi
*/
//==============================================================================


int main ()
{
      
      cout << " Let's go \n" ;
  
 	// for many classs, eatch instance Test-object represent instant of one -class
	// and one method of this class 

	Test **objects = InitDeffClasses();
  

	try{ 
		// TODO I'm not sure whereas is better to put the for lop befor the try block or not?
		for (int i = 0; i < 3; i++)
	     		objects[i]->execute();
		
	 }catch(std::exception& e ){
	      
	      cout << "Exception raised: " << e.what()  << '\n';
	     
	}  

    
   
   
      cout << endl;
      cout << "------------------------- \n" ;     
      cout << "     Second Example :)t\n";
      cout << "------------------------- \n" ;
      

      
      Test **objects2 = InitDeffClasseMethods<CWd>() ;
 

	for (int i = 0; i < 3; i++)
	{
	  try
	    {
	      
	     objects2[i]->execute();
	    
	    
	   } catch(Exception& e){
	     
	      cout << "Exception raised: " << e.what()  << '\n';

	   }   
	  
	}
 

      cout << endl;
      cout << "------------------------- \n" ;     
      cout << " Third Example using Boost\n";
      cout << "------------------------- \n" ;

 
 
      typedef  boost::shared_ptr< Test >  SPtr ;

      std::vector< SPtr  > v; 


      v.push_back( SPtr (new TestAdapter<CWd,void>(new CWd(),&CWd::Method1)));

      v.push_back( SPtr (new TestAdapter<CWd,void>(new CWd(),&CWd::Method2)));
	  
      v.push_back( SPtr (new TestAdapter<CWd,void>(new CWd(),&CWd::Method3)));
      
      v.push_back( SPtr (new TestAdapter<CWd,int >(new CWd(),&CWd::Method4)));
    
      
      for (int i = 0; i< v.size(); i++)
      {
	  try
	  {
		    
		  v [i]-> execute();
		  
		  
	  } catch(Exception& e){
		  
		   cout << "Exception raised: " << e.what()  << '\n';

	  }
		
	}



 
      cout << endl;
      cout << "------------------------- \n" ;     
      cout << " delete all pointers-objects\n";
      cout << "------------------------- \n" ;
    //  uses the new (polymporphism) 

      for (int i = 0; i < 3; i++) {
	delete objects[i];
      }
      
      delete objects;
  
      for (int i = 0; i < 3; i++) {
	delete objects2[i];
      }
      
      delete objects2;
      

	// ====================================================================
 	/*
  	 * 
  	 *
 	 *
	 */
 	// ====================================================================



	Test **object3 = InitTestClasseMethods<my_class,1>() ;


	delete object3 ;
	return 0;
}

// template<typename T>
// boost::shared_array< Test > InitDeffClasseMethodsSh()
// {
// boost::shared_array< Test > array ;
// array=boost::shared_array<Test>(new TestAdapter< T >(new T(),&T::Method1)) ;
// array=boost::shared_array<Test>(new TestAdapter< T >(new T(),&T::Method2) ) ;
// array=boost::shared_array<Test>(new TestAdapter< T >(new T(),&T::Method3))
// ;
//  
// return array;   
// }  

