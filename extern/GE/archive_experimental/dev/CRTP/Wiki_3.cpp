// =============================================================================
// this code was copied from : http://en.wikipedia.org/wiki/Curiously_recurring_template_pattern
// 
//  basic idea for design the engine using CRTP pattern
// 
// 
// =============================================================================




// =============================================================================
// includes :
// =============================================================================
#include <iostream>

// =============================================================================
// Base class has a pure virtual function for cloning
// =============================================================================
template<typename T>
class Shape {
public:
	virtual ~Shape() {}
	virtual Shape *clone() const = 0;
	virtual T area ()const =0 ;
};
// =============================================================================
// This CRTP class implements clone() for Derived
// =============================================================================
template <typename T, template<typename S> class Derived> 
class Shape_CRTP: public Shape <T>
{
    public:
	inline Shape<T> *clone() const {
		return new Derived<T>(static_cast<Derived<T> const&>(*this));
	}
	inline T area ()const {
		return (static_cast <Derived <T> const&> (*this) ). area () ;
	}
};
	
// =============================================================================
// Every derived class inherits from Shape_CRTP instead of Shape
// =============================================================================
template<typename T>
struct Square: public Shape_CRTP< T,Square >
{
	inline T area ()const {return m_hight * m_width ;}
	inline void Set(const T& h, const T&w) { m_hight = h ;m_width = w;}
    private :
	T m_hight ;
	T m_width ;
};

#define pi 3.14
template<typename T>
struct Circle: public Shape_CRTP<T,Circle>
{
	// note : return should be float and not ints,
	// its only for demonstration purpose
	inline T area ()const {return m_ray * m_ray* pi;}
	inline void Set(const T& ray) {m_ray = ray;}
    private :
	T m_ray ;
};


inline void PrintSizeOf(); // check the sizeof of these classes.
template<typename T>
inline void PrintArea(/*const */ Shape<T>* my_shpes);



int main()
{
	PrintSizeOf();
	
	Square<int> *tmp = new Square<int> ;
	tmp-> Set (20,4) ;
	
	Shape<int> *tmp2 = tmp->clone () ;


	Shape<int> *m_square = new Square<int> ;
	
	
	PrintArea (tmp) ;
	PrintArea (tmp2) ;
	
	Circle<int> *m_circle = new Circle<int> ;
	m_circle -> Set (2) ;
	
	PrintArea (m_circle) ;
	
	return (0) ;
}


template<typename T>
inline void PrintArea(/*const */ Shape<T>* my_shpes)
{
	std::cout<< "area : " << my_shpes -> area () << std::endl ;
}



// =============================================================================
// add you need.
// =============================================================================
inline void PrintSizeOf()
{
	std::cout << "size of (Shape) : "  << sizeof (Shape<int>)  << std::endl;
	std::cout << "size of (Square) : " << sizeof (Square<int>) << std::endl;
// 	std::cout << "size of (Shape_CRTP<Square> ) : " << sizeof (Shape_CRTP< Square <int> > ) << std::endl;
	
}
/*
 * 
 * Some results

size of (Shape) : 8
size of (Square) : 8
size of (Shape_CRTP<Square> ) : 8



*/



