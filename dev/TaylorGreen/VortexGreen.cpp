#include <iostream>
#include <fstream>
#include <cmath>

using  namespace std;


static const double  PI   =3.14159265 ;



inline void InitialTaylorGreenVortex(
        const double rho,
        const double U0,// = 1/a
        const double a ,
        const double P0 ,
        const int x,
        const int y,
        const int z,
        double& Ux, double& Uy,double& P , std::ofstream&  fs   )
{

    Ux =   U0 * ( std::cos (a*x) * std::sin (a*y )  ) ;

    Uy =  -U0 * ( std::sin (a*x) * std::cos (a*y )  ) ;

    // Uz = 0.0
    P = (0.250*U0*U0) *(  std::cos ( (2.*x/U0 )  ) +
                          std::cos ( (2.*y/U0  )  )    );

/*
     std::cout << 
        " cell:  x,y,z: "<< x << " " << y << " "<<  z <<
        "\t U (x,y  ) :  ("<< Ux  << " " << Uy <<")\n" ;
*/       
        
     fs  << x << " " << y << " "<<  " "<< Ux  << " " << Uy <<"\n" ;   
        
    
}


int main ()
{
    const int NX=100 ;

    double ux ,  uy ;

    const double P0  = 100 , rho=1.0 ;

    const double  a  =  2.0* PI /NX ;
    const double  U0 =  ( 1.0/(1000.0*a) ) ;
    double  Pressure ;

    std::cout << "a = " << a << "\nU0 = " << U0 << "\n  NX/2 : " << NX/2  << "\n"   ; 

    std::ofstream  fs;
    fs.open ( "test.txt",std::ofstream::out );
    fs  << "#x y \t ux \tuy \n" ;   

  
    int z = 1 ; 
    int y = NX/2  ; 
    
    for  (int  x = 1; x <= NX ; x ++ )
    {
            InitialTaylorGreenVortex(
                rho,
                U0,
                a ,
                P0 ,
                x,  y ,  z,
                ux ,  uy  ,  Pressure ,   fs );

    }


    fs.close();

    return (0); 
}


