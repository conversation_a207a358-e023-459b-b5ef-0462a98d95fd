// =============================================================================
/*!
//  \file    Config_Def.h
//  \brief   Header file for the  class
//  \author  cherif.Mihoubi
//  \email:  <EMAIL>
//
//  Created on 2010-10-31, 2010 , 20:13:40
//
*/
// =============================================================================

#ifndef CONFIG_DEF_HH
#define CONFIG_DEF_HH

// =============================================================================
/*
//	This program is free software; you can redistribute it and/or
//	modify it under the terms of the GNU General Public License
//	as published by the Free Software Foundation; either version 2
//	of the License, or (at your option) any later version.
//
//	This program is distributed in the hope that it will be useful,
//	but WITHOUT ANY WARRANTY; without even the implied warranty of
//	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
//	GNU General Public License for more details.
//
//	You should have received a copy of the GNU General Public License
//	along with this program; if not, write to the Free Software
//	Foundation, Inc., 51 Franklin Street, Fifth Floor,
//	Boston, MA  02110-1301, USA.
//
//	
//	Copyright (C) 2010, Cherif <<EMAIL>>
*/
// =============================================================================



// =============================================================================
/*!
//  @brief to include the importante files for any configuration needed by these
//  @brief class-files included in this directory
*/
// =============================================================================


// =============================================================================
// includes
// =============================================================================



	#include "ConfigListDefault.h"		//#include "../dev/List.h"
  
 
  
// =============================================================================
  
#endif // CONFIG_DEF_HH
