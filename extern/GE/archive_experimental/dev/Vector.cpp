// =======================================================================================
/*
// 
// Implementation file of Vector class 
//
*/
// =======================================================================================

#include "Vector.h"

namespace ge
{
	  template <typename Type, unsigned int Size>
	  inline std::ostream& operator<<(std::ostream &output,const Vector<Type,Size>&v )
	  {
		  output << '<' << v.data[0] ;
		  for (std::size_t i = 1; i < Size ; ++i)
		    output << " , " << v.data[i] ;
		  output  << '>'  ;
		  return output   ;
	  }
	
	  ///@brief Component-wise comparison.
	  template <typename Type, unsigned int Size>
	  inline bool Vector<Type,Size>::operator == (const Vector<Type,Size> & rhs) const
	  {
		for (std::size_t i = 0; i < Size ; ++i)
		{
		  if (data[i] != rhs.data[i])
				  return false;
		}
		return true;
	  }  

	  ///@brief Component-wise comparison.
	  template <typename Type, unsigned int Size>
	  inline bool Vector<Type,Size>::operator != (const Vector<Type,Size> & rhs) const
	  {
	      return !(*this == rhs);
	  }


	  template <typename Type, unsigned int Size>
	  template <typename T>
	  inline Vector<Type,Size>& Vector<Type,Size>::operator *= (const T& rhs)/// Scaling assignment.
	  {
		  for (size_t i = 0; i < Size; ++i)
			    data[i] *= rhs;
		  return *this;
	  }
	  

	  /// Inverse scaling assignment.
	  template <typename Type, unsigned int Size>
	  template <typename T> 
	  inline Vector<Type,Size>& Vector<Type,Size>::operator /= (const T& rhs)
	  {
		  assert (rhs != 0) ;
		  for (size_t i = 0; i < Size; ++i) 
		      data[i] /= rhs;
		  return *this;
	  }
	  

	  /// Scaling.
	  template <typename Type, unsigned int Size>
	  template <typename T>    
	  inline Vector<Type,Size> Vector<Type,Size>::operator * (const T& rhs) const
	  {
		return (Vector<Type,Size> (*this) *= rhs );
	  }


	  /// Inverse scaling.
	  template <typename Type, unsigned int Size>
	  template <typename T>
	  inline Vector<Type,Size> Vector<Type,Size>::operator / (const T& rhs) const
	  {
		assert (rhs != 0) ;
		return ( Vector(*this) /= rhs ) ;
	  }
	      
	  /// Negation.
	  template <typename Type, unsigned int Size>
	  inline Vector<Type,Size> Vector<Type,Size>::operator - () const
	  {
		  for (size_t i = 0; i < Size; ++i) 
		      data[i] *= -1;
		  return *this;
	  }

}// endnamespace ge