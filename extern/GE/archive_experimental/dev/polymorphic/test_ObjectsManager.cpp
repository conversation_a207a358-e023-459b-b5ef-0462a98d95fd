
#include <iostream>
#include "ObjectsManager.h"


static ObjectsManager<> m_objects_manager ; // note: its static member data

int main ()
{
  // initialisation
      m_objects_manager.insert( &TriangleMeshObject("First")  ) ;
      m_objects_manager.insert( &TriangleMeshObject("Second") ) ;
      m_objects_manager.insert( &TriangleMeshObject("Third")  ) ;
      m_objects_manager.insert( &TriangleMeshObject("Fourth") ) ;
    
     m_objects_manager.Transform () ;
   
   return (0) ;
}
