/*******************************************************************************
ConminC++, to solve constrained or unconstrained function minimization.
Copyright (C) 1999  <PERSON><PERSON> <PERSON><PERSON> Dunn

This program is free software; you can redistribute it and/or
modify it under the terms of the GNU General Public License
as published by the Free Software Foundation; either version 2
of the License, or (at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this program; if not, write to the Free Software
Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA  02111-1307, USA.
*******************************************************************************/

#pragma hdrstop
#include "example2_analysis.h"

#ifdef UNIX
#define USEUNIT( x ) // x
#else
#include <vcl.h>
#endif
//---------------------------------------------------------------------------
USEUNIT("example2_analysis.cpp");
USEUNIT("conmin.cpp");
USEUNIT("cnmn02.cpp");
USEUNIT("cnmn03.cpp");
USEUNIT("cnmn04.cpp");
USEUNIT("cnmn05.cpp");
USEUNIT("cnmn06.cpp");
USEUNIT("cnmn07.cpp");
USEUNIT("cnmn08.cpp");
USEUNIT("cnmn01.cpp");
//---------------------------------------------------------------------------
#pragma argsused

int main()
{
	const int ndv=4, ncon=3, nacp1=3;

	//   Initialize
	Ex2 ex2(ndv, ncon, nacp1);

	//   set parameters 
ex2.iprint = Debug4;
ex2.info = Cal_all;
ex2.itmax = 40;
ex2.infog = All;
ex2.nfdg = External;
ex2.nside = Unbounded;
ex2.icndir = 0;
ex2.nscal = 0;
ex2.fdch = 0;
ex2.fdchm = 0.;
ex2.ct = 0.;
ex2.ctmin = 0.;
ex2.ctl = 0.;
ex2.ctlmin = 0.;
ex2.theta = 0.;
ex2.phi = 0.;
ex2.delfun = 0.;
ex2.dabfun = 0.;
ex2.linear_obj = NonLinear;
ex2.itrm = 0;
ex2.alphax=0.;
ex2.abobj1=0;

//   Initialize constraint identification vector, isc.
for(int j=1; j<=ncon; j++)
  ex2.isc[j] = 0;

//   Initialize x-vector
for(int j=1; j<=ndv; j++)
  ex2.x[j]=1.;

//   Solve optimization
while( ex2.conmin() != Finished)
   ex2.analysis();

   return 0;
}
 
