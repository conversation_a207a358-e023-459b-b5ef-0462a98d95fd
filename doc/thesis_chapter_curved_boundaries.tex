\documentclass[12pt,a4paper]{report}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{algorithm}
\usepackage{algorithmic}
\usepackage{listings}
\usepackage{graphicx}
\usepackage{subfigure}
\usepackage{color}
\usepackage{xcolor}
\usepackage{hyperref}
\usepackage{geometry}
\usepackage{tikz}
\usepackage{pgfplots}
\usepackage{bm}
\usepackage{cite}

\geometry{margin=1in}

% Define colors for code listings
\definecolor{codegreen}{rgb}{0,0.6,0}
\definecolor{codegray}{rgb}{0.5,0.5,0.5}
\definecolor{codepurple}{rgb}{0.58,0,0.82}
\definecolor{backcolour}{rgb}{0.95,0.95,0.92}

% Code listing style
\lstdefinestyle{mystyle}{
    backgroundcolor=\color{backcolour},
    commentstyle=\color{codegreen},
    keywordstyle=\color{magenta},
    numberstyle=\tiny\color{codegray},
    stringstyle=\color{codepurple},
    basicstyle=\ttfamily\footnotesize,
    breakatwhitespace=false,
    breaklines=true,
    captionpos=b,
    keepspaces=true,
    numbers=left,
    numbersep=5pt,
    showspaces=false,
    showstringspaces=false,
    showtabs=false,
    tabsize=2,
    language=C++
}
\lstset{style=mystyle}

\begin{document}

\chapter{Advanced Curved Boundary Treatment with Turbulence Modeling in waLBerla}

\section{Introduction}

The accurate representation of complex geometries in Lattice Boltzmann Method (LBM) simulations remains a fundamental challenge in computational fluid dynamics. This chapter presents a comprehensive implementation of curved boundary conditions integrated with Large Eddy Simulation (LES) turbulence models within the waLBerla framework. The implementation addresses three critical aspects: (1) accurate boundary treatment using interpolated bounce-back schemes, (2) efficient voxelization of triangulated surface meshes, and (3) seamless integration with various turbulence models for high Reynolds number flows.

The primary motivation for this work stems from the need to simulate flow around complex geometries, such as the Ahmed body in automotive aerodynamics, where both geometric accuracy and turbulence modeling are crucial for predicting drag and flow separation phenomena.

\section{Mathematical Foundation}

\subsection{Lattice Boltzmann Method}

The Lattice Boltzmann equation with BGK collision operator is given by:
\begin{equation}
f_i(\mathbf{x} + \mathbf{c}_i \Delta t, t + \Delta t) = f_i(\mathbf{x}, t) - \frac{\Delta t}{\tau}[f_i(\mathbf{x}, t) - f_i^{eq}(\mathbf{x}, t)] + \Delta t F_i
\end{equation}

where $f_i$ represents the particle distribution function in direction $i$, $\mathbf{c}_i$ is the discrete velocity, $\tau$ is the relaxation time, $f_i^{eq}$ is the equilibrium distribution, and $F_i$ represents external forces.

For the D3Q19 velocity model, the equilibrium distribution is:
\begin{equation}
f_i^{eq} = w_i \rho \left[1 + \frac{\mathbf{c}_i \cdot \mathbf{u}}{c_s^2} + \frac{(\mathbf{c}_i \cdot \mathbf{u})^2}{2c_s^4} - \frac{\mathbf{u} \cdot \mathbf{u}}{2c_s^2}\right]
\end{equation}

where $w_i$ are the weights, $\rho$ is the density, $\mathbf{u}$ is the macroscopic velocity, and $c_s = 1/\sqrt{3}$ is the lattice sound speed.

\subsection{Bouzidi Boundary Condition}

The Bouzidi method provides second-order accurate boundary conditions for curved surfaces. For a boundary located at a distance $q\Delta x$ from a fluid node (where $0 < q < 1$), the bounce-back rule is:

For $q \geq 0.5$:
\begin{equation}
f_{\bar{i}}(\mathbf{x}_f, t + \Delta t) = 2q f_i^*(\mathbf{x}_f, t) + (1-2q)f_i^*(\mathbf{x}_f - \mathbf{c}_i\Delta t, t) + 6w_i\rho\frac{\mathbf{c}_i \cdot \mathbf{u}_w}{c^2}
\end{equation}

For $q < 0.5$:
\begin{equation}
f_{\bar{i}}(\mathbf{x}_f - \mathbf{c}_i\Delta t, t + \Delta t) = \frac{1}{2q}f_i^*(\mathbf{x}_f, t) + \frac{2q-1}{2q}f_{\bar{i}}^*(\mathbf{x}_f, t) + \frac{3w_i}{q}\mathbf{c}_i \cdot \mathbf{u}_w
\end{equation}

where $\bar{i}$ denotes the opposite direction, $f_i^*$ is the post-collision distribution, and $\mathbf{u}_w$ is the wall velocity.

\subsection{Turbulence Modeling}

\subsubsection{Large Eddy Simulation}

In LES, the effective relaxation time is modified to account for subgrid-scale turbulence:
\begin{equation}
\tau_{eff} = \tau_0 + \frac{\nu_t}{c_s^2 \Delta t}
\end{equation}

where $\nu_t$ is the turbulent eddy viscosity and $\tau_0$ is the molecular relaxation time.

\subsubsection{Smagorinsky Model}

The standard Smagorinsky model computes the eddy viscosity as:
\begin{equation}
\nu_t = (C_s \Delta)^2 |\bar{S}|
\end{equation}

where $C_s$ is the Smagorinsky constant, $\Delta$ is the filter width, and $|\bar{S}|$ is the strain rate magnitude:
\begin{equation}
|\bar{S}| = \sqrt{2\bar{S}_{ij}\bar{S}_{ij}}, \quad \bar{S}_{ij} = \frac{1}{2}\left(\frac{\partial \bar{u}_i}{\partial x_j} + \frac{\partial \bar{u}_j}{\partial x_i}\right)
\end{equation}

\subsubsection{Dynamic Smagorinsky Model}

The dynamic procedure computes $C_s$ locally using the Germano identity:
\begin{equation}
\mathcal{L}_{ij} = \widehat{\bar{u}_i \bar{u}_j} - \hat{\bar{u}}_i \hat{\bar{u}}_j
\end{equation}

where the hat denotes test filtering at scale $\hat{\Delta} = 2\Delta$. The dynamic coefficient is then:
\begin{equation}
C_s^2 = \frac{\langle \mathcal{L}_{ij} M_{ij} \rangle}{\langle M_{ij} M_{ij} \rangle}
\end{equation}

\section{System Architecture}

\subsection{Module Overview}

The implementation consists of three main modules:

\begin{enumerate}
\item \textbf{Turbulence Module}: Provides various LES models with collision operator integration
\item \textbf{Curved Boundary Module}: Handles triangle mesh representation and Bouzidi boundary conditions
\item \textbf{Voxelization Module}: Converts triangle meshes to flag field representation
\end{enumerate}

\begin{figure}[h]
\centering
\begin{tikzpicture}[scale=0.8, every node/.style={scale=0.8}]
    % Boxes
    \draw[thick] (0,0) rectangle (3,2) node[pos=.5] {Turbulence Module};
    \draw[thick] (4,0) rectangle (7,2) node[pos=.5] {Curved Boundary};
    \draw[thick] (8,0) rectangle (11,2) node[pos=.5] {Voxelization};
    \draw[thick] (2,-3) rectangle (5,-1) node[pos=.5] {LBM Core};
    \draw[thick] (6,-3) rectangle (9,-1) node[pos=.5] {Flag Field};
    
    % Arrows
    \draw[->, thick] (1.5,0) -- (3.5,-1);
    \draw[->, thick] (5.5,0) -- (3.5,-1);
    \draw[->, thick] (9.5,0) -- (7.5,-1);
    \draw[->, thick] (5.5,0) -- (7.5,-1);
    \draw[<->, thick] (3.5,1) -- (4,1);
    \draw[<->, thick] (7,1) -- (8,1);
\end{tikzpicture}
\caption{System architecture showing module interactions}
\end{figure}

\subsection{Class Hierarchy}

\begin{lstlisting}[caption={Base turbulence model interface},label={lst:turbmodel}]
class TurbulenceModel {
public:
    virtual ~TurbulenceModel() = default;
    
    // Compute effective relaxation time
    virtual Real computeEffectiveTau(
        const Real& tau0,
        const Uint x, const Uint y, const Uint z,
        const Real& rho,
        const Vector3<Real>& vel,
        const PDFField& pdf,
        const VelField& velField,
        const SimData& sim) const = 0;
        
    // Get model name
    virtual std::string getName() const = 0;
    
    // Check if model needs velocity gradient
    virtual bool needsVelocityGradient() const { return true; }
};
\end{lstlisting}

\section{Voxelization Algorithm}

\subsection{Overview}

The voxelization process converts triangulated surface meshes into a discrete flag field representation suitable for LBM simulations. This involves three main steps: (1) boundary cell detection, (2) inside/outside classification, and (3) flag field mapping.

\subsection{Ray-Triangle Intersection}

The Möller-Trumbore algorithm is used for efficient ray-triangle intersection testing:

\begin{algorithm}
\caption{Möller-Trumbore Ray-Triangle Intersection}
\label{alg:raytriangle}
\begin{algorithmic}[1]
\REQUIRE Ray origin $\mathbf{O}$, direction $\mathbf{D}$, triangle vertices $\mathbf{V}_0, \mathbf{V}_1, \mathbf{V}_2$
\ENSURE Intersection point $\mathbf{P}$, distance $t$, hit flag
\STATE $\mathbf{E}_1 \leftarrow \mathbf{V}_1 - \mathbf{V}_0$
\STATE $\mathbf{E}_2 \leftarrow \mathbf{V}_2 - \mathbf{V}_0$
\STATE $\mathbf{P} \leftarrow \mathbf{D} \times \mathbf{E}_2$
\STATE $det \leftarrow \mathbf{E}_1 \cdot \mathbf{P}$
\IF{$|det| < \epsilon$}
    \RETURN false \COMMENT{Ray parallel to triangle}
\ENDIF
\STATE $inv\_det \leftarrow 1/det$
\STATE $\mathbf{T} \leftarrow \mathbf{O} - \mathbf{V}_0$
\STATE $u \leftarrow (\mathbf{T} \cdot \mathbf{P}) \times inv\_det$
\IF{$u < 0$ or $u > 1$}
    \RETURN false
\ENDIF
\STATE $\mathbf{Q} \leftarrow \mathbf{T} \times \mathbf{E}_1$
\STATE $v \leftarrow (\mathbf{D} \cdot \mathbf{Q}) \times inv\_det$
\IF{$v < 0$ or $u + v > 1$}
    \RETURN false
\ENDIF
\STATE $t \leftarrow (\mathbf{E}_2 \cdot \mathbf{Q}) \times inv\_det$
\IF{$t > \epsilon$}
    \STATE $\mathbf{P} \leftarrow \mathbf{O} + t\mathbf{D}$
    \RETURN true, $t$, $\mathbf{P}$
\ENDIF
\RETURN false
\end{algorithmic}
\end{algorithm}

\subsection{Voxelization Process}

\begin{lstlisting}[caption={Main voxelization algorithm},label={lst:voxelize}]
std::shared_ptr<VoxelDataField> Voxelizer::voxelizeMesh(
    const TriangleMesh& mesh,
    const Domain& domain,
    const AABB& boundingBox) {
    
    Timer timer;
    timer.start();
    
    // Create voxel data field
    auto voxelData = std::make_shared<VoxelDataField>(
        domain.xSize(), domain.ySize(), domain.zSize());
    
    // Step 1: Voxelize boundary cells
    voxelizeBoundary(*voxelData, mesh, 0);
    
    // Step 2: Voxelize solid interior
    voxelizeSolid(*voxelData, mesh, 0);
    
    // Step 3: Mark near-boundary cells
    if (options_.markNearBoundary) {
        markNearBoundaryCells(*voxelData);
    }
    
    // Step 4: Compute distance field
    if (options_.computeDistanceField) {
        computeDistanceField(*voxelData, mesh, boundingBox);
    }
    
    timer.end();
    
    return voxelData;
}
\end{lstlisting}

\subsection{Inside/Outside Classification}

The point-in-mesh test uses ray casting with parity counting:

\begin{algorithm}
\caption{Point-in-Mesh Test}
\label{alg:pointinmesh}
\begin{algorithmic}[1]
\REQUIRE Point $\mathbf{P}$, Triangle mesh $\mathcal{M}$
\ENSURE Boolean inside flag
\STATE Cast ray from $\mathbf{P}$ in $+x$ direction
\STATE $count \leftarrow 0$
\FOR{each triangle $T \in \mathcal{M}$}
    \IF{Ray intersects $T$}
        \STATE $count \leftarrow count + 1$
    \ENDIF
\ENDFOR
\IF{$count \bmod 2 = 1$}
    \RETURN true \COMMENT{Inside}
\ELSE
    \RETURN false \COMMENT{Outside}
\ENDIF
\end{algorithmic}
\end{algorithm}

\section{Flag Field Mapping}

\subsection{Cell Classification}

The voxelization process classifies each lattice cell into one of four categories:

\begin{table}[h]
\centering
\begin{tabular}{|l|l|l|}
\hline
\textbf{Cell Type} & \textbf{Description} & \textbf{Flag Assignment} \\
\hline
OUTSIDE & Outside all meshes & FLUID \\
INSIDE & Inside solid region & OBST $|$ NOSLIP \\
BOUNDARY & Intersected by surface & BOUZIDI $|$ FLUID \\
NEAR\_BOUNDARY & Adjacent to boundary & NEAR\_OBST $|$ FLUID \\
\hline
\end{tabular}
\caption{Cell classification and flag mapping}
\label{tab:cellclass}
\end{table}

\subsection{Flag Mapping Algorithm}

\begin{lstlisting}[caption={Flag field mapping implementation},label={lst:flagmap}]
void FlagFieldMapper::mapToFlagField(
    const VoxelDataField& voxelData,
    IBlock* block,
    BlockDataID flagFieldID) {
    
    auto flagField = block->getData<FlagField<>>(flagFieldID);
    CellInterval cells = flagField->xyzSize();
    
    for (auto cell : cells) {
        const VoxelData& voxel = voxelData.get(cell);
        
        // Clear existing flags
        flagField->removeMask(cell, 
            defaultObstacleFlag_ | defaultBoundaryFlag_);
        
        // Map based on voxel type
        switch (voxel.type) {
            case VoxelData::INSIDE:
                // Inside obstacle - remove fluid, add obstacle
                flagField->removeMask(cell, defaultFluidFlag_);
                flagField->addMask(cell, defaultObstacleFlag_);
                break;
                
            case VoxelData::BOUNDARY:
                // Boundary cell - keep fluid, add boundary
                flagField->addMask(cell, defaultBoundaryFlag_);
                break;
                
            case VoxelData::NEAR_BOUNDARY:
                // Near boundary - mark for special treatment
                flagField->addMask(cell, defaultNearObstacleFlag_);
                break;
                
            case VoxelData::OUTSIDE:
                // Ensure fluid flag is set
                flagField->addMask(cell, defaultFluidFlag_);
                break;
        }
    }
}
\end{lstlisting}

\subsection{Boundary Link Creation}

For the Bouzidi method, boundary links connect fluid cells to their neighboring boundary cells:

\begin{lstlisting}[caption={Boundary link creation},label={lst:boundarylinks}]
struct BoundaryLink {
    Cell fluidCell;
    Cell boundaryCell;
    Uint direction;          // D3Q19 direction
    Real q;                  // Bouzidi parameter
    Vector3<Real> wallVelocity;
    Vector3<Real> wallNormal;
};

std::vector<BoundaryLink> createBoundaryLinks(
    const VoxelDataField& voxelData,
    IBlock* block,
    BlockDataID flagFieldID) {
    
    std::vector<BoundaryLink> links;
    
    for (auto it = voxelData.beginXYZ(); it != voxelData.end(); ++it) {
        if (it->type != VoxelData::BOUNDARY) continue;
        
        Cell boundaryCell = it.cell();
        
        // Check all D3Q19 directions
        for (Uint dir = 1; dir < D3Q19::Cellsize; ++dir) {
            Cell neighbor = boundaryCell.neighbor(dir);
            
            if (flagField->isSet(neighbor, fluidFlag)) {
                BoundaryLink link;
                link.fluidCell = neighbor;
                link.boundaryCell = boundaryCell;
                link.direction = D3Q19::opposite[dir];
                link.q = it->q;
                link.wallVelocity = it->wallVelocity;
                link.wallNormal = it->normal;
                
                links.push_back(link);
            }
        }
    }
    
    return links;
}
\end{lstlisting}

\section{Moving Boundaries}

\subsection{Overview}

Moving boundaries require special treatment to maintain conservation properties and numerical stability. The implementation tracks mesh positions between timesteps and updates the flag field accordingly.

\subsection{Update Algorithm}

\begin{algorithm}
\caption{Moving Mesh Update}
\label{alg:movingmesh}
\begin{algorithmic}[1]
\REQUIRE Moving mesh $\mathcal{M}$, timestep $\Delta t$, flag field $\mathcal{F}$
\ENSURE Updated flag field, boundary links
\STATE Store current voxelization as $\mathcal{V}_{old}$
\STATE Clear flags from old position:
\FOR{each cell $c \in \mathcal{V}_{old}$}
    \IF{$c$ is obstacle}
        \STATE Remove obstacle flags from $\mathcal{F}[c]$
        \STATE Add fluid flag to $\mathcal{F}[c]$
    \ENDIF
\ENDFOR
\STATE Update mesh position: $\mathbf{x}_{new} = \mathbf{x}_{old} + \mathbf{v} \Delta t$
\STATE Update mesh rotation if needed
\STATE Voxelize mesh at new position $\rightarrow \mathcal{V}_{new}$
\STATE Apply new flags:
\FOR{each cell $c \in \mathcal{V}_{new}$}
    \STATE Map voxel type to flags in $\mathcal{F}[c]$
\ENDFOR
\STATE Handle transition cells (obstacle $\rightarrow$ fluid)
\STATE Update boundary links
\end{algorithmic}
\end{algorithm}

\subsection{Transition Cell Treatment}

Cells that transition from obstacle to fluid require special initialization:

\begin{lstlisting}[caption={Transition cell handling},label={lst:transition}]
void handleTransitionCells(
    const VoxelDataField& oldVoxel,
    const VoxelDataField& newVoxel,
    IBlock* block,
    BlockDataID pdfFieldID) {
    
    auto pdfField = block->getData<PDFField>(pdfFieldID);
    
    // Find transition cells
    std::vector<Cell> transitionCells;
    
    for (auto cell : block->cells()) {
        const VoxelData& oldData = oldVoxel.get(cell);
        const VoxelData& newData = newVoxel.get(cell);
        
        // Cell was obstacle but is now fluid
        if (oldData.isObstacle() && !newData.isObstacle()) {
            transitionCells.push_back(cell);
        }
    }
    
    // Initialize PDFs for transition cells
    for (const auto& cell : transitionCells) {
        // Compute local equilibrium
        Real rho = 1.0;  // Default density
        Vector3<Real> u = getAverageVelocityFromNeighbors(cell);
        
        for (Uint i = 0; i < D3Q19::Cellsize; ++i) {
            pdfField->get(cell, i) = computeEquilibrium(i, rho, u);
        }
    }
}
\end{lstlisting}

\section{Parallel Implementation}

\subsection{MPI Domain Decomposition}

waLBerla employs MPI-based domain decomposition where the computational domain is divided into patches distributed across MPI processes:

\begin{lstlisting}[caption={MPI patch distribution},label={lst:mpipatch}]
// Create MPI Cartesian topology
int dims[3] = {npx, npy, npz};
int periods[3] = {periodic_x, periodic_y, periodic_z};
MPI_Cart_create(MPI_COMM_WORLD, 3, dims, periods, 1, &cartComm);

// Get process coordinates
int coords[3];
MPI_Cart_coords(cartComm, rank, 3, coords);

// Determine local patch range
int xStart, xEnd, yStart, yEnd, zStart, zEnd;
MPE_Decomp1d(totalPatchesX, npx, coords[0], &xStart, &xEnd);
MPE_Decomp1d(totalPatchesY, npy, coords[1], &yStart, &yEnd);
MPE_Decomp1d(totalPatchesZ, npz, coords[2], &zStart, &zEnd);

// Allocate local patches
for (int z = zStart; z <= zEnd; ++z) {
    for (int y = yStart; y <= yEnd; ++y) {
        for (int x = xStart; x <= xEnd; ++x) {
            patchField.AllocPatch(x, y, z);
        }
    }
}
\end{lstlisting}

\subsection{OpenMP Fine-Grained Parallelism}

Within each MPI process, OpenMP provides additional parallelism:

\begin{lstlisting}[caption={OpenMP parallelization pattern},label={lst:openmp}]
void voxelizeSolid(VoxelDataField& voxelData,
                   const TriangleMesh& mesh,
                   Uint meshID) {
    
#ifdef USE_OMP
    #pragma omp parallel for collapse(3) if(useMultiThreading)
#endif
    for (Uint x = 0; x < voxelData.xSize(); ++x) {
        for (Uint y = 0; y < voxelData.ySize(); ++y) {
            for (Uint z = 0; z < voxelData.zSize(); ++z) {
                auto& voxel = voxelData.get(x, y, z);
                
                if (voxel.type == VoxelData::BOUNDARY) continue;
                
                Vector3<Real> point(x + 0.5, y + 0.5, z + 0.5);
                
                if (isPointInside(point, mesh)) {
                    voxel.type = VoxelData::INSIDE;
                    voxel.meshID = meshID;
                }
            }
        }
    }
}
\end{lstlisting}

\section{Advanced Bouzidi Implementation}

\subsection{Interpolation Schemes}

The implementation provides multiple interpolation schemes for improved accuracy:

\subsubsection{Linear Interpolation}
For $q \geq 0.5$:
\begin{equation}
f_{\bar{i}} = 2q f_i^* + (1-2q)f_i^{**} + 6w_i q(1-q)\mathbf{c}_i \cdot \mathbf{u}_w
\end{equation}

\subsubsection{Cubic Interpolation}
For improved accuracy when $q < 0.5$:
\begin{equation}
f_{\bar{i}} = a_0 + a_1 q + a_2 q^2 + a_3 q^3 + 6w_i q(1-q)\mathbf{c}_i \cdot \mathbf{u}_w
\end{equation}

where the coefficients are determined from neighboring PDF values.

\subsubsection{Multi-Reflection Scheme}
For enhanced stability with small $q$:

\begin{lstlisting}[caption={Multi-reflection Bouzidi implementation},label={lst:multireflection}]
Real applyMultiReflection(
    Uint x, Uint y, Uint z,
    Uint direction,
    Real q,
    const Vector3<Real>& wallVelocity,
    const PDFField& pdfPre,
    const PDFField& pdfPost) {
    
    const Uint invDir = D3Q19::opposite[direction];
    
    if (q >= 0.5) {
        // Standard scheme
        return applyLinearBouzidi(x, y, z, direction, q, 
                                 wallVelocity, pdfPre, pdfPost);
    } else {
        // Multi-reflection for q < 0.5
        const Real chi = (2.0 * q - 1.0) / (2.0 * q);
        
        // First reflection
        const Real f_post = pdfPost.get(x, y, z, invDir);
        const Real f_pre = pdfPre.get(x, y, z, direction);
        
        // Compute intermediate value
        const Real f_temp = chi * f_pre + (1.0 - chi) * f_post + 
                           6.0 * D3Q19::w[direction] * u_wall / (2.0 * q);
        
        // Second reflection
        const Cell neighbor = Cell(x, y, z).neighbor(direction);
        if (pdfPost.coordinatesValid(neighbor)) {
            const Real f_neighbor = pdfPost.get(neighbor, invDir);
            return (1.0 - 1.0/(2.0*q)) * f_temp + 
                   1.0/(2.0*q) * f_neighbor;
        }
        
        return f_temp;
    }
}
\end{lstlisting}

\subsection{Corner and Edge Treatment}

Special treatment is required for cells near geometric singularities:

\begin{algorithm}
\caption{Corner Detection and Treatment}
\label{alg:corner}
\begin{algorithmic}[1]
\REQUIRE Cell position $(x,y,z)$, distance array $\{q_i\}$
\ENSURE Modified PDFs for corner cell
\STATE Identify missing directions: $\mathcal{D}_{missing} = \{i : q_i < 1\}$
\IF{$|\mathcal{D}_{missing}| \geq 2$}
    \STATE Cell is at corner or edge
    \STATE Compute average: $\bar{q} = \frac{1}{|\mathcal{D}_{missing}|}\sum_{i \in \mathcal{D}_{missing}} q_i$
    \FOR{each direction $i \in \mathcal{D}_{missing}$}
        \STATE Apply Bouzidi with $\bar{q}$
        \STATE Apply corner correction factor: $f_i \leftarrow f_i / (1 + 0.5(|\mathcal{D}_{missing}| - 1))$
    \ENDFOR
\ENDIF
\end{algorithmic}
\end{algorithm}

\section{Force Calculation}

\subsection{Momentum Exchange Method}

The force on a body is computed using the momentum exchange method:

\begin{equation}
\mathbf{F} = \sum_{\mathbf{x}_b} \sum_{i} (\mathbf{c}_i \Delta t) [f_i(\mathbf{x}_b, t) + f_{\bar{i}}(\mathbf{x}_b, t + \Delta t)]
\end{equation}

where the sum is over all boundary nodes $\mathbf{x}_b$.

\subsection{Galilean Invariant Formulation}

For moving boundaries, the Galilean invariant force calculation is:

\begin{lstlisting}[caption={Galilean invariant force calculation},label={lst:galileanforce}]
Vector3<Real> calculateGalileanInvariant(
    const TriangleMesh& mesh,
    const Field<Real,19>& pdfField,
    const Field<Real,3>& velField,
    const Vector3<Real>& bodyVelocity) {
    
    Vector3<Real> totalForce(0.0);
    
    for (const auto& link : boundaryLinks) {
        const Uint dir = link.direction;
        const Uint invDir = D3Q19::opposite[dir];
        
        // Get PDFs
        const Real f_in = pdfField.get(link.cell, dir);
        const Real f_out = pdfField.get(link.cell, invDir);
        
        // Get local velocity
        Vector3<Real> localVel = velField.get(link.cell);
        
        // Relative velocity
        Vector3<Real> relVel = localVel - bodyVelocity;
        
        // Lattice velocity
        Vector3<Real> ci(D3Q19::cx[dir], 
                        D3Q19::cy[dir], 
                        D3Q19::cz[dir]);
        
        // Galilean invariant momentum exchange
        Real momentum = f_in + f_out;
        
        // Correction for moving reference frame
        Real correction = 2.0 * D3Q19::w[dir] * rho0 * 
                         (ci * relVel);
        
        momentum += correction;
        
        // Force contribution
        totalForce += ci * momentum;
    }
    
    return totalForce;
}
\end{lstlisting}

\subsection{Force Coefficients}

The dimensionless force coefficients are computed as:

\begin{align}
C_D &= \frac{F_x}{\frac{1}{2}\rho U_\infty^2 A} \\
C_L &= \frac{F_y}{\frac{1}{2}\rho U_\infty^2 A} \\
C_M &= \frac{M_z}{\frac{1}{2}\rho U_\infty^2 A L}
\end{align}

where $A$ is the reference area and $L$ is the reference length.

\section{Turbulence Model Integration}

\subsection{MRT-LES Coupling}

The Multiple Relaxation Time (MRT) operator with turbulence is implemented as:

\begin{lstlisting}[caption={MRT-LES implementation},label={lst:mrtles}]
void MRTTurbulence::applyWithTurbulence(
    PDFField& pdf,
    const VelField& vel,
    const DensField& dens,
    const Real& nu_t) {
    
    // Transform to moment space
    Real m[19], meq[19];
    transformToMomentSpace(pdf, m);
    
    // Compute equilibrium moments
    computeEquilibriumMoments(dens, vel, meq);
    
    // Modify relaxation parameters
    Real s_eff[19];
    modifyRelaxationParameters(s_eff, nu_t, nu_0);
    
    // Collision in moment space
    for (int i = 0; i < 19; ++i) {
        m[i] = m[i] - s_eff[i] * (m[i] - meq[i]);
    }
    
    // Transform back to velocity space
    transformToVelocitySpace(m, pdf);
}

void modifyRelaxationParameters(
    Real s_eff[19],
    const Real& nu_t,
    const Real& nu_0) {
    
    // Effective viscosity
    Real nu_eff = nu_0 + nu_t;
    Real tau_eff = 3.0 * nu_eff + 0.5;
    
    // Modify shear mode relaxation rates
    s_eff[9] = s_eff[11] = s_eff[13] = s_eff[14] = s_eff[15] = 1.0 / tau_eff;
    
    // Keep other rates unchanged
    // ...
}
\end{lstlisting}

\subsection{Wall Model Integration}

Near-wall treatment with adaptive y+ is implemented:

\begin{lstlisting}[caption={Adaptive wall function},label={lst:wallfunction}]
Real AdaptiveWallFunction::computeFrictionVelocity(
    Real u_parallel,
    Real y,
    Real rho,
    Real tolerance,
    Uint maxIterations) {
    
    // Initial guess
    Real u_tau = std::sqrt(nu_ * std::abs(u_parallel) / y);
    
    // Newton-Raphson iteration
    for (Uint iter = 0; iter < maxIterations; ++iter) {
        Real yPlus = computeYPlus(y, u_tau);
        Real uPlus = getVelocityFromYPlus(yPlus, 1.0);
        
        // Target: u_parallel = u_tau * uPlus
        Real f = u_tau * uPlus - u_parallel;
        
        // Derivative approximation
        Real du_tau = 1e-8 * u_tau;
        Real yPlus_new = computeYPlus(y, u_tau + du_tau);
        Real uPlus_new = getVelocityFromYPlus(yPlus_new, 1.0);
        Real f_new = (u_tau + du_tau) * uPlus_new - u_parallel;
        
        Real df = (f_new - f) / du_tau;
        
        // Update
        Real delta = -f / df;
        u_tau += delta;
        
        if (std::abs(delta) < tolerance * u_tau) {
            break;
        }
    }
    
    return u_tau;
}
\end{lstlisting}

\section{Validation Framework}

\subsection{Test Cases}

The implementation includes several validation test cases:

\begin{table}[h]
\centering
\begin{tabular}{|l|l|l|l|}
\hline
\textbf{Test Case} & \textbf{Parameter} & \textbf{Reference} & \textbf{Tolerance} \\
\hline
Sphere drag & $C_D$ at $Re=100$ & 1.09 & 5\% \\
Cylinder flow & $St$ at $Re=100$ & 0.164 & 5\% \\
Flat plate & $C_f$ at $Re_x=10^5$ & Blasius & 5\% \\
Ahmed body & $C_D$ at 25° slant & 0.295 & 5\% \\
\hline
\end{tabular}
\caption{Validation test cases and expected results}
\label{tab:validation}
\end{table}

\subsection{Grid Convergence Study}

Grid convergence is assessed using Richardson extrapolation:

\begin{equation}
p = \frac{\ln[(f_3 - f_2)/(f_2 - f_1)]}{\ln(r)}
\end{equation}

where $f_1, f_2, f_3$ are solutions on successively refined grids with refinement ratio $r$.

\section{Performance Considerations}

\subsection{Computational Complexity}

The computational complexity of key operations:

\begin{table}[h]
\centering
\begin{tabular}{|l|l|}
\hline
\textbf{Operation} & \textbf{Complexity} \\
\hline
Voxelization & $O(N_t \cdot N_c)$ \\
Point-in-mesh test & $O(N_t)$ per point \\
Distance field computation & $O(N_c \cdot N_t)$ \\
Flag mapping & $O(N_c)$ \\
Boundary link creation & $O(N_b)$ \\
Force calculation & $O(N_b)$ \\
\hline
\end{tabular}
\caption{Computational complexity where $N_t$ = number of triangles, $N_c$ = number of cells, $N_b$ = number of boundary cells}
\end{table}

\subsection{Memory Requirements}

Memory usage per lattice cell:

\begin{lstlisting}[caption={VoxelData structure memory layout},label={lst:voxelmemory}]
struct VoxelData {
    CellType type;              // 4 bytes (enum)
    BCType bcType;              // 4 bytes (enum)
    Real distance;              // 8 bytes
    Uint triangleID;            // 4 bytes
    Uint meshID;                // 4 bytes
    Vector3<Real> closestPoint; // 24 bytes
    Vector3<Real> normal;       // 24 bytes
    Real q;                     // 8 bytes
    Vector3<Real> wallVelocity; // 24 bytes
    // Total: 108 bytes per cell
};
\end{lstlisting}

\subsection{Optimization Strategies}

\begin{enumerate}
\item \textbf{Spatial Hashing}: Accelerate triangle queries using spatial data structures
\item \textbf{Bounding Box Culling}: Skip triangles outside cell neighborhoods
\item \textbf{Cache Optimization}: Process cells in cache-friendly order
\item \textbf{Incremental Updates}: Only revoxelize moving mesh regions
\end{enumerate}

\section{Usage Example}

\subsection{Configuration File}

\begin{lstlisting}[caption={Parameter file configuration},label={lst:config}]
<CurvedBoundary>
    <reynoldsNumber> 100000 </reynoldsNumber>
    <characteristicLength> 1.044 </characteristicLength>
    <kinematicViscosity> 1.044e-5 </kinematicViscosity>
    
    <body>
        <name> ahmed_body </name>
        <meshFile> ahmed_body_25deg.stl </meshFile>
        <position> 5.0 2.5 1.0 </position>
        <velocity> 0.0 0.0 0.0 </velocity>
        <scale> 1.0 </scale>
        <boundaryType> noslip </boundaryType>
    </body>
    
    <forceOutputInterval> 100 </forceOutputInterval>
    <forceOutputFile> forces_ahmed.dat </forceOutputFile>
</CurvedBoundary>

<Turbulence>
    <model> DynamicSmagorinsky </model>
    <filterWidth> 2.0 </filterWidth>
    <averagingTime> 1000 </averagingTime>
    
    <wallModel>
        <type> Spalding </type>
        <adaptiveYPlus> true </adaptiveYPlus>
    </wallModel>
</Turbulence>
\end{lstlisting}

\subsection{Implementation Code}

\begin{lstlisting}[caption={Complete usage example},label={lst:usage}]
int main(int argc, char** argv) {
    // Initialize MPI
    mpi::Environment env(argc, argv);
    
    // Read configuration
    FileReader config("simulation.prm");
    
    // Create domain
    Domain domain(config);
    
    // Initialize curved boundary module
    CurvedBoundary curvedBC;
    curvedBC.initialize(config.getBlock("CurvedBoundary"), domain);
    
    // Initialize turbulence model
    auto turbModel = TurbulenceModelFactory::create(
        config.getBlock("Turbulence"));
    curvedBC.setTurbulenceModel(turbModel.get());
    
    // Voxelize meshes and setup flag field
    auto flagField = domain.getFlagField();
    curvedBC.voxelizeMeshes(domain, *flagField);
    
    // Main time loop
    for (Uint t = 0; t < maxTimesteps; ++t) {
        // Update moving meshes
        curvedBC.updateVoxelization(dt, *flagField);
        
        // LBM collision and streaming
        lbmSweep.apply(domain);
        
        // Apply turbulence model
        turbulenceSweep.apply(domain, turbModel.get());
        
        // Apply boundary conditions
        curvedBC.applyBoundaryConditions(domain);
        
        // Calculate forces
        curvedBC.calculateForces(
            domain.getPDFField(),
            domain.getVelField(),
            domain.getDensField()
        );
        
        // Output
        if (t % outputInterval == 0) {
            curvedBC.writeForceOutput(t);
        }
    }
    
    return 0;
}
\end{lstlisting}

\section{Conclusions}

This chapter presented a comprehensive implementation of curved boundary conditions with integrated turbulence modeling for the waLBerla framework. The key contributions include:

\begin{enumerate}
\item \textbf{Accurate Boundary Treatment}: Implementation of interpolated Bouzidi schemes with cubic interpolation and multi-reflection for enhanced accuracy and stability
\item \textbf{Efficient Voxelization}: Robust algorithm for converting triangle meshes to flag field representation with proper inside/outside classification
\item \textbf{Turbulence Integration}: Seamless coupling of various LES models with curved boundaries including adaptive wall functions
\item \textbf{Moving Boundary Support}: Conservative treatment of time-dependent geometries with proper flag field updates
\item \textbf{Parallel Implementation}: Efficient utilization of MPI domain decomposition with optional OpenMP fine-grained parallelism
\end{enumerate}

The implementation enables high-fidelity simulations of complex flows around arbitrary geometries, as demonstrated by the Ahmed body test case. The modular design allows for easy extension with new turbulence models, boundary schemes, and validation cases.

\section{Future Work}

Several directions for future enhancement include:

\begin{enumerate}
\item \textbf{GPU Acceleration}: Port voxelization and boundary treatment to CUDA/HIP
\item \textbf{Adaptive Mesh Refinement}: Local refinement near curved surfaces
\item \textbf{Immersed Boundary Method}: Alternative formulation for thin structures
\item \textbf{Fluid-Structure Interaction}: Coupling with structural solvers
\item \textbf{Thermal Effects}: Extension to thermal LBM with conjugate heat transfer
\end{enumerate}

\appendix

\section{Code Structure}

The complete implementation consists of the following files:

\subsection{Turbulence Module}
\begin{itemize}
\item \texttt{turbulence/TurbulenceModel.h} - Base interface
\item \texttt{turbulence/models/SmagorinskyModel.h/cpp} - Standard Smagorinsky
\item \texttt{turbulence/models/DynamicSmagorinskyModel.h/cpp} - Dynamic model
\item \texttt{turbulence/models/WALEModel.h/cpp} - WALE model
\item \texttt{turbulence/models/VremanModel.h/cpp} - Vreman model
\item \texttt{turbulence/collision/MRTTurbulence.h/cpp} - MRT-LES coupling
\item \texttt{turbulence/wallmodels/WallModel.h/cpp} - Wall functions
\end{itemize}

\subsection{Curved Boundary Module}
\begin{itemize}
\item \texttt{curvedboundary/CurvedBoundary.h/cpp} - Main interface
\item \texttt{curvedboundary/mesh/TriangleMesh.h/cpp} - Mesh data structure
\item \texttt{curvedboundary/readers/STLReader.h/cpp} - STL file reader
\item \texttt{curvedboundary/boundary/InterpolatedBouzidi.h/cpp} - Advanced Bouzidi
\item \texttt{curvedboundary/force/GalileanInvariantForce.h/cpp} - Force calculation
\item \texttt{curvedboundary/validation/ValidationSuite.h/cpp} - Test cases
\end{itemize}

\subsection{Voxelization Module}
\begin{itemize}
\item \texttt{curvedboundary/voxelization/VoxelData.h} - Data structures
\item \texttt{curvedboundary/voxelization/Voxelizer.h/cpp} - Main voxelization
\item \texttt{curvedboundary/voxelization/FlagFieldMapper.h/cpp} - Flag mapping
\item \texttt{curvedboundary/voxelization/MovingMeshHandler.h/cpp} - Moving meshes
\item \texttt{curvedboundary/voxelization/RayTriangleIntersection.h} - Geometry
\end{itemize}

\end{document}