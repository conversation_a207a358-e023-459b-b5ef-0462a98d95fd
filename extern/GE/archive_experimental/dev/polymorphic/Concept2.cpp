#include <iostream>
#include <vector>
#include <functional>
#include <algorithm>


	// frward declaration 
	class TMatrix {} ;

	
	// first attemp, wrong 
	template<class arg1, class arg2, class Result>
	class TransformStruct {
	  inline Result operator()(const arg1& obj,const arg2& matrix)const 
	  {
		  return matrix (obj);
	  }
	} ;
	
	
	
	
	

	// ==================================================================
	template <class Derived>
	struct TriShapeBase
	{
	  	// Constructor 
		TriShapeBase () {
		    m_id = m_total++ ;
		    std::cout<< "TriShapeBase-> Base Constructor  \n";
		}

		// Destructor 
		~TriShapeBase () {
		    m_id = m_total-- ;
		    std::cout << "Base::Destructor::Trace ----> object [ "
			      << m_id
			      << " ]\n" ;
		}
		
		
	        //TODO  Transform and Voxelize are special function, because
	        //	they don't need argument 
	        //	TODO extend to more function with more than arguments 
                inline void TransformInterface (){
                    return ( static_cast<Derived* >(this) -> Transform() ) ;
                }
                
                inline void VoxelizeInterface (){
		    return ( static_cast<Derived* >(this) -> Voxelize() ) ;
		}
		
		// Experimental only 
		inline void MoveInterface (const double& d_t){
		    return ( static_cast<Derived* >(this) -> Move(d_t) ) ;
		}		
		
	  protected:
		int 		m_id ;	  ///< the object id
		static int	m_total ; ///< the total number of objects
                TMatrix		m_transformation;///< transformation matrix
	}; 

	template <class Derived> int TriShapeBase<Derived>::m_total = 0;
	
	// =====================================================================
	//! @brief Sphere 
	// =====================================================================	
	struct Sphere : TriShapeBase<Sphere>
	{
		explicit Sphere () {}	// default constructor
		inline void Transform()
                {
			std::cout<< " sphere["<< m_id << "]::Transform" << std::endl ;
                }
                
                inline void Voxelize()
                {
			std::cout<< " sphere["<< m_id << "]::Transform" << std::endl ;
		}
		
		
		inline void Move(const double& delta_t )
		{
			std::cout<< " sphere["<< m_id << "]::Move, dt = "<< delta_t << std::endl ;
		}	
		
	  private:
		int point_m ;
	};
	
	// =====================================================================
	//!@brief Objects Manager class 
	// TODO Config repository
	// =====================================================================
	template<typename Type,
		 typename ObjType = TriShapeBase<Type> >
	class ObjectsManager
	{
	    public:
		  bool insert (const ObjTygpe* obj) {
			  m_Objects.push_back ( (ObjType*) obj ) ;
		  }
		  // insert
		  // find 
		  // erase, remove
		  
		  
		  inline void Transform ();
		  inline void Voxelize  (); //
		  
		  inline void Move(const double& dt ) ;

	    private:
		  std::vector< ObjType*  > m_Objects ;//TODO use boost::ptr_vector
	};
	
	// =====================================================================
	/*! @brief Apply the same transfomation to all objects */
	// =====================================================================
	template<typename T, typename ObjType>
	inline void ObjectsManager<T,ObjType>::Transform ()
	{
	      std::for_each( m_Objects.begin(),
			     m_Objects.end(),
			     // std::mem_fun<result,ObjType>
			     /*! @note 	pointer a member function that takes 
					no arguments and that returns a value of
					type Result*/
			     std::mem_fun<void,ObjType>(&ObjType::TransformInterface )
			   );
	}
	
	// =====================================================================
	/*! @brief Voxelize all objects */
	// =====================================================================
	template<typename T, typename ObjType>
	inline void ObjectsManager<T,ObjType>::Voxelize ()
	{
	  std::for_each( m_Objects.begin(),
			 m_Objects.end(),
			 // std::mem_fun<result,ObjType>
			 /*! @note pointer to member function that takes 
			 no arguments and that returns a value of
			 type Result*/
			 std::mem_fun<void,ObjType>(&ObjType::VoxelizeInterface)
			 );
	}	
	
	
	// =====================================================================
	/*! @brief Move all objects with time step dt */
	// =====================================================================
	template<typename T, typename ObjType>
	void ObjectsManager<T,ObjType>::Move (const double& dt)
	{
	      // iterator overal objects
	    
	}	
	
	// =====================================================================
	//! @brief main function :
	// =====================================================================	
	int main()
	{
	  //1. create object manager 
	  ObjectsManager<Sphere> *m_Manager = new ObjectsManager<Sphere> ;
	
	  //2. create some new objects inheritance from TriShapeBase:example sphere
		  m_Manager -> insert ( new Sphere() ) ;
		  m_Manager -> insert ( new Sphere() ) ;	  
		  m_Manager -> insert ( new Sphere() ) ;
		  m_Manager -> insert ( new Sphere() ) ;	  
		  m_Manager -> insert ( new Sphere() ) ;
	  
	  //2. Applaying transformation 
	  m_Manager -> Transform () ;

	  //3. Move with dt 
// 	  m_Manager -> Move (0.2) ;	  
	  
	  
	  
	  //3. free memory  
	  delete m_Manager ;
	  return (0) ;
	}
