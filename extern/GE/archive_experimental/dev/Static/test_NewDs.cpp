#include "NewDs.h"
// =============================================================================
// includes
// =============================================================================
#include <iostream>
#include <list>
#include "../../gmath/GEMath.h" //#include "Point3D.h" #include "Vector3D.h" #include "../misc/Constants.h"
#include "../../gmath/MeshManip.h"
#include "../../configure/ComputePrecision.h"
#include "../../mesh/StlMeshLoaderExtended.h"
#include "../../mesh/StlMeshTypeExtended.h"
// =============================================================================
#include "../../gmath/geRay.h"
// #include "../mesh/MeshMerger.h"
#include "../../gmath/Aabb.h"
#include "../../util/Plane.h"
#include "../../mesh/StlMeshExporter.h"
// =============================================================================



// =============================================================================
// Configure facedata sample
// =============================================================================
namespace ge
{
	struct TestFaceConfigure
	{
		typedef Compact config ; // floats by default
		// only float or double type are accepted
		enum { concept = allowed_type< config::Element>::valid };
		
		typedef int			length_type;   // NEW 
		
		typedef config::Element		real;
		typedef Point3D <real>		point_type; 
		typedef Vector3D<real>		vector_type;
		typedef Vector3D<real>		edge_type;

		// additionally,
		typedef geRay<TestFaceConfigure>::ray_type		ray_type ;
		typedef Aabb <TestFaceConfigure >			aabb_type ;
		//@note aabb_type should be defined before plane_type
		typedef Plane<TestFaceConfigure >			plane_type;


		typedef Mesh< TestFaceConfigure >		mesh_type ;
		typedef mesh_type::Face				triangle_type ;
		typedef mesh_type::faces_conatiner_type		faces_conatiner_type;
		typedef mesh_type::polygons_list		polygons_list ;
		typedef polygons_list::iterator		iterator;
		typedef polygons_list::const_iterator	const_iterator;
		
		typedef StlMeshTypeExtended<TestFaceConfigure>	mesh_type  ;
		
		
		typedef StlMeshLoaderExtended<mesh_type> 	object_loader ;
		typedef StlMeshExporter<mesh_type>	stl_exporter;//class contains only static member funtions

	};
}

// =============================================================================
// same typedefs to save typing
// =============================================================================
typedef TestFaceConfigure::mesh_type		mesh_type;
typedef TestFaceConfigure::mesh_type		mesh_type ;
typedef TestFaceConfigure::faces_conatiner_type faces_conatiner_type;

typedef mesh_type::point_type 			vertex_type ;
typedef mesh_type::vector_type			vector_type ;
typedef mesh_type::Face				triangle_type ;

typedef mesh_type::polygons_list		polygons_list;
// loader n exporter 
typedef TestFaceConfigure::object_loader	loader;
typedef TestFaceConfigure::stl_exporter		exporter;

// =============================================================================
template<typename polygons_list>
inline void Print (const polygons_list& poly);

int main (int argc, char* argv[] )
{
	if (argc != 2 )
               throw runtime_error ("please specify one arg to rnu this programm.\n");

	try
	{
		mesh_type my_object ;
		const vector_type n_ (1.0 , 0.0 , 0.0) ;
		vertex_type v0 , v1,v2 , v3 ;
	
		v0.X()= 1.0; v0.Y()= 1.0; v0.Z()= 1.0;
		v1.X()= 2.0; v1.Y()= 2.0; v1.Z()= 2.0; 
		v2.X()= 1.0; v2.Y()= 1.0; v2.Z()= 1.0;
		v3.X()= 3.0; v3.Y()= 3.0; v3.Z()= 3.0;
		//std::cout <<  v0 << std::endl;
		//my_object.AddVertex(v0);my_object.AddVertex(v1);my_object.AddVertex(v2);
		//my_object.push_back(v0,v1,v2,n_);my_object.push_back(v0,v1,v2,n_);my_object.push_back(v0,v1,v3,n_);
		
		faces_conatiner_type *polTmp = new faces_conatiner_type (my_object);
		
		polTmp -> push_back(v0, v1,v2 , n_);
		polTmp -> push_back(v0, v1,v3 , n_) ;
		std::cout<<"===========================================================\n";
		std::cout<<"Print () \n";
		std::cout<<"===========================================================\n";

		std::cout << *polTmp << std::endl ;
		std::cout<<"===========================================================\n";
		
		my_object.PrintVertices();
		
		std::cout<<"===========================================================\n";
		
		my_object.PrintEdges() ;
		
		std::cout<<"===========================================================\n";
		
		my_object.PrintFaces() ;
		
		std::cout<<"===========================================================\n";


		typedef std::vector< triangle_type>::const_iterator iterator ;
		for (iterator it=my_object.GetPolyListMesh().begin() ; it!=my_object.GetPolyListMesh().end() ;++it )
		{
			std::cout<< "normal : " << (*it).N() ;
			std::cout<< " ,	v["<< (*it).V0() << ", " << (*it).V1() << ", " << (*it).V2()<< "]"  << std::endl;
		}
		std::cout<<"===========================================================\n";





		// real world test.
		std::string BaseFile_name ( argv[1] ) ;
		
		if (BaseFile_name.empty() )
			throw InvalidArgument(__FILE__,__LINE__, "please specify file name without extension \".stl\"  \n" ) ;
		
		std::stringstream  file_name , file_name_tmp , file_name_lbm ;

		file_name     <<  BaseFile_name << ".stl" ;
		file_name_tmp <<  BaseFile_name << "_tm.stl" ;
		file_name_lbm <<  BaseFile_name << "_lbm.stl" ;

		mesh_type	*g_object  = new mesh_type ( file_name.str().c_str() );

		std::cerr << g_object->  GetFileName() << std::endl ;
		
		(g_object->GetPolyListMesh()).push_back( (v0 ,v1, v2) ) ;
		
		loader::Load(*g_object);
		loader::FileInfo(*g_object) ;
		
		
		exporter::Export("tmp.stl", false , *g_object) ;
		
	} catch (std::exception& e)
	{
		cout << "Error, "<< e.what() << endl;
	}
	return (0) ;
}



template<typename polygons_list>
inline void Print (const polygons_list& poly)
{
	typedef typename polygons_list::const_iterator const_iterator ;
	std::cout<< "polygons_list poly has : " << poly.size() << " faces\n";
	for (const_iterator it = poly.begin();it != poly.end(); ++it)
		std::cout<< *it << std::endl;
}
