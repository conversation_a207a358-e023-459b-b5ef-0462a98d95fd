// =============================================================================
/*!
// @file TriShapeBase.h
// @brief Header file for TriShapeBase class.
// <AUTHOR> <PERSON>
*/
// =============================================================================


#ifndef TRISHAPEBASE_HH
#define TRISHAPEBASE_HH

// includes
#include <iostream>

using std::cout ; 
using std::endl ;
using std::cerr ;


namespace ge
{
	// forward declarations,
	struct Ray   {} ; // Ray forward declaration
	struct Point {} ; // Point forward declaration 
	
	
	// =====================================================================
	/*!
	// @class TriShapeBase
	// @brief triangle mesh base class, it implement all commun interface functions.
	// @note each implemented funcion in any derived class from this class, 
	// *	 should have "Interface" include in its name in this base class
	
	// @example example_Implementation.cpp .
	// This is an example of how can implement function. say you have 
	// implement "implementation()" function in szbderived class, then you 
	// should implement interface/wrapper function in this base class 
	// "ImplementationIntreface()".\n	
	// More details about this example.
	// 
	*/ 
	// =====================================================================
	template <class Derived>
	struct TriShapeBase
	{
	  
		//Constructor 
		TriShapeBase () {
		    id_ = total_++ ; 
		    std::cout<< "TriShapeBase-> Base Constructor  \n";
		}

		// Destructor 
		~TriShapeBase (){ 
		    id_ = total_-- ; 
		    std::cout << "Base::Destructor::Trace ----> object [ "
			      << id_ 
			      << " ]\n" ;
		}

		//note: ImplementationInterface is just example how you can 
		//	implement function 
		inline void ImplementationInterface() { //@note don't use const
		      static_cast<Derived*>(this)->implementation(); 
		}

		//Intersect(const Ray& ray)
		inline bool Intersect (const Ray& ray ) const { 
		      static_cast<Derived* >(*this) -> Intersect (ray) ;
		}
	    
		//Trace Interface 
		inline void TraceInterface() { 
		      static_cast<Derived*>(this)->Trace(); 
		}

		//CreateInterface
		inline bool CreateInterface (const int & iter) {
		      return ( static_cast<Derived* >(this) -> Create (iter) ) ;
		}

		//ExportToStlInterface 
		inline void ExportToStlInterface (const char* fname){
		      static_cast<Derived* >(* this)->ExportToStl (fname) ; 
		}

	protected:
		  
		int         id_	  ; ///< the object id 
		static int  total_; ///< the total number of objects 
	
	}; // =========================================================================

	template <class Derived> int TriShapeBase<Derived>::total_ = 0; 

// =============================================================================
}// end namespace ge 
// =============================================================================

#endif 

