#ifndef EXCEPTION_H
#define EXCEPTION_H

#include <string>
#include <sstream>


#include <stdexcept>        // stdexcept header file contains runtime_error
#include <exception>        // exception header file contains exception def

// using std::runtime_error;  // standard C++ library class runtime_error
// using   std::out_of_range   
// using   std::range_error
// using   std::underflow_error
// using   std::overflow_error

// =============================================================================
/*!
   \class Exception exception.hh "exception.hh"
   \brief The Exception class provides infos about error conditions

   Instances of the Exception and its children classes are thrown
   to indicate an error. This normally means that the programs
   stop. See the try-catch block in the main program.

*/
// =============================================================================

  class Exception : public std::exception
  {
  public:

    /*!
	\brief This constructor creates an Exception which
     *  knows about the file and line it was thrown from.
        \param file (input) file where the exception was thrown
        \param line (input) line where the exception was thrown
        \param Msg (input) raison why the error occurred
    */ 
    Exception(const std::string& file, int line, const std::string& Msg){
      std::stringstream stream;
      stream << file << " line "  << line << ": " << Msg;
      text_ = stream.str();
    }

    virtual ~Exception() throw() {};  // Destructor may not throw any exception.

    virtual const char* what() const throw(){ // Get info 
      return text_.c_str();
    }

  private:

     std::string text_;  //! Exception text
  };

//---------------------------------------------------------------
  /*!
   \class BoundsViolation exception.hh "exception.hh"
   \brief A BoundsViolation object is thrown if an index is out of bound.
  */
//---------------------------------------------------------------
  class BoundsViolation : public Exception
  {
  public:
    BoundsViolation(const std::string& file,
		    const int & line ,
		    const std::string& Msg) : Exception(file, line, Msg){};
  };

//---------------------------------------------------------------
  /*!
   \class BadSize exception.hh "exception.hh"
   \brief A BadSize object is thrown some other objects are 
   * not properly initialized (e.g. zero)
*/
//---------------------------------------------------------------
  class BadSize : public Exception
  {
  public:
    BadSize(const std::string& file,
            const int &  line,
            const std::string& Msg):Exception(file,line,Msg){};
  };

//---------------------------------------------------------------

//---------------------------------------------------------------
  /*!
   \class ErrorOpening exception.hh "exception.hh"
   \brief If an error has occurred during the opening the file.

*/
//---------------------------------------------------------------
  class ErrorOpening : public Exception
  {
  public:
    ErrorOpening(const std::string& file,
            	 const int & line,
            	 const std::string& Msg):Exception(file,line,Msg){};
  };

//---------------------------------------------------------------

#endif

