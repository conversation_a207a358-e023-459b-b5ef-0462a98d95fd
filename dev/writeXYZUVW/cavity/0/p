/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  3.0.1                                 |
|   \\  /    A nd           | Web:      www.OpenFOAM.org                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       volScalarField;
    object      p;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

dimensions      [0 2 -2 0 0 0 0];

internalField   uniform 0;

boundaryField
{
    // Set empty. Explicit patch name should win.
    empty
    {
        type            MATCHEMPTYGROUP;
    }

    frontAndBack
    {
        type            empty;
    }

    ".*Back"
    {
        type            MATCHWILDCARD;
    }

    // Set walls. Last patchGroup should win.
    allBoundaryGroup
    {
        type            MATCHALLGROUP;
    }

    wallsGroup
    {
        type            zeroGradient;
    }

}

// ************************************************************************* //
