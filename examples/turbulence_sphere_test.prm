// Turbulence Model Test with Sphere Geometry
// This tests our turbulence models with curved boundaries using available sphere mesh

simdata {
    domainX         64;         // Domain size in x direction
    domainY         32;         // Domain size in y direction  
    domainZ         32;         // Domain size in z direction
    dx              0.002;      // Cell size delta x
    Uref            0.1;        // Reference velocity
    timesteps       200;        // Number of timesteps
    Lref            0.064;      // Reference length
    nu              1.0e-4;     // Viscosity
    rho             1.0;        // Density
    maxPermittedVel 0.15;       // Maximum velocity
    
    dump;  // Output all computed parameters
}

// Test our Dynamic Smagorinsky turbulence model
turbulence {
    model               dynamic_smagorinsky;  // Use our Dynamic Smagorinsky model
    filterWidth         2.0;                  // Test filter width = 2*dx
    strainRateMethod    nonequilibrium;       // Non-equilibrium method
    dynamicSmagorinskyAveraging lagrangian;   // Lagrangian averaging
    maxDynamicCs        0.3;                  // Limit Cs for stability
}

// Use STL geometry for curved boundaries
stl {
    // Sphere obstacle
    body {
        name        sphere;
        meshfile    ../extern/GE/src/test/sphere.stl;  // Use available sphere mesh
        position    32.0 16.0 16.0;                    // Center of sphere
        velocity    0.0 0.0 0.0;                       // Fixed body
        rotation    0.0 0.0 0.0;                       // No rotation
        refArea     0.01;                              // Reference area
        refLength   0.032;                             // Sphere diameter
        outputForces true;                             // Enable force output
    }
    
    // Global settings
    forceOutputInterval 50;               // Output forces every 50 steps
    forceOutputFile     sphere_forces.dat; // Force output file
    useOctree          true;              // Use octree for efficiency
    qValueThreshold    0.01;              // Minimum q-value
}

// Boundary conditions for flow around sphere
init {
    // Inlet (west)
    west {
        vel_in {
            y_L     1..'domainY'-1;
            z_L     1..'domainZ'-1;
            ux_L    0.08;  // Inlet velocity in lattice units
            uy_L    0.0;
            uz_L    0.0;
        }
    }
    
    // Outlet (east)
    east {
        prs_nils {
            y_L     1..'domainY'-1;
            z_L     1..'domainZ'-1;
            prs_L   0.0;  // Zero gauge pressure
        }
    }
    
    // Side walls - slip
    north {
        freeslip {
            x_L     0..'domainX'+1;
            z_L     0..'domainZ'+1;
        }
    }

    south {
        freeslip {
            x_L     0..'domainX'+1;
            z_L     0..'domainZ'+1;
        }
    }

    // Top and bottom - slip
    top {
        freeslip {
            x_L     0..'domainX'+1;
            y_L     0..'domainY'+1;
        }
    }

    bottom {
        freeslip {
            x_L     0..'domainX'+1;
            y_L     0..'domainY'+1;
        }
    }
}

// Output configuration
paraview {
    filename    turbulence_sphere;
    xstart      0;
    xend        64;
    ystart      0;
    yend        32;
    zstart      16;
    zend        16;      // Single plane through sphere center
    writevelocity  1;
    writepressure  1;
    writeeddyvisc  1;    // Output eddy viscosity to verify turbulence model
    writeflags     1;    // Show geometry
    interval       50;   // Output every 50 steps
    physical       1;    // Use physical units
}

// Monitor turbulence and force quantities
simoutput {
    DRAG_COEFFICIENT {
        time 25;  // Output drag coefficient every 25 steps
    }
    TURBULENT_KINETIC_ENERGY {
        time 25;  // Output TKE every 25 steps
    }
    EDDY_VISCOSITY_STATS {
        time 25;  // Output eddy viscosity statistics
    }
}

// Logging configuration
logging {
    logfile turbulence_sphere_test.log;
    append  0;
}
