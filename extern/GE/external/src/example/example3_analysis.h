/*******************************************************************************
ConminC++, to solve constrained or unconstrained function minimization.
Copyright (C) 1999  <PERSON><PERSON> <PERSON><PERSON> Dunn

This program is free software; you can redistribute it and/or
modify it under the terms of the GNU General Public License
as published by the Free Software Foundation; either version 2
of the License, or (at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this program; if not, write to the Free Software
Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA  02111-1307, USA.
*******************************************************************************/

//---------------------------------------------------------------------------
#ifndef Ex3_anaH
#define Ex3_anaH
#include "../src/conmin.h"
//---------------------------------------------------------------------------
class Ex3: public Conmin
{
public:
// default constructor
Ex3(int ndv=4, int ncon=3, int maxnac=3):
     Conmin(ndv, ncon, maxnac) {};
void analysis();

private:
double rho, a1, a2;
};
#endif
 
