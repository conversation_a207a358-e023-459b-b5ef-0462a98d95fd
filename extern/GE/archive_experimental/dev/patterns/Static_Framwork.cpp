// Metaprograms can be executed at compile time (static) or runtime (dynamic)

#include <iostream>
using namespace std;

template <class Concretization>
class Application: public Concretization
{ 
    public:
      inline void run()
      { 
	  typedef Application<Concretization> self;
	  self::init(); // static
	  self::main();
	  self::end();
      }
};

class MyApp
{ 
    public: //!@note the static-type is optional 
	inline static void  init(){ cout << "init" << endl; }
	inline /*static*/ void  main(){ cout << "main" << endl; }
	inline /*static*/ void  end() { cout << "end"  << endl; }
};

int main()
{
    Application<MyApp> app;
    app.run();
    return 0 ;
}

// Static Inheritance-Based Adapter
template <class Component>
class Adapter : public Component
{ 
  public:
	// refine operationA()
	void operationA()
	{ // ... adapter-specific work
	    Component::operationA();
	 //... adapter-specific work
	};
};

///Advantags :
//=============
// • Need no variable of typeComponent 
// • Inherited operations that remain unchanged don't need explicit forwarding
// • Only refined operations need to be overridden
// • Less interface dependencies, hence easier to maintain and to adapt