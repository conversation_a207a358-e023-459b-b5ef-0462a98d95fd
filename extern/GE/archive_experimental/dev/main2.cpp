// =============================================================================
// includes
// =============================================================================
#include <iostream>
#include <vector>
#include "ObjectCounter.h"	 	// object counter 
#include "../accel/Sphere.h" // this will include too:
// #include "../accel/TriShapeBase.h"// #include "../transformation/TMatrix.h"  // transformation matrix 
// =============================================================================



using namespace std;
using namespace transformation ;



//  ============================================================================
// type of Computational
//  ============================================================================
struct Compact{
        typedef double Precision ;
};
//  ============================================================================

template<class Conputaional = Compact>
struct MeshType
{
    typedef Point3D <Compact::Precision> Vertices_type;
    // TODO  use StlMeshType
    // vertices_list_type 
    // bla bla ...
};

// =============================================================================
// Object base class
// =============================================================================
template<class Conputaional>
struct ObjectBase : public ObjectCounter <ObjectBase <Conputaional> >
{
	inline bool Set(const transformation::TMatrix& mt)
	{
		m_mat = m_mat;
	}

	inline bool AppyingMatrix(){
	// TODO 
	// assert (m_mat is setted, or not empty) ;
		transformation::ApplyingTransform<ObjectBase>::Applying(this, m_mat) ;
	}

	// =====================================================================
	// return the number of a live objects 
	// =====================================================================

  private :
	transformation::TMatrix m_mat ; 
};








// =============================================================================
// class World
// =============================================================================
template<class Configure>
class World
{
	public:
		typedef typename Configure::Precision    ElemenType ;
		typedef typename Configure::mesh_type  mesh_type ;

		//  create new object 
		inline mesh_type* NewObject (){
			return new mesh_type () ;
			//TODO add to the container
        	}

		//  Add existing object
		inline void AddObject (mesh_type* object){
			m_objectlist.push_back(object) ;
        	}

        	inline std::size_t NumOfLiveObject(){
		  return mesh_type::live();
		}
      private:
		std::vector < mesh_type* > m_objectlist ;// boost::ptr_vector
};

//  ============================================================================



//  ============================================================================
// Sample of configure technique 
//  ============================================================================
template<class Conputaional = Compact>
struct ConfSample
{
	typedef typename Conputaional::Precision	Precision ;
        typedef ObjectBase<Precision>			mesh_type ;
};



//  ============================================================================
// 
//  ============================================================================
int main (int argc, char* argv[] )
{
        typedef World< ConfSample<> >  world_type ;
	typedef world_type::mesh_type mesh_type ;
	world_type  *my_World = new world_type ();
	

	mesh_type *tmp0 =  my_World -> NewObject( )  ;
	mesh_type *tmp1 =  my_World -> NewObject( )  ;
	mesh_type *tmp2 =  my_World -> NewObject( )  ;
	
	my_World -> AddObject (tmp0) ;
	my_World -> AddObject (tmp1) ;
	my_World -> AddObject (tmp2) ;
	
	std::cout <<   my_World-> NumOfLiveObject () << std::endl ;	
	
	
	TMatrix Matrix_2;
	Matrix_2.RotateX(60);
	std::cout<< "\n" ;
	std::cout<< "=======================================\n" ;
	std::cout<< "Element of the Matrix::RotateX(60) are: \n";
	std::cout<< "=======================================\n" ;    
	std::cout<< Matrix_2 << std::endl;
	

	tmp0 -> Set () ;
	
	
	// std::cout << " value -> " << *tmp << std::endl ;

// 	delete tmp0 ;
	delete tmp1 ;
	delete tmp2 ;
	
	std::cout <<  my_World -> NumOfLiveObject () << std::endl ;
	
        return (0) ;
}

