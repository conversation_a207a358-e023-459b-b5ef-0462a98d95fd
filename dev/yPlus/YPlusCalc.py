"""
----------------------------------------------------------------------------
DESCRIPTION :: This scirpt calculates the the first cell height based on a 
               desired y+ value. User's should modify the User Input section as
               required. It assumes an ideal gas for calculating the freestream
               velocity.
AUTHOR      :: <PERSON>, <EMAIL>
DATE        :: Nov 13 014
REVISION    :: 00  
INPUTS      :: None, Script
RETURNS     :: Output to screen
EXAMPLE     :: None
---------------------------------------------------------------------------- 
"""    
## User Inputs

#gamma=1.4 # gamma = cp/cv - heat capacity ratio
#Minf = 1.5 # Freestream Mach
#R = 287.058 # J/(kg-K)
#Tinf = 205.15 # K

#BL='turb'
BL='laminar'
yplus = 1
##

# Begin Calculations
#a = (gamma * R * Tinf)**0.5 # Spd Sound
mu = 1.0e-03 # N-s/m^2
rho = 1 # kg/m^3
L = 0.1 # m

Uinf  = 6 # Freestream Velocity
# Uinf = Minf * a 

Re = rho * Uinf * L / mu # Corresponding Reynolds Number


if (BL=='turb'):
 Cf = 0.026 / (Re)**(1/7) # Skin Friction, Turbulent BL
else:
 Cf = 0.664 / (Re**0.5)   # Laminar BL

tau_w = (Cf * rho * Uinf**2) / 2 # Wall Stream
Ufric = (tau_w / rho)**0.5 # Skin Friction Velocity
ds = (yplus * mu) / (Ufric * rho) # 1st Spacing

# Print Some Stuff to the Screen
print 'Uinf =',Uinf, 'm/s'
print 'Re =',Re
print 'For Yplus =',yplus
print '1st wall spacing =',ds,'m'
print '1st wall spacing =',ds*1000,'mm'
print '1st wall spacing =',ds*39.3701,'in'
