
#include "Logging.h"


namespace ge
{
	// =====================================================================
	// Definition and initialization of the static member variables
	// =====================================================================
	std::string Logging::file_;
	
	
	
	// =====================================================================
	/*!\fn Logging::Logging()
	// \brief Constructor of the logging class.
	//
	// The constructor is used only once by the single instance of the logging class. It relies on
	// the fact, that the logging class has already been configured. The constructor opens the log
	// file and appends a file header with a time stamp.
	 */
	Logging::Logging()
	{
//		boost::filesystem::remove_all( boost::lexical_cast<std::string>fName+"/" );
// 		boost::filesystem::create_directories( "AnaglyphicPov/left" );
		#ifdef GE_USE_MPI
			const std::string tmp =  Logging::file_ ;
			assert (tmp.find (".txt") != -1  ) ;
			const std::string fWext = tmp.substr ( 0 , tmp.find (".txt")  )  ; 
			std::stringstream	fileName ;
			fileName.str("");
			int rank , size  ;
			ASSERT_SUCCESS (MPI_Comm_rank(MPI_COMM_WORLD, &rank) );//Determines the rank
			ASSERT_SUCCESS (MPI_Comm_size(MPI_COMM_WORLD, &size) );//Determines the size 
			fileName << fWext << "_"<< rank <<".txt";

			//fileName << fWext << "_"<< MPI::COMM_WORLD.Get_rank()  <<".txt";
			log_.open(fileName.str().c_str() , std::ios::out /*| std::ios::app  | std::ofstream::ate */);
		#else
			log_.open (Logging::file_.c_str() , std::ios::out /*| std::ios::app  | std::ofstream::ate */);
		#endif 
		log_ << "//==============================================================================\n/*!\n";
		log_ << "// @brief Log File generated by \"GE\" \n";
		log_ << "// @date create on: "+ TimeToStr(std::time (&m_Ctime) )  +"\n";
		//log_ << "// @author:Cherif Mihoubi\n";
		#ifdef GE_USE_MPI
			ASSERT_SUCCESS (MPI_Comm_rank(MPI_COMM_WORLD, &rank) );//Determines the rank
			ASSERT_SUCCESS (MPI_Comm_size(MPI_COMM_WORLD, &size) );//Determines the size 
			int length;
			char* pName = new char[MPI::MAX_PROCESSOR_NAME+1];
			MPI::Get_processor_name(pName, length);
			std::string name(pName);
			delete [] pName;
			log_ << "// @processor rank : "	 + boost::lexical_cast<std::string> (rank) );
			log_ << "// @running on host : " + boost::lexical_cast<std::string> (name) );
			log_ << "// @number of processors in the group : " + boost::lexical_cast<std::string> ( size ) );
		#endif
		// TODO add date, time
		log_ << "*/\n//==============================================================================\n\n";
		
		#ifdef DEBUG
			log_ << " DEBUG ";
		#else
			log_ << " RELEASE ";
		#endif
	}
	// =====================================================================



	//======================================================================
	//
	//  UTILITY FUNCTIONS
	//
	//======================================================================

	// =====================================================================
	/*!\fn Logging& Logging::Instance()
	// \brief Access to the single instance of the logging class.
	//
	// \return The single instance of the logging class.
	// \exception std::runtime_error Logging class has not been configured.
	//
	// The 'Log' function directly commits the message string to file.
	 */
	Logging& Logging::Instance()
	{
		static std::auto_ptr<Logging> logger_;
		if( !logger_.get() )
		{
			if( file_.empty() )
				throw std::runtime_error( "Logging not configured!" );
			logger_ = std::auto_ptr<Logging>( new Logging() );
		}
		return *logger_.get();
	}
	// =====================================================================
	

	// =====================================================================
	/*!\fn Logging::~Logging()
	// \brief Destructor of the logging class.
	//
	// The destructor is used only once by the single instance of the logging class. The constructor
	// appends a footer in the log file and closes it.
	 */
	// =====================================================================
	Logging::~Logging()
	{
		//Writing the bottom line
		log_ << "// LOG END on : "+ TimeToStr(std::time (&m_Ctime) ) ;
		log_ << "//==============================================================================\n";
		//Closing the file stream
		log_.close();
	}
	// =====================================================================

}