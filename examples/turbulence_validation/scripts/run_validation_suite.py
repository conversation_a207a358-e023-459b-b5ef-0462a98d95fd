#!/usr/bin/env python3
"""
Automated Turbulence Validation Suite
Runs all validation cases and compares against reference data
"""

import os
import sys
import time
import subprocess
import json
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime

class ValidationSuite:
    def __init__(self, base_dir):
        self.base_dir = Path(base_dir)
        # Find the solver path dynamically
        walberla_root = self.base_dir.parent.parent
        self.solver_path = walberla_root / "build/X9DAi_par/bin/solver"
        self.results = {}
        self.start_time = None
        
    def run_validation_case(self, case_name, param_file, timeout=300):
        """Run a single validation case with timeout"""
        print(f"\n🔄 Running {case_name}...")
        start_time = time.time()
        
        try:
            # Run the simulation
            result = subprocess.run(
                [str(self.solver_path), param_file],
                cwd=self.base_dir.parent,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            
            runtime = time.time() - start_time
            
            if result.returncode == 0:
                print(f"✅ {case_name} completed successfully in {runtime:.1f}s")
                return {
                    'status': 'success',
                    'runtime': runtime,
                    'stdout': result.stdout,
                    'stderr': result.stderr
                }
            else:
                print(f"❌ {case_name} failed with return code {result.returncode}")
                return {
                    'status': 'failed',
                    'runtime': runtime,
                    'returncode': result.returncode,
                    'stdout': result.stdout,
                    'stderr': result.stderr
                }
                
        except subprocess.TimeoutExpired:
            print(f"⏰ {case_name} timed out after {timeout}s")
            return {
                'status': 'timeout',
                'runtime': timeout,
                'error': f'Timeout after {timeout}s'
            }
        except Exception as e:
            print(f"💥 {case_name} crashed: {str(e)}")
            return {
                'status': 'crashed',
                'runtime': time.time() - start_time,
                'error': str(e)
            }
    
    def extract_validation_metrics(self, case_name, log_file):
        """Extract validation metrics from simulation log"""
        metrics = {}
        
        if not os.path.exists(log_file):
            return metrics
            
        try:
            with open(log_file, 'r') as f:
                content = f.read()
                
            # Extract drag coefficient from log
            if 'drag coefficient' in content.lower():
                # Parse drag coefficient from output
                lines = content.split('\n')
                for line in lines:
                    if 'drag' in line.lower() and 'coefficient' in line.lower():
                        # Extract numerical value
                        import re
                        numbers = re.findall(r'[-+]?\d*\.?\d+', line)
                        if numbers:
                            metrics['drag_coefficient'] = float(numbers[-1])
                            
            # Extract Strouhal number if present
            if 'strouhal' in content.lower():
                lines = content.split('\n')
                for line in lines:
                    if 'strouhal' in line.lower():
                        import re
                        numbers = re.findall(r'[-+]?\d*\.?\d+', line)
                        if numbers:
                            metrics['strouhal_number'] = float(numbers[-1])
                            
            # Extract final kinetic energy
            if 'Ek :' in content:
                lines = content.split('\n')
                for line in reversed(lines):
                    if 'Ek :' in line:
                        import re
                        match = re.search(r'Ek\s*:\s*([-+]?\d*\.?\d+)', line)
                        if match:
                            metrics['final_kinetic_energy'] = float(match.group(1))
                            break
                            
        except Exception as e:
            print(f"Warning: Could not extract metrics from {log_file}: {e}")
            
        return metrics
    
    def compare_with_reference(self, case_name, metrics):
        """Compare simulation metrics with reference data"""
        reference_file = self.base_dir / f"reference_data/{case_name}.csv"
        
        if not reference_file.exists():
            return {'status': 'no_reference', 'message': 'No reference data available'}
            
        try:
            # Load reference data
            ref_data = pd.read_csv(reference_file, comment='#')
            
            # Extract global parameters
            global_params = ref_data[ref_data.columns[0]].str.contains('drag_coefficient|strouhal_number', na=False)
            
            comparison = {}
            
            # Compare drag coefficient
            if 'drag_coefficient' in metrics:
                ref_cd = ref_data[global_params & ref_data.columns[0].str.contains('drag_coefficient')]['value'].iloc[0]
                sim_cd = metrics['drag_coefficient']
                error = abs(sim_cd - ref_cd) / ref_cd * 100
                
                comparison['drag_coefficient'] = {
                    'reference': ref_cd,
                    'simulation': sim_cd,
                    'error_percent': error,
                    'status': 'pass' if error < 10 else 'fail'
                }
                
            # Compare Strouhal number if available
            if 'strouhal_number' in metrics:
                ref_st = ref_data[global_params & ref_data.columns[0].str.contains('strouhal_number')]['value'].iloc[0]
                sim_st = metrics['strouhal_number']
                error = abs(sim_st - ref_st) / ref_st * 100
                
                comparison['strouhal_number'] = {
                    'reference': ref_st,
                    'simulation': sim_st,
                    'error_percent': error,
                    'status': 'pass' if error < 15 else 'fail'
                }
                
            return comparison
            
        except Exception as e:
            return {'status': 'error', 'message': f'Error comparing with reference: {e}'}
    
    def run_quick_validation(self):
        """Run the quick validation suite (Phase 1)"""
        print("🚀 Starting Quick Validation Suite (Phase 1)")
        self.start_time = time.time()
        
        quick_cases = [
            {
                'name': 'quick_channel',
                'param_file': str(self.base_dir / 'quick_validation/parameter_files/quick_channel_validation.prm'),
                'timeout': 60,
                'reference': None
            },
            {
                'name': 'circular_cylinder_re40',
                'param_file': str(self.base_dir / 'quick_validation/parameter_files/circular_cylinder_re40_quick.prm'),
                'timeout': 180,
                'reference': 'circular_cylinder/re40_dennis_chang_1970'
            }
        ]
        
        for case in quick_cases:
            result = self.run_validation_case(
                case['name'], 
                case['param_file'], 
                case['timeout']
            )
            
            # Extract metrics if successful
            if result['status'] == 'success':
                log_file = f"{case['name']}.log"
                metrics = self.extract_validation_metrics(case['name'], log_file)
                result['metrics'] = metrics
                
                # Compare with reference if available
                if case['reference']:
                    comparison = self.compare_with_reference(case['reference'], metrics)
                    result['validation'] = comparison
                    
            self.results[case['name']] = result
            
        return self.generate_report()
    
    def run_full_validation(self):
        """Run the complete validation suite"""
        print("🚀 Starting Full Validation Suite")
        self.start_time = time.time()
        
        # Run quick validation first
        quick_results = self.run_quick_validation()
        
        # Check if quick validation passed
        quick_passed = all(
            result['status'] == 'success' 
            for result in self.results.values()
        )
        
        if not quick_passed:
            print("❌ Quick validation failed. Stopping full validation.")
            return self.generate_report()
            
        print("\n🎯 Quick validation passed! Proceeding to full validation...")
        
        # Additional validation cases would go here
        # For now, we'll just return the quick validation results
        
        return self.generate_report()
    
    def generate_report(self):
        """Generate validation report"""
        total_time = time.time() - self.start_time if self.start_time else 0
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'total_runtime': total_time,
            'summary': {
                'total_cases': len(self.results),
                'successful': sum(1 for r in self.results.values() if r['status'] == 'success'),
                'failed': sum(1 for r in self.results.values() if r['status'] == 'failed'),
                'timeout': sum(1 for r in self.results.values() if r['status'] == 'timeout'),
                'crashed': sum(1 for r in self.results.values() if r['status'] == 'crashed')
            },
            'cases': self.results
        }
        
        # Save report to file
        report_file = self.base_dir / 'validation_report.json'
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
            
        # Print summary
        print(f"\n📊 Validation Summary:")
        print(f"   Total runtime: {total_time:.1f}s")
        print(f"   Cases run: {report['summary']['total_cases']}")
        print(f"   ✅ Successful: {report['summary']['successful']}")
        print(f"   ❌ Failed: {report['summary']['failed']}")
        print(f"   ⏰ Timeout: {report['summary']['timeout']}")
        print(f"   💥 Crashed: {report['summary']['crashed']}")
        
        print(f"\n📄 Full report saved to: {report_file}")
        
        return report

def main():
    """Main entry point"""
    if len(sys.argv) < 2:
        print("Usage: python run_validation_suite.py [quick|full]")
        sys.exit(1)
        
    mode = sys.argv[1].lower()
    base_dir = Path(__file__).parent.parent
    
    suite = ValidationSuite(base_dir)
    
    if mode == 'quick':
        suite.run_quick_validation()
    elif mode == 'full':
        suite.run_full_validation()
    else:
        print("Invalid mode. Use 'quick' or 'full'")
        sys.exit(1)

if __name__ == '__main__':
    main()
