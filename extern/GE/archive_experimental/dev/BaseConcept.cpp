// 
// 
// 
//   Its only for testing nothing else
// 
// 
// 
// =============================================================================



// =============================================================================
// includes
// =============================================================================
#include <iostream>
#include <vector>
#include "ObjectCounter.h"	 	// object counter 
#include "../transformation/TMatrix.h"  // transformation matrix 
#include "../transformation/ApplyingTransformation.h"
// =============================================================================


using namespace transformation ;




//  ============================================================================
// type of Computainal
//  ============================================================================

struct Compact {
	typedef double Precision ;
};


// =============================================================================
// Object base class
// =============================================================================
template<typename Derived > 
struct ObjectBase : public ObjectCounter <ObjectBase <Derived> >
{
      public:
		// =============================================================
		// Constructor
		// =============================================================
		ObjectBase() {
			// 	  m_id = m_total++ ;
			std::cout<< "ObjectBase -> Base Constructor  \n";
		}
		inline bool SetInterface(const transformation::TMatrix& mt)
		{
			static_cast<Derived* >(this) -> Set (mt);
		}
		
		inline bool AppyingMatrixInterface()
		{
			static_cast<Derived* >(this) -> AppyingMatrix() ;
		}


		// =============================================================
		// return the number of a live objects 
		// =============================================================
		inline transformation::TMatrix& GetTMatrix()
		{
			return m_mat  ;
		}
		
		inline const transformation::TMatrix& GetTMatrix()const {
			return m_mat  ;
		}
		
		inline bool AppyingMatrixExp()
		{
		  // TODO add assert (m_transformation is not empty matrix) ;
// 		  transformation::ApplyingTransform<ObjectBase>::Applying(this,m_mat);
		}
		
		
      protected :
		transformation::TMatrix    m_mat ;
};


// =============================================================================
// Object class
// =============================================================================
template<typename Conputaional>
struct ObjDerived : public ObjectBase <ObjDerived <Conputaional > > 
{ 
	typedef typename Conputaional::Precision    Precision ;
	// ===================================================================
	// constructor
	// ===================================================================
	ObjDerived () {
		std::cout<< "		-> DerivedClass Constructor  \n";
		m_iiid =  new Point3D <float > (10. , 10. , 500.)  ;
	}
	// ===================================================================
	inline bool Set(const transformation::TMatrix& mt)
	{
		(*this).GetTMatrix() = mt; // refers  to base member data
	}
	
	inline bool AppyingMatrix()
	{
	      // TODO
	      // assert (m_mat is setesd, or not empty) ;
// 	      ApplyingTransformation<ObjectBase>::Applying(this, m_mat) ; // main idea
 	      ApplyingTransformation<Point3D<float> >::Applying (*m_iiid , (*this).GetTMatrix() ) ;
	}

  private:
	Point3D <float >* m_iiid ;
} ; 
// =============================================================================



int main()
{
//	ObjDerived <Compact> *tmp =  new ObjDerived<Compact>  (10.,10.,10.) ;
//	std::cout<< "Originally point : " << *tmp << std::endl ;
//	std::cout<< "\n" ;
	
	
	
	TMatrix Matrix_2;
	Matrix_2.RotateX(60);
	std::cout<< "\n" ;
	std::cout<< "=======================================\n" ;
	std::cout<< "Element of the Matrix::RotateX(60) are: \n";
	std::cout<< "=======================================\n" ;    
	std::cout<< Matrix_2 << std::endl;
	
	
// 	Matrix_2 (*tmp ) ;
// 	std::cout<<"Point After applying Transformation is -> "<<*tmp<<std::endl;
// 
// 
// 
// 
// 	Matrix_2.RotateX(-60);
// 	ApplyingTransform< Point3D<float> >::Applying(*tmp,Matrix_2) ; //  no return
// 	std::cout<<"Static Memeber\n"<< *tmp << std::endl ;
// 

////////////////////////////////////////////////////////////////////////////////
//  Real testing stuff
////////////////////////////////////////////////////////////////////////////////

// this is what I want 
	ObjectBase<ObjDerived<Compact > >   *t =  new ObjDerived<Compact > () ;


	t -> SetInterface (Matrix_2) ;
	
	t -> AppyingMatrixInterface (); // TODO I have stop here --> tonight

	
	t -> AppyingMatrixExp() ;

	delete t ;
	return (0) ;
}
