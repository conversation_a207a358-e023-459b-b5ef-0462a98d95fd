
#include <iostream>



int main ()
{
	int i = 25;
//	float fp = reinterpret_cast<float> (i)  ;
	// static_cast<>
	//std::cout << fp << std::endl;
	std::cout<<"size of float " << sizeof(float ) << std::endl ;
	std::cout<<"size of uint "  << sizeof(unsigned int ) << std::endl ;
	std::cout<<"size of int " << sizeof(int ) << std::endl ;
	std::cout<<"size of short " << sizeof( short ) << std::endl ;
	std::cout<<"size of unsigned short " << sizeof( unsigned short ) << std::endl ;
	std::cout<<"size of char " << sizeof( char ) << std::endl ;

	return (0) ;
}

