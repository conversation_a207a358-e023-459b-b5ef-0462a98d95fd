//=================================================================================================
/*!
//  \file DynamicSmagorinskyModel.h
//  \brief Dynamic Smagorinsky model with Germano identity
*/
//=================================================================================================

#ifndef _WALBERLA_TURBULENCE_DYNAMIC_SMAGORINSKY_MODEL_H
#define _WALBERLA_TURBULENCE_DYNAMIC_SMAGORINSKY_MODEL_H

#include "../TurbulenceModel.h"
#include "../filters/Filter.h"
#include <memory>

namespace walberla {
namespace turbulence {

//=================================================================================================
//
//  DYNAMIC SMAGORINSKY MODEL
//
//=================================================================================================

/*!\brief Dynamic Smagorinsky model using Germano identity
//
// The Dynamic Smagorinsky model computes the Smagorinsky constant locally
// and dynamically using the Germano identity. This eliminates the need for
// manual tuning of the constant and provides better near-wall behavior.
//
// The model uses test filtering at scale 2*Delta to compute:
// L_ij = <u_i u_j> - <u_i><u_j>  (Leonard stress)
// M_ij = 2 * Delta^2 * (<|S|S_ij> - 4 * |<S>|<S_ij>)
//
// Then C_s^2 = <L_ij M_ij> / <M_ij M_ij>
*/
class DynamicSmagorinskyModel : public TurbulenceModel
{
public:
    //**Constructor & Destructor********************************************************************
    DynamicSmagorinskyModel();
    virtual ~DynamicSmagorinskyModel() = default;
    
    //**Implementation of pure virtual functions****************************************************
    
    virtual Real computeEffectiveTau(
        const Real& tau0,
        const Uint x, const Uint y, const Uint z,
        const Real& rho,
        const Vector3<Real>& vel,
        const PDFField& pdf,
        const VelField& velField,
        const SimData& sim) const override;
    
    virtual Real computeEddyViscosity(
        const Uint x, const Uint y, const Uint z,
        const Real& rho,
        const Vector3<Real>& vel,
        const PDFField& pdf,
        const VelField& velField,
        const SimData& sim) const override;
    
    virtual Real getModelConstant() const override { return computedCs_; }
    virtual void setModelConstant(const Real& constant) override { 
        // Dynamic model computes its own constant
        LOG_WARNING("Dynamic Smagorinsky model computes its own constant. Ignoring setModelConstant.");
    }
    
    virtual std::string getModelName() const override { return "Dynamic Smagorinsky"; }
    
    virtual bool requiresFiltering() const override { return true; }
    
    //**Dynamic model specific functions************************************************************
    
    /*!\brief Compute dynamic Smagorinsky constant using Germano identity
    */
    Real computeDynamicConstant(
        const Uint x, const Uint y, const Uint z,
        const VelField& velField,
        const Real& filterWidth) const;
    
    /*!\brief Set the test filter ratio (default is 2.0)
    */
    void setTestFilterRatio(const Real& ratio) { testFilterRatio_ = ratio; }
    
    /*!\brief Enable/disable clipping of negative C_s values
    */
    void setClipNegativeCs(bool clip) { clipNegativeCs_ = clip; }
    
    /*!\brief Set bounds for C_s clipping
    */
    void setCsBounds(const Real& minCs, const Real& maxCs) {
        minCs_ = minCs;
        maxCs_ = maxCs;
    }
    
    /*!\brief Enable/disable averaging of C_s over homogeneous directions
    */
    void setAverageCs(bool average) { averageCs_ = average; }
    
    /*!\brief Initialize from simulation data
    */
    virtual void initialize(const SimData& sim) override;

protected:
    //**Protected helper functions******************************************************************

    /*!\brief Compute Leonard stress tensor L_ij
    */
    Matrix3<Real> computeLeonardStress(
        const Uint x, const Uint y, const Uint z,
        const VelField& velField) const;

    /*!\brief Compute model tensor M_ij
    */
    Matrix3<Real> computeModelTensor(
        const Uint x, const Uint y, const Uint z,
        const VelField& velField,
        const Real& filterWidth) const;
    
    /*!\brief Apply test filter to a field value
    */
    // TODO: Implement generic field filtering
    // template<typename T>
    // T applyTestFilter(
    //     const Uint x, const Uint y, const Uint z,
    //     const Field<T>& field) const;
    
    /*!\brief Apply test filter to velocity field
    */
    Vector3<Real> filterVelocity(
        const Uint x, const Uint y, const Uint z,
        const VelField& velField) const;
    
    /*!\brief Apply test filter to strain rate tensor
    */
    Matrix3<Real> filterStrainRateTensor(
        const Uint x, const Uint y, const Uint z,
        const VelField& velField) const;
    
protected:
    //**Member variables****************************************************************************
    mutable Real computedCs_;      // Last computed Smagorinsky constant
    Real testFilterRatio_;         // Ratio of test filter to grid filter (typically 2)
    bool clipNegativeCs_;          // Whether to clip negative C_s values
    Real minCs_;                   // Minimum allowed C_s
    Real maxCs_;                   // Maximum allowed C_s
    bool averageCs_;               // Whether to average C_s over homogeneous directions
    std::unique_ptr<Filter> filter_; // Filter implementation
};

//=================================================================================================
//
//  LAGRANGIAN DYNAMIC MODEL
//
//=================================================================================================

/*!\brief Lagrangian dynamic model with time averaging
//
// This variant of the dynamic model performs Lagrangian averaging along
// fluid particle trajectories to stabilize the computed C_s values.
*/
class LagrangianDynamicModel : public DynamicSmagorinskyModel
{
public:
    LagrangianDynamicModel();
    
    virtual Real computeEffectiveTau(
        const Real& tau0,
        const Uint x, const Uint y, const Uint z,
        const Real& rho,
        const Vector3<Real>& vel,
        const PDFField& pdf,
        const VelField& velField,
        const SimData& sim) const override;
    
    virtual std::string getModelName() const override { return "Lagrangian Dynamic Smagorinsky"; }
    
    /*!\brief Set the relaxation time scale for Lagrangian averaging
    */
    void setRelaxationTime(const Real& T) { relaxationTime_ = T; }
    
    /*!\brief Update Lagrangian averaged quantities
    */
    void updateLagrangianAverages(
        const VelField& velField,
        const Real& dt);

private:
    Real relaxationTime_;          // Time scale for Lagrangian averaging
    mutable ScalarField<Real> ILM_; // Lagrangian averaged <L_ij M_ij>
    mutable ScalarField<Real> IMM_; // Lagrangian averaged <M_ij M_ij>
};

} // namespace turbulence
} // namespace walberla

#endif // _WALBERLA_TURBULENCE_DYNAMIC_SMAGORINSKY_MODEL_H