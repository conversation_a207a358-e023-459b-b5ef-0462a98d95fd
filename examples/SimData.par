// A simdata block must be given. Here, the properties of the simulation has to be defined using
// an appropriate combination of the following quantities. Note that suffix "_L" denotes the lattice
// quantity, while without this suffix the physical quantity is meant. For details on the parameters
// see documentation of SimData struct.
// 
// domainX, domainY, domainZ, 
// xlength, ylength, zlength,
// dx,
// timesteps, t, dt,
// omega, tau, Re, Lref, Uref,
// Gx, Gy, Gz,
// maxPermittedVel, nu, nu_L
// rho_L, dt_L, dx_L, c, cs,
// gx_L, gy_L, gz_L

simdata
{
  domainX         200;        // The size of the grid in x direction in lattice cells
  
  domainY         20;         // The size of the grid in y direction in lattice cells
  
  domainZ         20;         // The size of the grid in z direction in lattice cells
  
  dx              0.0005;     // Cell size delta x
  
  Uref            0.01;       // characteristic Velocity for computation of Re
  
  timesteps       10000;      // The number of timesteps
  
  Lref            0.001;      // The characteristic length scale for computation of Re
  
  nu              10.0e-6;    // Viscosity of fluid
  
  rho             1e3;        // Density of fluid
    
  maxPermittedVel 0.02        // maximum (physical) velocity that can occur during simulation.
                              // This parameter is used to set the relation between spatial
                              // and temporal resolution in a harmonic way.
                              // This velocity will be taken to represent the internal
                              // lattice velocity of 0.1.
  
  dump;                       // Specify "dump" if you want to log all computed values
}
