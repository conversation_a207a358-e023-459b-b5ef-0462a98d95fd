//=================================================================================================
/*!
//  \file Filter.h
//  \brief Interface and implementations for LES filtering operations
*/
//=================================================================================================

#ifndef _WALBERLA_TURBULENCE_FILTER_H
#define _WALBERLA_TURBULENCE_FILTER_H

#include "../../Definitions.h"
#include "../../Vector3.h"
#include "../../Matrix3.h"
#include "../../VelField.h"
#include "../../ScalarField.h"
#include "../../MatrixField.h"

namespace walberla {
namespace turbulence {

//=================================================================================================
//
//  FILTER BASE CLASS
//
//=================================================================================================

/*!\brief Abstract base class for LES filters
*/
class Filter
{
public:
    virtual ~Filter() = default;
    
    /*!\brief Filter a scalar value at given location
    */
    virtual Real filterScalar(
        const Uint x, const Uint y, const Uint z,
        const ScalarField<Real>& field) const = 0;
    
    /*!\brief Filter a vector value at given location
    */
    virtual Vector3<Real> filterVector(
        const Uint x, const Uint y, const Uint z,
        const VelField& field) const = 0;
    
    /*!\brief Filter a tensor value at given location
    */
    virtual Matrix3<Real> filterTensor(
        const Uint x, const Uint y, const Uint z,
        const MatrixField& field) const = 0;
    
    /*!\brief Get the filter width ratio
    */
    virtual Real getFilterRatio() const = 0;
};

//=================================================================================================
//
//  BOX FILTER (TOP-HAT FILTER)
//
//=================================================================================================

/*!\brief Box filter implementation
//
// Simple averaging filter over neighboring cells.
// For a filter ratio of 2, averages over 2x2x2 = 8 cells.
*/
class BoxFilter : public Filter
{
public:
    explicit BoxFilter(Real filterRatio = 2.0) : filterRatio_(filterRatio) {}
    
    virtual Real filterScalar(
        const Uint x, const Uint y, const Uint z,
        const ScalarField<Real>& field) const override;
    
    virtual Vector3<Real> filterVector(
        const Uint x, const Uint y, const Uint z,
        const VelField& field) const override;
    
    virtual Matrix3<Real> filterTensor(
        const Uint x, const Uint y, const Uint z,
        const MatrixField& field) const override;
    
    virtual Real getFilterRatio() const override { return filterRatio_; }

private:
    Real filterRatio_;
};

//=================================================================================================
//
//  GAUSSIAN FILTER
//
//=================================================================================================

/*!\brief Gaussian filter implementation
//
// Applies Gaussian weighting to neighboring cells.
// More accurate but computationally more expensive than box filter.
*/
class GaussianFilter : public Filter
{
public:
    explicit GaussianFilter(Real filterRatio = 2.0);
    
    virtual Real filterScalar(
        const Uint x, const Uint y, const Uint z,
        const ScalarField<Real>& field) const override;
    
    virtual Vector3<Real> filterVector(
        const Uint x, const Uint y, const Uint z,
        const VelField& field) const override;
    
    virtual Matrix3<Real> filterTensor(
        const Uint x, const Uint y, const Uint z,
        const MatrixField& field) const override;
    
    virtual Real getFilterRatio() const override { return filterRatio_; }

private:
    Real filterRatio_;
    Real sigma_;       // Standard deviation of Gaussian
    int stencilSize_;  // Size of filter stencil
    std::vector<Real> weights_; // Precomputed weights
    
    void computeWeights();
    Real gaussianWeight(Real distance) const;
};

//=================================================================================================
//
//  SIMPSON FILTER
//
//=================================================================================================

/*!\brief Simpson's rule based filter
//
// Uses Simpson's 1/3 rule for more accurate filtering.
// Particularly useful for structured grids.
*/
class SimpsonFilter : public Filter
{
public:
    explicit SimpsonFilter(Real filterRatio = 2.0) : filterRatio_(filterRatio) {}
    
    virtual Real filterScalar(
        const Uint x, const Uint y, const Uint z,
        const ScalarField<Real>& field) const override;
    
    virtual Vector3<Real> filterVector(
        const Uint x, const Uint y, const Uint z,
        const VelField& field) const override;
    
    virtual Matrix3<Real> filterTensor(
        const Uint x, const Uint y, const Uint z,
        const MatrixField& field) const override;
    
    virtual Real getFilterRatio() const override { return filterRatio_; }

private:
    Real filterRatio_;
};

} // namespace turbulence
} // namespace walberla

#endif // _WALBERLA_TURBULENCE_FILTER_H