#ifndef STRUCTUREDCONFIGREPOSITORY_HH
#define STRUCTUREDCONFIGREPOSITORY_HH

//StructuredConfigRepositor.h

// forward declaration for the  compiler 
template <class Config> 
class Vector ;

template <class Config> 
class Matrix ;

// configure Vector, and Matrix
struct SampleConfig
{
	struct ForVector {
		typedef int Element ;
		typedef unsigned short size_type ;
		enum {initial_value =  45};
		enum {size = 5};
	};
	
	struct ForMatrix {
		typedef double Element ;
		typedef unsigned short size_type ;
		enum {rows = 5, cols= 5} ;
	};

	typedef Vector<SampleConfig > vector ;
	typedef Matrix<SampleConfig > matrix ;
};

template<class configure>
class Vector {
	public:
		typedef configure Config ; // exporting config
		typedef typename Config::ForVector   MyConfig ;
		typedef typename MyConfig::Element   Element  ; 
		typedef typename MyConfig::size_type SizeType ; 

	public :
		Vector () : size_(MyConfig::size) {
			for (int i =0 ; i < size() ; i++ )	
				el [i]= MyConfig::initial_value ;
		}

		inline const SizeType&  size()const {return size_  ;}

		inline Element& operator [](const int& index)      { return  el[index] ; } // TODO debug, check border
		inline Element  operator [](const int& index)const { return  el[index] ; } // TODO debug, check border

	private :
		Element el[MyConfig::size] ;
		SizeType size_ ;
};

template<class configure>
class Matrix {
        public:
                typedef configure Config ;
                typedef typename Config::ForMatrix MyConfig ;
                typedef typename MyConfig::Element Element  ;

};



#endif

