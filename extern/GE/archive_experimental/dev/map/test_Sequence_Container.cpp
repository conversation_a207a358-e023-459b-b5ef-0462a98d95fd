
#include "Sequence_Container.h"
#include "../../gmath/GEMath.h"

using namespace ge;

struct Vertex
{
      Vertex (): a(0) , b(0) , c (0) {} 
  private :
      int a , b, c ;
}; // does no thing 

struct Face   {}; // does no thing 

struct Vector {}; // does no thing 

struct container_config_c1 // default configuration for mesh 
{
	//!Container_Type  ;
	typedef Vertex  vertex_type ;
	typedef Face    face_type   ;
	typedef Vector  normal_type ;
	typedef Sequence_Container<vertex_type , false ,std::vector > vertex_Container_Type ;
	typedef Sequence_Container<normal_type , false ,std::vector > normal_Container_Type ;		
	typedef Sequence_Container<face_type   , false ,std::vector > facet_Container_Type  ;	
};



template< class config >
struct Mesh
{
	// types
	typedef typename config::vertex_type 		vertex_type ;
	typedef typename config::face_type		face_type   ;
	typedef typename config::normal_type		normal_type ;
	// containers-types
	typedef typename config::vertex_Container_Type 	vertex_Container_Type ;// should have add, find and remove fcts
	typedef typename config::facet_Container_Type   facet_Container_Type  ;
	typedef typename config::normal_Container_Type  normal_Container_Type ;	
	
  public:
	inline int Add    (const vertex_type& vert) ; // add new vertex
	inline int Add    (const face_type  & face) ; // add new triangle
	inline int Add    (const normal_type& norm) ; // add new normals
	
	
	inline int  Find   (const vertex_type& vert) ;
	inline int  Find   (const face_type  & face) ;	
	inline int  Find   (const normal_type& norm) ;	

	inline int  Remove (const vertex_type& vert) ;
	inline int  Remove (const face_type  & face) ;	
	inline int  Remove (const normal_type& norm) ;	
	
  private :// representation 
	vertex_Container_Type m_vertices ;
	facet_Container_Type  m_faces    ;
	normal_Container_Type m_normals  ;
  public:
	inline int VSize ()const {return m_vertices.size() ;} // vertices.size() 
	inline int FSize ()const {return m_faces.size()    ;} //    faces.size()   
};


int main ()
{

    Sequence_Container< int , 
		        false ,
		        std::vector 
		       > M_Containre ;
    
    M_Containre.add (2) ;
    M_Containre.add (3) ;
    M_Containre.add (3) ;
    
		       
		       

//     Mesh<container_config_c1> m_mesh  ; 
    
    
    
    return (0) ;
}

