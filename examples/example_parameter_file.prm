// This parameter file creates a canal with 2x3x4 patches and overall 20x200x20 
// cells. The simulation will perform 50000 time steps.
// The domain is initalized with inflow at south, and outflow at north.
// A series of 25 paraview output files with the name PARAVIEWFILE is created, 
// which outputs an area of x=0 to x=10 and complete size in y and z direction. 
// Additionally, the average density of the source, the destination and the 
// density field is written to the output file.
// NOTE that credentials like Uref and Lref in the simdata block as well as the
// inflow velocity in the vel_in block are expressed in dependence on a
// self-defined parameter "inflow_L" in simdata block. When you use expressions
// for parameters, a requirement is that all variables that are to be expanded
// have to be enclosed in ticks (') and that the expresion must contain at least
// one mathematical operation (e.g. 1.0*'inflow_L')!
// 

paraview {
  filename PARAVIEWFILE; //Name of the output file. MUST be given
  xstart 0;              //Stating point in x-direction. If not given, then we start at 0 
  xend 10;               //Last point in x-direction. If not given, then we finish at xSize of the domain
  writedensity 1;        //If writedensity!=0 or writedensity is not given, then the density is written. 
  writevelocity 1;       //If writevelocity!=0 or writevelocity is not given, then the velocity is written. 
  writeflags 1;          //If writeflags!=0 or writeflags is not given, then the flags are written. 
  physical 1;            //If physical==1, then all values are written in physical units, else in lattic units.
}

simdata{
  domainX     20;        //The size of the grid in x direction in lattice cells
  domainY     200;       //The size of the grid in y direction in lattice cells
  domainZ     20;        //The size of the grid in z direction in lattice cells
  dx          0.00005;   //Cell size delta x

  Uref        'inflow_L'*('dx'/'dt'); 
                         //Characteristic Velocity for computation of Re, in
                         //our case (Canal), the inflow velocity (physical)
  Lref        1.0*'xlength';    
                         //The characteristic length scale for computation 
                         //of Re, in our case (Canal) the diameter of inflow
                         //area (physical)
  Re 5;                  //We over-determine our parameter set in order to
                         //check the validity of our inputs

  inflow_L  0.005;       //The inflow (lattice) velocity (also used below for
                         //vel_in inflow conditions

  timesteps 50000;       //The number of timesteps  
  nu        1.0e-6;      //Viscosity
  rho       1.0e+3;      //Density
  maxPermittedVel 0.1;   //Use with care! This parameter determines the
                         //lattice velocity which is scaled to be 0.1.
                         //If you are sure what you are doing you can use this
                         //parameter to enhance runtime because it has direct
                         //influence on the dx-dt relationship

  dump;                  //dump all computed parameters into log
}

simoutput{
  AVG_DENS
  {
   time 1; //Writes the average density from the density field every timestep
  }
  AVG_CALC_DENS
  {
   time 1; //Writes the calculated average density from src and dst field every timestep
  }
}


patches
{
  xNumPatches 2; //Number of patches in x direction. If not given, then xNumPatches=1
  yNumPatches 3; //Number of patches in y direction. If not given, then yNumPatches=1
  zNumPatches 4; //Number of patches in z direction. If not given, then zNumPatches=1
}

logging {
   logfile filename; // Use 'filename' for output log file 
   append 0;         // overwrite file each time solver is executed
}

init
{
  north{                    
    prs_nils{               
        x_L  1..'domainX';   
        z_L  1..'domainZ';
        prs_L 0.0;
    }
  }
  
  south{
    vel_in{
        x_L      1..'domainX';
        z_L      1..'domainZ';
        ux_L   0.0;
        uy_L   1.0*'inflow_L';
        uz_L   0.0; 
    }
  }
  
  east{
    noslip{
        y_L  0..'domainY'+1;
        z_L  1..'domainZ';
    }
  }
  
  west{
    noslip{
        y_L  0..'domainY'+1;
        z_L  1..'domainZ';
    }
  }
  
  top{
    noslip{
        x_L    0..'domainX'+1;
        y_L    0..'domainY'+1;
    }
  }
  
  bottom{
    noslip{
        x_L  0..'domainX'+1;
        y_L  0..'domainY'+1;
    }
  }  
}
