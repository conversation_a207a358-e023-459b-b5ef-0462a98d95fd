# ------------------------------------------------------------------------------
#                             SRC-FILES LIST
# ------------------------------------------------------------------------------

SET(io_SRC
#	MeshLoader.cpp
	#StlMeshLoader.cpp
	anything3.cpp 
    )

# ------------------------------------------------------------------------------
#                             HEADERS-FILES LIST
# ------------------------------------------------------------------------------

SET (io_HEADERS
#	MeshImporter.h
#	MeshIdentifier.h
#	MeshLoader.h
#	StlMeshLoader.h
	DirectoryLoader.h
	DirectoryOpener.h
)

SET(io_srch 
        ${io_HEADERS}
        ${io_SRC}
   )



# ------------------------------------------------------------------------------
#                               ADD_LIBRARY
# ------------------------------------------------------------------------------

# IF (ENABLE_STATIC_LIB)   # static
	ADD_LIBRARY(io  STATIC  ${io_srch})
# ELSE(ENABLE_STATIC_LIB) # dynamic 
# 	ADD_LIBRARY(io  SHARED  ${io_srch})
# ENDIF(ENABLE_STATIC_LIB)

