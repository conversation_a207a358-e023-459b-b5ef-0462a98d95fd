// LoggerBasei  class 
#ifndef LOGGERBASE_HH
#define LOGGERBASE_HH

#include <iostream>
#include <vector>
#include <fstream>
#include <string>
#include <cassert>
#include <ctime> // get time
#include <boost/lexical_cast.hpp>
	
#ifdef GE_USE_MPI
#include <mpi.h>
#endif
// =================================================
// help function to convert std::time_t to string
// =================================================
const std::string TimeToStr(const std::time_t& time)
{
  return std::ctime( &time);
}
// =================================================


//==============================================================================
/*!
 * \class LoggerBase
 * \brief Logger base class
 *
 * \author Cherif Mihoubi       
 * TODO you need destructor .-> file.close
 */
//==============================================================================
namespace ge
{
	struct LoggerBase
	{
	    //constructor
	    LoggerBase(const char* fName = "ge_loggin.txt")
	    {
	      #ifdef GE_USE_MPI
			const std::string tmp = fName ;
			assert (tmp.find (".txt") != -1  ) ; //  
			const std::string fWext = tmp.substr ( 0 , tmp.find (".txt")  )  ; 
			std::stringstream	fileName ;
			fileName.str("");
			fileName << fWext << "_"<< MPI::COMM_WORLD.Get_rank()  <<".txt";
	      		pFile.open (fileName.str().c_str() , std::ios::out /*| std::ios::app  | std::ofstream::ate */);
	      #else
	      	pFile.open (fName , std::ios::out /*| std::ios::app  | std::ofstream::ate */);
	      #endif 
	      LoggerBase::Log("//==============================================================================\n/*!") ;
	      LoggerBase::Log("// @brief Log File generated by \"GE\" ") ;
	      LoggerBase::Log("// @date create on: "+ TimeToStr(std::time (&m_Ctime) ));            
	      //LoggerBase::Log("// @author:Cherif Mihoubi") ;
	      #ifdef GE_USE_MPI
	        int rank = MPI::COMM_WORLD.Get_rank();
                int size = MPI::COMM_WORLD.Get_size();    
                int length;
                char* pName = new char[MPI::MAX_PROCESSOR_NAME+1];
                MPI::Get_processor_name(pName, length);
                std::string name(pName);
                delete [] pName;
                LoggerBase::Log("// @processor rank : "	+ boost::lexical_cast<std::string> (rank) );
                LoggerBase::Log("// @running on host "  + boost::lexical_cast<std::string> (name) );
	      #endif
	      // TODO add date, time
	      LoggerBase::Log("*/\n//==============================================================================\n") ;
	    }




	    // Destructor  ===========================================================
	    ~LoggerBase () { pFile.close () ;} 

	    
	    
	    // Log function
	    inline static void Log(const std::string& Msg);





	  protected: // I'm base class member-data
	    static std::fstream pFile ;
	    std::time_t  m_Ctime ;
	};
	
	
	// =====================================================================
	// initialize the static member data
	// =====================================================================
	std::fstream LoggerBase::pFile ; 
	
	
	
	
	// =====================================================================
	// 
	// =====================================================================
	inline void LoggerBase::Log (const std::string& Msg)
	{
	  if (  pFile.is_open() )
		pFile << Msg << "\n";
	  else
		std::cerr << "Error opening file\n";
	}
	

	
}
//==============================================================================

#endif 
