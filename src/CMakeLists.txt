#Note: Please always specify "Domain.cpp" and "Domain.h" as first files in the dependency list
#      because the <PERSON><PERSON><PERSON> will process these files in the specified order and doing a make -j4
#      then will finish earlier!
SET(BasicSrcFiles Domain.h Domain.cpp IsPointer.h Communication.cpp
      Communication.h VelField.cpp DensField.cpp SweepStruct.cpp PDFField.cpp
      MatrixField.cpp ApplicationDefinitions.h ApplicationDefinitions.cpp
      FieldInterFace.h FieldInterFace.cpp SweepStruct.h Sweeps.h PatchField.h
      PatchField.cpp PatchStruct.cpp PatchStruct.h ScalarField.h XmGraceData.h
      XmGraceWriter.h XmGraceWriter.cpp Definitions.cpp Definitions.h WalberlaMath.h MathTrait.h Matrix3.h Convert.h BouzidiObstacles.h DataLayout.h Lattice.h Timing.h Timing.cpp Logging.cpp Logging.h FileReader.cpp FileReader.h SimData.h Vector3.h VelField.h PDFField.h DensField.h FlagField.h MatrixField.h Stream.h Collide.h CalcMacro.h BC.h ParaviewReader.h ParaviewReader.cpp DataOutput.h DataOutput.cpp OutputContainer.h OutputContainer.cpp ParaviewWriter.h ParaviewWriter.cpp VelObstacles.h PrsObstacles.h TimeDepFactor.h SimOutputCase.h SimOutputCase.cpp SimOutput.h SimOutput.cpp SysFuncs.h IPatch.h IPatch.cpp CalcPatch.h CalcPatch.cpp PhysicalCheck.cpp PhysicalCheck.h MOObstacles.h MOObstacles.cpp MPIData.h VecField.h ParamContainer.cpp ParamContainer.h RestartDefinitions.h RestartDefinitions.cpp RestartData.h Restart.h Restart.cpp NonConstructable.h  NonCopyable.h PatchEntry.h Geometry.h Geometry.cpp Utility.h)


SET(ItaniumOptimization CStreamCollide.c CBC.c)


SET(FreeSurfaceFiles FSUtilities.h FSUtilities.cpp FSNormals.h FSNormals.cpp FSBubbles.cpp FSBubbles.h FSBubbleMgmt.h FSDefinitions.h FSStreamCollide.h FSStreamCollide.cpp FSInterfaceCells.h FSGeometry.h FSGeometry.cpp)

SET(MovingObstacleFiles MOObstacleMapping.h MOGravity.h)

SET(BrownianMotionFiles Gaussian.h Gaussian.cpp FastNorm3.c FastNorm3.h BrownianData.h BrownianData.cpp BMCollide.h VanObjects.h Forces.h)

SET(PovRayVisualisation PovRayVis.h PovRayVis.cpp FSIsoSurface.h FSIsoSurface.cpp)

SET(MixtureFiles MIFieldWrapper.h MixData.h MixData.cpp MIFunctions.h MIDefinitions.h IsPDFField.h SVD.h)

SET(ShanChenFiles SCDefinitions.h)

SET(TurbulenceFiles 
    turbulence/TurbulenceModel.h turbulence/TurbulenceModel.cpp
    turbulence/TurbulenceModelFactory.cpp
    turbulence/models/SmagorinskyModel.h turbulence/models/SmagorinskyModel.cpp
    turbulence/models/DynamicSmagorinskyModel.h turbulence/models/DynamicSmagorinskyModel.cpp
    turbulence/models/WALEModel.h turbulence/models/WALEModel.cpp
    turbulence/models/VremanModel.h turbulence/models/VremanModel.cpp
    turbulence/filters/Filter.h turbulence/filters/Filter.cpp
    turbulence/collision/MRTTurbulence.h turbulence/collision/MRTTurbulence.cpp
    turbulence/collision/TRTTurbulence.h
    turbulence/wallmodels/WallModel.h turbulence/wallmodels/WallModel.cpp
    turbulence/TurbulentSweeps.h
    turbulence/validation/ChannelFlow.h
    turbulence/validation/TurbulenceValidation.h
    Turbulence.h StressRateTensor.h Tensor.h RegularizedLBM.h)

SET(CurvedBoundaryFiles
    curvedboundary/CurvedBoundary.h curvedboundary/CurvedBoundary.cpp
    curvedboundary/mesh/TriangleMesh.h curvedboundary/mesh/TriangleMesh.cpp
    curvedboundary/readers/STLReader.h curvedboundary/readers/STLReader.cpp
    curvedboundary/boundary/InterpolatedBouzidi.h curvedboundary/boundary/InterpolatedBouzidi.cpp
    curvedboundary/boundary/AdaptiveWallFunction.h curvedboundary/boundary/AdaptiveWallFunction.cpp
    curvedboundary/boundary/TriangleBouzidi.h
    curvedboundary/force/GalileanInvariantForce.h curvedboundary/force/GalileanInvariantForce.cpp
    curvedboundary/forces/ForceOutput.h
    curvedboundary/forces/SurfaceForceIntegrator.h
    curvedboundary/integration/CurvedBoundarySweeps.h
    curvedboundary/integration/CurvedBoundaryTurbulentSweeps.h
    curvedboundary/validation/ValidationSuite.h curvedboundary/validation/ValidationSuite.cpp
    curvedboundary/voxelization/VoxelData.h
    curvedboundary/voxelization/RayTriangleIntersection.h
    curvedboundary/voxelization/Voxelizer.h curvedboundary/voxelization/Voxelizer.cpp
    curvedboundary/voxelization/FlagFieldMapper.h curvedboundary/voxelization/FlagFieldMapper.cpp
    curvedboundary/voxelization/MovingMeshHandler.h curvedboundary/voxelization/MovingMeshHandler.cpp
    curvedboundary/CurvedBoundary_voxelization.cpp)

IF(WALBERLA_USE_MPI)
   SET(CurrentSrcFiles ${BasicSrcFiles}  ${PovRayVisualisation} ${BrownianMotionFiles} ${FreeSurfaceFiles} ${MovingObstacleFiles} ${MixtureFiles} ${ShanChenFiles} ${TurbulenceFiles} ComPatch.h ComPatch.cpp  )
   ELSE(WALBERLA_USE_MPI)
   SET(CurrentSrcFiles ${BasicSrcFiles}  ${PovRayVisualisation} ${BrownianMotionFiles} ${FreeSurfaceFiles} ${MovingObstacleFiles} ${MixtureFiles} ${ShanChenFiles} ${TurbulenceFiles})
ENDIF(WALBERLA_USE_MPI)

IF(NOT WIN32)
   SET(CurrentSrcFiles ${CurrentSrcFiles} ${ItaniumOptimization})
ENDIF(NOT WIN32)

#ADD_LIBRARY(walberla ${CurrentSrcFiles} ${walberla_SOURCE_DIR}/src/SvnRev.h)
ADD_LIBRARY(walberla ${CurrentSrcFiles} )

add_executable(solver main.cpp)

TARGET_LINK_LIBRARIES(solver walberla)



# Ensure that SvnRev.h always contains the newest committed SVN revision
#add_executable(adaptSvnRev adaptSvnRev.cpp)


IF(NOT $CMAKE_VERSION)
  SET(CMAKE_VERSION ${CMAKE_MAJOR_VERSION}.${CMAKE_MINOR_VERSION}.${CMAKE_PATCH_VERSION})
ENDIF(NOT $CMAKE_VERSION)  

SET(CMAKE_VERSION_COMPARE )
INCLUDE(${walberla_SOURCE_DIR}/CMake/VersionCompare.cmake)
VERSION_COMPARE(CMAKE_VERSION_COMPARE ${CMAKE_MAJOR_VERSION} ${CMAKE_MINOR_VERSION} ${CMAKE_PATCH_VERSION} 2 6 2)


#MESSAGE(STATUS "Your major version: ${CMAKE_VERSION}")
#IF(${CMAKE_VERSION_COMPARE} LESS 0)
 #  MESSAGE(STATUS "Call to adaptSvnRev will work only on Unix based systems with CMAKE version prior to 2.6.2 (Your version: ${CMAKE_VERSION})!")
 #  add_custom_command(OUTPUT ${walberla_SOURCE_DIR}/src/SvnRev.h COMMAND ${walberla_BINARY_DIR}/bin/adaptSvnRev ${walberla_SOURCE_DIR}/src/ DEPENDS adaptSvnRev ${CurrentSrcFiles})
#ELSE(${CMAKE_VERSION_COMPARE} LESS 0)
#   add_custom_command(OUTPUT ${walberla_SOURCE_DIR}/src/SvnRev.h COMMAND adaptSvnRev ${walberla_SOURCE_DIR}/src/ DEPENDS adaptSvnRev ${CurrentSrcFiles})
#ENDIF(${CMAKE_VERSION_COMPARE} LESS 0)
#
#add_dependencies(walberla adaptSvnRev)


