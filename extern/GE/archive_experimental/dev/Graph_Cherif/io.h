#ifndef IO_HH
#define IO_HH
#include <iostream>
#include <fstream>
#include <typeinfo>
#include <stdexcept>


#include "Vec3f.h"
#include "vertex.h"



using namespace std;
//------------------------------------------------------------------------------
// I/O binary file read/Write functions
//------------------------------------------------------------------------------

/*!
  @fn void Read_from_binary_file (const char * fileName,Container &MESH)
  \brief reads a binary STL (stereolithography) file.
  \param char* fileName
  \param Container& MESH
  @note the implementation is based on the STL-format (protocol) from the :
 * http://www.ennex.com/~fabbers/StL.asp
  \image html StL-binary.png "binary format specification"

  \note: The binary STL-file-format is:

    80 byte string = header containing nothing in particular \n

    4 byte int = number of faces \n

    For each face (52 bytes):\n

      3 4-byte FLOATs = components of normal vector to face;\n
      3 4-byte FLOATs = coordinates of V0;\n
      3 4-byte FLOATs = coordinates of V1;\n
      3 4-byte FLOATs = coordinates of V2;\n
      2-byte int = no data-meaning \n

  \author Cherif.Mihoubi
*/
//------------------------------------------------------------------------------
inline void Read_from_binary_file(const char *fileName,Graph &MESH)
{

  std::cout << "Read_from_binary_file ... " << std::endl;
  fstream f( fileName ,ios::binary|ios::in);
  
  try {
	if(f.fail() ) 
 	   throw runtime_error("Could not open input file.");
	// Get the size
	f.seekg(0, ios_base::end);
  	Uint fileSize = (Uint) f.tellg();
	cout << "The fileSize  is= "<< fileSize << "\n";
	f.seekg(0, ios_base::beg);
	//Read the Comment + number of the triangles
	char Comment [81] ;
	f.read(Comment, 80 );   // no data significance

	Comment [80]=0;
	    cout << "STL::Comment : " << Comment << endl ;
	// Get the number of triangles:
        int nt = ReadInt32 (f);
	cout << "the  number of triangles : " << nt << endl;
	
	if (fileSize != (84  + nt* 50 ) ) // Stl protocol
	      throw runtime_error ("Error during Openig the File.") ;

        // Read first triangle
        const vertex N  = ReadVertex (f);
        const vertex V0 = ReadVertex (f);
        const vertex V1 = ReadVertex (f);
        const vertex V2 = ReadVertex (f);
	
        f.seekg(2, ios_base::cur);  // Skip the flat (2 bytes)
	
        MESH.AddTriangle(V0,V1,V2);
///         Read the rest of triangles 
        for (int i=1 ; i<nt ; ++i ){
            vertex Nf = ReadVertex(f);
            vertex V0_ = ReadVertex (f);
            vertex V1_ = ReadVertex (f);
            vertex V2_ = ReadVertex (f);
            MESH.AddTriangle(V0_,V1_,V2_);
//          std::cout<<"V0= "<< V0_<< "\nV1_= "<<V1_<< "\nV2_= "<<V2_<< endl;
    // Skip the flat 
          f.seekg(2, ios_base::cur);
 	}
	f.close();

    } catch (exception &e){

            cout << "Error: " << e.what() << endl;

    }

}







#endif
