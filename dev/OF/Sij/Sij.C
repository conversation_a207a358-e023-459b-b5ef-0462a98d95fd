/*---------------------------------------------------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     |
    \\  /    A nd           | www.openfoam.com
     \\/     M anipulation  |
-------------------------------------------------------------------------------
    Copyright (C) 2011 OpenFOAM Foundation
    Copyright (C) 2019-2020 OpenCFD Ltd.
-------------------------------------------------------------------------------
License
    This file is part of OpenFOAM.

    OpenFOAM is free software: you can redistribute it and/or modify it
    under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    OpenFOAM is distributed in the hope that it will be useful, but WITHOUT
    ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
    FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
    for more details.

    You should have received a copy of the GNU General Public License
    along with OpenFOAM.  If not, see <http://www.gnu.org/licenses/>.

Application
    Test-tensorFields1

\*---------------------------------------------------------------------------*/

#include "tensorField.H"
#include "Random.H"
#include "fvCFD.H"

using namespace Foam;


// this is wrong method to calculate the mag of tensor
inline double mag3 (const tensor& Q)
{
          return std::sqrt( (Q(0,0)*Q(0,0) +
                             Q(1,1)*Q(1,1) +
                             Q(2,2)*Q(2,2) +
                       2.0*( Q(0,1)*Q(1,0) +
                             Q(0,2)*Q(2,0) +
                             Q(1,2)*Q(2,1)) ) );
}

// this is exactly how openfoan implement  mag (Tensor )
inline double mag2 (const tensor& T)
{
     double  mag=0;
     for (int i=0; i < 3; ++i){
          for (int j=0; j < 3; ++j){
              //cout << " T (" << i << ", "<< j << ") = " <<  T(i,j) << "\n";
              mag += ( T(i,j) * T(i,j) );// factor 2 is missing? 
          }
          //      cout<< endl;
     }
     return std::sqrt(mag);
}


// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

int main(int argc, char *argv[])
{
    // vectorField
    {

	const tensor Sij (1, 2, 3, 4, 5, 6, 7, 8, 9) ;
        //FixedList<scalarField, 3> cmpts(scalarField(vf1.size()));

	//volSymmTensorField  gradU = fvc::grad( vf1) ;
	
        Info<< nl  << 
 		"Sij : " <<( Sij )  << nl << 
		"\tmag3 'wrong' (sij) "<< mag3( Sij )  << nl << 
		"\tmag2 (sij) "<< mag2( Sij )  << nl << 
		"\tfoam::mag(Sij )" << mag( Sij )  << nl << 
		"\tfoam::magSqr(Sij )" << magSqr( Sij )  << nl;
	
        //Info<< nl  << " D tensor => " <<  symm(  fvc::grad( vf1)  )   << nl;

    }


    Info<< nl << "End\n" << nl;

    return 0;
}


// ************************************************************************* //
