
#ifndef LOGGING_HH
#define LOGGING_HH

// =============================================================================
#include <iostream>
#include <fstream>
#include <iomanip>
#include <string.h>
#include <stdio.h>//strlen
#include <ctime> // get time
#include <boost/scoped_ptr.hpp>	//scoped_ptr
#include <boost/utility.hpp>	//noncopyable
#include <stdexcept>
#include <memory>
// its similar to Klaus implementation in waLberla framework
// =============================================================================



namespace ge
{
	struct Logging
	{
		~Logging();
		// =============================================================

		//**Utility functions
		/*! \name Utility functions */
		//@{
		static inline void     Configure(const std::string& file,const bool& append);
		static        Logging& Instance();
		//@}
		// =============================================================

		//**Logging functions 
		template< typename Type>
		inline void LogError(const Type& logmessage);
		
	private:
		// Constructor
		explicit Logging();
		// =============================================================

		// Forbidden operations
		Logging( const Logging& log );
		Logging& operator=( const Logging& log );
		// =============================================================

		// Member variables
		/*! \name Member variables */
		//@{
		std::ofstream log_;        //!< Output log file.
		static std::string file_;  //!< File name for the log file.
	};

	// =====================================================================
	/*!\fn void Logging::Configure(const std::string&file,const bool append)
	// \brief Configuration of the logging instance.
	//
	// \param file Name of the log file.
	// \param append If false, file will be overwritten.
	// \return void
	*/
	// =====================================================================
	inline void Logging::Configure(const std::string&file,const bool&append)
	{
		file_ = file;
		if (!append){
			std::ofstream tmp;
			tmp.open( file.c_str(), std::ofstream::out );
			tmp.close();
		}
	}
	// =====================================================================
	
	
	// =====================================================================
	// 
	// =====================================================================
	template< typename Type >
	inline void Logging::LogError( const Type& logmessage )
	{
		log_ << " ERROR:   " << logmessage;
	}
	// =====================================================================


}// end namespace ge


#endif