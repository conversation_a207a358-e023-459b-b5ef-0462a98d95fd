// TestContainerWithLog
#ifndef TESTCONTAINERWITHLOG_HH
#define TESTCONTAINERWITHLOG_HH

#include "LoggerBase.h"


//==============================================================================
/*!
 * \class TestContainerWithLog
 * \brief TestContainerWithLog class
 *
 * \author Cherif <PERSON>
 */
//==============================================================================

namespace ge
{
	  template<class Configure>
	  struct TestContainerWithLog : public LoggerBase  
	  {
		    typedef typename Configure::ElementType ElementType ;  

		    inline static void Add(const ElementType &e) ;
		    inline static ElementType Size ();
		    // to do , better use functor objevt to log file
		    inline std::ostream& TraceMe ()const {return std::clog << "Trace :" ;}
	    private:
		    static std::vector<ElementType> m_data_4;//  note static member data
		    //					     //       ______  
	  };


	  template<class Configure>
	  std::vector<typename TestContainerWithLog<Configure>::ElementType> 
	  TestContainerWithLog<Configure>::m_data_4 ;


	  template<class Configure>
	  inline void TestContainerWithLog<Configure>::Add(const typename TestContainerWithLog<Configure>::ElementType& e)
	  {
	    pFile <<"Adding the Element " << e << std::endl ;
	    m_data_4.push_back(e) ;
	  }


	  template<class Configure>
	  inline typename TestContainerWithLog<Configure>::ElementType TestContainerWithLog<Configure>::Size ()
	  {
	    //TODO add logging hier !!!
	    return m_data_4.size() ;
	  }
}	  
//==============================================================================

#endif 
