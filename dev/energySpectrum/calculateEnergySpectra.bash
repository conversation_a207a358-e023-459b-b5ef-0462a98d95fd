#!/bin/bash


if [ "$#" != "1" ]; then
	nu=3.5014e-4 
else
	nu=$1
fi


echo "nu " $nu 

rm -f log.energy energy.dat 

#echo "ICs spectrum" 
#energySpectrum dataAfterICs.dat ICs.dat >>  log.energy 
#echo " " >> log.energy  ;


for i in data_* ; 
    do echo $i ; 

    if [ ! -f  d2$i ]; then
	#energySpectrum  $i d2$i  $nu  >>  log.energy  ;
	spectrum  $i d2$i  $nu  >>  log.energy  ;
	#run  $i d2$i >>  log.energy  ;
        echo " " >> log.energy  ;
    fi
done 


sed -i "s/t=data_//g" energy.dat  
sed -i "s/.dat//g" energy.dat  
sed -i "s/TkE=//g" energy.dat
sed -i "s/Eps=//g" energy.dat
sed -i "s/Urms_k=//g" energy.dat
sed -i "s/Lambda_k=//g" energy.dat
sed -i "s/Re_k=//g" energy.dat

sed  -i '1i #  time \t\t          tke \t\t\t epsilon \t Urms_k  \t \t  Lambda_k  \t\t Re_k ' energy.dat



