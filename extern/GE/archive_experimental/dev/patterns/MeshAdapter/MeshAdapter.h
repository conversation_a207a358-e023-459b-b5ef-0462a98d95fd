#ifndef MESHADAPTER_HH
#define MESHADAPTER_HH


class Mesh
{
  
  public:
    
	Mesh(unsigned u): amount(u){}
	
	Mesh& assign(Mesh const & rhs)
	{
	  if (this != &rhs)
	  amount = rhs.amount;
	  return *this;
	}
	
  private:
    
	inline Mesh& operator = (Mesh const& rhs) { return *this; }
	unsigned amount; // member-data
};





template <class T>
class MeshAdapter
{ 
   public:
	//! constructor
	MeshAdapter(T& m):m_meshref(m),m_meshcpy(0) 
	{}
	
	//! copy-constructor	
	MeshAdapter(const MeshAdapter& ma)
	  : m_meshref(ma.m_meshref),m_meshcpy(0)
	  {
	    m_meshcpy =new T( ma.mesh() );
	  }
	
	//! assing operator
	inline MeshAdapter& operator=(MeshAdapter const& ma)
	{ 
	    mesh().assign( ma.mesh() );
	    return *this;
	}
	
	~MeshAdapter()	{ delete m_meshcpy;} // delete only pointer
	
	inline operator       T&() 	 { return mesh(); }
	inline operator const T&() const { return mesh(); }
	
 private:
   
	inline const T& mesh()const{return m_meshcpy ? *m_meshcpy :m_meshref; }
	inline T&       mesh() 	   {return m_meshcpy ? *m_meshcpy :m_meshref; }
	
 private:	
	T&  m_meshref;
	T*  m_meshcpy;
};


#endif 

