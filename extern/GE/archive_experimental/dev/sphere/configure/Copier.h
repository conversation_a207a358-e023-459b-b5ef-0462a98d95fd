#ifndef COPIER_HH
#define COPIER_HH

namespace ge
{

 // ======================================================================
 //! 				<Copier>
 // ======================================================================

      template <class ElementType>
      struct MonomorphicCopier
      {
	        static ElementType* copy(const ElementType& e){ 
	            return new ElementType(e);
	        }
      };

      template <class ElementType>
      struct PolymorphicCopier
      {
	        static ElementType* copy(const ElementType& e) { 
	            return e.clone();    // polymorphic copy using 
	        }                      // virtual member function clone()
      };

      template <class ElementType>
      struct EmptyCopier
      {
	        static ElementType* copy(ElementType& e) { // note: not const
	                return &e;       // no copy
	        }
      };

} // end namespace ge 
   
#endif
