# ==========================================================================================================
#
#!/bin/bash
# 
# File:   Cleaner.bash
# Author: mihoubi
#
# Created on January 4, 2010, 11:40 AM
# note: use bash-script language and running only on Linux
#
# ==========================================================================================================

echo Begin cleaning	

	make clean -j 2					# First make clean, delete all lib&executable files
	rm -f CMakeCache.txt				# remove CMakeCache.txt

	find . -name '*.*~'  | xargs rm   		#  remove all tmp files
	find . -name '*.out' | xargs rm 		#  remove all *.out files
	find . -name '*.bak' | xargs rm			#  remove all *.bak files
	find . -name 'Makefile' | xargs rm		#  remove all Makefile-files
	find . -name 'cmake_install.cmake' | xargs rm	#  remove all cmake_install.cmake files
	find . -name 'CMakeFiles' | xargs rm -r		#  remove all CMakeFiles directories

echo End cleaning

# =========================================================================================================

