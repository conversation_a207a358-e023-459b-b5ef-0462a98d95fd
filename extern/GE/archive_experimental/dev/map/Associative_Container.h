//==============================================================================
/**
	@file Associative_Container.h 
	@brief Associative container's (based on Red-Black tree )
	<AUTHOR> <PERSON>
*/	
//==============================================================================




//==============================================================================

#ifndef ASSOCIATIVE_CONTAINER_HH
#define ASSOCIATIVE_CONTAINER_HH

#include "map_selector.h"


//==============================================================================
/**
	@class Associative_Container
    	@brief associative container for fast insert(add), remove and find
    	@note  better use only for objects-memory-managenent and not for mesh
	       data-structure.
*/ 
//==============================================================================



namespace ge
{
	struct map_config
	{
	      // basic definition
	      typedef char Key ; 
	      typedef int  Value ;
	      enum {Selector = 1};
	      // advances one
	      typedef map_selector<Key,Value,Selector >::type  	Map; 
	      typedef map_selector<Key,Value,Selector >::Pair   Pair ;// to use like this :>    mymap.insert ( Pair('z',200) );  	      
	      typedef Map::iterator  			 Iterator ;
	      typedef std::pair<Iterator,bool>  	 Ret;
	};
	
	
//	template <  typename Key, typename Value,int selector>
	template <typename Configure, int selector >	
	class Associative_Container
	{
	  
	  public:
	    
	     typedef typename Configure::Key 	  Key ;
	     typedef typename Configure::Value	  Value;
	     typedef typename Configure::Selector Selector ;	     
	     typedef typename Configure::Map 	  Map ;    
	     typedef typename Configure::Iterator Iterator ;
	     typedef typename Configure::Pair	  Pair ; // TODO, its wrong
	     typedef typename Configure::Ret	  Ret  ;
	     
	  public:
	    
	     //!@fn add wrapper/adapter function
	     inline void add(const Key& key, Value const &value){
	       add (key, value, Int_type< Selector >::value );  
	     }

	  private:
	     //!@fn specialiations for add new elememt to the list
	     //!@{
	     inline void add(const Key& key,const Value& value, Int_type<1> ){
		    my_map.insert ( Pair (key,value) );
	     }
	     
	     // polymorphic on the given value
	     inline void add(const Key& key,const Value& value, Int_type<2> ){
		    my_map.insert ( Pair (key,value.clone()));
	     }
	     
	     // polymorphic on the given key
	     inline void add(const Key& key,const Value& value, Int_type<3> ){
		    my_map.insert ( Pair (key.clone(),value));
	     }
	    //!@}
	  private:
	    Map  my_map ;
	};
} // end namespace ge

#endif 
