#ifndef CONTAINER_TYPE_HH
#define CONTAINER_TYPE_HH

#include <iostream>
#include <assert.h>


#include <vector>
#include <list>
#include <deque>

#include <limits>



/*   ============================================================================================
// 
//   Save associatitive Container class (debugor checked std::vector) 
// 
// 
//   first) associatitive Container is a sequence-container that organizes a set of objects, all
//   the same type, into a linear arrangement. vector, list, deque fall into this category.
// 
//   second) save in the sense, if you switch on te debug mode, it will check if there is violation of the 
//   the access-indices  
//       
//   ============================================================================================*/

//!TODO stl::list neeed to be implement :)
// TODO std::allocator in your considiration 

namespace ge
{
          /// Forward declaration of Container type. its desinged to work only with vertex-types 
	  template<typename T , typename Type>
	  struct Container{
	    typedef typename T::vertex vertex ; // container forward defintion 
	  };

	  /// Container type,  specialization-> std::vector
	  template<typename T >
	  struct Container<T, std::vector <T> > : public std::vector<T >
	  {
// 		typedef T value_type;
		typedef typename Container::value_type	     value_type ;
		typedef typename Container::size_type 	     size_type ;
		typedef typename Container::iterator  	     iterator  ;
		typedef typename Container::const_iterator   const_iterator  ;		
		typedef typename Container::difference_type  difference_type ;
		typedef typename Container::reference	     reference 	;
		typedef typename Container::const_reference  const_reference ;		

		//!constructors
		Container() {} 
		Container(const size_type& size, const T&  value= T() )  : std::vector <T > (size,value) {}
		Container(iterator first,iterator last)  : std::vector <T > (first,last) {}
		
		
		//! Container by default inherited all characteristics from std::vector, but on can implement save container (debug mode)
		//! to check and all these stuff
		#ifdef DEBUG 
		reference operator [] ( difference_type index )
		{
		    assert(index >= 0 && index < static_cast<difference_type > (this->size() ) ) ; // end assert
		    return std::vector<T>::operator[](index) ;
		}
		
		const_reference operator [] (difference_type index) const
		{
		    assert(index >= 0 && index < static_cast<difference_type > (this->size() ) ) ; // end assert
		    return std::vector<T>::operator[](index) ;
		}
		#endif
	  };


	  template<typename T >
	  struct Container<T, std::deque<T> >:public std::deque<T > // specialization-> std::deque
	  {
		typedef T value_type;
		typedef typename Container::size_type 	     size_type ;
		typedef typename Container::iterator  	     iterator  ;
		typedef typename Container::const_iterator   const_iterator  ;		
		typedef typename Container::difference_type  difference_type ;
		typedef typename Container::reference	     reference 	;
		typedef typename Container::const_reference  const_reference ;
		
		//!constructor
		Container() {} 
		Container(const size_type& size, const T&  value= T() )  : std::deque <T > (size,value) {} 
		Container(iterator first,iterator last)  : std::deque <T > (first,last) {}
		
		#ifdef DEBUG  
		reference operator [] ( difference_type index )
		{
		    assert(index >= 0 && index<static_cast<difference_type > (this->size() ) ) ; // end asssert
		    return std::deque<T>::operator[](index) ;
		}
		
		const_reference operator [] (difference_type index) const
		{
		    assert(index >= 0 && index<static_cast<difference_type > (this->size() ) ) ; // end asssert
		    return std::deque<T>::operator[](index) ;
		}
		#endif
	  };
}
#endif

