% Additional sections to be inserted into the thesis chapter

% Insert after Section 3.2 (Class Hierarchy)
\subsection{TRT Integration}

The Two-Relaxation-Time (TRT) collision operator with turbulence support is implemented as:

\begin{lstlisting}[caption={TRT-LES integration},label={lst:trtles}]
template<typename LatticeModel>
class TRTTurbulence {
public:
    TRTTurbulence(Real omega_plus, Real omega_minus)
        : omega_plus_(omega_plus), omega_minus_(omega_minus) {}
    
    void collideWithTurbulence(
        PDFField& pdf,
        const Real& nu_t,
        const Uint x, const Uint y, const Uint z) {
        
        // Compute moments
        Real rho, u[3];
        computeMoments(pdf, x, y, z, rho, u);
        
        // Effective relaxation with turbulence
        Real nu_eff = nu_0_ + nu_t;
        Real omega_eff = 1.0 / (3.0 * nu_eff + 0.5);
        
        // Split into symmetric and anti-symmetric parts
        for (Uint i = 0; i < LatticeModel::Cellsize; ++i) {
            Uint opp = LatticeModel::opposite[i];
            
            Real f_sym = 0.5 * (pdf(x,y,z,i) + pdf(x,y,z,opp));
            Real f_anti = 0.5 * (pdf(x,y,z,i) - pdf(x,y,z,opp));
            
            Real feq_i = computeEquilibrium(i, rho, u);
            Real feq_opp = computeEquilibrium(opp, rho, u);
            
            Real feq_sym = 0.5 * (feq_i + feq_opp);
            Real feq_anti = 0.5 * (feq_i - feq_opp);
            
            // TRT collision with turbulence
            f_sym = f_sym - omega_eff * (f_sym - feq_sym);
            f_anti = f_anti - omega_minus_ * (f_anti - feq_anti);
            
            pdf(x,y,z,i) = f_sym + f_anti;
        }
    }
};
\end{lstlisting}

% Insert after Section 4.3 (Inside/Outside Classification)
\subsection{Robust Inside/Outside Testing}

For ambiguous cases near edges or vertices, multiple rays are cast to ensure robust classification:

\begin{lstlisting}[caption={Robust inside test with multiple rays},label={lst:robustinside}]
bool Voxelizer::robustInsideTest(
    const Vector3<Real>& point,
    const TriangleMesh& mesh) const {
    
    // Cast multiple rays in different directions
    std::vector<Vector3<Real>> directions = {
        Vector3<Real>(1.0, 0.0, 0.0),     // +X
        Vector3<Real>(0.0, 1.0, 0.0),     // +Y
        Vector3<Real>(0.0, 0.0, 1.0),     // +Z
        Vector3<Real>(0.707, 0.707, 0.0), // XY diagonal
        Vector3<Real>(0.707, 0.0, 0.707), // XZ diagonal
        Vector3<Real>(0.0, 0.707, 0.707)  // YZ diagonal
    };
    
    Uint insideCount = 0;
    
    // Use multiple rays for ambiguous cases
    for (Uint i = 0; i < options_.numRaysForAmbiguous; ++i) {
        RayTriangleIntersection::Ray ray(point, directions[i]);
        Uint intersections = countIntersections(ray, mesh);
        
        if ((intersections % 2) == 1) {
            insideCount++;
        }
    }
    
    // Majority vote for robustness
    return insideCount > options_.numRaysForAmbiguous / 2;
}
\end{lstlisting}

% Insert in Section 6 (Moving Boundaries) after 6.2
\subsection{Collision Detection Between Moving Meshes}

When multiple meshes move independently, collision detection prevents interpenetration:

\begin{algorithm}
\caption{Mesh-Mesh Collision Detection and Response}
\label{alg:meshcollision}
\begin{algorithmic}[1]
\REQUIRE Moving meshes $\{\mathcal{M}_i\}$, timestep $\Delta t$
\ENSURE Updated velocities and positions
\FOR{each pair $(i,j)$ where $i < j$}
    \STATE Check AABB overlap: $\mathcal{B}_i \cap \mathcal{B}_j$
    \IF{AABBs overlap}
        \STATE Perform detailed triangle-triangle tests
        \IF{collision detected}
            \STATE Compute contact point $\mathbf{c}$ and normal $\mathbf{n}$
            \STATE Calculate relative velocity: $\mathbf{v}_{rel} = \mathbf{v}_i - \mathbf{v}_j$
            \STATE Compute impulse: $J = -\frac{(1+e)\mathbf{v}_{rel} \cdot \mathbf{n}}{1/m_i + 1/m_j}$
            \STATE Update velocities:
            \STATE $\mathbf{v}_i \leftarrow \mathbf{v}_i + J\mathbf{n}/m_i$
            \STATE $\mathbf{v}_j \leftarrow \mathbf{v}_j - J\mathbf{n}/m_j$
            \STATE Separate meshes by penetration depth
        \ENDIF
    \ENDIF
\ENDFOR
\end{algorithmic}
\end{algorithm}

\begin{lstlisting}[caption={Collision detection implementation},label={lst:collision}]
void MeshCollisionHandler::resolveCollisions(
    std::vector<MovingMesh>& meshes,
    const std::vector<CollisionInfo>& collisions,
    Real dt) {
    
    for (const auto& collision : collisions) {
        MovingMesh& mesh1 = getMesh(collision.meshID1);
        MovingMesh& mesh2 = getMesh(collision.meshID2);
        
        // Simple elastic collision response
        Vector3<Real> relVel = mesh1.velocity - mesh2.velocity;
        Real velAlongNormal = relVel * collision.contactNormal;
        
        if (velAlongNormal > 0) continue; // Separating
        
        // Compute impulse
        Real restitution = 0.5;
        Real impulse = -(1.0 + restitution) * velAlongNormal / 
                       (1.0/mesh1.mass + 1.0/mesh2.mass);
        
        // Apply impulse
        mesh1.velocity += collision.contactNormal * (impulse/mesh1.mass);
        mesh2.velocity -= collision.contactNormal * (impulse/mesh2.mass);
        
        // Separate meshes
        Real separation = collision.penetrationDepth * 0.5;
        mesh1.position -= collision.contactNormal * separation;
        mesh2.position += collision.contactNormal * separation;
    }
}
\end{lstlisting}

% Add to Section 7.1 (MPI Domain Decomposition)
\subsection{Automatic MPI Parallelization}

The voxelization naturally parallelizes across MPI processes without explicit communication:

\begin{lstlisting}[caption={Local patch voxelization},label={lst:localpatch}]
void CurvedBoundary::voxelizeMeshes(Domain& domain, FlagField& flagField) {
    // Each MPI process handles only its local patches
    for (auto& patch : domain.getLocalPatches()) {
        auto block = patch.getBlock();
        auto flagFieldID = patch.getFlagFieldID();
        
        // Get local bounds for this patch
        AABB localBounds = patch.getBoundingBox();
        
        // Voxelize only the local region
        auto localVoxelData = voxelizer_->voxelizeMesh(
            mesh, domain, localBounds);
        
        // Map to local flag field
        flagMapper_->mapToFlagField(*localVoxelData, block, flagFieldID);
    }
    
    // No MPI communication needed - framework handles ghost layers
}
\end{lstlisting}

% Add to Section 9.2 (Wall Model Integration)
\subsection{y+ Adaptive Wall Functions}

The implementation automatically adapts between viscous sublayer and log-law regions:

\begin{lstlisting}[caption={Adaptive y+ treatment},label={lst:yplusadaptive}]
void AdaptiveWallFunction::applyWallFunction(
    Uint x, Uint y, Uint z,
    const Vector3<Real>& wallNormal,
    Real wallDistance,
    Field<Real,3>& velField) {
    
    // Get tangential velocity
    Vector3<Real> vel = velField.get(x, y, z);
    Real u_normal = vel * wallNormal;
    Vector3<Real> u_tangent = vel - wallNormal * u_normal;
    Real u_parallel = u_tangent.length();
    
    // Compute friction velocity iteratively
    Real u_tau = computeFrictionVelocity(u_parallel, wallDistance);
    Real yPlus = wallDistance * u_tau / nu_;
    
    // Determine wall region
    WallRegion region = determineWallRegion(yPlus);
    
    // Apply appropriate wall function
    Real u_parallel_corrected;
    switch(region) {
        case VISCOUS_SUBLAYER:
            u_parallel_corrected = yPlus * nu_ / wallDistance;
            break;
            
        case BUFFER_LAYER:
            // Spalding's law for smooth transition
            u_parallel_corrected = spaldingWallFunction(yPlus) * u_tau;
            break;
            
        case LOG_LAYER:
            u_parallel_corrected = (1.0/kappa_) * log(E_ * yPlus) * u_tau;
            break;
    }
    
    // Update velocity field
    if (u_parallel > 1e-10) {
        u_tangent *= (u_parallel_corrected / u_parallel);
        velField.get(x, y, z) = u_tangent; // Normal component = 0
    }
}
\end{lstlisting}

% Add a new section before Conclusions
\section{Complete Feature Summary}

\subsection{Voxelization Features}
\begin{itemize}
    \item \textbf{Ray-triangle intersection}: Möller-Trumbore algorithm for efficiency
    \item \textbf{Scan conversion}: Boundary cell detection with configurable thickness
    \item \textbf{Distance field}: Signed distance computation for all cells
    \item \textbf{Robust inside/outside test}: Multiple ray casting for ambiguous cases
    \item \textbf{Multi-threaded}: OpenMP parallelization with proper guards
\end{itemize}

\subsection{Flag Field Mapping}
\begin{itemize}
    \item \textbf{Cell classification}: OUTSIDE → FLUID, INSIDE → OBST|NOSLIP
    \item \textbf{Boundary cells}: BOUNDARY → BOUZIDI|FLUID
    \item \textbf{Near-boundary}: NEAR\_BOUNDARY → NEAR\_OBST|FLUID
    \item \textbf{Per-mesh configuration}: Different BC types for each mesh
    \item \textbf{Dynamic updates}: Efficient flag updates for moving meshes
\end{itemize}

\subsection{Moving Mesh Support}
\begin{itemize}
    \item \textbf{Position tracking}: Store previous and current positions
    \item \textbf{Flag clearing}: Remove flags from old positions
    \item \textbf{Flag restoration}: Restore fluid flags in cleared regions
    \item \textbf{Transition cells}: Special treatment for obstacle→fluid cells
    \item \textbf{Collision detection}: Prevent mesh interpenetration
    \item \textbf{Incremental updates}: Only revoxelize affected regions
\end{itemize}

\subsection{Turbulence Integration}
\begin{itemize}
    \item \textbf{LES models}: Smagorinsky, Dynamic, WALE, Vreman
    \item \textbf{MRT-LES}: Full integration with relaxation parameter modification
    \item \textbf{TRT-LES}: Two-relaxation-time with turbulence support
    \item \textbf{Wall models}: Van Driest, Spalding, Werner-Wengle
    \item \textbf{y+ adaptive}: Automatic adaptation between wall regions
\end{itemize}

\subsection{Parallelization}
\begin{itemize}
    \item \textbf{Automatic MPI}: Each process handles local patches independently
    \item \textbf{No explicit communication}: Framework handles ghost layers
    \item \textbf{Optional OpenMP}: Guarded with \texttt{\#ifdef USE\_OMP}
    \item \textbf{Thread-safe}: Works with and without OpenMP
    \item \textbf{Natural scalability}: Voxelization scales with domain decomposition
\end{itemize}

% Add to the Usage Example section
\subsection{Complete Workflow Example}

\begin{lstlisting}[caption={Complete curved boundary workflow},label={lst:workflow}]
// 1. Initialize modules
CurvedBoundary curvedBC;
curvedBC.initialize(config.getBlock("CurvedBoundary"), domain);

// 2. Load meshes from STL files
curvedBC.addMesh("ahmed_body", "ahmed_body_25deg.stl");
curvedBC.getMesh("ahmed_body").setVelocity(Vector3<Real>(0,0,0));

// 3. Voxelize all meshes
curvedBC.voxelizeMeshes(domain, flagField);

// 4. Setup turbulence model
auto turbModel = std::make_shared<DynamicSmagorinskyModel>(0.1, 2.0);
curvedBC.setTurbulenceModel(turbModel.get());

// 5. Time loop
for (Uint t = 0; t < maxTimesteps; ++t) {
    // Update moving meshes (if any)
    curvedBC.updateVoxelization(dt, flagField);
    
    // Apply boundary conditions
    curvedBC.applyBoundaryConditions(patchField);
    
    // LBM with turbulence
    mrtTurbulence->applyWithTurbulence(pdfField, velField, 
                                       densField, eddyViscosity);
    
    // Calculate forces
    curvedBC.calculateForces(pdfField, velField, densField);
    
    // Output
    if (t % 100 == 0) {
        auto forces = curvedBC.getForces("ahmed_body");
        LOG_INFO("Cd = " << forces.dragCoefficient);
    }
}
\end{lstlisting}