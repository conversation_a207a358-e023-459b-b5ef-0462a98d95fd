#include <iostream>
#include "Logging.h"
#include <boost/lexical_cast.hpp>
#include <boost/filesystem.hpp>   // includes all needed Boost.Filesystem declarations

using namespace ge;


const bool logAppend = true ;

 
int main(int argc,char* argv[] )
{
	int rank = 0 ;
	const std::string logFile = "logFile_"+
				boost::lexical_cast<std::string>(rank)+".txt";

	std::cerr << "Configure...." << std::endl;
	Logging::Configure(logFile,logAppend);
	std::cerr << "End Configure." << std::endl;

	Logging::Instance().LogError("waL<PERSON><PERSON><PERSON> called with parameter file: \n");
// 					+ boost::lexical_cast<std::string>(argv[1])
// 					+"\n");

	return (0) ;
}
