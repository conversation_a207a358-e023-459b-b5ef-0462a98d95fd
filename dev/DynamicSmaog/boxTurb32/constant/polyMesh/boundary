/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  5.x                                   |
|   \\  /    A nd           | Web:      www.OpenFOAM.org                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       polyBoundaryMesh;
    location    "constant/polyMesh";
    object      boundary;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

6
(
    patch0_half0
    {
        type            cyclic;
        inGroups        1(cyclic);
        nFaces          1024;
        startFace       95232;
        matchTolerance  0.0001;
        transform       unknown;
        neighbourPatch  patch0_half1;
    }
    patch0_half1
    {
        type            cyclic;
        inGroups        1(cyclic);
        nFaces          1024;
        startFace       96256;
        matchTolerance  0.0001;
        transform       unknown;
        neighbourPatch  patch0_half0;
    }
    patch1_half0
    {
        type            cyclic;
        inGroups        1(cyclic);
        nFaces          1024;
        startFace       97280;
        matchTolerance  0.0001;
        transform       unknown;
        neighbourPatch  patch1_half1;
    }
    patch1_half1
    {
        type            cyclic;
        inGroups        1(cyclic);
        nFaces          1024;
        startFace       98304;
        matchTolerance  0.0001;
        transform       unknown;
        neighbourPatch  patch1_half0;
    }
    patch2_half0
    {
        type            cyclic;
        inGroups        1(cyclic);
        nFaces          1024;
        startFace       99328;
        matchTolerance  0.0001;
        transform       unknown;
        neighbourPatch  patch2_half1;
    }
    patch2_half1
    {
        type            cyclic;
        inGroups        1(cyclic);
        nFaces          1024;
        startFace       100352;
        matchTolerance  0.0001;
        transform       unknown;
        neighbourPatch  patch2_half0;
    }
)

// ************************************************************************* //
