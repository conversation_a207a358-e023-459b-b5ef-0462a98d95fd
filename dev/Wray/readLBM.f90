    program rd
     implicit none
    integer nx,ny,nz,i,j,k
    parameter (nx=128, ny=128, nz=128)
     real, parameter :: dx =0.04908738521234  
    real*4 u(nx,ny,nz), v(nx,ny,nz), w(nx,ny,nz)
!  The following open is intended to open the file for flat (no record structure) type access.
! This open statement may have to be modified on some systems to achieve flat access;
!  for instance, on some systems, recl is given in bytes rather than words.

    open(1,file='output.dat',access='direct',recl=3*nx*4,form='unformatted')
    OPEN(10, file='velocityLBM.dat', status='new')

    
    do k=1,nz
         do j=1,ny
            read(1,rec= 2+(j-1)+(k-1)*ny) (u(i,j,k),v(i,j,k),w(i,j,k),i=1,nx)
         end do
    end do
    


    do k=1,nz
        do j=1,ny
            do i=1,nx
                write (10,*) "(" , &
                       &  u(i,j,k),"  ",v(i,j,k) ,"  ",w(i,j,k) , ")"
                !write (10,*) u(i,j,k),"  ",v(i,j,k) ,"  ",w(i,j,k)
            end do
        end do 
    end do 
        
    
!40  format (f8.4 , f8.4 ,  f8.4, f8.4 , f8.4, f8.4)    
    
    end program rd





!    program rd
!    
!     character*4 eol
!       integer*4 iol
!       logical*1 nin(4)
!       character  buf*20, infile*50, outfile*50
!       equivalence (iol,eol)
!       
!       integer nx,ny,nz,i,j,k
!      parameter (nx=128, ny=128, nz=128)
!      real*4 u(nx,ny,nz), v(nx,ny,nz), w(nx,ny,nz)
!       
!       
!       
!    
!     isgi=1        ! use for almost any other fortran
!     
!     
!     iol=10*(1+256**3)        ! define end-of-line character
! 
!       write(*,*) 'input file?'
!       read(*,'(a50)') infile
! !       write(*,*) 'output file?'
! !       read(*,'(a50)') outfile
! 
!       open(1,file=infile,access='direct', recl=20/isgi)
!       read(1,rec=1) buf
!       i1=index(buf,'=')          !check for = in HEADERLENGTH
!       i2=index(buf,eol(4:4))     !check for end of line after length
! 
! 
! !     write(*,*) 'i1=',i1,'  i2=',i2,'  buf=',buf(i1+1:i2-1)
!       read(buf(i1+1:i2-1),'(i5)') lhead
! 
!       irec0=lhead/isgi     ! note that this may fail if isgi=4
!       write(*,*) 'header length is =',lhead, irec0
! 
!       if(mod(lhead,isgi).ne.0) 
!         stop 'header not multiple of word length'
! 
! !        close(1)
! 
! !        open(1,file=infile,access='direct', status='old', recl=3*nx,form='unformatted')
! !       open(2,file=outfile,access='direct', recl=1)
! 
! 
!       do k=1,nz
!          do j=1,ny
!             read(1,rec= 2+(j-1)+(k-1)*ny) (u(i,j,k),v(i,j,k),w(i,j,k),i=1,nx)
!          end do
!        end do
!       
! 
!    end program rd
