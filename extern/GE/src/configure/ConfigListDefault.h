#ifndef CONFIGLISTDEFAULT_HH
#define CONFIGLISTDEFAULT_HH

// =============================================================================
/*!
//	This program is free software; you can redistribute it and/or
//	modify it under the terms of the GNU General Public License
//	as published by the Free Software Foundation; either version 2
//	of the License, or (at your option) any later version.
//
//	This program is distributed in the hope that it will be useful,
//	but WITHOUT ANY WARRANTY; without even the implied warranty of
//	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
//	GNU General Public License for more details.
//
//	You should have received a copy of the GNU General Public License
//	along with this program; if not, write to the Free Software
//	Foundation, Inc., 51 Franklin Street, Fifth Floor,
//	Boston, MA  02110-1301, USA.
//
//	
//	Copyright (C) 2010, Cherif <<EMAIL>>
*/
// =============================================================================


// =============================================================================
// inlcudes 
// =============================================================================
#include "../dev/List.h"
#include "../gmath/Point3D.h"
#include "../accel/Sector.h"
#include "Config.h"



// =============================================================================
// config List Sample : 
// =============================================================================
template<typename ElementType >
struct ConfigListDefault
{
    //1. config first the basic type
	 typedef ElementType		 		ObjectType ;
      	 typedef const ObjectType         		ObjectArgType  ;
	 
      	 typedef EmptyCopier<ObjectArgType>		Copier; // or MonomorphicCopier, or 
	 //typedef PolymorphicCopier<ObjectType>	Copier; //


	 //typedef ElementDestructor ; //
      	 typedef EmptyDestructor   <ObjectType>   	Destructor ;
	 typedef DynamicTypeChecker<ObjectType>   	TypeChecker;// or
	 // typedef EmptyTypeChecker              	TypeChecker;
	 
	 typedef boost::ptr_vector<ObjectType>  	ContainerType ;	 
      	 typedef std::less<ObjectType>   	  	Compare ;// I will use it for accosiative container	 

	 typedef List <ConfigListDefault> 	 	FinalListType ;
};

#endif 
