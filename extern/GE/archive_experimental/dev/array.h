// =============================================================================
/* 
 * File:   array.h
 * Author: mihoubi
 *
 * Created on January 12, 2010, 11:11 AM
 */
// =============================================================================
#ifndef _ARRAY_H
#define	_ARRAY_H


#include <iostream>
#include <iterator>


template<class Ty, std::size_t N>
class array
{
 public:
    // NESTED TYPES
    typedef std::size_t 			 size_type;
    typedef std::ptrdiff_t 			 difference_type;
    typedef Ty& 				 reference;
    typedef const Ty& 				 const_reference;
    typedef Ty *				 pointer;
    typedef const Ty *				 const_pointer;
    typedef Ty		 			 value_type;
    
    typedef typename array<Ty,N>::iterator 	  iterator;
    typedef typename array<Ty,N>::const_iterator  const_iterator;
    typedef std::reverse_iterator<iterator> 	  reverse_iterator;
    typedef std::reverse_iterator<const_iterator> const_reverse_iterator;

    //!@brief CONSTRUCTORS (exposition only)
    array();
    array(const array& right);

    //!@brief MODIFICATION
    void assign(const Ty& val);
    array& operator=(const array& right);    // exposition only
    void swap(array& right);

    //!@brief ITERATORS
	iterator 		begin();
	const_iterator 	begin() const;
	iterator 		end();
	const_iterator 	end() const;
	reverse_iterator 		rbegin();
	const_reverse_iterator 	rbegin() const;
	reverse_iterator 		rend();
	const_reverse_iterator 	rend() const;

    //!@brief SIZE QUERIES
    
	size_type 	size() const;
	size_type 	max_size() const;
	bool        empty() const;

    //!@brief ELEMENT ACCESS
	reference 	at  ( size_type off);
	const_reference at  ( size_type off) const;
	
	reference operator[]( size_type off);
	const_reference operator[](size_type off) const;

	reference front();
	const_reference front() const;
	reference back();
	const_reference back() const;

    Ty *data();
    const Ty *data() const;

//
//    public :
//      class iterator {
//
//
//      };
//
//    class const_iterator {
//
//
//      };



};

#endif	/* _ARRAY_H */

