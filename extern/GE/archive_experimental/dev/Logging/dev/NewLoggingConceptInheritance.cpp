// =============================================================================
#include <iostream>
#include <fstream>
#include <string>
#include <ctime> // get time
#include <boost/scoped_ptr.hpp>	//scoped_ptr
#include <boost/utility.hpp>	//noncopyable
#include <stdexcept>
#include <memory>
// its similar to Klaus implementation in waLberla framework
// =============================================================================





	// http://www.box2d.org/manual.html




	// =====================================================================
	// Logging class implementation
	// =====================================================================
	template<class Derived>
	struct Logging : private boost::noncopyable
	{
		~Logging();
		inline static Logging& Instance ();
		inline static void Configure(const std::string& f_name);
		inline static void Log (const std::string& msg) ;
	  public:
		static std::string m_fileName;
		std::time_t  m_Ctime;
		std::fstream m_file ;
	protected:
		Logging();
	};
	// =====================================================================



	template<class Derived> 
	std::string Logging<Derived>::m_fileName;// Initialize static data member

	// =====================================================================
	// constructor 
	// =====================================================================
	template<class Derived>
	Logging<Derived>::Logging()
	{
		std::string tmp = (Logging::Instance().m_fileName) ;
		std::cout<<"Base Constructor\n";
		if (tmp.empty() )
			throw std::runtime_error( "Logging not configured!" );
		m_file.open ( tmp.c_str() );
	}
	
	// =====================================================================
	// dest 
	// =====================================================================
	template<class Derived>
	Logging<Derived>::~Logging()
	{
		m_file.close();
		std::cout<<"Destructor is called\n";
	}
	
	// =====================================================================
	// to acces static member data 
	// =====================================================================
	template<class Derived>
	inline Logging<Derived>& Logging<Derived>::Instance ()/*const*/
	{
		//typedef std::auto_ptr<Logging<Derived> >Ptr;//typedef boost::scoped_ptr<Logging>Ptr;
		typedef std::auto_ptr<Derived >Ptr;//typedef boost::scoped_ptr<Logging>Ptr;
		static Ptr my_loger;
		if( !my_loger.get() )
		{
			my_loger = Ptr(new Derived() );
		}
		return *my_loger.get();
	}
	
	
	
	// =====================================================================
	// configure file nanme
	// =====================================================================
	template<class Derived>
	inline void Logging<Derived>::Configure(const std::string& f_name)
	{
		//m_fileName = f_name;
		std::cerr<< "	-----> Base::Configure() ---> the given file name : "  << f_name << std::endl ;
		Derived::Configure(f_name);
	}
	
	
	// =====================================================================
	// 
	// =====================================================================
	template<class Derived>
	inline void Logging<Derived>::Log (const std::string& msg)
	{
		//(Logging::Instance().m_file) << "Error : " << msg  << "\n";
		std::cerr<< "	-----> Base::Log() ---> error msg : "  << msg << std::endl ;
		Derived::Log(msg);
	}




	struct ErrorsLogging :public Logging<ErrorsLogging>
	{
		//ErrorsLogging () :Logging<ErrorsLogging>()
		ErrorsLogging ()
		{
			std::cout<< "	------> Derived Constructor\n" ;
		} 
		
		// =============================================================
		// configure error log file name 
		// =============================================================
		inline static void Configure(const std::string& f_name)
		{
			m_fileName = f_name;
			std::cerr<< "	-----> Derived::Configure() --->File name : "  << m_fileName << std::endl ;
		}	

	
		// =============================================================
		// error Log function 
		// =============================================================
		inline static void Log (const std::string& msg)
		{
			std::cerr<< "	-----> Derived::log() ---> File name : "  << m_fileName << std::endl ;
			std::cerr<< "	-----> Derived::log() ---> error msg : "  << msg        << std::endl ;
			(Logging<ErrorsLogging>::Instance().m_file) << "Error : " << msg  << "\n";
		}
	};



#define LOG_ERROR(msg) Logging<ErrorsLogging>::Log(msg)


// =============================================================================
//
// =============================================================================
int main ()
{
	std::cout << "First configure\n" ;
	Logging<ErrorsLogging>::Configure("errro_log.txt");
	
	std::cout << "-----> start log error\n" ;
	Logging<ErrorsLogging>::Log ("test ....\n") ;
	std::cout << "-----> end log error\n" ;
	
	//LOG_ERROR("I´m Cherif, hihi ") ;
	
	std::cout << "Free Memo\n" ;
	return (0) ;
}
