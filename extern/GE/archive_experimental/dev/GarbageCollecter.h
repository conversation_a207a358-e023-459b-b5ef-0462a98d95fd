#ifndef GARBAGECOLLECTER_HH
#define GARBAGECOLLECTER_HH

// =============================================================================
/*! @file GarbageCollecter
    @brief a single-threaded garbage collector. 
    @brief The entire garbage collector is shown here. As explained, the garbage 
    @brief collector works by creating a new pointer type that provides built-in 
    @brief support for garbage collection based on reference counting.
    @brief The garbage collector is single-threaded, which means that it is  
    @brief quite portableand does not rely upon (or make assumptions about) 
    @brief the execution environment.
    @brief This code should be stored in a file called gc.h.
    @brief There are two things to notice as you look through the code. First, 
    @brief most of the member functions are quite short and are defined inside 
    @brief their respective classes in the interest of efficiency. Recall that 
    @brief a function defined within its class is automatically in-lined, which 
    @brief eliminates the overhead of a function call. Only a few functions are 
    @brief long enough to require their definition to be outside their class.
    @brief Secondly, notice the comment near the top of the file. If you want to 
    @brief watch the action of the garbage collector, simply turn on the 
    @brief display option by defining the macro called DISPLAY. For normal use,

*/
// =============================================================================

 
#include <iostream> 
#include <list>
#include <iterator>
#include <typeinfo> 
#include <cstdlib>
 
using namespace std; 


// =============================================================================
//! @note To watch the action of the garbage collector, define DISPLAY.
// =============================================================================
// 
//  #define DISPLAY 



// =============================================================================
//!@class OutOfRangeExc  
// !@brief  Exception thrown when an attempt is made to  
// !@brief  use an Iter that exceeds the range of the underlying object.
//!@brief  If an Iter encounters an attempt to access memory outside the range  
//!@brief  of the allocated memory, an OutOfRangeExc exception is thrown. 
// =============================================================================
//!

class OutOfRangeExc
{
  //! Add functionality if needed by your application. 
}; 





// =============================================================================
//!@class Iter
//!@brief  An iterator-like class for cycling through arrays that are pointed
//!  to by GCPtrs. Iter pointers ** do not ** participate in or affect garbage 
//!  collection. Thus, an Iter pointing to some object does not prevent that  
//!  object from being recycled. 
// =============================================================================


template <class T>
class Iter
{
  T *ptr;   //! current pointer value 
  T *end;   //! points to element one past end 
  T *begin; //! points to start of allocated array 
  unsigned length; //! length of sequence 
public: 
 
  //!default constructor 
  Iter() {  
    ptr    = end = begin = NULL; 
    length = 0; 
  } 
 
  Iter(T *p, T *first, T *last) { 
    ptr    =  p; 
    end    = last; 
    begin  = first; 
    length = last - first; 
  } 
   
  //!@fn size()
  //!@brief Return length of sequence to which this Iter points. 
  inline unsigned size()const { return length; } 
 
  //!@fn operator*()
  //!@brief Return value pointed to by ptr. Do not allow out-of-bounds access.
  inline T& operator*() {  
    if( (ptr >= end) || (ptr < begin) ) 
      throw OutOfRangeExc(); 
    return *ptr; 
  } 
 
  //!@brief Return address contained in ptr. 
  //!@note Do not allow out-of-bounds access.
  inline T* operator->() {  
    if( (ptr >= end) || (ptr < begin) ) 
      throw OutOfRangeExc(); 
    return ptr; 
  }
 
  //! Prefix ++. 
  inline Iter operator++() { 
    ptr++; 
    return *this; 
  } 
 
  //! Prefix --. 
  inline Iter operator--() { 
    ptr--; 
    return *this; 
  } 
 
  //! Postfix ++. 
  inline Iter operator++(int  notused) { 
    T *tmp = ptr; 
 
    ptr++; 
    return Iter<T>(tmp, begin, end); 
  } 
 
  //! Postfix --. 
  inline Iter operator--(int  notused) { 
    T *tmp = ptr; 
 
    ptr--; 
    return Iter<T>(tmp, begin, end); 
  } 
 
  //! Return a reference to the object at the 
  //! specified index. Do not allow out-of-bounds access. 
  inline T& operator[](const int& i) { 
    if( (i < 0) || (i >= (end-begin)) ) 
      throw OutOfRangeExc(); 
    return ptr[i]; 
  } 
 
  //! Define the relational operators. 
  //!@{
	inline bool operator==(const Iter& op2) { 
	  return ptr == op2.ptr; 
	} 
      
	inline bool operator!=(const Iter& op2) { 
	  return ptr != op2.ptr; 
	} 
      
	inline bool operator<(const Iter& op2) { 
	  return ptr < op2.ptr; 
	} 
      
	inline bool operator<=(const Iter& op2) { 
	  return ptr <= op2.ptr; 
	} 
      
	inline bool operator>(const Iter& op2) { 
	  return ptr > op2.ptr; 
	} 
      
	inline bool operator>=(const Iter& op2) { 
	  return ptr >= op2.ptr; 
	} 
  //!@}
  
  //! Subtract an integer from an Iter. 
  inline Iter operator-(const int& n) { 
    ptr -= n; 
    return *this; 
  } 
 
  //! Add an integer to an Iter. 
  inline Iter operator + (const int& n) { 
    ptr += n; 
    return *this; 
  } 
 
  //! Return number of elements between two Iters. 
  inline int operator - (const Iter<T> &itr2) { 
    return ptr - itr2.ptr; 
  } 
   
}; 
 





// =============================================================================
//!@class GCInfo 
//!@brief This class defines an element that is stored 
//! *       in the garbage collection information list.  
//! 
// =============================================================================

template <class T>
class GCInfo
{ 
 public: 
  unsigned refcount;    //! current reference count

  T *memPtr;            //! pointer to allocated memory 
 
  /*! isArray is true if memPtr points to an allocated array. It is false otherwise. */ 
  bool isArray;         //! true if pointing to array 
 
  /*! If memPtr is pointing to an allocated array, then arraySize contains its size */ 
  unsigned arraySize;   //! size of array 
 
  //! Here, mPtr points to the allocated memory. If this is an array, then size 
  //! specifies the size of the array.
  GCInfo(T *mPtr, unsigned size=0) { 
    refcount = 1;
    memPtr = mPtr; 
    if(size != 0) 
      isArray = true; 
    else 
      isArray = false; 
 
    arraySize = size; 
  } 
}; 
 
//! Overloading operator== allows GCInfos to be compared. 
//! This is needed by the STL list class.  
template <class T> bool operator == (const GCInfo<T>& ob1,const GCInfo<T>& ob2)
{ 
  return (ob1.memPtr == ob2.memPtr);
} 
 
 
 
 
//==============================================================================
//!@class  GCPtr
//!@brief implements a pointer type that uses garbage collection to release 
//!*      unused memory. A GCPtr must only be used to point to memory that was 
//!*      dynamically allocated using new.
//!*	  When used to refer to an allocated array, specify the array size.
//!*
//==============================================================================
//! 
template <class T, int size=0>
class GCPtr
{ 
  //! gclist maintains the garbage collection list. 
  static std::list< GCInfo<T> > gclist; 

  //! addr points to the allocated memory to which 
  //! this GCPtr pointer currently points. 
  T *addr; 
 
  /*! isArray is true if this GCPtr points to an allocated array. It is false 
     otherwise.
  */ 
  bool isArray; //! true if pointing to array 
 
  //! If this GCPtr is pointing to an allocated 
  //! array, then arraySize contains its size. 
  unsigned arraySize; //! size of the array 

  static bool first; //! true when first GCPtr is created

  //! Return an iterator to pointer info in gclist. 
  typename std::list< GCInfo<T> >::iterator findPtrInfo(T *ptr);

public: 
 
  typedef Iter<T> GCiterator; //! Define an iterator type for GCPtr<T>. 
 
  //! Construct both initialized and uninitialized objects. 
  GCPtr(T *t=NULL)
  {
    // Register shutdown() as an exit function.
    if(first) atexit(shutdown);
    first = false;
    
    typedef typename std::list< GCInfo<T> >::iterator iter;

    iter p ;
    p = findPtrInfo(t);

    // If t is already in gclist, then increment its reference count.
    // Otherwise, add it to the list.
    if(p != gclist.end())
      p->refcount++; // increment ref count
    else {
      // Create and store this entry. 
      GCInfo<T> gcObj(t, size); 
      gclist.push_front(gcObj); 
    }

    addr = t; 
    arraySize = size; 
    if(size > 0) isArray = true; 
    else isArray = false; 
    #ifdef DISPLAY 
      std::cout << "Constructing GCPtr. "; 
      if(isArray)  
        std::cout << " Size is " << arraySize << std::endl; 
      else 
        std::cout << std::endl; 
    #endif 
  } 
 
  //! Copy constructor. 
  GCPtr(const GCPtr &ob) {
  
    typedef typename std::list< GCInfo<T> >::iterator iter ;
    iter p;

    p = findPtrInfo(ob.addr);
    p->refcount++; // increment ref count
 
    addr = ob.addr; 
    arraySize = ob.arraySize; 
    if(arraySize > 0) isArray = true; 
    else isArray = false; 
    #ifdef DISPLAY 
      std::cout << "Constructing copy."; 
      if(isArray)  
        std::cout << " Size is " << arraySize << std::endl; 
      else 
        std::cout << std::endl; 
    #endif 
  } 
 
 
  ~GCPtr();  //! Destructor for GCPTr.
 
  //!@fn collect()
  //!@brief Collect garbage. 
  //!@return true if at least one object was freed. 
  static bool collect();
    
  
  //! Overload assignment of pointer to GCPtr
  inline T *operator = (T *t);
 
  inline GCPtr& operator = (GCPtr &rv);//! Overload assignment of GCPtr to GCPtr. 
 

  //! Return a reference to the object pointed to by this GCPtr.
  inline T& operator * (){ 
    return *addr; 
  } 
 
  inline T* operator->() { return addr;} //! Return the address being pointed to.   
 
  //! Return a reference to the object at the index specified by i. 
  inline T &operator[](const int& i){ 
    return addr[i]; 
  } 
 
  
  inline operator T *() { return addr; } //! Conversion function to T *. 
 
  
  inline Iter<T> begin() { //! Return an Iter to the start of the allocated memory. 
    int Size; 
 
    if(isArray) Size = arraySize; 
    else Size = 1; 
 
    return Iter<T>(addr, addr, addr + Size); 
  }     
 
  //!@fn    end()
  //!@brief Return an Iter to one past the end of an allocated array. 
  inline Iter<T> end() { 
    int Size; 
 
    if(isArray) Size = arraySize; 
    else Size = 1; 
 
    return Iter<T>(addr + Size, addr, addr + Size); 
  }
  
  
  //!@fn  gclistSize()
  //!@brief Return the size of gclist for this type of GCPtr. 
  inline static int gclistSize() { return gclist.size(); }
  
  
  //!@fn   showlist()
  //!@brief A utility function that displays gclist.
  inline static void showlist();  
  
  
  //!@fn shutdown()  
  //!@brief Clear gclist when program exits
  inline static void shutdown();
}; 
 
// ============================================================================
//
//! Creates storage for the static variables
// 
// ============================================================================
template <class T, int size>
  std::list<GCInfo<T> > GCPtr<T, size>::gclist;

template <class T, int size>
  bool GCPtr<T, size>::first = true;


 //! Destructor for GCPtr.
template <class T, int size> GCPtr<T, size>::~GCPtr() 
{

  typedef typename std::list<GCInfo<T> >::iterator iter  ;
  iter p; 
 
  p = findPtrInfo(addr);
  if(p->refcount) p->refcount--; //! decrement ref count

  #ifdef DISPLAY 
    std::cout << "GCPtr going out of scope.\n";
  #endif 

  
  collect();// Collect garbage when a pointer goes out of scope. 
 
  // For real use, you might want to collect unused memory less frequently, 
  // such as after gclist has reached a certain size, after a certain number 
  // of GCPtrs have gone out of scope, or when memory is low. 
} 
// ============================================================================ 
 



// ============================================================================
//!@brief Collect garbage.  Returns true if at least one object was freed. 
// ============================================================================
template <class T, int size> bool GCPtr<T, size>::collect() 
{  
  bool memfreed = false; 
    
  #ifdef DISPLAY 
    std::cout << "Before garbage collection for "; 
    showlist(); 
  #endif 

  typedef typename std::list< GCInfo<T> >::iterator iter ;
  
  iter  p; 
  do { 
 
    // Scan gclist looking for unreferenced pointers. 
    for(p = gclist.begin(); p != gclist.end(); p++) { 
      
      if(p->refcount > 0) continue; // If in-use, skip.

      memfreed = true; 

      // Remove unused entry from gclist.
      gclist.remove(*p); 

      // Free memory unless the GCPtr is null.
      if(p->memPtr) {
        if(p->isArray) { 
          #ifdef DISPLAY 
            std::cout << "Deleting array of size " 
                 << p->arraySize << std::endl; 
          #endif 
          delete[] p->memPtr; // delete array 
        } 
        else { 
          #ifdef DISPLAY 
            std::cout << "Deleting: " 
                 << *(T *) p->memPtr << "\n"; 
          #endif 
          delete p->memPtr; // delete single element 
        } 
      }

      // Restart the search.
      break;
    }

  } while(p != gclist.end()); 
 
  #ifdef DISPLAY 
    std::cout << "After garbage collection for "; 
    showlist(); 
  #endif 
 
  return memfreed; 
} 
 
 
// ============================================================================ 
//!@brief Overload assignment of pointer to GCPtr.
// ============================================================================
template <class T, int size> 
inline T * GCPtr<T, size>::operator=(T *t)
{ 
  typedef typename std::list< GCInfo<T> >::iterator iter ;
  iter p; 
 
  // First, decrement the reference count
  // for the memory currently being pointed to.
  p = findPtrInfo(addr);
  p->refcount--;

  // Next, if the new address is already existent in the system, increment its
  // count. Otherwise, create a new entry for gclist.
  p = findPtrInfo(t);
  if(p != gclist.end())
    p->refcount++;
  else {
    // Create and store this entry. 
    GCInfo<T> gcObj(t, size); 
    gclist.push_front(gcObj); 
  }

  addr = t; // store the address. 
 
  return t; 
}


// ============================================================================
//!@brief Overload assignment of GCPtr to GCPtr. 
// ============================================================================
template <class T, int size>
inline GCPtr<T, size> & GCPtr<T, size>::operator=(GCPtr &rv)
{ 
      typedef typename std::list< GCInfo<T> >::iterator iter ;
	
      iter  p; 

      // First, decrement the reference count
      // for the memory currently being pointed to.
      p = findPtrInfo(addr);
      p->refcount--;

      // Next, increment the reference count of
      // the new address.
      p = findPtrInfo(rv.addr);
      p->refcount++; // increment ref count

      addr = rv.addr;// store the address. 
    
      return rv; 
} 


// ============================================================================
//!@brief A utility function that displays gclist.
// ============================================================================
template <class T, int size>
inline void GCPtr<T, size>::showlist()
{ 
 typedef typename std::list< GCInfo<T> >::iterator iter ;
  iter p; 
 
  std::cout << "gcstd::list<" << typeid(T).name() << ", " 
       << size << ">:\n"; 
  std::cout << "memPtr      refcount    value\n"; 
     
  if(gclist.begin() == gclist.end()) { 
    std::cout << "           -- Empty --\n\n"; 
    return; 
  } 
  
  for(p = gclist.begin(); p != gclist.end(); p++) { 
    std::cout <<  "[" << (void *)p->memPtr << "]"
         << "      " << p->refcount << "     "; 
    if(p->memPtr) cout << "   " << *p->memPtr; 
    else cout << "   ---"; 
    std::cout << std::endl;       
  } 
  std::cout << std::endl; 
}



// ============================================================================
//!@brief  Find a pointer in gclist.
// ============================================================================
template <class T, int size>
inline 
typename std::list<GCInfo<T> >::iterator GCPtr<T,size>::findPtrInfo(T *ptr) 
{

  typedef typename std::list< GCInfo<T> >::iterator iter ;
  iter  p; 
 
  // Find ptr in gclist. 
  for(p = gclist.begin(); p != gclist.end(); p++)
    if(p->memPtr == ptr)
      return p;

  return p; 
} 



// ============================================================================
//!@fn Clear gclist when program exits.
// ============================================================================
template <class T, int size> 
inline void GCPtr<T, size>::shutdown()
{
  if(gclistSize() == 0) return; // list is empty

  typedef typename std::list< GCInfo<T> >::iterator iter ;
    iter  p;

  for(p = gclist.begin(); p != gclist.end(); p++) {
    // Set all reference counts to zero
    p->refcount = 0;
  }
 
  #ifdef DISPLAY    
    std::cout << "Before collecting for shutdown() for "
         << typeid(T).name() << "\n";  
  #endif

  collect();
 
  #ifdef DISPLAY    
    std::cout << "After collecting for shutdown() for "
         << typeid(T).name() << "\n";  
  #endif
}


// ============================================================================
/*!@brief An Overview of the Garbage Collector Classes

The garbage collector uses four classes: GCPtr, GCInfo, Iter, and OutOfRangeExc. 
Before examining the code in detail, it will be helpful to understand the role 
each class plays. 
GCPtr At the core of the garbage collector is the class GCPtr, which implements 
a garbage-collection pointer. GCPtr maintains a list that associates a reference 
count with each piece of memory allocated for use by a GCPtr. In general, here 
is how it works. Each time a GCPtr is pointed at a piece of memory, the 
reference count for that memory is incremented. If the GCPtr was previously 
pointing at a different piece of memory prior to the assignment, the reference 
count for that memory is decremented. Thus, adding a pointer to a piece of 
memory increases the memory’s reference count. Removing a pointer decreases the 
memory’s reference count. Each time a GCPtr goes out of scope, the reference 
count associated with the memory to which it currently points is decremented.

When a reference count drops to zero, that piece of memory can be released.
GCPtr is a template class that overloads the * and –> pointer operators and the
array indexing operator [ ]. Thus, GCPtr creates a new pointer type and 
integrates it into the C++ programming environment. This allows a GCPtr to be 
used in much the same way that you use a normal C++ pointer. However, for 
reasons that will be made clear later in this chapter, GCPtr does not overload 
the ++, – –, or the other arithmetic operations defined for pointers. Thus, 
except through assignment, you cannot change the address to which a GCPtr object
points. This may seems like a significant restriction, but it isn’t because the 
Iter class provides these operations.
For the sake of illustration, the garbage collector runs whenever a GCPtr object
goes out of scope. At that time, the garbage collection list is scanned and all
memory with a reference count of zero is released, even if it was not originally
associated with the GCPtr that went out of scope. Your program can also 
explicitly request garbage collection if you need to recycle memory earlier.
GCInfo
As explained, GCPtr maintains a list that links reference counts with allocated 
memory. Each entry in this list is encapsulated in an object of type GCInfo. 
GCInfo stores the reference count in its refcount field and a pointer to the 
memory in its memPtr field. Thus, a GCInfo object binds a reference count to a 
piece of allocated memory.
GCInfo defines two other fields: isArray and arraySize. If memPtr points to an 
allocated array, then its isArray member will be true and the length of the 
array will be stored in its arraySize field.
The Iter Class
As explained, a GCPtr object allows you to access the memory to which it points
by using the normal pointer operators * and –>, but it does not support pointer 
arithmetic. To handle situations in which you need to perform pointer arithmetic, 
you will use an object of type Iter. Iter is a template class similar in 
function to an STL iterator, and it defines all pointer operations, including 
pointer arithmetic. The main use of Iter is to enable you to cycle through the 
elements of a dynamically allocated array. It also provides bounds checking.

You obtain an Iter from GCPtr by calling either begin( ) or end( ), which work 
much like their equivalents in the STL.
It is important to understand that although Iter and the STL iterator type are 
similar, they are not the same, and you cannot use one in place of the other.

*/
// ============================================================================




#endif 


