// Simple Turbulence Model Test
// This parameter file tests our turbulence models with a basic channel flow

simdata {
    domainX     64;       // Domain size in x direction
    domainY     32;       // Domain size in y direction
    domainZ     32;       // Domain size in z direction
    dx          0.001;    // Cell size delta x

    Uref        0.1;      // Reference velocity
    Lref        0.032;    // Reference length
    Re          100;      // Reynolds number

    timesteps   500;      // Number of timesteps
    nu          1.0e-5;   // Viscosity
    rho         1.0;      // Density

    dump;  // Output all computed parameters
}

// Turbulence modeling - test different models
turbulence {
    model               smagorinsky;      // Start with basic Smagorinsky
    smagorinskyConstant 0.18;            // Standard Smagorinsky constant
    filterWidth         2.0;             // Test filter width = 2*dx
    strainRateMethod    nonequilibrium;  // Non-equilibrium method
}

init {
    // Inlet (south)
    south {
        vel_in {
            x_L     1..'domainX';
            z_L     1..'domainZ';
            ux_L    0.0;
            uy_L    0.05;  // Inlet velocity
            uz_L    0.0;
        }
    }

    // Outlet (north)
    north {
        prs_nils {
            x_L     1..'domainX';
            z_L     1..'domainZ';
            prs_L   0.0;
        }
    }

    // Side walls - no slip
    east {
        noslip {
            y_L     0..'domainY'+1;
            z_L     1..'domainZ';
        }
    }

    west {
        noslip {
            y_L     0..'domainY'+1;
            z_L     1..'domainZ';
        }
    }

    // Top and bottom - no slip
    top {
        noslip {
            x_L     0..'domainX'+1;
            y_L     0..'domainY'+1;
        }
    }

    bottom {
        noslip {
            x_L     0..'domainX'+1;
            y_L     0..'domainY'+1;
        }
    }
}

// Output configuration
paraview {
    filename    turbulence_test;
    xstart      0;
    xend        64;
    ystart      0;
    yend        32;
    zstart      16;
    zend        16;       // Single plane
    writevelocity  1;
    writepressure  1;
    writeeddyvisc  1;     // Output eddy viscosity to verify turbulence model
    interval       100;   // Output every 100 steps
    physical       1;     // Use physical units
}

// Monitor turbulence quantities
simoutput {
    TURBULENT_KINETIC_ENERGY {
        time 50;  // Output TKE every 50 steps
    }
    EDDY_VISCOSITY_STATS {
        time 50;  // Output eddy viscosity statistics
    }
}

// Logging configuration
logging {
    logfile simple_turbulence_test.log;
    append  0;
}
