#ifndef FUNCTOR_HH
#define FUNCTOR_HH


      // =======================================================================
      // abstract base class
      // =======================================================================
      class Functor
      {
      public:

	  // two possible functions to call member function. virtual cause derived
	  // classes will use a pointer to an object and a pointer to a member function
	  // to make the function call
	  virtual void operator()(const char* string)=0;  // call using operator
	  virtual void Call(const char* string)=0;        // call using function
      };


   
   
   // ==========================================================================   
   // derived template class
   // ==========================================================================
   template <class TClass> class TSpecificFunctor : public Functor
   {
   private:
      void (TClass::*fpt)(const char*);   // pointer to member function
      TClass* pt2Object;                  // pointer to object

   public:
      // constructor - takes pointer to an object and pointer to a member and stores
      // them in two private variables
      TSpecificFunctor( TClass* _pt2Object, // pointer to object
			void(TClass::*_fpt)(const char*)//pointer to member function
		      )
         { 
	   pt2Object = _pt2Object; 
	   fpt=_fpt;
	 };
	 
	
      // =======================================================================
      // override operator "()"
      virtual void operator()(const char* string)
       {
	 (*pt2Object.*fpt)(string) ;              // execute member function
       };

       
       
      // =======================================================================
      // override function "Call"
      virtual void Call(const char* string)
        {
	  (*pt2Object.*fpt)(string);		 // execute member function
	};              
   };
   // ==========================================================================
   
   
   
#endif
   