#ifndef LIST_HH
#define LIST_HH
#include <iostream>

//----------------------------------------------------------------------
// PtrList
//----------------------------------------------------------------------

template <class Config_>
class PtrList
{
    public: // 
      
	typedef Config_ Config;// <- export Config>
	
	// retrieve the needed types from the repository> 
	typedef typename Config::ElementType          ElementType;
	typedef typename Config::ElementArgumentType  ElementArgumentType;
	typedef typename Config::Copier               Copier;
	typedef typename Config::Destroyer            Destroyer;
	typedef typename Config::TypeChecker          TypeChecker;
	typedef typename Config::FinalListType        FinalListType;
  
    public:
    
	PtrList (ElementArgumentType& h, FinalListType *t = 0)
	  : head_(0), tail_(t) { set_head(h); }


	~PtrList() { Destroyer::destroy(head_); }

	void set_head(ElementArgumentType& h)
	{
	  TypeChecker   ::check(h);
	  head_ = Copier::copy(h);
	}

	inline ElementType&   head()       { return *head_; }
	inline FinalListType* tail() const { return tail_ ; }
	
	inline void set_tail(FinalListType *t) { tail_ = t; }
 
private:// data members
	ElementType*   head_;   // top, first element 
	FinalListType* tail_;   // note: not PtrList* but FinalListType*
	
};// end PtrList

//----------------------------------------------------------------------
// LengthList
//----------------------------------------------------------------------
template <class BasicList>
class LengthList : public BasicList
{
public:
	// export config
	typedef typename BasicList::Config Config;
private:
	// retrieve the needed types from the repository
	typedef typename Config::ElementType          ElementType;
	typedef typename Config::ElementArgumentType  ElementArgumentType;
	typedef typename Config::LengthType           LengthType;
	typedef typename Config::FinalListType        FinalListType;

	LengthType length_;

	LengthType compute_length() const // modified by cherif
	{
	  return  static_cast<BasicList> (this)->tail() ? static_cast<BasicList> (this)->tail()->length()+1 : 1;
	}

public:
	// constructor
	LengthList (ElementArgumentType& h, FinalListType *t = 0) : BasicList(h, t){
	  length_ = compute_length();
	}

	void set_tail(FinalListType *t) {
	  BasicList::set_tail(t);
	  length_ = compute_length();
	}

	LengthType length() const { return length_; }
};

//----------------------------------------------------------------------
// TracedList
//----------------------------------------------------------------------
template <class OptCounterList>
class TracedList : public OptCounterList
{
  public:
	typedef typename OptCounterList::Config Config;
	typedef typename Config::ElementType          ElementType;
	typedef typename Config::ElementArgumentType  ElementArgumentType;
	typedef typename Config::FinalListType        FinalListType;

	inline std::ostream& trace_stream() { return std::clog << "trace: "; }
  public:
  
	TracedList (ElementArgumentType& h, FinalListType *t = 0) // constructor
	  : OptCounterList(h, t) 
	{
	    trace_stream() << "construct(" << h << ")" << std::endl;
	}
	
	inline void set_head(ElementArgumentType& h) {
	    trace_stream() << "set_head(" << h << ")" << std::endl;
	    OptCounterList::set_head(h);
	}

	inline ElementType& head() { 
	    trace_stream() << "head() = " << OptCounterList::head() << std::endl;
	    return OptCounterList::head();
	}
};

// ==========================================================================
// utilities printing 
// ==========================================================================

template <class List>
inline void print_list(List* l) 
{
	std::cout << "list[ ";
	for ( ; l; l = l->tail() )
	    std::cout << l->head() << " ";
	std::cout << "]\n";
}

template <class List>
inline void push_front(typename List::ElementArgumentType& e, List*& l)
{
	l = new List(e, l);
}

#endif