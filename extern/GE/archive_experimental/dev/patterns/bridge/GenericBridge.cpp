#include <iostream>


template <class impl>
class BaseAbsract
{
  public:// typdefs 
	typedef impl  ConcretImplType ;
	typedef typename ConcretImplType::vertices_iterator vertices_iterator ;
// 	typedef typename ConcretImplType::faces_iterator faces_iterator ;	
  public:
      // constructor
      BaseAbsract () {mesh_imp = new ConcretImplType ;}
     
     // Destructor 
      ~BaseAbsract() {if (mesh_imp) delete mesh_imp ; }
    
     inline void operation (){
	  mesh_imp -> operation_impl() ;
     }

    private:
        ConcretImplType* mesh_imp ; // TODO use boost::shared_ptr + no need Dest  
};

class ConcretBaseImplementation
{
  public:
	typedef int vertices_iterator ; 
  public :
    void operation_impl (){std::cout << "hello World\n" ;}
};



template <class StlMeshImpl>
class StlMesh :public BaseAbsract <StlMeshImpl>{

};



class ConcretSTLImplementation: public ConcretBaseImplementation
{
  public:
	typedef int vertices_iterator ; 
  public :
	void operation_impl (){std::cout << "NONONO World\n" ;}
};



int main()
{
    BaseAbsract<ConcretBaseImplementation> *tmp = new BaseAbsract<ConcretBaseImplementation> ();

    
    tmp-> operation () ;
    
    StlMesh<ConcretSTLImplementation> *st_tmp = new StlMesh<ConcretSTLImplementation> ;
    st_tmp-> operation () ;
    
//     delete tmp ;
    return (0) ;
}