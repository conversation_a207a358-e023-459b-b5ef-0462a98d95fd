
#ifndef OBJECTS_HH
#define OBJECTS_HH

class ObjectA // object of type A suppot only default inplementation
{ 
 public:
     void implementation (){
         cout<<"ObjectA::implementation() "<<endl;
     }
};
 
 
class ObjectB // but Object B can support optimiszation implemattion , example sse 
{
public:
     void optimised_implementation (){
         cout<<"ObjectB::optimised_implementation() "<<endl;
     }
     // TODO  static member function is better 
};




#endif 

