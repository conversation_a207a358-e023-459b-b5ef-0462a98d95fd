

#include <iostream>
#include <algorithm>
#include <functional>
#include <vector>
#include <cmath>
#include <cassert>
#include <iterator>


/*Arg1 	The function object's first argument type 	 
Arg2 	The function object's second argument type 	 
Result 	The function object's result type */	  


template <typename Arg1, typename Arg2, class Result>
struct exponentiate : public std::binary_function<Arg1, Arg2, Result>
{
  inline Result operator()(const Arg1& x,const Arg2& y)
  { 
	  std::cout << "(x,y)  = ("<< x << " , " << y << ")\n";
	  return pow(x, y);
  }
  
  inline const Result operator()(const Arg1& x,const Arg2& y)const
  { 
    return pow(x, y);
  }
};


int main ()
{
      const int N = 10;
      std::vector<double> V1(N);
      std::vector<double> V2(N);
      std::vector<double> V3(N);
      
      std::ostream_iterator< double > output( std::cout, " ");
      
      
      std::fill(V1.begin(), V1.end(), 2);
      std::fill(V2.begin(), V2.end(), 4);
      
      // print V1
      std::copy(V1.begin(), V1.end(), output  );
      std::cout << std::endl;
     
      // print V2   
      std::copy(V2.begin(), V2.end(), output   );
      std::cout << std::endl;        
      
      // assert equality size
      assert(V2.size() >= V1.size() && V3.size() >= V1.size());
      
      
      
      std::transform(V1.begin(), V1.end(),
		     V2.begin(), 
		     V3.begin(), 
		     exponentiate<double,double,double>() );
      
      std::copy(V3.begin(), V3.end(), output );
      std::cout << std::endl; 
  
      
      return (0) ;
}

