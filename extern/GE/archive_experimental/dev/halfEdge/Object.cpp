#ifndef MESH_HH
#define MESH_HH




template<class Configure>
struct Mesh
{
	typedef Configure configure;
	typedef typename configure::Triangle Triangle ;
	typedef typename configure::Vertex  Vertex ;
	
	explicit Mesh (const int& n_faces, const int&n_vert ) ;
	
	// set function 
	
	// get function

	// iterator 

	// print

           private:
	int m_NVertices, m_NFaces ;// number of vertices, and number of faces
	Triangle	 m_triangles [Nfaces] ;
	Vertex 	 m_vertives  [Nverts] ;
};

// default constructor
Mesh::Mesh (const int& n_faces, const int&n_vert )
{
	m_NVertices =  n_vert;
	m_NFaces	  =  n_faces ;
}








#endif