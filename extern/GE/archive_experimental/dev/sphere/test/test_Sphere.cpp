
#include "../Sphere.h"



using namespace ge ;

int main ()
{
	/* test 3D point */ 
	// Point3D point = {1.0 , 2.0  , 3.05 } ;
	// std::cout << "Point  -> " << point << std::endl ;
	
	TriShapeBase<Sphere> * bb   = new Sphere  ;
	TriShapeBase<Sphere> * bb_1 = new Sphere  ;
	TriShapeBase<Sphere> * bb_2 = new Sphere  ;
	
		bb   -> TraceInterface() ;  
		bb_1 -> TraceInterface() ;  	
		bb_2 -> TraceInterface() ;	

	bb -> CreateInterface (55); 
// 	bb -> ExportToStlInterface ("shpere.stl") ;
		
	delete bb ;
    delete bb_1;
    delete bb_2;
   
	return (0) ;
}

