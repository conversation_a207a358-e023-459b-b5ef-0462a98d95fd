// ===========================================
Item 7 – Const Pointers and Pointers to Const
// ===========================================
	T *pt = new T; // ptr to T
	const T *pct = pt; // ptr to const T
	T *const cpt = pt; // const ptr to T

	const T *p1; // ptr to const T, traditioal
	T const *p2; // same

	* Common Error *
	T const *p3; // ptr to const
	T *const p4 = pt; // const ptr to non-const

	const T *const cpct1 = pt; // everything is const
	T const *const cpct2 = cpct1; // same

	It is often simpler to use a reference in preference to a const pointer.

	const T &rct = *pt; // rather than const T *const
	T &rt = *pt; // rather than T *const


// ===========================================
Item 8 – Pointers to Pointers
// ===========================================
	  - “multilevel” pointer

	  int *pi; // a ptr
	  int **ppi; // two level multilevel ptr
	  int ***pppi; // a three level multilevel ptr

	  Shape *picture[MAX];
	  Shape **pic1 = picture;

	  // moves a pointer to refer to the next occurence of a character in a string:
	  // p – the value we want to change
	  void scanTo( const char **p, char c )
	  {
		while( **p && **p != c )
		  ++*p;
	  }

	  char s[] = “Hello, World!”;
	  const char *cp = s;
	  scanTo(&cp, ‘,’); // move cp to first comma

	  - safer process
	  void scanTo( const char *&p, char c )
	  {
		while( *p && *p != c )
		    ++p;
	  }

	  char s[] = “Hello, World!”;
	  const char *cp = s;
	  scanTo(cp, ‘,’); // move cp to first comma

	  * Common Misconception *
	  - conversion that apply pointers also apply to pointers to pointers

	  Circle *c = new Circle;
	  Shape *s = c; // fine

	  Circle **cc = &c;
	  Shape **ss = cc; // error

	  char *s1 = 0;
	  const char *s2 = s1; // OK
	  char *a[MAX]; // aka char **
	  const char **ps = a; // er