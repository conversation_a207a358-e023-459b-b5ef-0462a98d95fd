#ifndef DESTROYER_HH
#define DESTROYER_HH

namespace ge
{
    //! ==========================================================================
    //! 				Destroyer
    //! ==========================================================================
	template <class ElementType>
	struct ElementDestroyer 
	{
	  static void destroy(ElementType* e) { delete e; }
	};

	template <class ElementType>
	struct EmptyDestroyer {
	  static void destroy(ElementType* e) {}  // do nothing
	};

    //! ==========================================================================
}

#endif
