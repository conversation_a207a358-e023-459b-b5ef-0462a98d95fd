#ifndef RIGIDBODY_HH
#define RIGIDBODY_HH


// =============================================================================
/*
//	This program is free software; you can redistribute it and/or
//	modify it under the terms of the GNU General Public License
//	as published by the Free Software Foundation; either version 2
//	of the License, or (at your option) any later version.
//
//	This program is distributed in the hope that it will be useful,
//	but WITHOUT ANY WARRANTY; without even the implied warranty of
//	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
//	GNU General Public License for more details.
//
//	You should have received a copy of the GNU General Public License
//	along with this program; if not, write to the Free Software
//	Foundation, Inc., 51 Franklin Street, Fifth Floor,
//	Boston, MA  02110-1301, USA.
//
//	
//	Copyright (C) 2011, Cherif <<EMAIL>>
*/
// =============================================================================


#define RIGIDBODY_DEBUG

// =============================================================================
// includes
// =============================================================================
#include <cmath>
#include <cassert>
#include "RigidBodyConfig.h" // contains all necessaries configuration struct
//#include "RigidBodyConfigPrecise.h"
#include "../mesh/MeshClipper.h"
#include "../mesh/MeshNormalsChecker.h"
#include "../misc/Timing.h"
#include "ApplyingForces.h"
// =============================================================================
//#ifdef PRESSURE_COEFFICIENT
#include "../tool/Vtk_Exp.h"
#include "../../../../src/VtkExporter.h"
#include <fstream>
//#endif
// #define APP_FORCE




// TODO add obj-aabb of type ints, needed for:
//  - flood-fill algorithm.
//  - Swap the object-border after the flood-fill algorithm.
//  - collision-detection.

namespace ge
{
template<typename object_conf = RigidBodyConfig>
struct RigidBody : boost::noncopyable
{
    // =============================================================
    // Typdedefs
    // =============================================================
    typedef typename object_conf::mesh_type  	mesh_type ;
    typedef typename mesh_type::polygons_list	polygons_list;
    typedef typename mesh_type::triangle_type	triangle_type;
    typedef typename mesh_type::iterator		iterator    ;
    typedef typename mesh_type::const_iterator	const_iterator;
    typedef typename triangle_type::real		real ;
    typedef typename triangle_type::point_type	point_type ;
    typedef typename triangle_type::vector_type	vector_type;
    typedef typename triangle_type::edge_type	edge_type;
    // =============================================================
    typedef typename object_conf::ray_type		ray_type ;// TODO not Elegant
    //		typedef RigidBodyConfig::plane_type		plane_type;
    typedef typename object_conf::aabb_type		aabb_type;

    typedef typename object_conf::object_loader	object_loader;
    typedef typename object_conf::stl_exporter 	exporter;// stl_exporter
    // povray
    typedef typename object_conf::pov_inc_exporter	pov_inc_exporter;//converter to  pov-ray mesh format
    typedef typename object_conf::povray_exporter	povray_exporter;

    typedef typename object_conf::anagly_povray_exporter	anagly_povray_exporter;
    //

#ifdef NO_STAND_ALONE_GE		
    // =============================================================
    // for dynamic simulation
    // =============================================================
    typedef typename object_conf::cell_type		cell_type;
    typedef typename object_conf::brd_cell_type		brd_cell_type;
    typedef typename object_conf::cell_list_type	cell_list_type;
    typedef typename object_conf::brd_cell_list_type	brd_cell_list_type;
    typedef typename object_conf::patch_brd_cell_list_type patch_brd_cell_list_type;
#endif

    typedef typename object_conf::sector_type		sector_type;
    typedef typename object_conf::sector_list_type		sector_list_type;
    //typedef MeshClipper<mesh_type>				object_clipper ;// some utilities to cut the mesh
    //typedef MeshMerger<mesh_type>				stl_merger ;
    // =============================================================
    // export typedef
    typedef typename object_conf::objects_list_type		objects_list_type;
    typedef RigidBody<object_conf>				rigid_body_type;
    // =============================================================

    // =============================================================
    //! @brief Default TM constructor.
    // =============================================================
#ifdef GE_USE_MPI
    explicit RigidBody(const char* f_name,
                       const aabb_type& proc_aabb,//current running processor aabb.
                       const real& density_=1.0,
                       const bool& dyn = true );
#else
    explicit RigidBody(const char* f_name,
                       const real& density_=1.0,
                       const bool& dyn=true);
#endif
    // =============================================================
    //! @brief TM destructor.
    // =============================================================
    ~ RigidBody();


    // =============================================================
    //! @brief main functions
    // =============================================================
    // dynamic simulation
    // @{
    inline const bool& IsDynamic()const ;
    inline bool& IsDynamic() ;
    inline void Update (const real& dt) ;
    inline void UpdateTrianglesMeshPositionOnlyTranslation(const point_type& move_to, const bool&recomputeNormal = true);
    inline void UpdateTrianglesMeshPositionOnlyTranslation(const vector_type& move_to,const bool&recomputeNormal = true);
#ifdef NO_STAND_ALONE_GE
    inline void UpdateTheMappedInsideCells(const point_type& move_to);
    inline void UpdateTheMappedInsideCells(const vector_type& move_to);
    inline void UpdateTheMappedBorderCells(const point_type& move_to);// basically for BZ
    inline void UpdateTheMappedBorderCells(const vector_type& move_to);// basically for BZ
#endif		
    //inline void UpdateTrianglesMeshPositionOnlyRotation();
    inline void UpdateTrianglesMesh();
    inline void Export(const char* f_name,const bool& Binary= true)const;
    inline void AddSector(sector_type* sec);
    inline void ReComputeObjectInertiaMomentsAndCOM();
#ifdef NO_STAND_ALONE_GE
    // =============================================================
    // For debug, Get/Set list of border cells
    // Global coordinate
    //@{
    inline const brd_cell_list_type& GetBrdCellsList()const{return m_brd_cells;}
    inline const cell_list_type&	GetInsideCellsList()const{return m_InsideCells;}

    inline brd_cell_list_type&	GetBrdCellsList(){return m_brd_cells;}
    inline cell_list_type&		GetInsideCellsList(){return m_InsideCells;}

    inline patch_brd_cell_list_type& GetLinkedBrdCellsList(){return m_linked_brd_cells;}
    inline const patch_brd_cell_list_type& GetLinkedBrdCellsList()const{return m_linked_brd_cells;}
    //@}
#endif
    // =============================================================
    // global coordinate too, used to compute the forces on the object surface.
    //@{
    inline void addForceAtPos(const real& fx   ,const real& Fy, const real&Fz,
                              const real& pos_x,const real& pos_y, const real& pos_z);
    //@}
    // =============================================================

    // @}
    // =============================================================


    // =============================================================
    //! @brief  help function
    // =============================================================
    // @{
    inline void Info () const ;
    inline void ForcesAndTorquesInfo()const;
    inline void StateVariablesInfo()const;
    inline void GetFileGeomInfo()const;
    inline void ComputationalInfo()const;
private:
    inline void FlagsInfo()const;
    // @}
    // =============================================================

public:
    // =============================================================
    // Optional, preprocessing the triangles Mesh
    // =============================================================
    // @{
    inline void rotate_TM (const real& Rx,const real& Ry,const real& Rz);
    inline void Pitch     (const real& Rx_deg) ;// rotate aroun x-axis
    inline void Yaw       (const real& Ry_deg) ;// rotate aroun y-axis
    inline void Roll      (const real& Rz_deg) ;// rotate aroun z-axis
    inline void Scale     (const real& scale_fac);
    inline void Translate (const vector_type& pos);
    inline void ReComputeTheNormals(void);
    inline void InverseTheNormals(void) ;
    inline bool CheckTheNormals()const ;
    // @}
    // =============================================================

    // =============================================================
    //! @brief member function to configure this RigidBody into lbm .
    // =============================================================
    // @{
    inline void ComfigToLBMDomain(  const AXIS& which_axis,		// in which axis
                                    const real& axis_Size,		// lbm "AXIS"-domain size
                                    const point_type& _pos);	// position of the  object boundingBox in lbm-domain
    inline void ComfigToLBMDomain(	const point_type& _pos ,	// position of the  object boundingBox in lbm-domain
				    const real& DomainX,	// lbm x-domain size
				    const real& DomainY,	//
				    const real& DomainZ);
#ifdef GE_USE_MPI
    inline void ComfigToLBMDomain(  const AXIS& which_axis,		// in which axis
                                    const real& axis_Size,		// lbm "AXIS"-domain size
                                    const point_type& _pos,  	// position of the  object boundingBox in lbm-domain
                                    const aabb_type&  ProcAabb);	// processor bounding box
    inline void ComfigToLBMDomain(  const point_type& _pos ,	// position of the  object boundingBox in lbm-domain
				    const real& DomainX,	// lbm x-domain size
				    const real& DomainY,	//
				    const real& DomainZ,
                                    const aabb_type&  ProcAabb);	// processor bounding box
#endif

    inline void SetDensity(const real& den);			// added recently

    //======================================================================
    // set this object to use second order bouzidi BCs
    //======================================================================
    inline bool& SetObjectToUseBouzidiBCs();
    inline const bool& SetObjectToUseBouzidiBCs()const;
    // @}
    // =============================================================

public:

    inline real& GetTotalMass(){return m_mass ;}
    inline const real& GetTotalMass()const{return m_mass ;}
    inline const GMatrix33<real>&  I0()const {return *m_I ;}

    inline void SetObjectFlags(const unsigned int& insideflag,const unsigned int& outsideflag,const unsigned int& boundaryflag );
    inline const unsigned int GetInsideFlag()const ;
    inline const unsigned int GetOutsideFlag()const ;
    inline const unsigned int GetBorderFlag()const ;

    inline double& V0x() {return m_v0_x;}
    inline const double& V0x()const {return m_v0_x;}

    inline double& V0y() {return m_v0_y;}
    inline const double& V0y()const {return m_v0_y;}

    inline double& V0z() {return m_v0_z;}
    inline const double& V0z()const {return m_v0_z;}
    // =============================================================
    // const/modifer acces functions
    // Extern
    // =============================================================
    // @{
    // total force applied to the object [N]


// // #ifdef GE_USE_MPI
// //     inline vector_type&  F()
// //     {
// //         real Fx = -1 ;
// //         real Fy = -1 ;
// //         real Fz = -1 ;
// // 
// //         real Fx_send = m_Force [0] ;
// //         real Fy_send = m_Force [1] ;
// //         real Fz_send = m_Force [2] ;
// // 
// // 	if  (Fx_send  <= 0.000001f  )  Fx_send = 0 ;
// // 	if  (Fy_send  <= 0.000001f  )  Fy_send = 0 ;
// // 	if  (Fz_send  <= 0.000001f  )  Fz_send = 0 ;
// // 	
// //         int rank, size ;
// //         const int MASTER =0 ;
// // 
// //         //  Determine the sender and receiver
// // 	std::cout << "before init"<< endl ;
// //         MPI_Comm_rank( MPI_COMM_WORLD, &rank );
// //         MPI_Comm_size( MPI_COMM_WORLD, &size );
// // 	
// // 	if (rank == MASTER)
// // 		std::cout << "number of processors : " << size << endl;
// // 
// // 	if( typeid(real) == typeid(float) )
// // 	{
// // 		MPI_Reduce( &Fx_send, &Fx , 1 , MPI_FLOAT, MPI_SUM, MASTER, MPI_COMM_WORLD );
// // 		MPI_Reduce( &Fy_send, &Fy , 1 , MPI_FLOAT, MPI_SUM, MASTER, MPI_COMM_WORLD );
// // 		MPI_Reduce( &Fz_send, &Fz , 1 , MPI_FLOAT, MPI_SUM, MASTER, MPI_COMM_WORLD );
// // 	}
// // 	if( typeid(real) == typeid(double) )
// // 	{
// // 		MPI_Reduce( &Fx_send, &Fx , 1 , MPI_DOUBLE, MPI_SUM, MASTER, MPI_COMM_WORLD );
// // 		MPI_Reduce( &Fy_send, &Fy , 1 , MPI_DOUBLE, MPI_SUM, MASTER, MPI_COMM_WORLD );
// // 		MPI_Reduce( &Fz_send, &Fz , 1 , MPI_DOUBLE, MPI_SUM, MASTER, MPI_COMM_WORLD );
// // 	}
// //             
// //         if (rank == MASTER )
// //                         std::cerr << "processor : " <<  rank  << " -->  Fx  "<< Fx  << ", Fy = " << Fy << ", Fz = "<< Fz  <<  endl;
// // //        }
// // 
// //        this->m_Force = vector_type (Fx,Fy,Fz)  ;
// //        return  this->m_Force ; //vector_type (Fx,Fy,Fz) ;
// //     }
// //     inline const vector_type& F()const 
// //     {
// //         real Fx = -1 ;
// //         real Fy = -1 ;
// //         real Fz = -1 ;
// // 
// //         real Fx_send = m_Force [0] ;
// //         real Fy_send = m_Force [1] ;
// //         real Fz_send = m_Force [2] ;
// // 	
// // 	if  (Fx_send  <= 0.000001f  )  Fx_send = 0 ;
// // 	if  (Fy_send  <= 0.000001f  )  Fy_send = 0 ;
// // 	if  (Fz_send  <= 0.000001f  )  Fz_send = 0 ;
// // 	
// //         int rank, size ;
// //         const int MASTER =0 ;
// // 
// //         //  Determine the sender and receiver
// // 	std::cout << "before init"<< endl ;
// //         MPI_Comm_rank( MPI_COMM_WORLD, &rank );
// //         MPI_Comm_size( MPI_COMM_WORLD, &size );
// // 	
// // 	if (rank == MASTER)
// // 		std::cout << "number of processors : " << size << endl;
// // 
// // //        for (int procoss_id = 0; procoss_id < size; procoss_id ++)
// // //        {
// // 	      if( typeid(real) == typeid(float) ){
// // 			  MPI_Reduce( &Fx_send, &Fx , 1 , MPI_FLOAT, MPI_SUM, MASTER, MPI_COMM_WORLD );
// // 			  MPI_Reduce( &Fy_send, &Fy , 1 , MPI_FLOAT, MPI_SUM, MASTER, MPI_COMM_WORLD );
// // 			  MPI_Reduce( &Fz_send, &Fz , 1 , MPI_FLOAT, MPI_SUM, MASTER, MPI_COMM_WORLD );
// // 	      }
// // 	      if( typeid(real) == typeid(double) )
// // 	      {
// // 		MPI_Reduce( &Fx_send, &Fx , 1 , MPI_DOUBLE, MPI_SUM, MASTER, MPI_COMM_WORLD );
// // 		MPI_Reduce( &Fy_send, &Fy , 1 , MPI_DOUBLE, MPI_SUM, MASTER, MPI_COMM_WORLD );
// // 		MPI_Reduce( &Fz_send, &Fz , 1 , MPI_DOUBLE, MPI_SUM, MASTER, MPI_COMM_WORLD );
// // 	      }
// // 		   
// // 	if (rank == MASTER )
// //                std::cerr << "processor : " <<  rank  << " -->  Fx  "<< Fx  << ", Fy = " << Fy << ", Fz = "<< Fz  <<  endl;
// // 
// // 
// //         return vector_type (Fx,Fy,Fz)  ;
// //     }
// // #else
// #ifdef GE_USE_MPI
//     inline vector_type&  F()
//     {
//         float Fx = -1 ;
//         float Fy = -1 ;
//         float Fz = -1 ;
// 
//         float Fx_send = (float) m_Force [0] ;
//         float Fy_send = (float) m_Force [1] ;
//         float Fz_send = (float) m_Force [2] ;
// 
// 	if  (Fx_send  <= 0.000001f  )  Fx_send = 0 ;
// 	if  (Fy_send  <= 0.000001f  )  Fy_send = 0 ;
// 	if  (Fz_send  <= 0.000001f  )  Fz_send = 0 ;
// 	
//         int rank, size ;
//         const int MASTER =0 ;
// 
//         //  Determine the sender and receiver
// 
//         MPI_Comm_rank( MPI_COMM_WORLD, &rank );
//         MPI_Comm_size( MPI_COMM_WORLD, &size );
// 	MPI_Barrier (MPI_COMM_WORLD) ;
// 	MPI_Reduce( &Fx_send, &Fx , 1 , MPI_FLOAT, MPI_SUM, MASTER, MPI_COMM_WORLD );
// 	MPI_Barrier (MPI_COMM_WORLD) ;
// 	MPI_Reduce( &Fy_send, &Fy , 1 , MPI_FLOAT, MPI_SUM, MASTER, MPI_COMM_WORLD );
// 	MPI_Barrier (MPI_COMM_WORLD) ;
// 	MPI_Reduce( &Fz_send, &Fz , 1 , MPI_FLOAT, MPI_SUM, MASTER, MPI_COMM_WORLD );
// 	MPI_Barrier (MPI_COMM_WORLD) ;
//             
//         if (rank == MASTER )
//                 std::cerr << "processor : " <<  rank  << " -->  Fx  "<< Fx  << ", Fy = " << Fy << ", Fz = "<< Fz  <<  endl;
// 	else
// 		std::cerr << "processor : " <<  rank  << " -->  Fx_send  "<< Fx_send  << ", Fy_send = " << Fy_send << ", Fz_send = "<< Fz_send  <<  endl;
// 	   
//        this->m_Force = vector_type (Fx,Fy,Fz)  ;
//        return  this->m_Force ; //vector_type (Fx,Fy,Fz) ;
//     }
//     inline const vector_type& F()const 
//     {
//         float Fx = -1 ;
//         float Fy = -1 ;
//         float Fz = -1 ;
// 
//         float Fx_send = (float) m_Force [0] ;
//         float Fy_send = (float) m_Force [1] ;
//         float Fz_send = (float) m_Force [2] ;
// 
// 	if  (Fx_send  <= 0.000001f  )  Fx_send = 0 ;
// 	if  (Fy_send  <= 0.000001f  )  Fy_send = 0 ;
// 	if  (Fz_send  <= 0.000001f  )  Fz_send = 0 ;
// 	
//         int rank, size ;
//         const int MASTER =0 ;
// 
//         //  Determine the sender and receiver
// // 	std::cout << "before init"<< endl ;
//         MPI_Comm_rank( MPI_COMM_WORLD, &rank );
//         MPI_Comm_size( MPI_COMM_WORLD, &size );
// 	
// // 	if (rank == MASTER)
// // 		std::cout << "number of processors : " << size << endl;
// MPI_Barrier (MPI_COMM_WORLD) ;
// 	MPI_Reduce( &Fx_send, &Fx , 1 , MPI_FLOAT, MPI_SUM, MASTER, MPI_COMM_WORLD );
// 	MPI_Barrier (MPI_COMM_WORLD) ;
// 	MPI_Reduce( &Fy_send, &Fy , 1 , MPI_FLOAT, MPI_SUM, MASTER, MPI_COMM_WORLD );
// 	MPI_Barrier (MPI_COMM_WORLD) ;
// 	MPI_Reduce( &Fz_send, &Fz , 1 , MPI_FLOAT, MPI_SUM, MASTER, MPI_COMM_WORLD );
// 	MPI_Barrier (MPI_COMM_WORLD) ;
//             
//         if (rank == MASTER )
// 		std::cerr << "processor : " <<  rank  << " -->  Fx  "<< Fx  << ", Fy = " << Fy << ", Fz = "<< Fz  <<  endl;
// 	else
// 		std::cerr << "processor : " <<  rank  << " -->  Fx_send  "<< Fx_send  << ", Fy_send = " << Fy_send << ", Fz_send = "<< Fz_send  <<  endl;
// 	
//         return vector_type (Fx,Fy,Fz)  ;
//     }
// #else
    inline vector_type&       F() 	{return m_Force ;}
    inline const vector_type& F()const 	{return m_Force ;}
// #endif

    // total Force applied to the object [???]
    inline vector_type& 		Tau() 	{return  m_Torque;}
    inline const vector_type& Tau()const 	{return m_Torque;}

    inline vector_type& UniField(){return  m_uni_field ;}
    inline const vector_type& UniField()const{return  m_uni_field ;}

    inline void ResetForceAndMoment(){
        this-> F()   = vector_type (0.0 , 0.0 , 0.0 ) ;
        this-> Tau() = vector_type (0.0 , 0.0 , 0.0 ) ;
    }
    inline void AddGravitationalField(const real& gx_L,const real& gy_L,const real& gz_L) ;
    // @}
    // =============================================================


    // =============================================================
    // const/modifer acces functions
    // state variables depend on time
    // =============================================================
    // @{
    inline point_type&	 GetCOM (){return m_x  ;}// x (t) c.o.m. position
    inline const point_type& GetCOM ()const{return m_x  ;}// x (t) c.o.m. position

    // R(t)  rotation matrix
    inline GMatrix33<real>& GetRt(){return  m_R;}
    inline const GMatrix33<real>& GetRt()const{return m_R;}

    // Linear momentum  P(t)
    inline vector_type& P(){return m_P;}
    inline const vector_type& P()const{return m_P;}

    // Angular momentum  L(t)
    inline vector_type&	  L()     {return m_L;}
    inline const vector_type& L()const{return m_L;}

    inline const vector_type& V()const{ (P() /m_mass) ; }


    // @}
    // =============================================================


    // =============================================================
    // Get the mesh as an object
    // =============================================================
    //@{
    inline mesh_type* GetObject(){return m_stl_mesh;}
    inline const mesh_type* GetObject()const{return m_stl_mesh;}
    //@}
    // =============================================================


    //additional
    //return true if the point "P" is inside the object-aabb
    //	 else return false.
    inline const bool IsInsideObjectBoundingBox(const point_type& P)const;
    inline const bool IsInsideObjectExtraBoundingBox(const point_type& P)const;

    // =============================================================
    // object bounding box
    // =============================================================
    //@{
    inline const point_type& AabbMin()const{return m_stl_mesh->AbbMin();}
    inline const point_type& AabbMax()const{return m_stl_mesh->AbbMax();}
    inline const real Radios()const;
    //@}
    // =============================================================


private :

    // constants	------------------------------------------------
    real		m_mass ; // mass =  volume * density  [kg]
    const GMatrix33<real>*m_I ; // Inertia tensor with origin at c.o.m --> diagonal matrix// TODO optimize to Vector3D

    // variables		----------------------------------------
    point_type    	m_x ; // position of c.o.m
    GMatrix33<real>	m_R ; // rotation matrix
    vector_type   	m_P ; // l. Momentum
    vector_type   	m_L ; // Angular. Momentum

    // Extern, accumulators --------------------------------------
    vector_type            m_uni_field;// unnoiform field, dosen't have to reset each time step, example gravity field
    vector_type		m_Force;  // total Force applied to the object  [N]
    vector_type		m_Torque; // total Force applied to the object [N/m]

    // triangles mesh	----------------------------------------
    mesh_type*		m_stl_mesh ;// hold all mesh object info
    bool m_ObjectWithSecondOrderBZBCs;// if the object will use seconde
    // Bouzidi BCs

    unsigned int m_flag[3];// <m_flag[0],m_flag[1],m_flag[2]> explicitly mean |-> flag <insideflag, outsideflag, boundaryflag>
    sector_list_type	m_sector_list;// alls ector arounding/touched by object bounding box.
#ifdef NO_STAND_ALONE_GE
    brd_cell_list_type  	m_brd_cells; // Bouzidi or near obstacl cells
    cell_list_type  	m_InsideCells;// for debug,will be used for compute the force, coupling with waLBerla
    patch_brd_cell_list_type m_linked_brd_cells; // map <patch_uniq_id, vector <brdcells> >
#endif
    double m_v0_x , m_v0_y ,m_v0_z ;
    //cell_list_type  	m_brd_cells_loc; //

    bool             m_compute_Forces; // switcher if you activate force computing or not



    bool plot_pressureCoeff;
    bool m_drag_coeff;
    bool m_lift_coeff;
    std::string fname_param; // pressureCoeff



public:
    inline bool& PlotPressure() {return plot_pressureCoeff; }
    inline const bool& PlotPressure() const {return plot_pressureCoeff; }

    inline bool& DragCoeff() {return m_drag_coeff; }
    inline const bool& DragCoeff() const {return m_drag_coeff; }


    inline bool& LiftCoeff() {return m_lift_coeff ; }
    inline const bool& LiftCoeff() const {return m_lift_coeff; }




    inline bool& SwitchComputeForces();
    inline const bool& SwitchComputeForces()const;



    // only for cylinder case, i used for my thesis --> see validation section.
    inline void  PlotPressureCoefficient (const int& object_id,
                                          const int& time_step,
                                          const double& dx,
                                          const double& dt,   walberla::SimData& sim ,walberla::PatchField& patchField_);


    // only for cylinder case, i used for my thesis --> see validation section.
    inline void  ComputeDragCoefficient (const int& object_id,
                                          const int& time_step,
                                          const double& dx,
                                          const double& dt, 
					  walberla::SimData& sim ,
					  walberla::PatchField& patchField_);

    inline void  ComputeLiftCoefficient (const int& object_id,
                                          const int& time_step,
                                          const double& dx,
                                          const double& dt,   
					  walberla::SimData& sim ,
					  walberla::PatchField& patchField_);


private:
    // not implemented
    //RigidBody (const RigidBody&     ); // copy constructor
    //void operator=(const RigidBody& ); // assignement operator
};





#ifdef GE_USE_MPI
// =====================================================================
// @brief default TM constructor.
// =====================================================================
template<class Conf>
RigidBody<Conf>::RigidBody(const char* f_name ,
                           const typename RigidBody<Conf>::aabb_type& procAabb,
                           const typename RigidBody<Conf>::real& density_,
                           const bool& dyn  )
{
    typedef typename RigidBody<Conf>::mesh_type	mesh_type ;
    typedef typename RigidBody<Conf>::object_loader object_loader;
    m_stl_mesh = new mesh_type (f_name,density_,dyn ) ;
    // first load the file

    std::cout << "MPI mode --> Start Load the mesh\n";
    // this not working, because the stl object have to scale to LBM first before you clipped
    //  object_loader::Load (  procAabb , *m_stl_mesh ); 

     StlMeshLoader<mesh_type>::Load (*m_stl_mesh );
     // scale 
     // 
     
     
     
     
     // clipp to object to processor aabb

    std::cout << "MPI mode --> End Load the mesh\n";

    /*
        // TODO recompute the normals
        std::cout << "MPI mode --> Start Recompute the normals\n";
         ReComputeTheNormals() ;
        std::cout << "End Recompute Normals\n";


        // FIXME add if and olny if case the object is set to be dynamic
        std::cout << "MPI mode -->Start computing the inertia tensor ,and c.o.m\n";
        ComputeObjectInertiaMomentsAndCOM (* m_stl_mesh) ;
        std::cout << "MPI mode -->End the inertia tensor ,and c.o.m\n";


        m_mass  = m_stl_mesh-> GetTotalMass();
        m_I	= m_stl_mesh-> I0() ;
        GetRt().InitToIdentityMatrix() ;
        */
    this -> SetObjectToUseBouzidiBCs() = false ;
}
#else
// =====================================================================
// @brief default TM constructor.
// =====================================================================
template<class Conf>
RigidBody<Conf>::RigidBody(const char* f_name,
                           const typename RigidBody<Conf>::real& density_,
                           const bool& dynamic)
{
    typedef typename RigidBody<Conf>::mesh_type 	mesh_type ;
    typedef typename RigidBody<Conf>::object_loader	object_loader;

    m_stl_mesh = new mesh_type (f_name,density_, dynamic) ;
    // first load the file
    std::cout << "Start Load the mesh\n";
#ifdef BENCH_GE
    const double lstart  = WcTiming () ;
#endif

    // FIXME later
#ifdef TMP_PRO // tmp compiler fix
    StlMeshLoader<mesh_type>::Load (*m_stl_mesh );
#else
    object_loader::Load (*m_stl_mesh );
#endif

    std::cout << "End Load the mesh\n";
#ifdef BENCH_GE
    const double load_run = WcTiming() - lstart ;
    std::cout << "		---> Load run time : " << load_run << " sec\n" ;
#endif

    // this->SetDynamicParameters();//moments of inertia, c.o.m ans so one.
    /*
        // TODO recompute the normals
        #ifdef RIGIDBODY_DEBUG
        std::cout << "Start Recompute the normals\n";
        #endif

        ReComputeTheNormals() ;
        #ifdef RIGIDBODY_DEBUG
        std::cout << "End Recompute Normals\n";
        #endif
        */
#ifdef RIGIDBODY_DEBUG
    std::cout << "Start computing the inertia tensor ,and c.o.m\n";
#endif
    // ============================================================
#ifdef BENCH_GE
    const double lcompInertia  = WcTiming () ;
#endif

    ComputeObjectInertiaMomentsAndCOM (* m_stl_mesh) ;

    this-> GetCOM () =  m_stl_mesh->GetCentroid();

#ifdef BENCH_GE
    std::cout << "		---> ComputeObjectInertiaMomentsAndCOM run time : " << WcTiming() - lcompInertia  << " sec\n" ;
#endif
    // ============================================================
#ifdef RIGIDBODY_DEBUG
    std::cout << "End the inertia tensor ,and c.o.m\n";
#endif

    m_mass  = m_stl_mesh-> GetTotalMass();

#ifdef RIGIDBODY_DEBUG
    std::cout << "	--> m_stl_mesh-> GetTotalMass() : "<< m_mass <<  "\n";
#endif

#ifdef RIGIDBODY_DEBUG
    std::cout << "	--> m_stl_mesh-> IO()\n";
#endif

    m_I	= &m_stl_mesh-> I0() ;


#ifdef RIGIDBODY_DEBUG
    std::cout << "	--> GetRt().InitToIdentityMatrix()\n";
#endif

    GetRt().InitToIdentityMatrix() ;

    this -> SetObjectToUseBouzidiBCs() = false ;

    // initial some variables to zero
    //this->P()	= vector_type (0.0 , 0.0 , 0.0 );
    this->F()	  = vector_type (0.0 , 0.0 , 0.0 );
    this->Tau() = vector_type (0.0 , 0.0 , 0.0 );
    this->UniField()= vector_type (0.0 , 0.0 , 0.0 );
#ifdef RIGIDBODY_DEBUG
    std::cout << "End Constructor\n";
#endif
}
#endif

// =====================================================================
//!@brief TM destructor
// =====================================================================
template<class Conf>
RigidBody<Conf>::~RigidBody()
{
    //delete m_stl_mesh ;
}

template<class Conf>
inline void RigidBody<Conf>::SetObjectFlags(const unsigned int& insideflag,const unsigned int& outsideflag,const unsigned int& boundaryflag )
{
    m_flag[0] = insideflag;
    m_flag[1] = outsideflag;
    m_flag[2] = boundaryflag;
}


// Get object flag
template<class Conf> inline const unsigned int RigidBody<Conf>::GetInsideFlag()const {return m_flag[0];}
template<class Conf> inline const unsigned int RigidBody<Conf>::GetOutsideFlag()const{return m_flag[1];}
template<class Conf> inline const unsigned int RigidBody<Conf>::GetBorderFlag()const {return m_flag[2];}



// =====================================================================
//
// =====================================================================
template<class Conf>
inline void RigidBody<Conf>::Update(const typename RigidBody<Conf>::real& dt)
{
    typedef typename RigidBody<Conf>::vector_type	vector_type ;
    typedef typename RigidBody<Conf>::real		real ;
    // if you throw an assertion, its most properly you haven't set
    // forces of torques applied to(on) this object
    assert ( F().Magnitude() + Tau().Magnitude() != 0. );

#ifdef RIGIDBODY_DEBUG
    std::cout << "dt : " << dt << "\n";
    std::cout << "Forces  : " << this -> F()   << "\n";
    std::cout << "Torques : " << this -> Tau() << "\n";
    std::cout << "COM : " << this -> GetCOM()  << "\n";
#endif
    // update the position
    P()		+=   F() * dt ;
    GetCOM ()	+=   (P()/m_mass) * dt;

    // only and if only we have Torque
    if  ( Tau().Magnitude() != 0. )
    {
        // update the position
        L() 	+=  Tau()* dt ;
#ifdef RIGIDBODY_DEBUG
        std::cout << "Angular Momentum : "<< L( )	<< std::endl ;
        std::cout << " --->GetRt()  :\n"   << (this->GetRt()) << std::endl ;
        std::cout << " --->Transpose (this -> GetRt()) :\n"<< (* Transpose (this -> GetRt()) ) << std::endl ;
#endif

        const GMatrix33<real>  I  =  this ->GetRt() * this -> I0() * (* Transpose (this -> GetRt()) );

#ifdef RIGIDBODY_DEBUG
        std::cout << "Rotation Inertia  I :\n"<<  I << std::endl ;
#endif

        const vector_type omega = (* Transpose ( I ) ) * L()  ;
        const real angel	= omega.Magnitude()    * dt   ;

#ifdef RIGIDBODY_DEBUG
        std::cout << "m_mass : " << m_mass << "\n";
        std::cout << "lineare velocity	: " <<  P() /m_mass << "\n";
        std::cout << "lineare speed  [m/s]	: " << (P() /m_mass).Magnitude()<< "\n";
        std::cout << "angular velocity 	: " << omega << "\n"; // Problem
        std::cout << "angular speed  [?/?]    : " << angel << "\n";
#endif

        vector_type axis = omega ;
        axis.normalize(); // if its null vector_type, then keep null.

        const real c = std::cos(angel) ;
        const real s = std::sin(angel) ;
        const real t = 1. -c  ;
#ifdef RIGIDBODY_DEBUG

#endif
        this ->GetRt() = GMatrix33<real>
                (
                    t*axis.x()*axis.x() + c		  ,	t*axis.x()*axis.y() - axis.z()*s  , t*axis.x()*axis.z() + axis.y()*s ,
                    t*axis.x()*axis.y() + axis.z()*s  ,	t*axis.y()*axis.y() + c 	  , t*axis.y()*axis.z() - axis.x()*s ,
                    t*axis.x()*axis.z() - axis.y()*s  ,	t*axis.y()*axis.z() + axis.x()*s  , t*axis.z()*axis.z() + c
                    );
    }
}




// =====================================================================
//!@brief print some info
// =====================================================================
template<class Conf>
inline void RigidBody<Conf>::Info()const
{
    // assert that we have dynamic object.
    assert (  m_stl_mesh -> IsDynamic () ) ;
    std::cout<<"// =============================================================================\n";
    std::cout<<"//	Constant simulation quantities info \n";
    std::cout<<"// =============================================================================\n";
    std::cout<<"density	: "	<< this->GetObject()-> GetDensity()   <<"\t[kg/m^3]\n";
    std::cout<<"mass	: "	<< m_mass  <<"\t[kg]\n";
    std::cout<<"Io	: \n"	 	<< *m_I     << "\t[kg+m^2]\n";
    std::cout<<"initial speed (from prm file) :\t" << this->V0x() << ","<<this->V0y() << ","<<this->V0z() <<"\n";
}

// =====================================================================
//
// =====================================================================
template<class Conf>
inline void RigidBody<Conf>::ForcesAndTorquesInfo()const
{
    // assert thaat we have dynamic object.
    assert (  m_stl_mesh -> IsDynamic () ) ;
    std::cout<<"// =============================================================================\n";
    std::cout<<"//	Additionally physical quantities Info\n";
    std::cout<<"// =============================================================================\n";
    std::cout<<"uniform field (i.e gravitational) [N]	: " << UniField() << "\n" ;
    std::cout<<"total hydrodynamics forces applied to the object [N]  	: " << F()  << "\n" ;
    std::cout<<"total forces applied to the object (+gravitational field) [N]  	: " << F() + UniField()  << "\n" ;
    std::cout<<"total torque applied to the object [N.m]	: " << Tau() << "\n" ;
    std::cout<<"linear acceleration [m/m^2]			: " << F()/m_mass<< "\n" ;
}

// =====================================================================
//
// =====================================================================
template<class Conf>
inline void RigidBody<Conf>::StateVariablesInfo()const
{
    // assert thaat we have dynamic object.
    assert (  m_stl_mesh -> IsDynamic () ) ;
    std::cout<<"// =============================================================================\n";
    std::cout<<"//	State Variables Info\n";
    std::cout<<"// =============================================================================\n";
    std::cout<<"x(t), is c.o.m : "<< m_x<< "\t[m]\n" ;
    std::cout<<"R(t), rotation matrix:\n"<< m_R << "\t[m]\n";
    std::cout<<"P(t), Linear momentum: " << m_P << "\t[kg*m/s]|[N*s]\n";
    std::cout<<"L(t), Angular momentum: "<< m_L << "\t[?]\n";
    std::cout<<"I(t) = R(t)*I0* Transpose(R(t)): [kg*m^2]\n "
            << GetRt() * I0()  * (* Transpose( GetRt() ) ) << "\n";
}

// =====================================================================
//
// =====================================================================
template<class Conf>
inline void RigidBody<Conf>::ComputationalInfo()const
{
    typedef typename RigidBody<Conf>::real real ;
    std::string comp_type ;
    if( typeid(real) == typeid(float) )	{comp_type = "float" ;}
    else if( typeid(real) == typeid(double)){comp_type = "double";}
    else {
        std::cout <<"Error: only floats or double are allowed_type."
                 << typeid(real).type_info::name() <<  "\n";
    }

    std::cout<<"// =============================================================================\n";
    std::cout<<"//	Computational info \n";
    std::cout<<"// =============================================================================\n";
    std::cout<<"type use for computational	: "<< comp_type<<  "\n";
#ifdef DEBUG
    std::cout<<"Compiling mode                  : "<< "DEBUG\n" ;
#else
    std::cout<<"Compiling mode                  : "<< "RELEASE\n" ;
#endif
    std::cout<<"type of this object		: ";
    if ( m_stl_mesh -> IsDynamic() )
    {
        std::cout<<  "dynamic" << "\n";
    }else{
        std::cout<<  "fix (or static)" << "\n";
    }

    if ( m_stl_mesh -> IsDynamic() )
    {
        std::cout<<"sector_list -> size()           : " << m_sector_list.size()  << "\n";
    }
}


// =====================================================================
//
// =====================================================================
template<class Conf>
inline void RigidBody<Conf>::GetFileGeomInfo() const
{
    typename RigidBody<Conf>::object_loader	object_loader;
    object_loader::FileInfo( *m_stl_mesh );// implemented in MeshManip.h file
    m_stl_mesh -> GeomInfo();	// print some geometric info
    //this -> FlagsInfo();		// object flags
    if ( IsDynamic() )
        m_stl_mesh -> DynmaicInfo();
}


// =====================================================================
//
// =====================================================================
template<class Conf>
inline void RigidBody<Conf>::FlagsInfo() const
{
    std::cout<<"// =============================================================================\n";
    std::cout<<"//	object flag<inside,outside,border> info \n";
    std::cout<<"// =============================================================================\n";
    std::cout<<"flag <" << m_flag[0] << ","<< m_flag[1] << ","<< m_flag[2] << ">\n";
}
// =====================================================================
//
// =====================================================================
// dynmaic simulation
template<class Conf>
inline const bool& RigidBody<Conf>::IsDynamic()const
{
    return m_stl_mesh ->IsDynamic();
}


template<class Conf>
inline bool& RigidBody<Conf>::IsDynamic()
{
    return m_stl_mesh ->IsDynamic();
}

// =====================================================================
// update all vertices position, and recompute the normals
// =====================================================================
template<typename Conf> // of type TM
inline void RigidBody<Conf>::UpdateTrianglesMesh()
{
    assert ( this-> IsDynamic() );

    // =============================================================
    // typedefs
    // =============================================================
    typedef typename RigidBody<Conf>::iterator	iterator;
    // =============================================================
    const std::size_t nt = m_stl_mesh->GetPolyListMesh().size();
    iterator	  it = m_stl_mesh->GetPolyListMesh().begin();

    std::cout<<"COM : \t" << this->GetCOM() << "\n";
    for (std::size_t i=0 ; i != nt ; ++ i )
    {
        (*it).V0() =  (GetRt() * (*it).V0()) + this->GetCOM() ;
        (*it).V1() =  (GetRt() * (*it).V1()) + this->GetCOM() ;
        (*it).V2() =  (GetRt() * (*it).V2()) + this->GetCOM() ;
        ++ it ;
    }
    // recompute the normals
    Object_reComputeNormals (* GetObject() ) ;
}


// =====================================================================
//
// =====================================================================
template<typename Conf> // of type TM
inline void RigidBody<Conf>::UpdateTrianglesMeshPositionOnlyTranslation
(const typename RigidBody<Conf>::point_type& move_to,const bool&recomputeNormals)
{
    assert ( this-> IsDynamic() );

    // =============================================================
    // typedefs
    // =============================================================
    typedef typename RigidBody<Conf>::iterator	iterator;
    // =============================================================
    const std::size_t nt = m_stl_mesh->GetPolyListMesh().size();
    iterator it = m_stl_mesh->GetPolyListMesh().begin();

    std::cout<<"COM : \t" << this->GetCOM() << "\n";
    std::cout<<"move_to : \t" << move_to << "\n";
    for (std::size_t i=0 ; i != nt ; ++ i )
    {
        (*it).V0() +=  move_to ;
        (*it).V1() +=  move_to ;
        (*it).V2() +=  move_to ;
        ++ it ;
    }
    // recompute the normals
    if (recomputeNormals)
        Object_reComputeNormals (* GetObject() ) ;
}
// =====================================================================
//
// =====================================================================
template<typename Conf> // of type TM
inline void RigidBody<Conf>::UpdateTrianglesMeshPositionOnlyTranslation
(const typename RigidBody<Conf>::vector_type& move_to,const bool&recomputeNormals)
{
    assert ( this-> IsDynamic() );

    // =============================================================
    // typedefs
    // =============================================================
    typedef typename RigidBody<Conf>::iterator	iterator;
    // =============================================================
    const std::size_t nt = m_stl_mesh->GetPolyListMesh().size();
    iterator it = m_stl_mesh->GetPolyListMesh().begin();

    std::cout<<"COM : \t" << this->GetCOM() << "\n";
    std::cout<<"move_to : \t" << move_to << "\n";
    for (std::size_t i=0 ; i != nt ; ++ i )
    {
        std::cout<<"VO (befor moved) : \t" << (*it).V0() << "\t";
        (*it).V0() +=  move_to ;
        std::cout<<"VO (after moved) : \t" << (*it).V0() << "\n";
        (*it).V1() +=  move_to ;
        (*it).V2() +=  move_to ;
        ++ it ;
    }

    // recompute the normals
    if (recomputeNormals)
        Object_reComputeNormals (* GetObject() ) ;
}




#ifdef NO_STAND_ALONE_GE
// =====================================================================
// update (only) the position (and not speed) of all mapped cells:
// mapped inside-cells without touchnig the border-cells
//
// =====================================================================
template<typename Conf> // of type TM
inline void RigidBody<Conf>::UpdateTheMappedInsideCells(const typename RigidBody<Conf>::point_type& move_to)
{
    assert ( this-> IsDynamic() );
    assert (m_InsideCells.size() != 0 );
    typedef typename RigidBody<Conf>::cell_list_type cell_list_type ;
    typedef typename cell_list_type::iterator iterator;
    for (iterator it=m_InsideCells.begin() ; it != m_InsideCells.end() ; ++ it )
    {
        (*it)->x  +=  move_to.X();
        (*it)->y  +=  move_to.Y();
        (*it)->z  +=  move_to.Z();
    }
}

// =====================================================================
// update (only) the position (and not speed) of all mapped cells:
// mapped inside-cells without touchnig the border-cells
//
// =====================================================================
template<typename Conf> // of type TM
inline void RigidBody<Conf>::UpdateTheMappedInsideCells(const typename RigidBody<Conf>::vector_type& move_to)
{
    assert ( this-> IsDynamic() );
    assert (m_InsideCells.size() != 0 );
    typedef typename RigidBody<Conf>::cell_list_type cell_list_type ;
    typedef typename cell_list_type::iterator iterator;

    for (iterator it=m_InsideCells.begin() ; it != m_InsideCells.end() ; ++ it )
    {
        (*it)->x  +=  move_to[0];
        (*it)->y  +=  move_to[1];
        (*it)->z  +=  move_to[2];
    }
}



// =====================================================================
// update (only) the position (and not speed) of mapped border cells:
// mapped border-cells without touchnig the inside-cells
//
// =====================================================================
template<typename Conf> // of type TM
inline void RigidBody<Conf>::UpdateTheMappedBorderCells(const typename RigidBody<Conf>::point_type& move_to)
{
    assert ( this-> IsDynamic() && m_ObjectWithSecondOrderBZBCs==true);
    assert (m_brd_cells.size() != 0 );

    typedef typename RigidBody<Conf>::brd_cell_list_type  brd_cell_list_type;
    typedef typename brd_cell_list_type::iterator iterator;
    assert (m_brd_cells.size() != 0 );



    for (iterator it=m_brd_cells.begin() ; it != m_brd_cells.end() ; ++ it )
    {
        (*it)->x  +=  move_to.X();
        (*it)->y  +=  move_to.Y();
        (*it)->z  +=  move_to.Z();
    }
}


// =====================================================================
// update (only) the position (and not speed) of mapped border cells:
// mapped border-cells without touchnig the inside-cells
//
// =====================================================================
template<typename Conf> // of type TM
inline void RigidBody<Conf>::UpdateTheMappedBorderCells(const typename RigidBody<Conf>::vector_type& move_to)
{
    assert ( this-> IsDynamic() && m_ObjectWithSecondOrderBZBCs==true);
    assert (m_brd_cells.size() != 0 );
    typedef typename RigidBody<Conf>::brd_cell_list_type  brd_cell_list_type;
    typedef typename brd_cell_list_type::iterator iterator;
    assert (m_brd_cells.size() != 0 );
    for (iterator it=m_brd_cells.begin() ; it != m_brd_cells.end() ; ++ it )
    {
        (*it)->x  +=  move_to[0];
        (*it)->y  +=  move_to[1];
        (*it)->z  +=  move_to[2];
    }
}
#endif








// =====================================================================
//
// =====================================================================
template<class Conf>
inline void RigidBody<Conf>::Export(const char* f_name,const bool& Binary)const
{
#ifdef RIGIDBODY_DEBUG
    std::cout << "export to :" << f_name << std::endl ;
    std::cout << " " << m_stl_mesh->size() << "\n" ;
#endif
    //this-> Info () ;
    exporter::Export (  f_name , Binary , *m_stl_mesh );
}

// =====================================================================
//
// =====================================================================
template<class Conf>
inline void RigidBody<Conf>::AddSector(typename RigidBody<Conf>::sector_type* sec)
{
    m_sector_list.push_back(sec) ;
}












//======================================================================
//!@brief  rotation the entire mesh, note that the order is really
//!@brief  important, first around x-axis, then y and finaly z.
//======================================================================
template<class Conf>
inline void RigidBody<Conf>::rotate_TM (const typename RigidBody<Conf>::real& x,
                                        const typename RigidBody<Conf>::real& y,
                                        const typename RigidBody<Conf>::real& z)
{
    if (x != 0. ) this -> Pitch ( x ) ;
    if (y != 0. ) this -> Yaw   ( y ) ;
    if (z != 0. ) this -> Roll  ( z ) ;
    m_stl_mesh -> HaveBeenPreProc() = true ;
}

// =====================================================================
//
// =====================================================================
template<class Conf>
inline void RigidBody<Conf>::Pitch (const typename RigidBody<Conf>::real& angel_deg)
{
    Object_rotate_x ( *m_stl_mesh, angel_deg ) ;
    // reset the bounding box
    SetBoundingBoxOfMeshObject (*m_stl_mesh) ;
    m_stl_mesh -> HaveBeenPreProc() = true ;
}

// =====================================================================
//
// =====================================================================
template<class Conf>
inline void RigidBody<Conf>::Yaw (const typename RigidBody<Conf>::real& angel_deg)
{
    Object_rotate_y( *m_stl_mesh, angel_deg ) ;
    // reset the bounding box
    SetBoundingBoxOfMeshObject ( *m_stl_mesh) ;
    m_stl_mesh -> HaveBeenPreProc() = true ;
}

// =====================================================================
//
// =====================================================================
template<class Conf>
inline void RigidBody<Conf>::Roll (const typename RigidBody<Conf>::real& angel_deg)
{
    Object_rotate_z( *m_stl_mesh, angel_deg );
    // reset the bounding box
    SetBoundingBoxOfMeshObject ( *m_stl_mesh) ;
    m_stl_mesh -> HaveBeenPreProc() = true ;
}


// =====================================================================
//
// =====================================================================
template<class Conf>
inline void RigidBody<Conf>::Scale (const  typename RigidBody<Conf>::real& scale_fac)
{
    // 		std::cout << "----> RigidBody<Conf>::Scale ---> m_stl_mesh ->size() :"
    // 			<< m_stl_mesh ->size()  << std::endl ;
    Object_scale( *m_stl_mesh,  scale_fac);
    // reset the bounding box
    SetBoundingBoxOfMeshObject ( *m_stl_mesh) ;
    m_stl_mesh -> HaveBeenPreProc() = true ;
}


// =====================================================================
//
// =====================================================================
template<class Conf>
inline void RigidBody<Conf>::Translate(const typename RigidBody<Conf>::vector_type& pos)
{
    Object_translate (*m_stl_mesh, pos) ;
    // reset the bounding box
    SetBoundingBoxOfMeshObject ( *m_stl_mesh) ;
    m_stl_mesh -> HaveBeenPreProc() = true ;
}

// =====================================================================
//
// =====================================================================
template<class Conf>
inline void RigidBody<Conf>::ReComputeTheNormals(void)
{
    Object_reComputeNormals (*m_stl_mesh) ;
    m_stl_mesh -> HaveBeenPreProc() = true ;
}

template<class Conf>
inline bool RigidBody<Conf>::CheckTheNormals()const
{
    return ( Object_CheckNormals (*m_stl_mesh) ) ;
}




// =====================================================================
//
// =====================================================================
template<class Conf>
inline void RigidBody<Conf>::InverseTheNormals(void)
{
    Object_inverseNormals   (*m_stl_mesh) ;
    m_stl_mesh -> HaveBeenPreProc() = true ;
}
// =====================================================================



// =====================================================================
//
// =====================================================================
template<class Conf>
inline void RigidBody<Conf>::SetDensity(const typename RigidBody<Conf>::real& den) // added recently
{
    assert (den != 0 ) ;
    m_stl_mesh -> SetDensity (den) ;
}


// // ==============================================================================
//! @brief help functions to configure the triangles Mesh into
//!        lbm simulation domain
// // ==============================================================================
// @{
template<class Conf>
inline void RigidBody<Conf>::ComfigToLBMDomain(const AXIS& which_axis,
                                               const typename RigidBody<Conf>::real& axis_Size,
                                               const typename RigidBody<Conf>::point_type& _pos )
{
    typedef typename RigidBody<Conf>::real real ;
    const real Lx =  (m_stl_mesh-> GetLengthX() );
    const real Ly =  (m_stl_mesh-> GetLengthY() );
    const real Lz =  (m_stl_mesh-> GetLengthZ() );
    const real aspect_ratio_y_x = Ly  / Lx ; // aplha
    const real aspect_ratio_z_x = Lz  / Lx ; // beta
    const real aspect_ratio_x_y =   1.0 / aspect_ratio_y_x ;// 1./alpha
    const real aspect_ratio_x_z =   1.0 / aspect_ratio_z_x ;// 1./beta

    real DomainX, DomainY , DomainZ ;
    switch ( which_axis )
    {
    case  X_AXIS :
        DomainX = axis_Size ;		// dx = axis_Size
        DomainY = aspect_ratio_y_x* axis_Size ; // aplha * dx
        DomainZ = aspect_ratio_z_x* axis_Size ; // beta  * dy
        this -> ComfigToLBMDomain (_pos,DomainX, DomainY , DomainZ);
        break;

    case  Y_AXIS :
        DomainX = axis_Size*aspect_ratio_x_y;
        DomainY = axis_Size;
        DomainZ = axis_Size*aspect_ratio_z_x * aspect_ratio_x_y ;
        this -> ComfigToLBMDomain (_pos,DomainX, DomainY , DomainZ);
        break;

    case  Z_AXIS :
        DomainX = axis_Size*aspect_ratio_x_z;// Lz / beta
        DomainY = DomainX * aspect_ratio_y_x;// Lx *alpha
        DomainZ = axis_Size;
        this -> ComfigToLBMDomain (_pos,DomainX, DomainY , DomainZ);
        break;

    default: //throw an error
        InvalidArgument (__FILE__,__LINE__,"Unknow directions." ) ;
    }

    SetBoundingBoxOfMeshObject ( *m_stl_mesh) ;
    m_stl_mesh -> HaveBeenPreProc() = true ;
}


//======================================================================
template<class Conf>
inline void RigidBody<Conf>::ComfigToLBMDomain(const typename RigidBody<Conf>::point_type& _pos ,// pos
					       const typename RigidBody<Conf>::real& DomainX,
					       const typename RigidBody<Conf>::real& DomainY,
					       const typename RigidBody<Conf>::real& DomainZ )
{
#ifdef DEBUG
    std::cout << "RigidBody<Conf>::ComfigToLBMDomain(...)::Starting scketching to lbm domain.\n" ;
#endif
    ScaleObjectToLatticeDomain
            (
                *m_stl_mesh,
                _pos       ,
                DomainX    ,
                DomainY    ,
                DomainZ
                );
    SetBoundingBoxOfMeshObject ( *m_stl_mesh) ;
    m_stl_mesh -> HaveBeenPreProc() = true ;
#ifdef DEBUG
    std::cout << "RigidBody<Conf>::ComfigToLBMDomain(...)::Ending scketching to lbm domain.\n" ;
#endif
}

#ifdef GE_USE_MPI

template<class Conf>
inline void RigidBody<Conf>::ComfigToLBMDomain(const AXIS& which_axis,
                                               const typename RigidBody<Conf>::real& axis_Size,
                                               const typename RigidBody<Conf>::point_type& _pos,
                                               const typename RigidBody<Conf>::aabb_type& ProcAabb
                                               )
{
    typedef typename RigidBody<Conf>::real real ;
    const real Lx =  (m_stl_mesh-> GetLengthX() );
    const real Ly =  (m_stl_mesh-> GetLengthY() );
    const real Lz =  (m_stl_mesh-> GetLengthZ() );
    const real aspect_ratio_y_x = Ly  / Lx ; // aplha
    const real aspect_ratio_z_x = Lz  / Lx ; // beta
    const real aspect_ratio_x_y =   1.0 / aspect_ratio_y_x ;// 1./alpha
    const real aspect_ratio_x_z =   1.0 / aspect_ratio_z_x ;// 1./beta

    real DomainX, DomainY , DomainZ ;
    switch ( which_axis )
    {
    case  X_AXIS :
        DomainX = axis_Size ;             // dx = axis_Size
        DomainY = aspect_ratio_y_x* axis_Size ; // aplha * dx
        DomainZ = aspect_ratio_z_x* axis_Size ; // beta  * dy
        this -> ComfigToLBMDomain (_pos,DomainX, DomainY , DomainZ,ProcAabb);
        break;

    case  Y_AXIS :
        DomainX = axis_Size*aspect_ratio_x_y;
        DomainY = axis_Size;
        DomainZ = axis_Size*aspect_ratio_z_x * aspect_ratio_x_y ;
        this -> ComfigToLBMDomain (_pos,DomainX, DomainY , DomainZ,ProcAabb);
        break;

    case  Z_AXIS :
        DomainX = axis_Size*aspect_ratio_x_z;// Lz / beta
        DomainY = DomainX * aspect_ratio_y_x;// Lx *alpha
        DomainZ = axis_Size;
        this -> ComfigToLBMDomain (_pos,DomainX, DomainY , DomainZ,ProcAabb);
        break;

    default: //throw an error
        InvalidArgument (__FILE__,__LINE__,"Unknown scale directions." ) ;
    }
    SetBoundingBoxOfMeshObject ( *m_stl_mesh) ;
    m_stl_mesh -> HaveBeenPreProc() = true ;
}

//======================================================================
template<class Conf>
inline void RigidBody<Conf>::ComfigToLBMDomain(const typename RigidBody<Conf>::point_type& _pos ,// pos
					       const typename RigidBody<Conf>::real& DomainX,
					       const typename RigidBody<Conf>::real& DomainY,
					       const typename RigidBody<Conf>::real& DomainZ,
                                               const typename RigidBody<Conf>::aabb_type& ProAabb
                                               )
{
    ParallelScaleObjectToLatticeDomain
            (
                *m_stl_mesh,
                _pos       ,
                DomainX    ,
                DomainY    ,
                DomainZ    ,
                ProAabb
                );
    SetBoundingBoxOfMeshObject ( *m_stl_mesh) ;
    m_stl_mesh -> HaveBeenPreProc() = true ;
}
//======================================================================
#endif









//======================================================================
// set this object to use second order bouzidi BCs
// note that the costruct set this to falsem i.e. any rigid body is autamaticlly
// by default set to not use any second ordeeeeeeer BDs.
//
//======================================================================
template<class Conf>
inline bool& RigidBody<Conf>::SetObjectToUseBouzidiBCs()
{
    return m_ObjectWithSecondOrderBZBCs ;
}

template<class Conf>
inline const bool& RigidBody<Conf>::SetObjectToUseBouzidiBCs()const
{
    return m_ObjectWithSecondOrderBZBCs ;
}
// @}
// =====================================================================


template<class Conf>
inline const bool RigidBody<Conf>::IsInsideObjectBoundingBox(const point_type& P)const
{
    return (m_stl_mesh -> IsInsideObjectBoundingBox(P) );
}

// with extra bb
template<class Conf>
inline const bool RigidBody<Conf>::IsInsideObjectExtraBoundingBox(const point_type& P)const
{
    return (m_stl_mesh -> IsInsideObjectExtraBoundingBox(P) );
}


// =====================================================================
// this function clipp an given rigidBody with respect to an aabb,
// if the return is true, ResultPolygCut will contains a list of all triangles
// cutted to this aabb,
// else no thing will be return
// =====================================================================
//	template<class Conf>
//	inline bool RigidBody<Conf>::ClippToAabb(const typename RigidBody<Conf>::aabb_type& aa_m,
//					 typename RigidBody<Conf>::polygons_list& ResultPolygCut)const
//	{
//		typedef typename RigidBody<Conf>::object_clipper	clipper;
//		// no copy is needed
//		const mesh_type& obj = *( this-> GetObject());
//		if ( clipper::Clipp( obj , aa_m , ResultPolygCut) )
//			return true ;
//		else
//			return false;
//	}



template<class Conf>
inline const typename RigidBody<Conf>::real RigidBody<Conf>::Radios()const
{
    typedef typename RigidBody<Conf>::vector_type vector_type ;
    typedef typename RigidBody<Conf>::real real;
    const vector_type diag = (this->AabbMax()- this->AabbMin() );

    return 0.5 * diag [0] * diag[1] * diag [2];
}


template<class Conf>
inline void RigidBody<Conf>::addForceAtPos(
        const typename RigidBody<Conf>::real& Fx,
        const typename RigidBody<Conf>::real& Fy,
        const typename RigidBody<Conf>::real& Fz,
        const typename RigidBody<Conf>::real& pos_x,
        const typename RigidBody<Conf>::real& pos_y,
        const typename RigidBody<Conf>::real& pos_z)
{
    typedef typename RigidBody<Conf>::real        real ;
    typedef typename RigidBody<Conf>::point_type   point_type ;
    typedef typename RigidBody<Conf>::vector_type  vector_type;

    /*
        ApplyingForces(object_conf& object,
               const typename object_conf::vector_type& force ,
               const typename object_conf::point_type& where
               )*/

    //ApplyingForces ( &(*this) , vector_type (Fx,Fy,Fz) , point_type (pos_x,pos_y,pos_z)  ) ;

#ifdef RIGIDBODY_DEBUG
    //std::cout<< "\tFx,Fy,Fz\t : " << Fx <<","<< Fy << "," << Fz <<"\n" ;
    //std::cout<< "\t BEFORE : m_Force[0],m_Force[1],m_Force[2]\t : " << m_Force[0] <<","<< m_Force[1] << "," << m_Force[2] <<"\n" ;
#endif

    this->m_Force[0] +=  Fx;
    this->m_Force[1] +=  Fy;
    this->m_Force[2] +=  Fz;

#ifdef RIGIDBODY_DEBUG
    //std::cout<< "\t AFTER : m_Force[0],m_Force[1],m_Force[2]\t : " << m_Force[0] <<","<< m_Force[1] << "," << m_Force[2] <<"\n" ;
    //std::cout<< "\t------> The new force value : "  << this-> m_Force << "\t" ;
#endif
    //typename object_conf::vector_type temp = ( where )-( object.GetCOM() ) ;
    // 		const typename object_conf::real x = where.X() -  object.GetCOM().X() ;

    // update the total tork , and not accumulate it
    const vector_type tmp_2  (pos_x - (this->GetCOM()).X()  ,
                              pos_y - (this->GetCOM()).Y()  ,
                              pos_z - (this->GetCOM()).Z()     ) ;
    //		this->Tau() +=  Math::Cross( point_type (pos_x,pos_y,pos_z) - (this->GetCOM()) ,  vector_type (Fx,Fy,Fz)  );
    this->Tau() +=  Math::Cross(  tmp_2  , vector_type (Fx,Fy,Fz)  );

}


template<class Conf>
inline void RigidBody<Conf>::ReComputeObjectInertiaMomentsAndCOM()
{
    ComputeObjectInertiaMomentsAndCOM(*m_stl_mesh) ;
}




template<class Conf>
inline bool& RigidBody<Conf>::SwitchComputeForces()
{
    return m_compute_Forces;
}

template<class Conf>
inline const bool& RigidBody<Conf>::SwitchComputeForces()const
{
    return m_compute_Forces;
}


template<class Conf>
inline void RigidBody<Conf>::AddGravitationalField(
        const typename RigidBody<Conf>::real& gx_L,
        const typename RigidBody<Conf>::real& gy_L,
        const typename RigidBody<Conf>::real& gz_L)
{
    typedef typename RigidBody<Conf>::real real ;
    const real volume = this->m_mass ;
}




// ================================================================================
//
// ================================================================================
template<class Conf>
inline void RigidBody<Conf>::PlotPressureCoefficient (const int& object_id,
                                                      const int& time_step,
                                                      const double& dx,
                                                      const double& dt,
                                                      walberla::SimData& sim ,
                                                      walberla::PatchField& patchField_
                                                      )
{
    std::ifstream outfile;
    int m_n = -1 ;
    int m_R = -1 ;
    // circle center
    double m_center_x = -1; double m_center_y = -1; double m_center_z = -1;
    const double rho_infini     =  sim.rho ; // SI
    double u_infini_phys  = -1; // m/s

    std::stringstream tmp;
    tmp << "object_cp_"<< object_id << ".dprm";


    try
    {
        outfile.open (tmp.str().c_str() );
        if (outfile.is_open())
        {
            while ( outfile.good() )
            {
                std::string line , ttt ;
                std::getline (outfile,line);
                if (line.empty()) continue ;
                std::string t("//");

                if (line.compare(0, t.length(), t) == 0){
                    continue ;
                }

                std::istringstream tmp (line) ;

                //std::cout << "line : \t" <<line <<  "\n";
                if (line.find("m_n") != string::npos)                  { tmp >> ttt >> m_n; continue ;}
                if (line.find("m_R") != string::npos)                  { tmp >> ttt >> m_R; continue ;}
                if (line.rfind("m_center_x") != string::npos)          { tmp >> ttt >> m_center_x; continue ;}
                if (line.rfind("m_center_y") != string::npos)          { tmp >> ttt >> m_center_y; continue ;}
                if (line.rfind("m_center_z") != string::npos)          { tmp >> ttt >> m_center_z; continue ;}
                if (line.rfind("rho_infini") != string::npos)          { 
		  continue ;
		}
                if (line.rfind("u_infini_phys") != string::npos)       { tmp >> ttt >> u_infini_phys; continue ;}
            }
            outfile.close();
        }
        else
        {
            throw runtime_error ("Error opening.");
        }

        if (m_n == -1 ||  m_R == -1    || m_center_x == -1  ||
                m_center_y == -1           || m_center_z == -1  ||
                rho_infini == -1           ||  u_infini_phys == -1 )
            throw runtime_error ("Wrong parammeter!!! I can´t plot pressure coeffficients.");

    }
    catch (std::exception& e)
    {
        std::cout << "exception caught: " << e.what() << std::endl;
    }




    const double theta  = walberla::devide (m_n) ;
    std::vector< walberla::Cell > brd_cells;

    walberla::Bordercells (theta,m_center_x ,m_center_y,m_center_z, m_R,brd_cells ) ;

    std::cout<<"Pressure Coeff brder cells container--> size() : " << brd_cells.size() << std::endl;

    if( time_step == 0  )
        ExportToUnstructuredGrid (brd_cells , "PressureCoeff.vtk") ;


    // without scale units
    const double pressure_infi   = 1./3. * rho_infini ; // Cylinder
    const double Coeff =  0.5 * rho_infini *(u_infini_phys * u_infini_phys)  ;//*(1.01325E05);
    //const double u_infini_phys   =  ( u_infini_L * dx)  / (dt)  ;




    walberla::PressurCoefficient (time_step,theta,brd_cells, sim, patchField_ , Coeff ,pressure_infi , (dt) , (dx) ) ;
}






// ================================================================================
// plot drag coefficient
// ================================================================================
template<class Conf>
inline void RigidBody<Conf>::ComputeDragCoefficient ( const int& object_id,
                                                      const int& time_step,
                                                      const double& dx,
                                                      const double& dt,
                                                      walberla::SimData& sim ,
                                                      walberla::PatchField& patchField_
                                                      )
{

    std::ifstream outfile;
    double D = -1 ;
    double H = -1 ;
    // circle center
    const double rho_infini     =  sim.rho ; // SI
    double u_infini_phys  = -1; // m/s
    vector_type tmp_dir  ;
    double x ,y ,z ;
    std::stringstream tmp;
    tmp << "object_cd_"<< object_id << ".dprm";
    
    try
    {
        outfile.open (tmp.str().c_str() );
        if (outfile.is_open())
        {
            while ( outfile.good() )
            {
                std::string line , ttt , tttttt ;
		
                std::getline (outfile,line);
		
                if (line.empty()) continue ;
                std::string t("//");
                if (line.compare(0, t.length(), t) == 0){ continue ;}
                std::istringstream tmp_line (line) ;

		// std::cerr << "---> line \t " << line << "\n" ;
                if (line.find("D ") != string::npos)                  { tmp_line >> ttt >> D ;
// 		  std::cerr << "ttt\t" << ttt << "\tD\t" << D << "\n" ;
		  continue ;}
                if (line.find("H") != string::npos)                  { tmp_line >> ttt >> H; continue ;}
//                 if (line.rfind("rho_infini") != string::npos)        { tmp_line >> ttt >> rho_infini; continue ;}
if (line.rfind("rho_infini") != string::npos)   {  continue ;}
                if (line.rfind("u_infini_phys") != string::npos)     { tmp_line >> ttt >> u_infini_phys; continue ;}
                
                

                // cerr << "line " <<  line <<  "\n" ;
		
                if (line.rfind("dragDir") != string::npos)     {
// // 		  cerr << "dragDir" << tmp_dir <<  "\n" ;
		  tmp_line >> tttttt >> 
		  tttttt >> tmp_dir.x()  >> 
 		  tttttt >> tmp_dir.y()  >> 
		  tttttt >> tmp_dir.z()  >>   
		  tttttt    ;
		 
// 		  cerr << "dragDir" << tmp_dir <<  "\n" ; ///FIXME error reading the dragDir --> dragDir<0, 0, 0.0140145>
		  
		  continue ;
		  
		}
            }
            outfile.close();
        }
        else
        {
            throw runtime_error ("Error opening.");
        }

        if (D == -1  ||    H == -1            ||  u_infini_phys == -1 )
            throw runtime_error ("Wrong parammeter!!! I can´t write drag coeffficients data.");

    }
    catch (std::exception& e)
    {
        std::cout << "exception caught: " << e.what() << std::endl;
    }

    
    
//     std::cerr << " D\t" << D << "\tH\t" << H << "\tu_infini_phys\t"  <<u_infini_phys << "\trho_infini \t" <<  rho_infini << "\n" ;
    // without scale units
    const double Coeff          =  0.5 * rho_infini *(u_infini_phys * u_infini_phys) * D *H  ;


    const std::string Results_dir_name  ("Results") ;
    if ( !boost::filesystem::exists( Results_dir_name + "/dragCoeff" ) )
        boost::filesystem::create_directories(Results_dir_name + "/dragCoeff"  );

    std::stringstream fn ;
    fn    << Results_dir_name <<  "/dragCoeff/"<< "object_"<<object_id << ".txt" ;

    	  

    const double lbm_to_SI_coeff = (dx*dx*dx*dx) /(dt*dt) ;
    
    if (time_step ==0 )
    {
		std::ofstream f( fn.str().c_str() , std::ios_base::out | std::ios_base::app ) ;
		if (f.is_open())
		{
			f << "# time_step\t (cd) \t (cd)physic \n #lbm_to_SI_coeff =  (dx^4) /(dt^2) : "<< lbm_to_SI_coeff << "\n" ;
		}
    }
    
    
#ifdef GE_USE_MPI
       int rank, size ;
       const int MASTER =0 ;

        /* Determine the sender and receiver */
        MPI_Comm_rank( MPI_COMM_WORLD, &rank );
        MPI_Comm_size( MPI_COMM_WORLD, &size );

	if (rank == MASTER)
	{
		std::ofstream f( fn.str().c_str() , std::ios_base::out | std::ios_base::app ) ;


		if (f.is_open())
		{
		    f << time_step *dt<< 
		    "   " <<  (this-> F()[2] )/ ( Coeff )  <<    // lbm scale
		    "   " <<  (this-> F()[2] * lbm_to_SI_coeff )/ ( Coeff )  <<   "\n"; // physic scale

		    f.close() ;
		}else{
		    throw runtime_error ("Error opening in file : " + boost::lexical_cast<std::string> (__FILE__)+
					",line : " +
					boost::lexical_cast<std::string> (__LINE__)  );
		}
	}
#else
    std::ofstream f( fn.str().c_str() , std::ios_base::out | std::ios_base::app ) ;
        


    if (f.is_open())
    {
       //  std::cerr << " F()[2] \t " <<    F()[2] << "\t Coeff \t " <<  Coeff <<  "\n";
        f << time_step *dt<< 
	"   " <<  (this-> F()[2] )/ ( Coeff )  <<    // lbm scale
	"   " <<  (this-> F()[2] * lbm_to_SI_coeff )/ ( Coeff )  <<   "\n"; // physic scale

        f.close() ;
    }else{
        throw runtime_error ("Error opening in file : " + boost::lexical_cast<std::string> (__FILE__)+
                             ",line : " +
                             boost::lexical_cast<std::string> (__LINE__)  );
    }
#endif

}




// ================================================================================
// plot lift coefficient
// ================================================================================
template<class Conf>
inline void RigidBody<Conf>::ComputeLiftCoefficient (const int& object_id,
                                                      const int& time_step,
                                                      const double& dx,
                                                      const double& dt,
                                                      walberla::SimData& sim ,
                                                      walberla::PatchField& patchField_
                                                      )
{
    std::ifstream outfile;
    double D = -1 ;
    double H = -1 ;    
    // circle center
    const double rho_infini     =  sim.rho ; // SI
    double u_infini_phys  = -1; // m/s
    std::stringstream tmp;
    tmp << "object_cl_"<< object_id << ".dprm";
    try
    {
        outfile.open (tmp.str().c_str() );
        if (outfile.is_open())
        {
            while ( outfile.good() )
            {
                std::string line , ttt ;
                std::getline (outfile,line);
                if (line.empty()) continue ;
                std::string t("//");
                if (line.compare(0, t.length(), t) == 0){ continue ;}
                std::istringstream tmp (line) ;
                if (line.find("D ") != string::npos)                  { tmp >> ttt >> D; continue ;}
                if (line.find("H") != string::npos)                  { tmp >> ttt >> H; continue ;}
                if (line.rfind("rho_infini") != string::npos)      
		{ 
		   continue ;
		}
                if (line.rfind("u_infini_phys") != string::npos)       { tmp >> ttt >> u_infini_phys; continue ;}
            }
            outfile.close();
        }
        else
        {
            throw runtime_error ("Error opening.");
        }

        if (D == -1  ||   H == -1  ||    rho_infini == -1           ||  u_infini_phys == -1 )
            throw runtime_error ("Wrong parammeter!!! I can´t export lift coeffficients data.");

    }
    catch (std::exception& e)
    {
        std::cout << "exception caught: " << e.what() << std::endl;
    }

    // scale unit coeff
    const double Coeff          =  0.5 * rho_infini *(u_infini_phys * u_infini_phys) * D  * H ;

    const std::string Results_dir_name  ("Results") ;
    if ( !boost::filesystem::exists( Results_dir_name + "/liftCoeff" ) )
        boost::filesystem::create_directories(Results_dir_name + "/liftCoeff"  );

    std::stringstream fn ;
    fn    << Results_dir_name <<  "/liftCoeff/"<< "object_"<<object_id << ".txt" ;

    
    const double lbm_to_SI_coeff = (dx*dx*dx*dx) /(dt*dt) ;
    if (time_step ==0 )
    {
		std::ofstream f( fn.str().c_str() , std::ios_base::out | std::ios_base::app ) ;
		if (f.is_open())
		{
			f << "# time_step[s]\t (cl) \t (cl)physic\n#lbm_to_SI_coeff =  (dx^4) /(dt^2) : "<< lbm_to_SI_coeff << "\n" ;
		}
    }
        
    if (time_step ==0 )
    {
		std::ofstream f( fn.str().c_str() , std::ios_base::out | std::ios_base::app ) ;
		if (f.is_open())
		{
			f << "# time_step\t physic Force\n" ;
		}
    }
    
    
    std::ofstream f( fn.str().c_str() , std::ios_base::out | std::ios_base::app ) ;

    if (f.is_open())
    {
        f << time_step*dt << 
	"   " <<  (this-> F()[1] )/ ( Coeff )   <<   // lbm scale 
	"   " <<  (this-> F()[1] * lbm_to_SI_coeff)/ ( Coeff ) <<   "\n"; // physic scale

        f.close() ;
    }else{
        throw runtime_error ("Error opening in file : " + boost::lexical_cast<std::string> (__FILE__)+
                             ",line : " +
                             boost::lexical_cast<std::string> (__LINE__)  );
    }
}




} // end namespace ge 

#endif

