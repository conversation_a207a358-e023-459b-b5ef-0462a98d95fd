#include <iostream>
#include <list>
// =============================================================================
/*!
//  \brief Nested class technique can be used to implemete nice c++ iterator
//  \brief (const_iterator) as its explained by this example.
// \author Cherif.Mihoubi
*/
// =============================================================================

template<typename ElementType = int>
struct SampleConf
{
	typedef ElementType element_type ;
	typedef std::list<element_type >			container_type;
	typedef std::list<element_type >::iterator		Iterator;
	typedef std::list<element_type >::const_iterator	Const_Iterator;  
};


// =============================================================================
// Container class implemetation 
// =============================================================================
template<typename config_ = SampleConf<> >
class X
{
  public:
	// =====================================================================
	// typedefs
	// =====================================================================  
	typedef config_   configure ;
	typedef typename  configure::element_type	element_type;
	typedef typename  configure::container_type	container_type;
	typedef typename  configure::Iterator		Iterator;
	typedef typename  configure::Const_Iterator	Const_Iterator;
	// =====================================================================
  private:
	container_type  _list; //->std::list<int>
  public :
	class iterator ;
	class const_iterator ;
  public:
	// =====================================================================
	// public member functions 
	// =====================================================================
	inline iterator begin();
	inline void push_back(const element_type &);//Inserts element after the last  
  public:
	// =====================================================================
	// Nested class definition 
	// =====================================================================
	class iterator
	{
	   public:
		// =============================================================
		// Constructor 
		// =============================================================
		iterator(const std::list<int>::iterator& _i   =std::list<int>::iterator(),
			 const std::list<int>::iterator& _last=std::list<int>::iterator()
			) : i(_i), last(_last)
			{}
			
		// =============================================================
		inline int& operator* () const{ return *i; }
		// =============================================================
	   private:
		std::list<int>::iterator i   ;
		std::list<int>::iterator last;
	};
	// =====================================================================
  private :
	int m_num ;
};


template <typename Conf>
inline typename X<Conf>::iterator X<Conf>::begin()
{
	return iterator (_list.begin(), _list.end() );
}

template <class Conf>
inline void X<Conf>::push_back(const typename X<Conf>::element_type & in)//Inserts element after the last 
{
	_list.push_back (in);
}

// =============================================================================
// main function
// =============================================================================
int main ()
{
	X<> *m_x =  new X<>( ) ; // container 
	X<>::iterator it ;	 // container iterator, you can also extend by 
				 // const_iterator 
     
     
	m_x -> push_back (2)   ; 
	m_x -> push_back (20)  ;
	m_x -> push_back (200) ;
    
	it = m_x -> begin() ;
    

	std::cout<< *it << std::endl ;
    
	return (0) ;
}
// =============================================================================



