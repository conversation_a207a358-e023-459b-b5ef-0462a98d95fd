//=================================================================================================
/*!
//  \file SimData.h
//  \brief Contains the SimData struct
//  \author <PERSON>
 */
//=================================================================================================

#ifndef _WALBERLA_SIM_DATA_H
#define _WALBERLA_SIM_DATA_H
#include "Definitions.h"
#include <sstream>

namespace walberla{

   /*!\brief Contains all lattice data parameters
    *
    * The simulation can be done by adding a simdata block to the parameter file with the following parameters:
    * - domainX   //the size of the grid in x direction in lattice cells (number of inner cells to be computed)
    * - domainY   //the size of the grid in y direction in lattice cells (number of inner cells to be computed)
    * - domainZ   //the size of the grid in z direction in lattice cells (number of inner cells to be computed)
    * - timesteps //the number of timesteps
    * - dt_L      //lattice timestep delta t
    * - dx_L      //lattice cell size delta x
    * - nu_L      //lattice viscosity
    * - gx_L      //lattice gravitational force in x direction
    * - gy_L      //lattice gravitational force in y direction
    * - gz_L      //lattice gravitational force in z direction
    * - Re        //the dimensionless Reynolds number
    * - omega     //omega value of the simulation
    * - tau       //tau
    * - nu        //viscosity
    * - rho       //density of the fluid
    * - xLength   //the size of the grid in x direction
    * - yLength   //the size of the grid in y direction
    * - zLength   //the size of the grid in z direction
    * - dx        //cell size delta x
    * - dt        //timestep delta t
    * - ux        //inflow velocity in x direction
    * - uy        //inflow velocity in y direction
    * - uz        //inflow velocity in z direction
    * - gx        //gravitational force in x direction
    * - gy        //gravitational force in y direction
    * - gz        //gravitational force in z direction
    * An example can be found in \ref SimData.par
    */
   struct SimData{
      /*!\name Lattice parameters */
      //@{
      SimData(){domainX=0;domainY=0;domainZ=0;timesteps=0;xLength=0;yLength=0;zLength=0;dx=0;dt=0;gx=0;gy=0;gz=0;

      numFluidCells=0;xNumPatches=0;yNumPatches=0;zNumPatches=0;MOBouzidi=false;compressible=0;itaniumOptimizationLevel=0;g_init=false;useFiniteDifference=false;}

      //! the size of the grid in x direction in lattice cells (without boundaries)
      //! It is always the size of the whole domain not the size of the patches.
      Uint domainX;
      //! the size of the grid in y direction in lattice cells (without boundaries)
      //! It is always the size of the whole domain not the size of the patches.
      Uint domainY;
      //! the size of the grid in z direction in lattice cells (without boundaries)
      //! It is always the size of the whole domain not the size of the patches.
      Uint domainZ;
      //! the number of timesteps
      Uint timesteps;
      //! lattice timestep delta t
      Real dt_L;
      //! lattice cell size delta x
      Real dx_L;
      //! lattice viscosity
      Real nu_L;
      //! lattice gravitational force in x direction
      Real gx_L;
      //! lattice gravitational force in y direction
      Real gy_L;
      //! lattice gravitational force in z direction
      Real gz_L;
      //! lattice speed of sound
      Real cs;

      //! smagorinsky constant (for turbulence model)
      //! cD used for turbulent kinetic energy (cD=0.1)
      //! cE used for turbulent dissipation energy ( 0.9 <= cE <= 1.1)
      Real csmag;
      Real cD , cE ;
      //! whether to use finite difference method for strain rate computation
      bool useFiniteDifference;
      //@}
      /*!\name Physical parameters */
      //@{
      //! the dimensionless Reynolds number
      Real Re;
      //! omega value of the simulation
      Real omega;
      //! tau
      Real tau;
      //! viscosity
      Real nu;
      //! density
      Real rho;
      //! the size of the grid in x direction
      Real xLength;
      //! the size of the grid in y direction
      Real yLength;
      //! the size of the grid in z direction
      Real zLength;
      //! cell size delta x
      Real dx;
      //! timestep delta t
      Real dt;
      //! gravitational force in x direction
      Real gx;
      //! gravitational force in y direction
      Real gy;
      //! gravitational force in z direction
      Real gz;
      //!< Number of patches in x direction
      Uint xNumPatches;
      //! Number of patches in y direction
      Uint yNumPatches;
      //! Number of patches in z direction
      Uint zNumPatches;
      //! Overall number of fluid cells in the domain -> For benchmarking
      unsigned long int numFluidCells;
      //! the largest obstacle Uid +1
      Uint maxNumObstacleID;
      //! Will use Bouzidi for moving obstacles if set to true
      bool MOBouzidi;
      int compressible;
      int itaniumOptimizationLevel;
      bool g_init;
      Real prsMax_;
      Real prsMin_;
      //@}
      
      #  ifdef  USE_STL_GE
   public:
	// the pos of the stl bounding box in the lattice domain
	Uint STLobjekt_pos_x  ;
	Uint STLobjekt_pos_y  ;
	Uint STLobjekt_pos_z  ;
	Uint STLObjectdomainX ;// the "x" lattice size triangle mesh "scaled" 
	Uint STLObjectdomainY ;// the "y" lattice size triangle mesh "scaled" 
	Uint STLObjectdomainZ ;// the "z" lattice size triangle mesh "scaled" 
	// seting of the flage field of the stl trianglulation mesh
	Uint insideflag;
	Uint outsideflag;
	Uint boundaryflag;
	
	Uint NumberOfSeeds ; //  number od seeds points for the flood field
	// TODO, its tompory solution ,,, in case only of seed one (1 patch, i mpi-process),
	Uint Seed_x ;
	Uint Seed_y ;
	Uint Seed_z ;	
	std::string TMeshToRowfileName ; //the output filename, after voelization the trinagle-mesh, 
	//				this is the file name of the row-data.
	
      #endif

      std::string ToString(){
         std::ostringstream oss;
         oss << " SimData.h:Parameters:" << std::endl;
         oss << "---------------------" << std::endl;
         oss << " dt      "<< dt << std::endl;
         oss << " dx      "<< dx << std::endl;
         oss << " tau     "<< tau << std::endl;
         oss << " omega   "<< omega << std::endl;
         oss << " nu      "<< nu << std::endl;
         oss << " rho     "<< rho << std::endl;
         oss << " Re      "<< Re << std::endl;
         oss << " gx      "<< gx << std::endl;
         oss << " gy      "<< gy << std::endl;
         oss << " gz      "<< gz << std::endl;
         oss << " xLength "<< xLength << std::endl;
         oss << " yLength "<< yLength << std::endl;
         oss << " zLength "<< zLength << std::endl;
         oss << "---------------------" << std::endl;
         oss << " dt_L    "<< dt_L << std::endl;
         oss << " dx_L    "<< dx_L << std::endl;
         oss << " nu_L    "<< nu_L << std::endl;
         oss << " gx_L    "<< gx_L << std::endl;
         oss << " gy_L    "<< gy_L << std::endl;
         oss << " gz_L    "<< gz_L << std::endl;
         oss << " domainX "<< domainX << std::endl;
         oss << " domainY "<< domainY << std::endl;
         oss << " domainZ "<< domainZ << std::endl;
	 oss << " numFluidCells "<< numFluidCells << std::endl;
	 if(compressible)
	    oss << " compressible " << std::endl;
         if(MOBouzidi)
            oss << " Bouzidi Boundary Conditions for Moving Obstacles active!" << std::endl;

         oss << "---------------------" << std::endl;
         return oss.str();
      }
   };

    # ifdef  USE_STL_GE
       static bool STL_BLOCK_EXIST_IN_PARM ;// to indicate if there is stl-block
                                            // or in the given param file.
    #endif
}




#endif

