#include <iostream>
#include "MyClass.h"


class X {};

struct ConfigureSamle{
      typedef X ElementType ;
};



class toy// A trivial class to use for the example
{
    public:
        int x;
};
// =============================================================================
/*!		@brief programm test 					      */
// =============================================================================
int main()
{
  
    typedef MyClass<ConfigureSamle > my_class  ;
    
  
    
    toy **tPoint; // a pointer to a pointer to an object from the "toy" class
    tPoint = new toy *[33]; // use as an array of 33 pointers to "toy" objects
    
    
//     toy *tArray[100];       // an array of 100 pointers to "toy" objects
    
    int i;

    for (i = 0; i < 5; i++) 
    {
        tPoint[i]    = new toy;
        tPoint[i]->x = 10*i;
//         tArray[i] = new toy;
//         tArray[i]->x = i;
    }

    
    
     
     for (i = 0; i < 5; i++) {
        std::cout << "tPoint[" << i << "]->x = " << tPoint[i]->x
	  //	              << ", tArray[" << i << "]->x = " << tArray[i]->x
             << std::endl;
     }
    return (0) ;
}

 
