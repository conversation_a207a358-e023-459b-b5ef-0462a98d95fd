/*******************************************************************************
ConminC++, to solve constrained or unconstrained function minimization.
Copyright (C) 1999  <PERSON><PERSON> <PERSON><PERSON> Dunn

This program is free software; you can redistribute it and/or
modify it under the terms of the GNU General Public License
as published by the Free Software Foundation; either version 2
of the License, or (at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this program; if not, write to the Free Software
Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA  02111-1307, USA.
*******************************************************************************/

//---------------------------------------------------------------------------
// parameters :
const static int N_x = 200  ;
const static int N_y = 100  ;
const static int N_P = 4    ;
const static double EPSILON = 0.00001    ;
const static double EPSILON2 = 0.001    ;
// for multi objectives optimization , we define two coefficients 
const double w_1 = 1.0 ;
const double w_2 = 1.0 ;


#include "main_analysis.h"
#include <math.h>
//---------------------------------------------------------------------------





double Ex1_Analysis(double *x, double *g)
{
	// Routine to calculate objective(s) function and
	// constraints
	const double aobj_1 =  2.0 *( N_y*( x[1] -1.0) + N_x*( x[2] - 1.0) ) ;
	const double aobj_2 =  2.0 *( x[2]*( x[1] -1.0) + x[1]*( x[2] - 1.0) ) ;

	//
	//
	// Constraint values
	// total number of processors should be equal to np
	g[1] = N_P - EPSILON - ( x[1] * x[2] ) ;
	g[2] = (x[1] * x[2] )- N_P - EPSILON ;

	// x1 and x2 should be integer, that mean x1%2 and x2%2 should equal to zero
	//g[3] = - EPSILON2 - ( fmod( x[1],2) ) ;
	//g[4] = ( fmod ( x[1], 2 )  )-  EPSILON2 ;
	


	const double aobj = w_1 * aobj_1 + w_2 *aobj_2 ;
	return aobj;
}
