//=================================================================================================
/*!
//  \file TurbulenceModel.h
//  \brief Base interface for all turbulence models in waLBerla
//  \author Enhanced turbulence module
*/
//=================================================================================================

#ifndef _WALBERLA_TURBULENCE_MODEL_H
#define _WALBERLA_TURBULENCE_MODEL_H

#include "../Definitions.h"
#include "../PDFField.h"
#include "../VelField.h"
#include "../DensField.h"
#include "../Vector3.h"
#include "../Matrix3.h"
#include "../SimData.h"

namespace walberla {
namespace turbulence {

//=================================================================================================
//
//  TURBULENCE MODEL BASE CLASS
//
//=================================================================================================

/*!\brief Abstract base class for all turbulence models
//
// This class provides the interface that all turbulence models must implement.
// It supports both incompressible and compressible formulations, various collision
// operators (BGK, MRT, TRT, Cumulant), and different approaches for computing
// eddy viscosity.
*/
class TurbulenceModel
{
public:
    //**Type definitions****************************************************************************
    enum ModelType {
        SMAGORINSKY,
        DYNAMIC_SMAGORINSKY,
        WALE,
        VREMAN,
        MIXED_SCALE,
        NONE
    };
    
    enum StrainRateMethod {
        NON_EQUILIBRIUM,    // Compute from non-equilibrium PDFs
        FINITE_DIFF,        // Compute from velocity gradients
        HYBRID             // Use both methods
    };

    //**Constructor & Destructor********************************************************************
    TurbulenceModel() : modelType_(NONE), strainRateMethod_(NON_EQUILIBRIUM) {}
    virtual ~TurbulenceModel() = default;

    //**Pure virtual functions**********************************************************************
    /*!\name Pure virtual functions - must be implemented by derived classes */
    //@{
    
    /*!\brief Compute the effective relaxation time (tau_eff) including turbulence effects
    //
    // \param tau0 The molecular relaxation time
    // \param x,y,z Cell coordinates
    // \param rho Density at the cell
    // \param vel Velocity at the cell
    // \param pdf PDF field (for non-equilibrium method)
    // \param velField Velocity field (for finite difference method)
    // \param sim Simulation parameters
    // \return The effective relaxation time including turbulent effects
    */
    virtual Real computeEffectiveTau(
        const Real& tau0,
        const Uint x, const Uint y, const Uint z,
        const Real& rho,
        const Vector3<Real>& vel,
        const PDFField& pdf,
        const VelField& velField,
        const SimData& sim) const = 0;
    
    /*!\brief Compute the turbulent eddy viscosity
    //
    // \param x,y,z Cell coordinates
    // \param rho Density at the cell
    // \param vel Velocity at the cell
    // \param pdf PDF field
    // \param velField Velocity field
    // \param sim Simulation parameters
    // \return The turbulent eddy viscosity (nu_t)
    */
    virtual Real computeEddyViscosity(
        const Uint x, const Uint y, const Uint z,
        const Real& rho,
        const Vector3<Real>& vel,
        const PDFField& pdf,
        const VelField& velField,
        const SimData& sim) const = 0;
    
    /*!\brief Get the model constant (e.g., Smagorinsky constant)
    */
    virtual Real getModelConstant() const = 0;
    
    /*!\brief Set the model constant
    */
    virtual void setModelConstant(const Real& constant) = 0;
    
    //@}
    
    //**Virtual functions with default implementation***********************************************
    /*!\name Virtual functions with default implementation */
    //@{
    
    /*!\brief Compute strain rate tensor
    //
    // Default implementation provides both non-equilibrium and finite difference methods
    */
    virtual Matrix3<Real> computeStrainRateTensor(
        const Uint x, const Uint y, const Uint z,
        const Real& rho,
        const Vector3<Real>& vel,
        const PDFField& pdf,
        const VelField& velField,
        const Real& tau,
        Real& strainRateMagnitude) const;
    
    /*!\brief Compute strain rate magnitude
    */
    virtual Real computeStrainRateMagnitude(const Matrix3<Real>& S) const;
    
    /*!\brief Apply wall damping function (for near-wall treatment)
    */
    virtual Real applyWallDamping(const Real& eddyViscosity, const Real& yPlus) const {
        return eddyViscosity; // Default: no damping
    }
    
    /*!\brief Compute turbulent kinetic energy (if applicable to model)
    */
    virtual Real computeTurbulentKineticEnergy(
        const Matrix3<Real>& strainRateTensor,
        const Real& eddyViscosity) const {
        return 0.0; // Default: not computed
    }
    
    /*!\brief Compute turbulent dissipation rate (if applicable to model)
    */
    virtual Real computeTurbulentDissipation(
        const Real& turbulentKineticEnergy,
        const Real& lengthScale) const {
        return 0.0; // Default: not computed
    }
    
    //@}
    
    //**Access functions****************************************************************************
    /*!\name Access functions */
    //@{
    ModelType getModelType() const { return modelType_; }
    StrainRateMethod getStrainRateMethod() const { return strainRateMethod_; }
    void setStrainRateMethod(StrainRateMethod method) { strainRateMethod_ = method; }
    //@}
    
    //**Utility functions***************************************************************************
    /*!\name Utility functions */
    //@{
    
    /*!\brief Get model name as string
    */
    virtual std::string getModelName() const = 0;
    
    /*!\brief Initialize model-specific parameters
    */
    virtual void initialize(const SimData& sim) {}
    
    /*!\brief Check if model is suitable for compressible flows
    */
    virtual bool supportsCompressible() const { return true; }
    
    /*!\brief Check if model requires filtering operations
    */
    virtual bool requiresFiltering() const { return false; }
    
    //@}

protected:
    //**Protected utility functions*****************************************************************
    
    /*!\brief Compute non-equilibrium stress tensor (Pi^neq)
    */
    Matrix3<Real> computeNonEquilibriumStressTensor(
        const Uint x, const Uint y, const Uint z,
        const Real& rho,
        const Vector3<Real>& vel,
        const PDFField& pdf) const;
    
    /*!\brief Compute strain rate tensor using finite differences
    */
    Matrix3<Real> computeStrainRateTensorFD(
        const Uint x, const Uint y, const Uint z,
        const VelField& velField) const;
    
    /*!\brief Compute velocity gradient tensor
    */
    Matrix3<Real> computeVelocityGradient(
        const Uint x, const Uint y, const Uint z,
        const VelField& velField,
        const Real& dx = 1.0) const;

    //**Member variables****************************************************************************
    ModelType modelType_;
    StrainRateMethod strainRateMethod_;
};

//=================================================================================================
//
//  TURBULENCE MODEL FACTORY
//
//=================================================================================================

/*!\brief Factory class for creating turbulence models
*/
class TurbulenceModelFactory
{
public:
    /*!\brief Create a turbulence model based on type and parameters
    */
    static std::unique_ptr<TurbulenceModel> create(
        TurbulenceModel::ModelType type,
        const SimData& sim);
    
    /*!\brief Create a turbulence model from string name
    */
    static std::unique_ptr<TurbulenceModel> create(
        const std::string& modelName,
        const SimData& sim);
};

} // namespace turbulence
} // namespace walberla

#endif // _WALBERLA_TURBULENCE_MODEL_H