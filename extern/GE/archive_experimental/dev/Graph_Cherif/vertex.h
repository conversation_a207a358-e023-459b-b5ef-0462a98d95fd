#ifndef VERTEX_HH
#define VERTEX_HH

#include <map>
#include <iostream>
#include <string>
#include <cmath>

#include "Vec3f.h"
#define eps_ 0.000001
#define Uint int

struct vertex
{
	float m_x , m_y , m_z;
	vertex (): m_x( 0.0 ), m_y( 0.0 ), m_z( 0.0 ){} // default constructor
	
	vertex(const float& x,const float& y,const float& z)
	: m_x( x )
	, m_y( y )
	, m_z( z )
	{}
	
	vertex (const vertex& v): m_x( v.m_x ), m_y ( v.m_y ) , m_z ( v.m_z ) {}
	vertex (const Vec3f & v): m_x( v.x() ), m_y ( v.y() ) , m_z ( v.z() ) {}
   
	inline friend std::ostream& operator << (std::ostream& output,const vertex &v)
	{
		output << "vertex <"<<v.m_x<<" , "<<v.m_y<<" , "<<v.m_z<<"> ";
		return output;
	}
	
	//inline bool operator == (const vertex& v)const {
	//	   return (	(m_x == v.m_x) &&
	//		  (m_y == v.m_y) &&
	//		  (m_z == v.m_z)	); 
	//}
		
	//inline bool operator != (const vertex& v)const {
	//	   return (	(m_x != v.m_x) || 
	//		  (m_y != v.m_y) ||
	//		  (m_z != v.m_z)	); 
	//}
};


	// =====================================================================
	// compare operator
	// =====================================================================
	inline bool operator == (const vertex& v1,const vertex& v2 )
	{
		return( (fabs( v1.m_x - v2.m_x) <= eps_ ) &&
			(fabs( v1.m_y - v2.m_y) <= eps_ ) &&
			(fabs( v1.m_z - v2.m_z) <= eps_ )   );
	}
	
	
	//debug mode
	//return ( (fabs( v1.m_x -  v2.m_x) <= eps_ ) &&
	//		 (fabs( v1.m_y -  v2.m_y) <= eps_ ) &&
	//		 (fabs( v1.m_z -  v2.m_z) <= eps_ )   );
	//}



	// =====================================================================
	// compare functor
	// =====================================================================
	struct VertexCompare
	{
		inline bool operator()(const vertex& _v0,const vertex& _v1)const
		{
		//	if ( _v0.m_x == _v1.m_x ) 
		//	{
		//		if(  _v0.m_y == _v1.m_y ) 
		//		{
		//			return (_v0.m_z < _v1.m_z- eps_);
		//		} else 
		//			return (_v0.m_y < _v1.m_y - eps_);
		//	}
		//	else 
		//		return (_v0.m_x < _v1.m_x - eps_);
			if (fabs(_v0.m_x - _v1.m_x) <= eps_) 
			{
				if (fabs(_v0.m_y - _v1.m_y) <= eps_) 
				{
					return (_v0.m_z < _v1.m_z- eps_);
				} else 
					return (_v0.m_y < _v1.m_y - eps_);
			}else 
				return (_v0.m_x < _v1.m_x - eps_);
		}
	};



//=================================================
// binary IO utility 
//(Intel chips are little endian, Mac PowerPC or
//  G4/G5 are big-endian)
//=================================================
/// Read a 32-bit integer, endian independent.
inline static Uint ReadInt32(std::istream &inStream)
{
	unsigned char Bufer[4];
	inStream.read((char *) Bufer, 4);
	return ((Uint) Bufer[0]) | (((Uint) Bufer[1]) << 8) |
		 (((Uint) Bufer[2]) << 16) | (((Uint) Bufer[3]) << 24);
}

/// Read a vertex, endian independent.
inline vertex ReadVertex(std::istream &in )
{
	union{
		Uint i;
		float  f;
	} val;
	vertex result;
	val.i = ReadInt32(in);
	result.m_x = val.f;
	//cout<<"val.i = "<<val.i<<"\nval.f = "<<val.f<<"result.x"<<result.x<<endl;
	val.i = ReadInt32(in);
	result.m_y = val.f;
	val.i = ReadInt32(in);
	result.m_z = val.f;
	return result;
}










// struct coordinatecompare
// {
//	 /*inline*/ bool operator()(const coordinate& a ,const coordinate& b )const {
//		 return ( a.m_x == b.m_x ) ? ( a.m_y < b.m_y ) : ( a.m_x < b.m_x );
//	 }
//	 
//	 bool operator()( const coordinate& a ,const coordinate& b )const
//	 {
//		 return ( a.m_x == b.m_x ) ? ( a.m_y < b.m_y ) : ( a.m_x < b.m_x );
//	 }
//	 
// //   bool operator()( const coordinate& _v0, const coordinate& _v1 ) const
// //   {
// //	 if (fabs(_v0.x - _v1.x) <= eps_) 
// //	 {
// //	   if (fabs(_v0.y - _v1.y) <= eps_) 
// //	   {
// //	return (_v0.z < _v1.z- eps_);
// //	   }
// //	   else return (_v0.y < _v1.y - eps_);
// //	 }
// //	 else return (_v0.x < _v1.x - eps_);
// //   }
//	 
//	 
//	 
// };



#endif
