set term pdfcairo
set output "Spectrum.pdf"


set grid 
set logscale xy
set yrange [0.0001:]
set xrange [1:]


plot 	"graphs.mapNearest/0/Ek.xy" u 1:($2/2) w l lw 2 t "64^3 mapNearest" , \
	"graphs.cellPointInterpolate/0/Ek.xy" u 1:($2/2) w l lw 2 t "64^3 cellPointInterpolate" , \
	"graphs.interpolate/0/Ek.xy" u 1:($2/2) w l lw 2 t "64^3 interpolate" , \
	"../boxTurb128/graphs/0/Ek.xy" u 1:($2/2) w l lw 2 t "128^3 resolution"	





