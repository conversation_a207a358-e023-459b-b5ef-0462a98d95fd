// auto_ptr Is Unusual :
// =====================
// - RAII
// - a resource handle template that serves many resource handle needs
// - it overloads operator-> and operator *
// - it is very efficient (1)
// - its destructor will free whatever it is pointing to (2)
// - it behaves like a built-in pointer (3)


using std::auto_ptr;
auto_ptr aShape ( new Circle );
  aShape->draw();   // draw a Circle
(*aShape).draw();   // draw it again



auto_ptr aCircle( new Circle );
aShape = aCircle; // valid



// 2 Situations where auto_ptr should be avoided:
// =====================
// 1) Don’t use as container elements
// 2) auto_ptr should refer to a single element, not an array because when the
//	object which the auto_ptr refers is deleted, it will be deleted via 
//	operator delete, not array delete.

vector<auto_ptr > shapes; 		// likely error, bad idea
auto_ptr ints ( new int[32] );		// bad idea, no error (yet)

// - A standard vector or string is a reasonable alternative to an auto_ptr to
//  an array