//=================================================================================================
/*!
//  \file TurbulenceModelFactory.h
//  \brief Factory for creating turbulence models
*/
//=================================================================================================

#ifndef _WALBERLA_TURBULENCE_MODEL_FACTORY_H
#define _WALBERLA_TURBULENCE_MODEL_FACTORY_H

#include "../Definitions.h"
#include "../SimData.h"
#include "TurbulenceModel.h"
#include <memory>
#include <string>

namespace walberla {
namespace turbulence {

//=================================================================================================
//
//  TURBULENCE MODEL FACTORY
//
//=================================================================================================

/*!\brief Factory class for creating turbulence models
//
// This factory provides a centralized way to create different types of
// turbulence models based on either enum types or string names.
*/
class TurbulenceModelFactory
{
public:
    /*!\brief Create a turbulence model by type
    //
    // \param type The model type to create
    // \param sim Simulation data containing model parameters
    // \return Unique pointer to the created model, or nullptr if creation failed
    */
    static std::unique_ptr<TurbulenceModel> create(
        TurbulenceModel::ModelType type,
        const SimData& sim);
    
    /*!\brief Create a turbulence model by name
    //
    // \param modelName String name of the model (case-insensitive)
    // \param sim Simulation data containing model parameters
    // \return Unique pointer to the created model, or nullptr if creation failed
    //
    // Supported model names:
    // - "Smagorinsky" or "Smag"
    // - "Dynamic" or "Dynamic_Smagorinsky"
    // - "WALE"
    // - "Vreman"
    // - "Mixed" or "Mixed_Scale" (not yet implemented)
    // - "None" or "Laminar"
    */
    static std::unique_ptr<TurbulenceModel> create(
        const std::string& modelName,
        const SimData& sim);

private:
    // Private constructor to prevent instantiation
    TurbulenceModelFactory() = delete;
    TurbulenceModelFactory(const TurbulenceModelFactory&) = delete;
    TurbulenceModelFactory& operator=(const TurbulenceModelFactory&) = delete;
};

} // namespace turbulence
} // namespace walberla

#endif // _WALBERLA_TURBULENCE_MODEL_FACTORY_H
