#include <iostream>
#include "Functor.h"

using namespace std;

//-----------------------------------------------------------------------------------------
// 4.3 Example of How to Use Functors

// dummy class A
struct TClassA{
    TClassA(){};
    void Display(const char* text) { cout << text << endl; };
    /* more of TClassA */
};

// dummy class B
struct TClassB{
    TClassB(){};
    void Display(const char* text) { cout << text << endl; };
    /* more of TClassB */
};


// main program
int main(int argc, char* argv[])
{
	// 1. instantiate objects of TClassA and TClassB
	TClassA objA;
	TClassB objB;
	
	
	// 2. instantiate TSpecificFunctor objects ...
	//    a ) functor which encapsulates pointer to object and to member of TClassA
	TSpecificFunctor<TClassA> specFuncA(&objA, &TClassA::Display);
	
	//    b) functor which encapsulates pointer to object and to member of TClassB
	TSpecificFunctor<TClassB> specFuncB(&objB, &TClassB::Display);
	
	
	// 3. make array with pointers to Functor, the base class, and initialize it
	Functor* vTable[] = { &specFuncA, &specFuncB };
	
	
	// 4. use array to call member functions without the need of an object
	vTable[0]->Call("TClassA::Display called!");        // via function "Call"
	(*vTable[1])   ("TClassB::Display called!");        // via operator "()"
	
	
	// hit enter to terminate
	cout << endl << "Hit Enter to terminate!" << endl;
	cin.get();
  

	return 0;
}

