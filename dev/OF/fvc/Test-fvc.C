/*---------------------------------------------------------------------------*\
  =========                 |
  \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox
   \\    /   O peration     |
    \\  /    A nd           | www.openfoam.com
     \\/     M anipulation  |
-------------------------------------------------------------------------------
    Copyright (C) 2011-2013 OpenFOAM Foundation
-------------------------------------------------------------------------------
License
    This file is part of OpenFOAM.

    OpenFOAM is free software: you can redistribute it and/or modify it
    under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    OpenFOAM is distributed in the hope that it will be useful, but WITHOUT
    ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
    FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
    for more details.

    You should have received a copy of the GNU General Public License
    along with OpenFOAM.  If not, see <http://www.gnu.org/licenses/>.

Application
    test

Description
    Finite volume method test code.

\*---------------------------------------------------------------------------*/

#include "fvCFD.H"

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

 //- Return SGS kinetic energy
        //  calculated from the given velocity gradient
//https://github.com/OpenFOAM/OpenFOAM-2.1.x/blob/master/src/turbulenceModels/incompressible/LES/Smagorinsky/Smagorinsky.H
        volScalarField k(const tmp<volTensorField>& gradU) 
        {
        	
	    const double Ce  =1.048 ;
	    const double Ck =  0.094;

            //return (2.0*ck_/ce_)*sqr(delta())*magSqr(dev(symm(gradU)));
            return (2.0*Ck/Ce)*magSqr(dev(symm(gradU)));
        }

// Here,
// I guess 
//  Sij = dev(symm(gradU))


// WALE :  https://develop.openfoam.com/Development/openfoam/blob/OpenFOAM-v2012/src/TurbulenceModels/turbulenceModels/LES/WALE/WALE.C 


int main(int argc, char *argv[])
{
    #include "setRootCase.H"

    #include "createTime.H"
    #include "createMesh.H"
    #include "createFields.H"

    tmp<volTensorField> gradU = fvc::grad( U ) ;


    volSymmTensorField D(symm( gradU )); 

    volSymmTensorField Sij = dev ( D ); 

    Info  << " Sd " <<  ( Sij && Sij) << endl;
    // in WALE model :
    //volSymmTensorField Sd= dev(symm(gradU & gradU)) ;

    //volTensorField Sd= dev(symm(gradU & gradU)) ;

    volSymmTensorField g( symm(gradU & gradU ) ); 
    //tmp<volSymmTensorField> Sd (dev(symm(gradU & gradU))  ); 
    //Info  << " Sd " <<  dev(symm(gradU & gradU)) << endl;

    Info<< "End\n" << endl;
}


// ************************************************************************* //
