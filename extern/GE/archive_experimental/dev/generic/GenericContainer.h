#ifndef _CONTAINER_H
#define _CONTAINER_H



//==============================================================================
// include
//==============================================================================

#include "../misc/Common.h"
#include "../tool/Obj3DS.h"

#include "../util/Point.h"
#include "../util/Vector.h"
#include "../util/Triangle.h"

#include "../util/Vertex.h"
#include "../util/TFace.h"

#include <vector>
#include <algorithm>
#include <list>


//==============================================================================
// map
//==============================================================================
// typedef std::vector<Point >   POINTS ;
// typedef std::vector<Vertex* > VERTICES ;
// typedef std::vector<Vector >  NORMALES ;
// typedef std::vector<TFace  >  FACETS; // or triangles

//==============================================================================
// Container Class Definition
//==============================================================================
 /*! \brief Container is container which hold all informations concerning the
  * geometry, it's the Mesh representation class \n

 * there are many ways to represent triangles mesh  object, using different
 * methods to store the vertex, edge and face data. These include (see:
 * http://en.wikipedia.org/wiki/Polygon_mesh ): \n
 - \a Face-Vertex {Meshes} - A simple list of vertices, and a set of polygons that
   point to the vertices it uses.\n
 - Winged-Edge {Meshes} - In which each edge points to two vertices, two faces,
    * and the four (clockwise and counterclockwise) edges that touch it.
    * Winged-Edge meshes allow constant time traversal of the surface,
    * but with higher storage requirements.\n
 - \a Half-Edge {Meshes}.\n
 - \a Quad-Edge {Meshes}.\n
 - \a Corner-Table.\n
 - \a Vertex-Vertex {Meshes} - A vv mesh represents only vertices, which point to
 * other vertices. Both the edge and face information is implicit in the
 * representation. However, the simplicity of the representation allows for many
 * efficient operations to be performed on meshes. \n

  \note In the current version, for less complexity(read more efficiency), only FV(Face-Vertex),
 *  versions of Mesh representation is implemented (see figure 1). \n

  \image html MeshFV.jpg  "figure 1: Face-Vertex Meshes "


  \note Indices advantage :
 * - Vertex indices = indices into a vertex array
 * - Enable shared vertices


  \author Cherif Mihoubi

*/
//==============================================================================

using namespace ge;

// struct Precise{
// 	typedef double Element ;
// };
// 
// struct Compact{
// 	typedef float Element ;
// };



struct DefaultConfigue {
  	typedef std::vector<Point >   POINTS ;
	typedef std::vector<Vertex* > VERTICES ;
	typedef std::vector<Vector >  NORMALES ;
	typedef std::vector<TFace  >  FACETS; // or triangles
};

template<typename Config_ = DefaultConfigue>
class Container
{
	// ---------------------------------------------------------------------
   	// typedefs
  	// --------------------------------------------------------------------- 
    	typedef Container<Config_>* Ptr;// for generic programming
    	typedef std::auto_ptr<Container<Config_>* > AutoPtr; //
    	typedef Config_ configure ;
	
    	typedef typename configure::POINTS 	POINTS ;
    	typedef typename configure::VERTICES  	VERTICES;
    	typedef typename configure::NORMALES 	NORMALES;
    	typedef typename configure::FACETS  	FACETS;
	//----------------------------------------------------------------------
	
    	// Member Data    
        POINTS   VVPoint;    // Point array
        VERTICES VVertices;  // vertices array
        NORMALES VNormals;   // Normals array
        FACETS   VVTriangles;// Triangle array
        std::vector<Triangle > TM ;// TM :) Triangle Mesh
        Point pMin, pMax ;
        
    // Member methods

  private:
        inline void AddNewNormal  (const Vector& N ){VNormals.push_back(N);}
        inline void AddNewVertex  (const Point & a );

        int  FindVertexIndex  (const Point & a );
        void AddNewFaceNbIndex(TFace&T ,const int &index );
        void RemoveDuplicatesFacetIndices(TFace& T );
	void RemoveDuplicatesVertexNbIndices(Vertex& V);
        bool ItsInVertexNeighborbsList(const int V_id,const int s_id);

public:
        Container()
        {
            Point Maxx (0. , 0. , 0. );
            Point Minn (100000. , 100000. , 100000. );
            pMin = Minn ;
            pMax = Maxx ;
        }//  default constructor
//        ~Container(){Delete();}

        inline void push_back (const Point &a,   const Point & b,
                               const Point &c,   const Vector& N );


        inline void AddNewTriangle(   const Vector&N ,
                                      const Point &a ,
                                      const Point &b ,
                                      const Point &c );
    // Set functions
        void BuildFaceVertexMeshesRepresentation();
	void SeTFacetItsConnectedFacetList();// not suitable, it's find all connected surface
	void DetectBorders();                // not suitable, it's find all connected surface
	void BuildVertexVertexMeshesRepresentation();// find and setup the neighborbs

        void Check(); // for Debug
        inline void RecomputeNormals () ;

public:
    //Get Functions
        inline int GetNumOfTriangles ()const {return VVTriangles.size();}
        inline int GetNumOfVertices  ()const {return VVPoint.size();}

        inline Point GetAABBMin ()     const {return pMin;}

        inline Point GetVertex  (const int id) {return VVPoint [id] ;}

        inline Vector GetNormal (const uint32 &Triang_id )const ;
        inline int*  GetVerticesIndices (const uint32 &Triang_id ) ;
        inline Point  GetV0     (const uint32 &Triang_id )const ;
        inline Point  GetV1     (const uint32 &Triang_id )const ;
        inline Point  GetV2     (const uint32 &Triang_id )const ;

        inline VERTICES GetVVertices  ()const{return VVertices;}  // vertices array
        inline NORMALES GetVNormals   ()const{return VNormals;}   // Normals array
        inline FACETS   GetVVTriangles()const{return VVTriangles;}// Triangle array
        inline POINTS   GetVVPoint    ()const{return VVPoint;}    // Point array

        inline Point GetVVPoint(const int id)const{return VVPoint[id];}// Point array
        inline void DSInterface(std::list<ge::Obj3DS> objList );
//         inline GetV0 ();
private :
        inline void Delete(){
             VVPoint.clear();
             VVertices.clear();
             VNormals.clear();
             VVTriangles.clear();
        }
};


template<typename Config>
inline int* Container<Config>::GetVerticesIndices (const uint32 & Triang_id )
{
    return (VVTriangles [Triang_id].v_indices) ;
}



// -----------------------------------------------------------------------------
/*!
  @name push_back(const Point &a,const Point & b,const Point &c,const Vector& N)
  @brief add only the first triangle in the list
  @note this function should be called only one time, in the initialisation
  
*/
// -----------------------------------------------------------------------------
template<typename Config>
inline void Container<Config>::push_back(const Point &a,   const Point & b,
                                 const Point &c,   const Vector& N )
 {
     VVPoint.push_back(a);
     VVPoint.push_back(b);
     VVPoint.push_back(c);
     VNormals.push_back(N);
     
     pMin.Min(a) ;    pMin.Min(b) ;    pMin.Min(c) ;
     pMax.Max(a) ;    pMax.Min(b) ;    pMax.Min(c) ;
     TFace Ttemp ;
     Ttemp.v_indices[0] = 0 ;
     Ttemp.v_indices[1] = 1 ;
     Ttemp.v_indices[2] = 2 ;
     Ttemp.visited=false;
     VVTriangles.push_back (Ttemp );
 }
// -----------------------------------------------------------------------------
/*!
  @name void AddNewTriangle( const Point &a, const Point &b,
   *                         const Point &c, const Vector&N )
  @brief add new triangle in the list
  
*/
// -----------------------------------------------------------------------------
template<typename Config>
inline void Container<Config>::AddNewTriangle(const Vector&N ,
                                      const Point &a , 
                                      const Point &b ,
                                      const Point &c )
{
    AddNewNormal(N); //  new normal
    AddNewVertex(a); // V0
    AddNewVertex(b); // V1
    AddNewVertex(c); // V2 

    TFace Ttemp ;
    Ttemp.v_indices[0] = FindVertexIndex (a);
    Ttemp.v_indices[1] = FindVertexIndex (b);
    Ttemp.v_indices[2] = FindVertexIndex (c);
    Ttemp.visited      = false;
    VVTriangles.push_back (Ttemp );
}

// -----------------------------------------------------------------------------
/*!
  @name void AddNewTriangle( const Point &a, const Point &b,
   *                     const Point &c, const Vector&N )
  @brief return V0 of the triangle_id
  @param uint32 &Triang_id
  @return V0
  
*/
// -----------------------------------------------------------------------------
template<typename Config>
inline Point Container<Config>::GetV0 (const uint32 &Triang_id )const
{
    #ifdef _DEBUG
        ASSERTMSG(VVPoint.size() != 0, "Vertices-Vector is empty !! ") ;
        ASSERTMSG(VVTriangles.size() != 0, "Triangles-Vector is empty !! "); 
    #endif
    return VVPoint [ VVTriangles [Triang_id].v_indices[0] ]  ;
}

//  @return V1
template<typename Config>
inline Point Container<Config>::GetV1 (const uint32 &Triang_id )const
{
    #ifdef _DEBUG
        ASSERTMSG(VVPoint.size() != 0, "Vertices-Vector is empty !! ") ;
        ASSERTMSG(VVTriangles.size() != 0, "Triangles-Vector is empty !! ");
    #endif
    return VVPoint [ VVTriangles [Triang_id].v_indices[1] ]  ;
}

//  @return V2
template<typename Config>
inline Point Container<Config>::GetV2 (const uint32 &Triang_id )const
{
    #ifdef _DEBUG
        ASSERTMSG(VVPoint.size() != 0, "Vertices-Vector is empty !! ");
        ASSERTMSG(VVTriangles.size() != 0, "Triangles-Vector is empty !! ");
    #endif
    return VVPoint [ VVTriangles [Triang_id].v_indices[2] ]  ;
}

template<typename Config>
inline Vector Container<Config>::GetNormal (const uint32 &Triang_id )const {
    return VNormals[Triang_id] ;
}

//------------------------------------------------------------------------------
/*!
  @name bool AddNewVertex( Point & a )
  @brief
  @param Point& a
  @return mean it's new vertex, which  have been addd to the list
	    if false : mean it's exist already in the lists
  for(uint32 i = 0; i < (*o).mIndices.size(); ++ i)
*/
//------------------------------------------------------------------------------
template<typename Config>
inline void Container<Config>::AddNewVertex(const Point & a )
{
    std::vector<Point >::iterator current = VVPoint.begin();

	while (current != VVPoint.end() )
	{
 	  if ( *std::find(VVPoint.begin(),VVPoint.end(),a) != a )
	   {
		VVPoint.push_back (a) ;
                pMin.Min(a) ;
                pMax.Max(a) ;
		break;
// 		r = false ;
		} else {
			break;
		        current++;
                }
	}
}




// -----------------------------------------------------------------------------
/*!
  @name void DSInterface( list<Obj3DS> objList )
  @brief Convert the loaded object list to the mesh structore (merge all geometries)
  
*/
// -----------------------------------------------------------------------------
template<typename Config>
inline void Container<Config>::DSInterface(std::list<Obj3DS> objList )
{
    std::cerr <<"Not yet Finished..." <<std::endl;
    int nObjs = objList.size();
    std::cout << "Number of imerged geometies is := " << nObjs << std::endl;

  for(std::list<Obj3DS>::iterator o = objList.begin(); o != objList.end(); ++ o)
  {
    // Append...

      std::cout <<"(*o).mIndices.size() == " << (*o).mIndices.size() <<std::endl;
     for(uint32 i = 0; i < (*o).mIndices.size();  i+=3 )
     {
                TFace Ttemp ;
//                cout << "i , i+1 , i+2,  i+3 <"<<i<<" , "<< i+1<<" , "<< i+2<<">\n";
                Ttemp.v_indices[0] = (*o).mIndices [i];
                Ttemp.v_indices[1] = (*o).mIndices [i+1];
                Ttemp.v_indices[2] = (*o).mIndices [i+2];
                Ttemp.visited      = false;
                VVTriangles.push_back (Ttemp );
     }

     // Transcode the data

      for(uint32 i = 0; i < (*o).mVertices.size(); ++ i){
             // vector to  point ?
            VVPoint.push_back( (*o).mVertices[i] ) ;
      }
  }
//  RecomputeNormals ();

}
// -----------------------------------------------------------------------------
/*!
  @name inline void RecomputeNormals ()
  @brief recompute the normals, for 3ds Mesh
  
*/
// -----------------------------------------------------------------------------
template<typename Config>
inline void Container<Config>::RecomputeNormals (){
//    normalize()
    for(uint32 i = 0 ; VVTriangles.size() ; ++ i)
    {
        // tmp = (v1-v0)^(v2-v0)
        Vector tmp  =  GetV1(i)-GetV0(i) ;
        Vector tmp2 =((GetV2(i)-GetV0(i) )^ tmp ) ;
        tmp2.normalize();
//      Vector tt   = tmp2.normalize();
        VNormals.push_back ( tmp2 );
    }
//
}





#endif // Container_H

