

-- RELEASE -- SVN Revision 134 -- LOG BEGIN , Tuesday, 17.June 2025, 11:30---------------------------------


 INFO:    wa<PERSON><PERSON><PERSON><PERSON> called with parameter file: square_cylinder_turbulence.prm
 INFO:    Domain.cpp::InitSimData:  SimData.h:Parameters:
---------------------
 dt      0.000833333
 dx      0.001
 tau     0.75
 omega   1.33333
 nu      0.0001
 rho     1
 Re      6.4
 gx      0
 gy      0
 gz      0
 xLength 0.08
 yLength 0.04
 zLength 0.02
---------------------
 dt_L    1
 dx_L    1
 nu_L    0.0833333
 gx_L    0
 gy_L    0
 gz_L    0
 domainX 80
 domainY 40
 domainZ 20
 numFluidCells 0
---------------------

 INFO:    PatchField.cpp::Create:  PatchField.cpp:Parameters:
---------------------
 xNumPatches 1
 yNumPatches 1
 zNumPatches 1
---------------------

 WARNING: Domain.h::SetBC: Lattice density for prs_nils boundary condition at east side has extreme value: 0. Adjust resolution!
 WARNING: Domain.cpp::InitializeBoundaries:
 Even GE is activated through CMake config, there is no blocks named 'stl' in parameter file. You are not used the STL GeometricEngine !
 ERROR:   Domain.cpp::InitializeBoundaries: Your boundary description is not consistent. There are cells with UNDEFINED state:
           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,40,1) (global position (x,y,z)=(0,40,1))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,40,1) (global position (x,y,z)=(81,40,1))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,40,2) (global position (x,y,z)=(0,40,2))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,40,2) (global position (x,y,z)=(81,40,2))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,40,3) (global position (x,y,z)=(0,40,3))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,40,3) (global position (x,y,z)=(81,40,3))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,40,4) (global position (x,y,z)=(0,40,4))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,40,4) (global position (x,y,z)=(81,40,4))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,40,5) (global position (x,y,z)=(0,40,5))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,40,5) (global position (x,y,z)=(81,40,5))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,40,6) (global position (x,y,z)=(0,40,6))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,40,6) (global position (x,y,z)=(81,40,6))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,40,7) (global position (x,y,z)=(0,40,7))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,40,7) (global position (x,y,z)=(81,40,7))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,40,8) (global position (x,y,z)=(0,40,8))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,40,8) (global position (x,y,z)=(81,40,8))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,40,9) (global position (x,y,z)=(0,40,9))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,40,9) (global position (x,y,z)=(81,40,9))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,40,10) (global position (x,y,z)=(0,40,10))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,40,10) (global position (x,y,z)=(81,40,10))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,40,11) (global position (x,y,z)=(0,40,11))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,40,11) (global position (x,y,z)=(81,40,11))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,40,12) (global position (x,y,z)=(0,40,12))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,40,12) (global position (x,y,z)=(81,40,12))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,40,13) (global position (x,y,z)=(0,40,13))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,40,13) (global position (x,y,z)=(81,40,13))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,40,14) (global position (x,y,z)=(0,40,14))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,40,14) (global position (x,y,z)=(81,40,14))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,40,15) (global position (x,y,z)=(0,40,15))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,40,15) (global position (x,y,z)=(81,40,15))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,40,16) (global position (x,y,z)=(0,40,16))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,40,16) (global position (x,y,z)=(81,40,16))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,40,17) (global position (x,y,z)=(0,40,17))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,40,17) (global position (x,y,z)=(81,40,17))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,40,18) (global position (x,y,z)=(0,40,18))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,40,18) (global position (x,y,z)=(81,40,18))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,40,19) (global position (x,y,z)=(0,40,19))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,40,19) (global position (x,y,z)=(81,40,19))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,0,20) (global position (x,y,z)=(0,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(1,0,20) (global position (x,y,z)=(1,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(2,0,20) (global position (x,y,z)=(2,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(3,0,20) (global position (x,y,z)=(3,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(4,0,20) (global position (x,y,z)=(4,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(5,0,20) (global position (x,y,z)=(5,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(6,0,20) (global position (x,y,z)=(6,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(7,0,20) (global position (x,y,z)=(7,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(8,0,20) (global position (x,y,z)=(8,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(9,0,20) (global position (x,y,z)=(9,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(10,0,20) (global position (x,y,z)=(10,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(11,0,20) (global position (x,y,z)=(11,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(12,0,20) (global position (x,y,z)=(12,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(13,0,20) (global position (x,y,z)=(13,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(14,0,20) (global position (x,y,z)=(14,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(15,0,20) (global position (x,y,z)=(15,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(16,0,20) (global position (x,y,z)=(16,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(17,0,20) (global position (x,y,z)=(17,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(18,0,20) (global position (x,y,z)=(18,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(19,0,20) (global position (x,y,z)=(19,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(20,0,20) (global position (x,y,z)=(20,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(21,0,20) (global position (x,y,z)=(21,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(22,0,20) (global position (x,y,z)=(22,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(23,0,20) (global position (x,y,z)=(23,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(24,0,20) (global position (x,y,z)=(24,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(25,0,20) (global position (x,y,z)=(25,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(26,0,20) (global position (x,y,z)=(26,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(27,0,20) (global position (x,y,z)=(27,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(28,0,20) (global position (x,y,z)=(28,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(29,0,20) (global position (x,y,z)=(29,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(30,0,20) (global position (x,y,z)=(30,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(31,0,20) (global position (x,y,z)=(31,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(32,0,20) (global position (x,y,z)=(32,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(33,0,20) (global position (x,y,z)=(33,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(34,0,20) (global position (x,y,z)=(34,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(35,0,20) (global position (x,y,z)=(35,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(36,0,20) (global position (x,y,z)=(36,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(37,0,20) (global position (x,y,z)=(37,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(38,0,20) (global position (x,y,z)=(38,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(39,0,20) (global position (x,y,z)=(39,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(40,0,20) (global position (x,y,z)=(40,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(41,0,20) (global position (x,y,z)=(41,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(42,0,20) (global position (x,y,z)=(42,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(43,0,20) (global position (x,y,z)=(43,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(44,0,20) (global position (x,y,z)=(44,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(45,0,20) (global position (x,y,z)=(45,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(46,0,20) (global position (x,y,z)=(46,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(47,0,20) (global position (x,y,z)=(47,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(48,0,20) (global position (x,y,z)=(48,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(49,0,20) (global position (x,y,z)=(49,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(50,0,20) (global position (x,y,z)=(50,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(51,0,20) (global position (x,y,z)=(51,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(52,0,20) (global position (x,y,z)=(52,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(53,0,20) (global position (x,y,z)=(53,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(54,0,20) (global position (x,y,z)=(54,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(55,0,20) (global position (x,y,z)=(55,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(56,0,20) (global position (x,y,z)=(56,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(57,0,20) (global position (x,y,z)=(57,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(58,0,20) (global position (x,y,z)=(58,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(59,0,20) (global position (x,y,z)=(59,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(60,0,20) (global position (x,y,z)=(60,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(61,0,20) (global position (x,y,z)=(61,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(62,0,20) (global position (x,y,z)=(62,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(63,0,20) (global position (x,y,z)=(63,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(64,0,20) (global position (x,y,z)=(64,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(65,0,20) (global position (x,y,z)=(65,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(66,0,20) (global position (x,y,z)=(66,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(67,0,20) (global position (x,y,z)=(67,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(68,0,20) (global position (x,y,z)=(68,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(69,0,20) (global position (x,y,z)=(69,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(70,0,20) (global position (x,y,z)=(70,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(71,0,20) (global position (x,y,z)=(71,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(72,0,20) (global position (x,y,z)=(72,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(73,0,20) (global position (x,y,z)=(73,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(74,0,20) (global position (x,y,z)=(74,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(75,0,20) (global position (x,y,z)=(75,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(76,0,20) (global position (x,y,z)=(76,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(77,0,20) (global position (x,y,z)=(77,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(78,0,20) (global position (x,y,z)=(78,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(79,0,20) (global position (x,y,z)=(79,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(80,0,20) (global position (x,y,z)=(80,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,0,20) (global position (x,y,z)=(81,0,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,1,20) (global position (x,y,z)=(0,1,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,1,20) (global position (x,y,z)=(81,1,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,2,20) (global position (x,y,z)=(0,2,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,2,20) (global position (x,y,z)=(81,2,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,3,20) (global position (x,y,z)=(0,3,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,3,20) (global position (x,y,z)=(81,3,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,4,20) (global position (x,y,z)=(0,4,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,4,20) (global position (x,y,z)=(81,4,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,5,20) (global position (x,y,z)=(0,5,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,5,20) (global position (x,y,z)=(81,5,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,6,20) (global position (x,y,z)=(0,6,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,6,20) (global position (x,y,z)=(81,6,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,7,20) (global position (x,y,z)=(0,7,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,7,20) (global position (x,y,z)=(81,7,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,8,20) (global position (x,y,z)=(0,8,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,8,20) (global position (x,y,z)=(81,8,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,9,20) (global position (x,y,z)=(0,9,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,9,20) (global position (x,y,z)=(81,9,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,10,20) (global position (x,y,z)=(0,10,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,10,20) (global position (x,y,z)=(81,10,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,11,20) (global position (x,y,z)=(0,11,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,11,20) (global position (x,y,z)=(81,11,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,12,20) (global position (x,y,z)=(0,12,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,12,20) (global position (x,y,z)=(81,12,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,13,20) (global position (x,y,z)=(0,13,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,13,20) (global position (x,y,z)=(81,13,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,14,20) (global position (x,y,z)=(0,14,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,14,20) (global position (x,y,z)=(81,14,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,15,20) (global position (x,y,z)=(0,15,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,15,20) (global position (x,y,z)=(81,15,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,16,20) (global position (x,y,z)=(0,16,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,16,20) (global position (x,y,z)=(81,16,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,17,20) (global position (x,y,z)=(0,17,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,17,20) (global position (x,y,z)=(81,17,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,18,20) (global position (x,y,z)=(0,18,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,18,20) (global position (x,y,z)=(81,18,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,19,20) (global position (x,y,z)=(0,19,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,19,20) (global position (x,y,z)=(81,19,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,20,20) (global position (x,y,z)=(0,20,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,20,20) (global position (x,y,z)=(81,20,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,21,20) (global position (x,y,z)=(0,21,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,21,20) (global position (x,y,z)=(81,21,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,22,20) (global position (x,y,z)=(0,22,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,22,20) (global position (x,y,z)=(81,22,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,23,20) (global position (x,y,z)=(0,23,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,23,20) (global position (x,y,z)=(81,23,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,24,20) (global position (x,y,z)=(0,24,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,24,20) (global position (x,y,z)=(81,24,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,25,20) (global position (x,y,z)=(0,25,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,25,20) (global position (x,y,z)=(81,25,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,26,20) (global position (x,y,z)=(0,26,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,26,20) (global position (x,y,z)=(81,26,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,27,20) (global position (x,y,z)=(0,27,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,27,20) (global position (x,y,z)=(81,27,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,28,20) (global position (x,y,z)=(0,28,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,28,20) (global position (x,y,z)=(81,28,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,29,20) (global position (x,y,z)=(0,29,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,29,20) (global position (x,y,z)=(81,29,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,30,20) (global position (x,y,z)=(0,30,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,30,20) (global position (x,y,z)=(81,30,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,31,20) (global position (x,y,z)=(0,31,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,31,20) (global position (x,y,z)=(81,31,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,32,20) (global position (x,y,z)=(0,32,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,32,20) (global position (x,y,z)=(81,32,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,33,20) (global position (x,y,z)=(0,33,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,33,20) (global position (x,y,z)=(81,33,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,34,20) (global position (x,y,z)=(0,34,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,34,20) (global position (x,y,z)=(81,34,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,35,20) (global position (x,y,z)=(0,35,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,35,20) (global position (x,y,z)=(81,35,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,36,20) (global position (x,y,z)=(0,36,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,36,20) (global position (x,y,z)=(81,36,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,37,20) (global position (x,y,z)=(0,37,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,37,20) (global position (x,y,z)=(81,37,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,38,20) (global position (x,y,z)=(0,38,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,38,20) (global position (x,y,z)=(81,38,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,39,20) (global position (x,y,z)=(0,39,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,39,20) (global position (x,y,z)=(81,39,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,40,20) (global position (x,y,z)=(0,40,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,40,20) (global position (x,y,z)=(81,40,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(0,41,20) (global position (x,y,z)=(0,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(1,41,20) (global position (x,y,z)=(1,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(2,41,20) (global position (x,y,z)=(2,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(3,41,20) (global position (x,y,z)=(3,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(4,41,20) (global position (x,y,z)=(4,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(5,41,20) (global position (x,y,z)=(5,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(6,41,20) (global position (x,y,z)=(6,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(7,41,20) (global position (x,y,z)=(7,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(8,41,20) (global position (x,y,z)=(8,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(9,41,20) (global position (x,y,z)=(9,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(10,41,20) (global position (x,y,z)=(10,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(11,41,20) (global position (x,y,z)=(11,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(12,41,20) (global position (x,y,z)=(12,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(13,41,20) (global position (x,y,z)=(13,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(14,41,20) (global position (x,y,z)=(14,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(15,41,20) (global position (x,y,z)=(15,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(16,41,20) (global position (x,y,z)=(16,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(17,41,20) (global position (x,y,z)=(17,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(18,41,20) (global position (x,y,z)=(18,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(19,41,20) (global position (x,y,z)=(19,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(20,41,20) (global position (x,y,z)=(20,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(21,41,20) (global position (x,y,z)=(21,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(22,41,20) (global position (x,y,z)=(22,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(23,41,20) (global position (x,y,z)=(23,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(24,41,20) (global position (x,y,z)=(24,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(25,41,20) (global position (x,y,z)=(25,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(26,41,20) (global position (x,y,z)=(26,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(27,41,20) (global position (x,y,z)=(27,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(28,41,20) (global position (x,y,z)=(28,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(29,41,20) (global position (x,y,z)=(29,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(30,41,20) (global position (x,y,z)=(30,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(31,41,20) (global position (x,y,z)=(31,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(32,41,20) (global position (x,y,z)=(32,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(33,41,20) (global position (x,y,z)=(33,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(34,41,20) (global position (x,y,z)=(34,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(35,41,20) (global position (x,y,z)=(35,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(36,41,20) (global position (x,y,z)=(36,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(37,41,20) (global position (x,y,z)=(37,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(38,41,20) (global position (x,y,z)=(38,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(39,41,20) (global position (x,y,z)=(39,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(40,41,20) (global position (x,y,z)=(40,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(41,41,20) (global position (x,y,z)=(41,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(42,41,20) (global position (x,y,z)=(42,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(43,41,20) (global position (x,y,z)=(43,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(44,41,20) (global position (x,y,z)=(44,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(45,41,20) (global position (x,y,z)=(45,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(46,41,20) (global position (x,y,z)=(46,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(47,41,20) (global position (x,y,z)=(47,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(48,41,20) (global position (x,y,z)=(48,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(49,41,20) (global position (x,y,z)=(49,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(50,41,20) (global position (x,y,z)=(50,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(51,41,20) (global position (x,y,z)=(51,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(52,41,20) (global position (x,y,z)=(52,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(53,41,20) (global position (x,y,z)=(53,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(54,41,20) (global position (x,y,z)=(54,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(55,41,20) (global position (x,y,z)=(55,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(56,41,20) (global position (x,y,z)=(56,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(57,41,20) (global position (x,y,z)=(57,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(58,41,20) (global position (x,y,z)=(58,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(59,41,20) (global position (x,y,z)=(59,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(60,41,20) (global position (x,y,z)=(60,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(61,41,20) (global position (x,y,z)=(61,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(62,41,20) (global position (x,y,z)=(62,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(63,41,20) (global position (x,y,z)=(63,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(64,41,20) (global position (x,y,z)=(64,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(65,41,20) (global position (x,y,z)=(65,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(66,41,20) (global position (x,y,z)=(66,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(67,41,20) (global position (x,y,z)=(67,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(68,41,20) (global position (x,y,z)=(68,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(69,41,20) (global position (x,y,z)=(69,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(70,41,20) (global position (x,y,z)=(70,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(71,41,20) (global position (x,y,z)=(71,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(72,41,20) (global position (x,y,z)=(72,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(73,41,20) (global position (x,y,z)=(73,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(74,41,20) (global position (x,y,z)=(74,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(75,41,20) (global position (x,y,z)=(75,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(76,41,20) (global position (x,y,z)=(76,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(77,41,20) (global position (x,y,z)=(77,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(78,41,20) (global position (x,y,z)=(78,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(79,41,20) (global position (x,y,z)=(79,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(80,41,20) (global position (x,y,z)=(80,41,20))
.           |-> Cell flag in Patch number=(0) at relative position (x,y,z)=(81,41,20) (global position (x,y,z)=(81,41,20))
.