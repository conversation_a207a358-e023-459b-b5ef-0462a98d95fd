#include <boost/ptr_container/ptr_vector.hpp>
#include <boost/utility.hpp>
#include <algorithm>
#include <iostream>



using namespace std;
using namespace boost;



struct Point
{  
      explicit Point (const int&  a, const int&b, const int&c)
	  :x (a), y (b), z (c) 
	  {}
      ~Point() {std::cout << "Base Point Destructor\n" ;}
      friend std::ostream& operator << (std::ostream& out ,const Point& P) ;
  public:
      int x,y,z ;
};

std::ostream& operator << (std::ostream& out ,const Point& P)
{
    out << "<" << P.x<< ","<< P.y << "," <<P.z<< ">\n";
    return out ;
}

struct m_point :Point
{
	m_point(const int&  a, const int&b, const int&c) : Point (a,b,c){
	    d = 999999 ;
	    std::cout<< "Not Base class" << std::endl ;
	}
	
      friend std::ostream& operator << (std::ostream& out ,const m_point& P) ;	
  private:
    int d  ;
};


std::ostream& operator << (std::ostream& out ,const m_point& P)
{
    out << "<" << P.x<< ","<< P.y << "," <<P.z<< "," << P.d << ">\n";
    return out ;
}





template<typename ElementType>
struct ConfigueSample
{
  typedef ptr_vector<ElementType> container_type; 
};



template<typename Configure>
struct Container // : public container_type //todo
{
    typedef typename Configure::container_type points_type;
//     typedef ptr_vector<Point>  ;
    typedef typename points_type::iterator iterator ;
    typedef typename points_type::const_iterator const_iterator ;
    

    // one way of adding new elements
    //
    inline void add( Point* c )
    {
        m_points.push_back( c );
    }
    
// //     // you don't need this method , at least for  the  moment
// //     // second way of adding new elements
// //     //
//     
//       void add(const Point& c )
//       {
// 	    std::cout <<  "Second way to add new Point\n";
// 	    m_points.push_back( new_clone( c ) );
//       }

      inline void remove( iterator where ) { m_points.erase( where ); }
    
      inline iterator        begin()       {return m_points.begin() ;}
      inline const_iterator  begin() const {return m_points.begin() ;}
      inline iterator        end() 	   {return m_points.end() ;}
      inline const_iterator  end()   const {return m_points.end() ;}
      inline std::size_t     size() 	   {return m_points.size() ;}
      
/*
      // element access
      //!@{
            //!@note  don't throw any BoundsViolation Exception
            T& operator[]( size_type index );
      const T& operator[]( size_type index ) const;// Returns a reference to the n'th element
      
	    //!@note  throw any BoundsViolation Exception if  index > size or negative
            T& at( size_type index );
      const T& at( size_type index ) const;// Returns a reference to the n'th element
      
      //!@}
*/
      
      
      //       Throws: 
      inline Point& operator [] (const std::size_t& index){ // TODO assert
	return m_points[index];
      }
      
      inline const Point operator [](const std::size_t& index)const { // TODO assert
	return m_points[index];
      }   
      
  private:
    points_type  m_points ;
};

int main()
{
  
      std::cout << "============	Boost ptr_vector ===================\n";
      
      Container<ConfigueSample <Point> > m_c ; //  my points container
      
      m_c.add ( new Point (0 ,0  ,0 ) ) ;
      m_c.add ( new Point (1 ,11 ,1 ) ) ;
      m_c.add ( new Point (2 ,22 ,2 ) ) ;
      m_c.add ( new m_point (0 ,0  ,0 ) ) ; // repetition

      
      
  
      Container<ConfigueSample <Point> >::const_iterator it = m_c.begin() ;
      for ( ;it != m_c.end() ; ++it )
	std::cout << *it ;
      
      
      std::cout << ".........\n" ;
      for (int i=0 ; i != m_c.size() ; ++i)
	std::cout << m_c[i] ;
      
            
      std::cout << ".........\n";
      std::cout << "size -> " << m_c.size() << std::endl;      
      std::cout << ".........\n" ;
      
      
      std::cout << "===========		Free memory  ===================\n";
      
      
      return (0) ;
}



