#ifndef MESH_HH
#define MESH_HH

#include <boost/shared_ptr.hpp>
#include "MeshImpl.h"

//  its interface to real implementation of the class MeshImpl --> Bridg-pattern 
template<typename T>
class Mesh
{
  private :
    class MeshImpl;// implementation class 
    boost::shared_ptr<MeshImpl> mesh_i ;  
  
  public:
    typename MeshImpl::MeshType MeshType;

    // Conctructor 
    Mesh (const char* fileName ) {}
    // Destructor
    // Set functions
    // Get functions 
    // Help function 
  private:
    
};



#endif