#include <cmath>
#include <iostream>

// ============================================================================
// ============================================================================
struct X{
	X(const int& t) : m_a (t) {}
	inline int& A(){return m_a ;}
	inline const int& A()const{return m_a ;}
private:
	int m_a ; 
private:
	X(const X& other) ;
	X operator = (const X&o ) ;
};


// ============================================================================
inline X* XPtr(){
	X *tmp = new X (10) ;
	return tmp ;
}

std::ostream& operator<<(std::ostream& out, const X& m_x)
{
	out << m_x.A() << std::endl ;
	return out ;
}



inline void Test (X& m_x )
{
	m_x.A() = 12 ; 
}




int main ()
{
	//std::cout<< " sqrt (2 ) = " << std::sqrt (2) << std::endl ;
	std::cout<<"test : " << *XPtr() << std::endl ;

	X&  tmp   = *XPtr() ;
	std::cout<<"test referance : " << tmp  << std::endl ;

	const X&  tmp2   = *XPtr() ;
	std::cout<<"test referance2 : " << tmp2  << std::endl ;


	Test (tmp ) ; 
	std::cout<<"test referance : " << tmp  << std::endl ;

	// Test (tmp2 ) ; // its invalid 

	return (0) ;
}



