#ifndef ITERATOR_HH
#define ITERATOR_HH


template<class T>
class Iterator 
{
public:
    // constructors, destructor ....
    bool operator==(const Iterator<T>&) const;
    bool operator!=(const Iterator<T>&) const;
    
    Iterator<T>& operator++();
    /*{}*/
    
    // preﬁx
    Iterator<T> operator++(int);
    // postﬁx
    T& operator*() const;
    T* operator->() const;
private:
  // accociation with containter
//     T p;
};

//     T& operator*() const
//     {
//   
//     };
//     
//     T* operator->() const
//     {
//    
//     };


#endif