//=================================================================================================
/*!
//  \file test_turbulence_models.cpp
//  \brief Comprehensive unit tests for turbulence models
//  \author AI Assistant
//  \date 2024
*/
//=================================================================================================

#include "../src/Definitions.h"
#include "../src/SimData.h"
#include "../src/turbulence/TurbulenceModelFactory.h"
#include "../src/turbulence/models/SmagorinskyModel.h"
#include "../src/turbulence/models/DynamicSmagorinskyModel.h"
#include "../src/turbulence/models/WALEModel.h"
#include "../src/turbulence/models/VremanModel.h"
#include <iostream>
#include <iomanip>
#include <cassert>
#include <cmath>

using namespace walberla;
using namespace walberla::turbulence;

// Test utilities
class TestResults {
public:
    int passed = 0;
    int failed = 0;
    
    void pass(const std::string& testName) {
        std::cout << "[PASS] " << testName << std::endl;
        passed++;
    }
    
    void fail(const std::string& testName, const std::string& reason = "") {
        std::cout << "[FAIL] " << testName;
        if (!reason.empty()) {
            std::cout << " - " << reason;
        }
        std::cout << std::endl;
        failed++;
    }
    
    void summary() {
        std::cout << "\n=== TEST SUMMARY ===" << std::endl;
        std::cout << "Passed: " << passed << std::endl;
        std::cout << "Failed: " << failed << std::endl;
        std::cout << "Total:  " << (passed + failed) << std::endl;
        std::cout << "Success Rate: " << std::fixed << std::setprecision(1) 
                  << (100.0 * passed / (passed + failed)) << "%" << std::endl;
    }
};

// Create test simulation data
SimData createTestSimData() {
    SimData sim;
    sim.domainX = 100;
    sim.domainY = 50;
    sim.domainZ = 50;
    sim.viscosity = 1e-5;
    sim.density = 1.0;
    sim.velocity = 10.0;
    return sim;
}

// Test matrix operations
Matrix3<Real> createTestStrainRateTensor() {
    Matrix3<Real> S;
    // Create a realistic strain rate tensor
    S(0,0) = 0.1;   S(0,1) = 0.05;  S(0,2) = 0.02;
    S(1,0) = 0.05;  S(1,1) = -0.05; S(1,2) = 0.03;
    S(2,0) = 0.02;  S(2,1) = 0.03;  S(2,2) = -0.05;
    return S;
}

Matrix3<Real> createTestVelocityGradient() {
    Matrix3<Real> gradU;
    // Create a realistic velocity gradient tensor
    gradU(0,0) = 0.1;   gradU(0,1) = 0.05;  gradU(0,2) = 0.02;
    gradU(1,0) = 0.08;  gradU(1,1) = -0.05; gradU(1,2) = 0.03;
    gradU(2,0) = 0.04;  gradU(2,1) = 0.06;  gradU(2,2) = -0.05;
    return gradU;
}

// Test 1: Factory Creation Tests
void testFactoryCreation(TestResults& results) {
    std::cout << "\n=== Testing Factory Creation ===" << std::endl;
    
    SimData sim = createTestSimData();
    
    // Test creation by enum type
    try {
        auto smag = TurbulenceModelFactory::create(TurbulenceModel::ModelType::SMAGORINSKY, sim);
        if (smag && smag->getModelType() == TurbulenceModel::ModelType::SMAGORINSKY) {
            results.pass("Factory creation by enum - Smagorinsky");
        } else {
            results.fail("Factory creation by enum - Smagorinsky", "Invalid model returned");
        }
    } catch (const std::exception& e) {
        results.fail("Factory creation by enum - Smagorinsky", e.what());
    }
    
    try {
        auto dynamic = TurbulenceModelFactory::create(TurbulenceModel::ModelType::DYNAMIC_SMAGORINSKY, sim);
        if (dynamic && dynamic->getModelType() == TurbulenceModel::ModelType::DYNAMIC_SMAGORINSKY) {
            results.pass("Factory creation by enum - Dynamic Smagorinsky");
        } else {
            results.fail("Factory creation by enum - Dynamic Smagorinsky", "Invalid model returned");
        }
    } catch (const std::exception& e) {
        results.fail("Factory creation by enum - Dynamic Smagorinsky", e.what());
    }
    
    try {
        auto wale = TurbulenceModelFactory::create(TurbulenceModel::ModelType::WALE, sim);
        if (wale && wale->getModelType() == TurbulenceModel::ModelType::WALE) {
            results.pass("Factory creation by enum - WALE");
        } else {
            results.fail("Factory creation by enum - WALE", "Invalid model returned");
        }
    } catch (const std::exception& e) {
        results.fail("Factory creation by enum - WALE", e.what());
    }
    
    try {
        auto vreman = TurbulenceModelFactory::create(TurbulenceModel::ModelType::VREMAN, sim);
        if (vreman && vreman->getModelType() == TurbulenceModel::ModelType::VREMAN) {
            results.pass("Factory creation by enum - Vreman");
        } else {
            results.fail("Factory creation by enum - Vreman", "Invalid model returned");
        }
    } catch (const std::exception& e) {
        results.fail("Factory creation by enum - Vreman", e.what());
    }
}

// Test 2: String-based Factory Creation
void testStringFactoryCreation(TestResults& results) {
    std::cout << "\n=== Testing String-based Factory Creation ===" << std::endl;
    
    SimData sim = createTestSimData();
    
    // Test various string formats
    std::vector<std::pair<std::string, TurbulenceModel::ModelType>> testCases = {
        {"Smagorinsky", TurbulenceModel::ModelType::SMAGORINSKY},
        {"smagorinsky", TurbulenceModel::ModelType::SMAGORINSKY},
        {"SMAGORINSKY", TurbulenceModel::ModelType::SMAGORINSKY},
        {"Smag", TurbulenceModel::ModelType::SMAGORINSKY},
        {"Dynamic", TurbulenceModel::ModelType::DYNAMIC_SMAGORINSKY},
        {"Dynamic_Smagorinsky", TurbulenceModel::ModelType::DYNAMIC_SMAGORINSKY},
        {"WALE", TurbulenceModel::ModelType::WALE},
        {"wale", TurbulenceModel::ModelType::WALE},
        {"Vreman", TurbulenceModel::ModelType::VREMAN},
        {"vreman", TurbulenceModel::ModelType::VREMAN},
        {"None", TurbulenceModel::ModelType::NONE},
        {"Laminar", TurbulenceModel::ModelType::NONE}
    };
    
    for (const auto& testCase : testCases) {
        try {
            auto model = TurbulenceModelFactory::create(testCase.first, sim);
            if (model && model->getModelType() == testCase.second) {
                results.pass("String factory creation - " + testCase.first);
            } else {
                results.fail("String factory creation - " + testCase.first, "Wrong model type returned");
            }
        } catch (const std::exception& e) {
            results.fail("String factory creation - " + testCase.first, e.what());
        }
    }
    
    // Test invalid string
    try {
        auto invalid = TurbulenceModelFactory::create("InvalidModel", sim);
        if (!invalid) {
            results.pass("String factory creation - Invalid model returns nullptr");
        } else {
            results.fail("String factory creation - Invalid model should return nullptr");
        }
    } catch (const std::exception& e) {
        results.pass("String factory creation - Invalid model throws exception");
    }
}

// Test 3: Eddy Viscosity Computation
void testEddyViscosityComputation(TestResults& results) {
    std::cout << "\n=== Testing Eddy Viscosity Computation ===" << std::endl;
    
    SimData sim = createTestSimData();
    Matrix3<Real> S = createTestStrainRateTensor();
    Matrix3<Real> gradU = createTestVelocityGradient();
    Real cellSize = 1.0;
    
    // Test Smagorinsky model
    try {
        auto smag = TurbulenceModelFactory::create(TurbulenceModel::ModelType::SMAGORINSKY, sim);
        Real nut = smag->computeEddyViscosity(S, cellSize);
        if (nut >= 0.0 && std::isfinite(nut)) {
            results.pass("Smagorinsky eddy viscosity computation");
            std::cout << "  Smagorinsky nut = " << nut << std::endl;
        } else {
            results.fail("Smagorinsky eddy viscosity computation", "Invalid result");
        }
    } catch (const std::exception& e) {
        results.fail("Smagorinsky eddy viscosity computation", e.what());
    }
    
    // Test WALE model
    try {
        auto wale = TurbulenceModelFactory::create(TurbulenceModel::ModelType::WALE, sim);
        Real nut = wale->computeEddyViscosity(gradU, cellSize);
        if (nut >= 0.0 && std::isfinite(nut)) {
            results.pass("WALE eddy viscosity computation");
            std::cout << "  WALE nut = " << nut << std::endl;
        } else {
            results.fail("WALE eddy viscosity computation", "Invalid result");
        }
    } catch (const std::exception& e) {
        results.fail("WALE eddy viscosity computation", e.what());
    }
    
    // Test Vreman model
    try {
        auto vreman = TurbulenceModelFactory::create(TurbulenceModel::ModelType::VREMAN, sim);
        Real nut = vreman->computeEddyViscosity(gradU, cellSize);
        if (nut >= 0.0 && std::isfinite(nut)) {
            results.pass("Vreman eddy viscosity computation");
            std::cout << "  Vreman nut = " << nut << std::endl;
        } else {
            results.fail("Vreman eddy viscosity computation", "Invalid result");
        }
    } catch (const std::exception& e) {
        results.fail("Vreman eddy viscosity computation", e.what());
    }
}

int main() {
    std::cout << "=================================================================================================\n";
    std::cout << "                           WALBERLA TURBULENCE MODEL UNIT TESTS\n";
    std::cout << "=================================================================================================\n";
    
    TestResults results;
    
    // Run all tests
    testFactoryCreation(results);
    testStringFactoryCreation(results);
    testEddyViscosityComputation(results);
    
    // Print summary
    results.summary();
    
    return (results.failed == 0) ? 0 : 1;
}
