/*******************************************************************************
ConminC++, to solve constrained or unconstrained function minimization.
Copyright (C) 1999  <PERSON><PERSON> <PERSON><PERSON> Dunn

This program is free software; you can redistribute it and/or
modify it under the terms of the GNU General Public License
as published by the Free Software Foundation; either version 2
of the License, or (at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this program; if not, write to the Free Software
Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA  02111-1307, USA.
*******************************************************************************/

#pragma hdrstop
#include "example1a_analysis.h"

#ifdef UNIX
#define USEUNIT( x ) // x
#else
#include <vcl.h>
USEUNIT("example1a_analysis.cpp");
USEUNIT("conmin.cpp");
USEUNIT("cnmn02.cpp");
USEUNIT("cnmn03.cpp");
USEUNIT("cnmn04.cpp");
USEUNIT("cnmn05.cpp");
USEUNIT("cnmn06.cpp");
USEUNIT("cnmn07.cpp");
USEUNIT("cnmn08.cpp");
USEUNIT("cnmn01.cpp");
//---------------------------------------------------------------------------
#endif

//---------------------------------------------------------------------------
#pragma argsused

int main(int argc, char **argv)
{
	const int ndv=4, ncon=3, nacp1=3;

//   Initialize
Ex1 ex1(ndv, ncon, nacp1);

//   set parameters 
ex1.iprint = Debug4;
ex1.info = Cal_all;
ex1.itmax = 40;
ex1.infog = All;
ex1.nfdg = Fdiff;
ex1.nside = Unbounded;
ex1.icndir = 0;
ex1.nscal = 0;
ex1.fdch = 0;
ex1.fdchm = 0.;
ex1.ct = 0.;
ex1.ctmin = 0.;
ex1.ctl = 0.;
ex1.ctlmin = 0.;
ex1.theta = 0.;
ex1.phi = 0.;
ex1.delfun = 0.;
ex1.dabfun = 0.;
ex1.linear_obj = NonLinear;
ex1.itrm = 0;
ex1.alphax=0.;
ex1.abobj1=0;

//   Initialize constraint identification vector, isc.
for(int j=1; j<=ncon; j++)
  ex1.isc[j] = 0;

//   Initialize x-vector
for(int j=1; j<=ndv; j++)
  ex1.x[j]=1.;

//   Solve optimization
while( ex1.conmin() != Finished)
   ex1.analysis();

        return 0;
}
 
