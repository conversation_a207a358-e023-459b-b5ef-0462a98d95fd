# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.5

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/data/LES/walberla/validate

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/data/LES/walberla/validate

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake cache editor..."
	/usr/bin/ccmake -H$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -H$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/data/LES/walberla/validate && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/data/LES/walberla/validate/CMakeFiles /home/<USER>/data/LES/walberla/extern/GE/src/dev/ds/CMakeFiles/progress.marks
	cd /home/<USER>/data/LES/walberla/validate && $(MAKE) -f CMakeFiles/Makefile2 ../extern/GE/src/dev/ds/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/data/LES/walberla/validate/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/data/LES/walberla/validate && $(MAKE) -f CMakeFiles/Makefile2 ../extern/GE/src/dev/ds/clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/data/LES/walberla/validate && $(MAKE) -f CMakeFiles/Makefile2 ../extern/GE/src/dev/ds/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/data/LES/walberla/validate && $(MAKE) -f CMakeFiles/Makefile2 ../extern/GE/src/dev/ds/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/data/LES/walberla/validate && $(CMAKE_COMMAND) -H$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
../extern/GE/src/dev/ds/CMakeFiles/ds.dir/rule:
	cd /home/<USER>/data/LES/walberla/validate && $(MAKE) -f CMakeFiles/Makefile2 ../extern/GE/src/dev/ds/CMakeFiles/ds.dir/rule
.PHONY : ../extern/GE/src/dev/ds/CMakeFiles/ds.dir/rule

# Convenience name for target.
ds: ../extern/GE/src/dev/ds/CMakeFiles/ds.dir/rule

.PHONY : ds

# fast build rule for target.
ds/fast:
	cd /home/<USER>/data/LES/walberla/validate && $(MAKE) -f ../extern/GE/src/dev/ds/CMakeFiles/ds.dir/build.make ../extern/GE/src/dev/ds/CMakeFiles/ds.dir/build
.PHONY : ds/fast

GVertex.o: GVertex.cpp.o

.PHONY : GVertex.o

# target to build an object file
GVertex.cpp.o:
	cd /home/<USER>/data/LES/walberla/validate && $(MAKE) -f ../extern/GE/src/dev/ds/CMakeFiles/ds.dir/build.make ../extern/GE/src/dev/ds/CMakeFiles/ds.dir/GVertex.cpp.o
.PHONY : GVertex.cpp.o

GVertex.i: GVertex.cpp.i

.PHONY : GVertex.i

# target to preprocess a source file
GVertex.cpp.i:
	cd /home/<USER>/data/LES/walberla/validate && $(MAKE) -f ../extern/GE/src/dev/ds/CMakeFiles/ds.dir/build.make ../extern/GE/src/dev/ds/CMakeFiles/ds.dir/GVertex.cpp.i
.PHONY : GVertex.cpp.i

GVertex.s: GVertex.cpp.s

.PHONY : GVertex.s

# target to generate assembly for a file
GVertex.cpp.s:
	cd /home/<USER>/data/LES/walberla/validate && $(MAKE) -f ../extern/GE/src/dev/ds/CMakeFiles/ds.dir/build.make ../extern/GE/src/dev/ds/CMakeFiles/ds.dir/GVertex.cpp.s
.PHONY : GVertex.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... ds"
	@echo "... GVertex.o"
	@echo "... GVertex.i"
	@echo "... GVertex.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/data/LES/walberla/validate && $(CMAKE_COMMAND) -H$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

