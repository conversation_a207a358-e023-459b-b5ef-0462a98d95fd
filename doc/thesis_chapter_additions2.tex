% Additional sections for completeness

% Add to Section 2.3 (Turbulence Modeling) after D<PERSON> Smagorinsky
\subsubsection{WALE Model}

The Wall-Adapting Local Eddy-viscosity (WALE) model provides better near-wall behavior:

\begin{equation}
\nu_t = (C_w \Delta)^2 \frac{(S_{ij}^d S_{ij}^d)^{3/2}}{(\bar{S}_{ij}\bar{S}_{ij})^{5/2} + (S_{ij}^d S_{ij}^d)^{5/4}}
\end{equation}

where $S_{ij}^d$ is the traceless symmetric part of the square of the velocity gradient tensor:

\begin{equation}
S_{ij}^d = \frac{1}{2}(\bar{g}_{ij}^2 + \bar{g}_{ji}^2) - \frac{1}{3}\delta_{ij}\bar{g}_{kk}^2, \quad \bar{g}_{ij} = \frac{\partial \bar{u}_i}{\partial x_j}
\end{equation}

\subsubsection{Vreman Model}

The Vreman model is designed to give zero eddy viscosity for 2D flows:

\begin{equation}
\nu_t = c \sqrt{\frac{B_\beta}{\alpha_{ij}\alpha_{ij}}}
\end{equation}

where:
\begin{align}
\alpha_{ij} &= \frac{\partial u_j}{\partial x_i} \\
\beta_{ij} &= \Delta_m^2 \alpha_{mi}\alpha_{mj} \\
B_\beta &= \beta_{11}\beta_{22} - \beta_{12}^2 + \beta_{11}\beta_{33} - \beta_{13}^2 + \beta_{22}\beta_{33} - \beta_{23}^2
\end{align}

% Add to Section 9.2 after wall functions
\subsection{Pressure Gradient Effects}

For flows with strong pressure gradients, the wall model includes correction terms:

\begin{lstlisting}[caption={Enhanced wall model with pressure gradient},label={lst:pressgrad}]
Real EnhancedWallModel::computeWallShearWithPressureGradient(
    Real u_parallel,
    Real y,
    Real dpdx,        // Pressure gradient along wall
    Real rho) const {
    
    // Base wall shear from standard model
    Real tau_w_base = computeWallShearStress(u_parallel, y, rho);
    
    // Pressure gradient correction
    Real correction = y * dpdx / 2.0;
    
    // Modified wall shear
    Real tau_w = tau_w_base + correction;
    
    // Compute pressure gradient parameter
    Real p_plus = computePressureGradientParameter(dpdx, nu_, u_tau);
    
    // Apply van Driest damping with pressure gradient
    Real A_plus = A_vd_ * (1.0 - 11.8 * p_plus);
    Real damping = 1.0 - exp(-y_plus / A_plus);
    
    return tau_w * damping;
}
\end{lstlisting}

\subsection{Werner-Wengle Power Law}

For high Reynolds number flows, the Werner-Wengle model provides a simple power law:

\begin{equation}
u^+ = \begin{cases}
y^+ & \text{if } y^+ \leq 11.81 \\
8.3(y^+)^{1/7} & \text{if } y^+ > 11.81
\end{cases}
\end{equation}

% Add to Force Calculation section
\subsection{Force Filtering for LES}

In LES, force signals require filtering to remove high-frequency fluctuations:

\begin{lstlisting}[caption={Force filtering implementation},label={lst:forcefilter}]
class ForceFilter {
public:
    enum FilterType { BOX, GAUSSIAN, SPECTRAL };
    
    Vector3<Real> filterForce(
        const Vector3<Real>& force,
        const Vector3<Real>& position,
        const Field<Real,3>& forceField) {
        
        Vector3<Real> filteredForce(0.0);
        Real weightSum = 0.0;
        
        Int radius = Int(ceil(filterWidth_));
        
        // Apply spatial filter
        for (Int dx = -radius; dx <= radius; ++dx) {
            for (Int dy = -radius; dy <= radius; ++dy) {
                for (Int dz = -radius; dz <= radius; ++dz) {
                    Real r = sqrt(dx*dx + dy*dy + dz*dz);
                    Real weight = 0.0;
                    
                    switch(filterType_) {
                        case BOX:
                            weight = (r <= filterWidth_) ? 1.0 : 0.0;
                            break;
                        case GAUSSIAN:
                            weight = exp(-0.5 * r * r / (sigma_ * sigma_));
                            break;
                    }
                    
                    filteredForce += weight * forceField.get(x+dx, y+dy, z+dz);
                    weightSum += weight;
                }
            }
        }
        
        return filteredForce / weightSum;
    }
    
    // Temporal filtering using exponential moving average
    Vector3<Real> temporalFilter(const Vector3<Real>& currentForce, Real dt) {
        const Real alpha = dt / (dt + filterTimeConstant_);
        filteredForce_ = alpha * currentForce + (1.0 - alpha) * filteredForce_;
        return filteredForce_;
    }
};
\end{lstlisting}

% Add to validation section
\subsection{Grid Convergence with Richardson Extrapolation}

The implementation includes automated grid convergence studies:

\begin{lstlisting}[caption={Grid convergence study},label={lst:gridconvergence}]
class GridConvergenceStudy {
public:
    void run(const std::vector<Uint>& resolutions) {
        results_.clear();
        
        for (Uint resolution : resolutions) {
            // Create domain with specified resolution
            Domain domain(resolution, resolution, resolution);
            
            // Run simulation
            auto result = testCase_->run(domain, curvedBC_);
            
            Real gridSpacing = 1.0 / Real(resolution);
            results_.push_back({gridSpacing, result.computedValue});
        }
        
        // Compute order of convergence
        convergenceOrder_ = computeConvergenceOrder();
        
        // Richardson extrapolation for zero-spacing value
        extrapolatedValue_ = richardsonExtrapolation();
    }
    
    Real computeConvergenceOrder() const {
        if (results_.size() < 3) return 0.0;
        
        // Using three finest grids
        Real h1 = results_[0].first;
        Real h2 = results_[1].first;
        Real f1 = results_[0].second;
        Real f2 = results_[1].second;
        Real f3 = results_[2].second;
        
        Real r = h2 / h1;
        Real p = log((f3 - f2) / (f2 - f1)) / log(r);
        
        return p;
    }
    
    Real richardsonExtrapolation() const {
        Real h1 = results_[0].first;
        Real h2 = results_[1].first;
        Real f1 = results_[0].second;
        Real f2 = results_[1].second;
        Real p = convergenceOrder_;
        
        // Extrapolated value at h=0
        return f1 + (f1 - f2) / (pow(h2/h1, p) - 1.0);
    }
};
\end{lstlisting}

% Add a summary table at the end
\section{Implementation Summary}

\begin{table}[h]
\centering
\begin{tabular}{|l|l|l|}
\hline
\textbf{Module} & \textbf{Features} & \textbf{Status} \\
\hline
\multirow{5}{*}{Turbulence} & Smagorinsky (standard \& dynamic) & \checkmark \\
 & WALE model & \checkmark \\
 & Vreman model & \checkmark \\
 & MRT-LES integration & \checkmark \\
 & TRT-LES integration & \checkmark \\
\hline
\multirow{5}{*}{Curved Boundary} & Linear/Cubic interpolation & \checkmark \\
 & Multi-reflection scheme & \checkmark \\
 & Corner/edge treatment & \checkmark \\
 & STL mesh reader & \checkmark \\
 & Force calculation & \checkmark \\
\hline
\multirow{6}{*}{Voxelization} & Ray-triangle intersection & \checkmark \\
 & Robust inside/outside test & \checkmark \\
 & Distance field computation & \checkmark \\
 & Flag field mapping & \checkmark \\
 & Moving mesh support & \checkmark \\
 & Collision detection & \checkmark \\
\hline
\multirow{4}{*}{Wall Models} & Van Driest damping & \checkmark \\
 & Spalding's law & \checkmark \\
 & Werner-Wengle & \checkmark \\
 & y+ adaptive treatment & \checkmark \\
\hline
\multirow{5}{*}{Parallelization} & MPI domain decomposition & Automatic \\
 & OpenMP support & Optional \\
 & Ghost layer communication & Framework \\
 & Load balancing & Not implemented \\
 & GPU support & Not implemented \\
\hline
\multirow{5}{*}{Validation} & Sphere drag & \checkmark \\
 & Cylinder flow & \checkmark \\
 & Flat plate & \checkmark \\
 & Ahmed body & \checkmark \\
 & Grid convergence & \checkmark \\
\hline
\end{tabular}
\caption{Complete implementation feature matrix}
\label{tab:features}
\end{table}