#ifndef OBJECT_HH
#define OBJECT_HH

#include <list>
#include <set>
#include <functional>

// =============================================================================
// Observer Pattern in c++ 
// Define a one-to-many dependency between objects (its parts)  so that when one object 
// changes state, all its dependents (its parts)
// are notified and updated automatically
// 
// TODO implement use template
// =============================================================================

class Object ; // forward definition , anf o virtual fcts

// =============================================================================
class Observer
{
  public :
        virtual void OnUpdateOn (Object*) = 0  ; // pure virtual fc

};
// =============================================================================


class Object //Subject
{
   public:
	virtual void Attach (Observer* o ) {my_list.insert (o) ;}
	virtual void Detach (Observer* o ) {my_list.erase  (o) ;}

	virtual void Notify ()
	{
	  for(std::set<Observer*>::iterator it=my_list.begin();it!= my_list.end();++it)
			(*it)->OnUpdateOn (this) ;
	}
  private:
	std::set< Observer * > my_list ; 
};


// =============================================================================
class ClockTimer: public Object 
{
   public:
        inline void Tick (){
		Notify () ; // every so often . tick
	}
};


// =============================================================================
class DigitalClock :public Observer
{
	public:
		DigitalClock (ClockTimer* t ) : m_timer (t)
		{
			m_timer -> Attach (this) ; 
		}
	
		~DigitalClock () {m_timer -> Detach (this) ;}

                inline void OnUpdateOn (Object* timer) {/* query timer and redraw */  ; }
	private:
		ClockTimer* m_timer ;
};


// =============================================================================
#endif

