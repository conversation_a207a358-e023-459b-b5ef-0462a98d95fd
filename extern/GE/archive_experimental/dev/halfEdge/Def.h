#ifndef DEF_HH
#define DEF_HH


#include<vector>
#include<map>
#include <utility> //pair

// #include <tr1/unordered_map>
#include <string>
#include <iostream>
// ============================================================================
// #include <tr1/unordered_map>
// typedef std::tr1::unordered_map<std::string, int> AgeTable;
// 
// int main() 
// {
//       AgeTable ages;
//       ages.insert( std::make_pair( "Joe", 25 ) );
//       ages.insert( std::make_pair( "Sally", 18 ) );
//       ages.insert( std::make_pair( "Billy", 11 ) );
//       AgeTable::iterator iter;
//       for ( iter = ages.begin(); iter != ages.end(); iter++ )
//       {
// 	  std::cout << iter->first << " is " << iter->second << std::endl;
//       }
// }

// ============================================================================
/*
	HalfEdge dataStructor  example 
	
	Mesh ==	halfEdge table    +  // 
	        	EdgeTable         +  // Edgemap 
 	      	vertices table    +
	        	facet table
	       //                  <int       , Haledge>                
	     == std::vector<Halfedge>+ // Halfedge <=> std::pair<v_incident, he_opp >
	        std::map<v_start  , v_end> +    // 
	        float *vertices[]  + 
	        int*   faces[]
// info
	typedef std::pair < int , int>    AuxEdge;   // Edge  < v_start  , v_endpair >
	typedef std::map  < AuxEdge, int> EdgeMap;  // Edge list < edge , edgeindex >

	typedef std::pair  <int , int>    Halfedge;	  //  v_end ,  opp
	typedef std::vector<Halfedge > 	  HalfEdges;  //  Table

*/
// ============================================================================

using std::cout ;
using std::cerr ;
using std::endl ;
namespace ge
{
// ============================================================================	
// half  edge 
// ============================================================================
typedef std::pair  <int , int>  Halfedge; 	//! < v_end ,  opp >
typedef std::vector<Halfedge >  HalfEdges; 	//!  HalfEdges table 

// ============================================================================	
// Edge 
// ============================================================================
typedef std::pair< int   , int>  Edge ; 	//!  < v_start   ,  v_end  >  
typedef std::map < Edge  , int>  EdgeMap ; 
                //!<edge, edgeindex>// ?maybe->better |->typedef std::map <int,Edge> EdgeMap ; 


// ============================================================================	
// TODO improove by using hash table
// ============================================================================
// typedef std::tr1::unordered_map < Edge  , int>  EdgeMap ;
// typedef std::set < Edge  , int>  EdgeMap ; 	//!  < edge      ,  edgeindex >
// hash_set/hash_map


// ============================================================================	
//Vertex
// ============================================================================
typedef std::pair< int , int >  VerID ;  //!   <vertex_id, He_id > 

  std::vector<float > VERTICES ;

	typedef struct vertex
	{
		// representation
	 	float m_x , m_y , m_z ;
		// constructor
	 	vertex (const float& x, const float&y, const float&z)
	         : m_x (x), m_y (y), m_z (z)
		{}
	} vertex;
    
	
//
// ============================================================================	
// Input vertices array
// TODO, read from “.STL” file -> “.Obj” -> half Edge data structure
// improove th voxelization algorithm 
// Cherif. Mihoubi
// ============================================================================

// some vertices
	vertex v0 ( 0.f  , 0.f  ,  0.f )  ;
	vertex v1 ( 0.f  , 1.f  ,  0.f )  ;
	vertex v2 ( 0.5f , 0.5f , 0.5f )  ;
	vertex v3 (-0.5f , 0.5f , 0.5f )  ; 

const float vertices[]=	{ // vertices
			v0.m_x   , v0.m_y ,   v0.m_z , 	// v0 <x , y , z>
			v1.m_x   , v1.m_y ,   v1.m_z ,      	// v1 <x , y , z>
			v2.m_x   , v2.m_y ,   v2.m_z , 	// v2 <x , y , z>
			v3.m_x   , v3.m_y ,   v3.m_z , 	// v3 <x , y , z>
		};

//  faces array 
const int   faces[]   = { 0 , 2 , 1 , // face [0] <v1_indx, v2_indx , v3_index>
			  1 , 2 , 3 , // face [1]
			  0 , 1 , 3 , // face [2]
			  0 , 3 , 2   // face [3]
                        };

}  // end namespace ge
// ============================================================================	

#endif
