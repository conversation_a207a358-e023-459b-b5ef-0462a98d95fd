#ifndef COUNTER_HH
#define COUNTER_HH


#include <iostream>


//! <configure::ElementType>
//! <config::LenghType> : int, long or short 	
// config::
// config::


template <typename T, typename Configure>
struct Counter /*: boost::noncopyable*/
{
  	typedef typename Configure::LenghType LenghType; // export 

  protected :// representaion   

	static LenghType objects_created;
    	static LenghType objects_alive;
  
  public :
  
    	Counter(){
        	++objects_created;
        	++objects_alive;
    	}
 
    	virtual ~Counter() { --objects_alive;}

    	// info functions     
    	inline LenghType GetCountObjects_created() const {return objects_created;}
    	inline LenghType GetCountObjects_alive  () const {return objects_alive;}
};

// initialization 
template <typename T, typename Configure>
typename Counter<T,Configure>::LenghType Counter<T,Configure>::objects_created( 0 );

template <typename T, typename Configure>
typename Counter<T,Configure>::LenghType Counter<T,Configure>::objects_alive  ( 0 );



// template <typename T>
// struct TraceCounter : T /*: boost::noncopyable*/
// {
//     TraceCounter() : T () 
//     {
// 	std::cout<< "Constructor " << std::endl ;
// 	std::cout<< "GetCountObjects_created   ->  " << GetCountObjects_created( ) << std::endl;
// 	std::cout<< "GetCountObjects_alive     ->  " << GetCountObjects_alive( )   << std::endl;
// 	std::cout<< "=========================================================== " << std::endl ;
//     }
//  
//     ~TraceCounter()
//     {
// 	std::cout<< "Destructor " << std::endl ;
// 	std::cout<< "GetCountObjects_created   ->  " << GetCountObjects_created( ) << std::endl;
// 	std::cout<< "GetCountObjects_alive     ->  " << GetCountObjects_alive( )   << std::endl;
// 	std::cout<< "=========================================================== " << std::endl;	
//     }
// };





#endif




// template <typename T>
// struct /*: boost::noncopyable*/
// {
//     Counter()
//     {
//         ++objects_created;
//         ++objects_alive;
// // 	std::cout<< "Constructor " << std::endl ;
// // 	std::cout<< "GetCountObjects_created   ->  " << GetCountObjects_created( ) << std::endl;
// // 	std::cout<< "GetCountObjects_alive     ->  " << GetCountObjects_alive( )   << std::endl;
// // 	std::cout<< "=========================================================== " << std::endl ;
//     }
//  
//     virtual ~Counter()
//     {
//         --objects_alive;
// // 	std::cout<< "Destructor " << std::endl ;
// // 	std::cout<< "GetCountObjects_created   ->  " << GetCountObjects_created( ) << std::endl;
// // 	std::cout<< "GetCountObjects_alive     ->  " << GetCountObjects_alive( )   << std::endl;
// // 	std::cout<< "=========================================================== " << std::endl;	
//     }
// // representaion
//     static int objects_created;
//     static int objects_alive;
// // info functions     
//     inline int GetCountObjects_created( ) const {return objects_created;}
//     inline int GetCountObjects_alive  ( ) const {return objects_alive;}        
// };
// // initialization 
// template <typename T> int Counter<T>::objects_created( 0 );
// template <typename T> int Counter<T>::objects_alive  ( 0 );
