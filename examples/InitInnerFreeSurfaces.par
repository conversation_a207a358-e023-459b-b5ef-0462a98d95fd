// The init block supports an inner block which specifies objects or free-surface related
// descriptions inside the domain.
// For the free surface extension currently the following types of blocks are supported:
// atmosphere, bubble, drop
// Assume that the domain is initially completely filled with liquid. Both atmosphere blocks
// and bubble blocks may only be written in areas where liquid resides, drop blocks only at
// places where gas cells were placed. Thus, drop blocks are only allowed recursively inside
// atmosphere or bubble blocks.
// If the description results in intersecting objects, warnings are issued. If an object would
// be placed too close to another one (i.e. they would have to share the one cell for their
// interfaces), an error will be thrown.
// Similarly to the descriptions of (moving) objects (see other example files),
// every parameter can be specified in coordinates or lattice measures. For specifying lattice
// measures, just add "_L" to the name of the parameter.
// All values can be numbers or expressions that will be evaluated by PhysicalCheck class, thus,
// every quantity specified directly or indirectly in simdata block can be used.
// For explicit examples see the end of this file.


init {
  include BoundaryDescriptions.par

   // The bubble is a region of gas enclosed by fluid
   bubble {
      type sphere;                             // the initial shape of the bubble (currently 'sphere' and 'block' are supported)
      position_L <'sizeX'/2,14,12>;            // the position of the bubble (required if 'type' is 'sphere')
      radius_L 2;                              // the radius (required if 'type' is 'sphere')
   }            


   // The atmosphere is a region of gas with constant pressure of 1. 
   // There can only be one atmosphere!
   atmoshpere {
      x_L 1..'domainX'-1;                      // range in x direction
      y   'dx'..'ylength';                     // range in y direction
      z_L 'domainZ'/2..'domainZ'-1;            // range in z direction
      
      // inside the atmosphere drops can be specified
      drop {
         type sphere;                          // the initial shape of the bubble (currently 'sphere' and 'block' are supported)
         position_L <10,12,'domainZ'/2+10>;    // the position of the drop (required if 'type' is 'sphere')
         radius_L 4;                           // the radius (required if 'type' is 'sphere')
         
         // inside drops, again bubbles can be specified
         bubble {
            type sphere;                       // the initial shape of the bubble (currently 'sphere' and 'block' are supported)
            position_L <12,14,'domainZ'/2+12>; // the position of the bubble
            radius_L 2;                        // the radius (required if 'type' is 'sphere')
         }            
      }
   }

   // Also in a bubble, drops can be specified
   bubble {
      type rect;                               // the initial shape of the bubble (currently 'sphere' and 'block' are supported)
      x_L 0.25*'domainX'..0.75*'domainX';      // range in x direction (required if 'type' is 'rect')
      y_L 10..25;                              // range in x direction (required if 'type' is 'rect')
      z_L 10..30;                              // range in x direction (required if 'type' is 'rect')

      drop {
         type sphere;                          // the initial shape of the drop (currently only 'sphere' supported)
         position_L <((3*'sizeX')/4)-9,19,17>; // the position of the drop (required if 'type' is 'sphere')
         radius_L 4;                           // the radius (required if 'type' is 'sphere')
      }
   }            

}