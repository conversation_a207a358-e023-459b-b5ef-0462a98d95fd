
## rm -f espec*.dat

for i in data_*.dat ;
    do
        filename="$i"
	fname=$(basename "$filename")
	extension="${fname##*.}"
	fname="${fname%.*}"
	time=${fname#*_}
  	#echo  "$i"  "   " "extension" $extension   "     fname " $fname  "   time   " ${fname#*_}
#        echo  "espec${fname#*_}.dat" 
	file_exist=espec"${fname#*_}".dat 

        if [ ! -f  $file_exist   ]; then
	       	echo "process  " ${fname#*_}  " ... "
 	        python2 tkespec.py ${fname#*_} >> log.python 	
        fi 
done 


