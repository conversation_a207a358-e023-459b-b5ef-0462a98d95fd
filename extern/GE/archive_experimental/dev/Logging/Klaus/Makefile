# Compiler configuration
CXX      = g++
# CXXFLAGS = -Wall -Werror -Wextra -Wshadow
CXXFLAGS = -O3 
#  -O3 -DNDEBUG 

# Compiler environment
#if cluster
# LIBS    = -lm -L../../lib -lpe -L/central/boost/1.43/lib -lboost_thread -lboost_system -lboost_filesystem -L/opt/scali/lib64 -lmpi
#else no cluster
LIBS    = -lm -L../../lib \
	  -L/central/boost/1.43/lib\
	  -lboost_thread \
	  -lboost_system  \
	  -lboost_filesystem

# INCLUDE = -I../.. -isystem /central/boost/1.43/include -isystem /opt/scali/include
INCLUDE =

SRC	= main.cpp Logging.cpp
PROG	= logger 


# Build rules
$(PROG): $(SRC)
	$(CXX) $(CXXFLAGS) $(SRC) -o $@ $(INCLUDE) $(LIBS)


# Clean up rules
clean:
	@echo "Cleaning up..."
	rm -f $(PROG) *.*~ *~ logFile*


# ----- Setting the independent commands -----
.PHONY: clean
