
#include <fstream>               // for std::cout
#include <iostream>               // 
#include "boost/filesystem.hpp"   // includes all needed Boost.Filesystem declarations
#include <boost/lexical_cast.hpp>

int main ()
{
	std::string  dir = "Cherif";

	if ( boost::filesystem::is_directory (  dir  ) )
	 	boost::filesystem::remove( dir );
/* 	
boost::filesystem::create_directory( dir );

//	boost::filesystem::remove_all( dir +  "/foobar" );
	boost::filesystem::create_directories( dir+ "/foobar" );
	
	std::ofstream file( (dir + "/foobar/cheeze").c_str() );
	file << "tastes good!\n";
	file.close();
	if ( !boost::filesystem::exists( dir + "/foobar/cheeze" ) )
	  std::cout << "Something is rotten in foobar\n";

  */      
	return (0) ;
}
