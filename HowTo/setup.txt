/*
Projekt Setup
Author: <PERSON>
Date:14.11.2006
Last-Update: 14.11.2006
*/

To set up the projekt call ccmake in the mode in the base direktory
of the project.

#ccmake .

In the ccmake menu you can either setup the basics by just pressing 'c' or go
the 't' in the advanced mode to setup stuff like the compiler flags. All these
options can be modified later on in the file CMakeCache.txt, which is located
in the base directory. Afterwards you can generate the project by pressing 'g'
and compile the porject by just executing
the flowing comand in the base directory.

#make 

If you add new files to the src directory, these have to be added to the
CMakeList.txt to the variable "CurrentSrcFiles", including the correspondig header files. 

SET(CurrentSrcFiles "main.cpp AddedFile.cpp")

