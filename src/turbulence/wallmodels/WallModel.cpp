//=================================================================================================
/*!
//  \file WallModel.cpp
//  \brief Implementation of wall models for LES
*/
//=================================================================================================

#include "WallModel.h"
#include "../../Logging.h"
#include <cmath>
#include <algorithm>
#include <queue>

namespace walberla {
namespace turbulence {

//=================================================================================================
//
//  VAN DRIEST DAMPING IMPLEMENTATION
//
//=================================================================================================

Vector3<Real> VanDriestDamping::computeWallShearStress(
    const Vector3<Real>& velocity,
    const Real& distance,
    const Real& density,
    const Real& viscosity) const
{
    // For Van Driest, we typically use a simple linear approximation near wall
    // tau_w = mu * du/dy ≈ mu * u/y
    Real velocityMag = std::sqrt(velocity[X]*velocity[X] + 
                                velocity[Y]*velocity[Y] + 
                                velocity[Z]*velocity[Z]);
    
    Real tau_w = viscosity * velocityMag / distance;
    
    // Return wall shear stress vector aligned with velocity
    Vector3<Real> wallShear;
    if(velocityMag > 1e-10) {
        wallShear[X] = tau_w * velocity[X] / velocityMag;
        wallShear[Y] = tau_w * velocity[Y] / velocityMag;
        wallShear[Z] = tau_w * velocity[Z] / velocityMag;
    } else {
        wallShear = Vector3<Real>(0.0, 0.0, 0.0);
    }
    
    return wallShear;
}

Real VanDriestDamping::computeNearWallEddyViscosity(
    const Real& distance,
    const Real& frictionVelocity,
    const Real& viscosity) const
{
    // Compute y+
    Real yPlus = computeYPlus(distance, frictionVelocity, viscosity);
    
    // Van Driest damping function
    Real fmu = getDampingFunction(yPlus);
    
    // Mixing length model with damping
    const Real kappa = 0.41; // von Karman constant
    Real lm = kappa * distance * fmu;
    
    // Eddy viscosity from mixing length
    // nu_t = lm^2 * |du/dy| ≈ lm * u_tau * fmu
    return lm * frictionVelocity * fmu;
}

//=================================================================================================
//
//  WALL FUNCTION IMPLEMENTATION
//
//=================================================================================================

Vector3<Real> WallFunction::computeWallShearStress(
    const Vector3<Real>& velocity,
    const Real& distance,
    const Real& density,
    const Real& viscosity) const
{
    Real velocityMag = std::sqrt(velocity[X]*velocity[X] + 
                                velocity[Y]*velocity[Y] + 
                                velocity[Z]*velocity[Z]);
    
    // Solve for friction velocity using Newton iteration
    Real uTau = solveFrictionVelocity(velocityMag, distance, viscosity);
    
    // Wall shear stress magnitude
    Real tau_w = density * uTau * uTau;
    
    // Return wall shear stress vector
    Vector3<Real> wallShear;
    if(velocityMag > 1e-10) {
        wallShear[X] = tau_w * velocity[X] / velocityMag;
        wallShear[Y] = tau_w * velocity[Y] / velocityMag;
        wallShear[Z] = tau_w * velocity[Z] / velocityMag;
    } else {
        wallShear = Vector3<Real>(0.0, 0.0, 0.0);
    }
    
    return wallShear;
}

Real WallFunction::computeNearWallEddyViscosity(
    const Real& distance,
    const Real& frictionVelocity,
    const Real& viscosity) const
{
    Real yPlus = computeYPlus(distance, frictionVelocity, viscosity);
    
    if(yPlus < 11.6) {
        // Viscous sublayer - no turbulent viscosity
        return 0.0;
    } else {
        // Log layer - use mixing length hypothesis
        Real lm = kappa_ * distance;
        return lm * frictionVelocity - viscosity;
    }
}

Real WallFunction::computeUPlus(const Real& yPlus) const
{
    if(yPlus < 11.6) {
        // Viscous sublayer: u+ = y+
        return yPlus;
    } else {
        // Log layer: u+ = (1/kappa) * ln(y+) + B
        return (1.0 / kappa_) * std::log(yPlus) + B_;
    }
}

Real WallFunction::solveFrictionVelocity(
    const Real& velocity,
    const Real& distance,
    const Real& viscosity) const
{
    // Newton iteration to solve:
    // u/u_tau = f(y+ = y*u_tau/nu)
    
    const Real tol = 1e-6;
    const int maxIter = 20;
    
    // Initial guess
    Real uTau = 0.1 * velocity;
    
    for(int iter = 0; iter < maxIter; ++iter) {
        Real yPlus = distance * uTau / viscosity;
        Real uPlus = computeUPlus(yPlus);
        
        // Residual: F = u/u_tau - u+
        Real F = velocity / uTau - uPlus;
        
        // Derivative: dF/du_tau
        Real dF_duTau;
        if(yPlus < 11.6) {
            // d(u+)/d(u_tau) = y/nu for viscous sublayer
            dF_duTau = -velocity / (uTau * uTau) - distance / viscosity;
        } else {
            // d(u+)/d(u_tau) = (1/kappa) * (1/u_tau) for log layer
            dF_duTau = -velocity / (uTau * uTau) - distance / (kappa_ * viscosity);
        }
        
        // Newton update
        Real delta = -F / dF_duTau;
        uTau += delta;
        
        // Ensure positive
        uTau = std::max(uTau, 1e-10);
        
        if(std::abs(delta) < tol * uTau) {
            break;
        }
    }
    
    return uTau;
}

//=================================================================================================
//
//  WERNER-WENGLE MODEL IMPLEMENTATION
//
//=================================================================================================

Vector3<Real> WernerWengleModel::computeWallShearStress(
    const Vector3<Real>& velocity,
    const Real& distance,
    const Real& density,
    const Real& viscosity) const
{
    Real velocityMag = std::sqrt(velocity[X]*velocity[X] + 
                                velocity[Y]*velocity[Y] + 
                                velocity[Z]*velocity[Z]);
    
    Real tau_w;
    
    // Werner-Wengle integral approach
    Real nu = viscosity / density;
    Real ratio = velocityMag * distance / nu;
    
    if(ratio < yCrit_) {
        // Linear region
        tau_w = density * nu * velocityMag / distance;
    } else {
        // Power law region
        Real factor = std::pow(A_, -1.0/B_) * std::pow(nu, (1.0-B_)/B_);
        tau_w = density * std::pow(velocityMag, (1.0+B_)/B_) * 
                std::pow(distance, -(1.0+B_)/B_) * factor;
    }
    
    // Return wall shear stress vector
    Vector3<Real> wallShear;
    if(velocityMag > 1e-10) {
        wallShear[X] = tau_w * velocity[X] / velocityMag;
        wallShear[Y] = tau_w * velocity[Y] / velocityMag;
        wallShear[Z] = tau_w * velocity[Z] / velocityMag;
    } else {
        wallShear = Vector3<Real>(0.0, 0.0, 0.0);
    }
    
    return wallShear;
}

Real WernerWengleModel::computeNearWallEddyViscosity(
    const Real& distance,
    const Real& frictionVelocity,
    const Real& viscosity) const
{
    Real yPlus = computeYPlus(distance, frictionVelocity, viscosity);
    
    if(yPlus < yCrit_) {
        // Linear region - no turbulent viscosity
        return 0.0;
    } else {
        // Power law region
        // Derive eddy viscosity from power law velocity profile
        const Real kappa = 0.41;
        Real lm = kappa * distance * (1.0 - std::exp(-yPlus / 26.0));
        return lm * frictionVelocity - viscosity;
    }
}

//=================================================================================================
//
//  WALL MODEL MANAGER IMPLEMENTATION
//
//=================================================================================================

WallModelManager::WallModelManager()
    : wallModel_(std::make_unique<WallFunction>()),
      maxSearchRadius_(10.0)
{
}

void WallModelManager::computeWallDistances(
    const FlagField<Uint>& flagField,
    ScalarField<Real>& wallDistance)
{
    // Use breadth-first search to compute wall distances
    const int nx = flagField.XSize();
    const int ny = flagField.YSize();
    const int nz = flagField.ZSize();
    
    // Initialize distances to large value
    const Real maxDist = 1e10;
    for(int z = 0; z < nz; ++z) {
        for(int y = 0; y < ny; ++y) {
            for(int x = 0; x < nx; ++x) {
                wallDistance.Set(x, y, z, maxDist);
            }
        }
    }
    
    // Queue for BFS
    std::queue<std::tuple<int, int, int>> queue;
    
    // Find all wall cells and initialize queue
    for(int z = 1; z < nz-1; ++z) {
        for(int y = 1; y < ny-1; ++y) {
            for(int x = 1; x < nx-1; ++x) {
                if(flagField.IS_FLAG(x, y, z, NOSLIP)) {
                    wallDistance.Set(x, y, z, 0.0);
                    queue.push(std::make_tuple(x, y, z));
                }
            }
        }
    }
    
    // BFS to compute distances
    const int dx[] = {-1, 1, 0, 0, 0, 0};
    const int dy[] = {0, 0, -1, 1, 0, 0};
    const int dz[] = {0, 0, 0, 0, -1, 1};
    
    while(!queue.empty()) {
        auto pos = queue.front();
        Uint x = std::get<0>(pos);
        Uint y = std::get<1>(pos);
        Uint z = std::get<2>(pos);
        queue.pop();
        
        Real currentDist = wallDistance.Get(x, y, z);
        
        // Check all neighbors
        for(int i = 0; i < 6; ++i) {
            int nx = x + dx[i];
            int ny = y + dy[i];
            int nz = z + dz[i];
            
            if(nx >= 0 && nx < flagField.XSize() &&
               ny >= 0 && ny < flagField.YSize() &&
               nz >= 0 && nz < flagField.ZSize() &&
               flagField.IS_FLAG(nx, ny, nz, FLUID)) {
                
                Real newDist = currentDist + 1.0; // Lattice units
                
                if(newDist < wallDistance.Get(nx, ny, nz)) {
                    wallDistance.Set(nx, ny, nz, newDist);
                    if(newDist < maxSearchRadius_) {
                        queue.push(std::make_tuple(nx, ny, nz));
                    }
                }
            }
        }
    }
    
    LOG_INFO("Computed wall distances for domain");
}

void WallModelManager::applyWallModel(
    const VelField& velField,
    const ScalarField<Real>& wallDistance,
    ScalarField<Real>& eddyViscosity,
    const Real& molecularViscosity,
    const Real& density)
{
    const int nx = velField.XSize();
    const int ny = velField.YSize();
    const int nz = velField.ZSize();
    
    for(int z = 1; z < nz-1; ++z) {
        for(int y = 1; y < ny-1; ++y) {
            for(int x = 1; x < nx-1; ++x) {
                Real dist = wallDistance.Get(x, y, z);
                
                if(dist < maxSearchRadius_ && dist > 0.0) {
                    // Get velocity
                    Vector3<Real> vel;
                    velField.GET_VEL(x, y, z, vel);
                    
                    // Compute wall shear stress
                    Vector3<Real> wallShear = wallModel_->computeWallShearStress(
                        vel, dist, density, molecularViscosity);
                    
                    // Get friction velocity
                    Real uTau = wallModel_->computeFrictionVelocity(wallShear, density);
                    
                    // Compute near-wall eddy viscosity
                    Real nu_t_wall = wallModel_->computeNearWallEddyViscosity(
                        dist, uTau, molecularViscosity);
                    
                    // Blend with existing eddy viscosity
                    Real nu_t_current = eddyViscosity.Get(x, y, z);
                    Real blending = std::exp(-dist / 5.0); // Smooth blending
                    Real nu_t_final = blending * nu_t_wall + (1.0 - blending) * nu_t_current;
                    
                    eddyViscosity.Set(x, y, z, nu_t_final);
                }
            }
        }
    }
}

Vector3<Real> WallModelManager::getWallAdjacentVelocity(
    const VelField& velField,
    const Uint x, const Uint y, const Uint z,
    const FlagField<Uint>& flagField) const
{
    // Find the nearest fluid cell to the wall
    Vector3<Real> velocity(0.0, 0.0, 0.0);
    
    // Check all neighboring cells
    for(int dz = -1; dz <= 1; ++dz) {
        for(int dy = -1; dy <= 1; ++dy) {
            for(int dx = -1; dx <= 1; ++dx) {
                if(dx == 0 && dy == 0 && dz == 0) continue;
                
                int nx = x + dx;
                int ny = y + dy;
                int nz = z + dz;
                
                if(flagField.IS_FLAG(nx, ny, nz, FLUID)) {
                    velField.GET_VEL(nx, ny, nz, velocity);
                    return velocity;
                }
            }
        }
    }
    
    return velocity;
}

void WallModelManager::findNearestWall(
    const Uint x, const Uint y, const Uint z,
    const FlagField<Uint>& flagField,
    Uint& wallX, Uint& wallY, Uint& wallZ,
    Real& distance) const
{
    distance = 1e10;
    wallX = x;
    wallY = y;
    wallZ = z;
    
    // Search in expanding cube
    int searchRadius = static_cast<int>(maxSearchRadius_);
    
    for(int r = 1; r <= searchRadius; ++r) {
        for(int dz = -r; dz <= r; ++dz) {
            for(int dy = -r; dy <= r; ++dy) {
                for(int dx = -r; dx <= r; ++dx) {
                    // Only check surface of cube
                    if(std::abs(dx) != r && std::abs(dy) != r && std::abs(dz) != r) continue;
                    
                    int nx = x + dx;
                    int ny = y + dy;
                    int nz = z + dz;
                    
                    if(nx >= 0 && nx < flagField.XSize() &&
                       ny >= 0 && ny < flagField.YSize() &&
                       nz >= 0 && nz < flagField.ZSize() &&
                       flagField.IS_FLAG(nx, ny, nz, NOSLIP)) {
                        
                        Real dist = std::sqrt(dx*dx + dy*dy + dz*dz);
                        if(dist < distance) {
                            distance = dist;
                            wallX = nx;
                            wallY = ny;
                            wallZ = nz;
                        }
                    }
                }
            }
        }
        
        // Early exit if wall found
        if(distance < 1e9) break;
    }
}

} // namespace turbulence
} // namespace walberla