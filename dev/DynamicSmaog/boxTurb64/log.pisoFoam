/*---------------------------------------------------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  5.x                                   |
|   \\  /    A nd           | Web:      www.OpenFOAM.org                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
Build  : 5.x-197d9d3bf20a
Exec   : pisoFoam
Date   : Jul 16 2018
Time   : 13:52:58
Host   : "mihoubi-X9DAi"
PID    : 29368
I/O    : uncollated
Case   : /media/mihoubi/data/LES/walberla/dev/DynamicSmaog/boxTurb64
nProcs : 1
sigFpe : Enabling floating point exception trapping (FOAM_SIGFPE).
fileModificationChecking : Monitoring run-time modified files using timeStampMaster (fileModificationSkew 10)
allowSystemOperations : Allowing user-supplied system call operations

// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //
Create time

Create mesh for time = 0


PISO: Operating solver in PISO mode

Reading field p

Reading field U

Reading/calculating face flux field phi



--> FOAM FATAL IO ERROR: 
Unable to set reference cell for field p
    Please supply either pRefCell or pRefPoint


file: /media/mihoubi/data/LES/walberla/dev/DynamicSmaog/boxTurb64/system/fvSolution.PISO from line 39 to line 40.

    From function bool Foam::setRefCell(const volScalarField&, const volScalarField&, const Foam::dictionary&, Foam::label&, Foam::scalar&, bool)
    in file cfdTools/general/findRefCell/findRefCell.C at line 105.

FOAM exiting

