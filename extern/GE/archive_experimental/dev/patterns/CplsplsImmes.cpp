// Item 16 – Pointers to Member Functions Are Not Pointers
// - the address of a non-static member function does not give you an address, 
// it gives you the pointer to the member function
// -

class Point{

};

class Shape 
{
  public:
      //…
      void moveTo( Point newLocation );
      bool validate() const;
      virtual bool draw() const = 0;
      //…
};

class Circle : public Shape 
{
    //…
    bool draw() const;
    //..
};
//…
void (Shape::*mf1)( Point ) = &Shape::moveTo; 	// not a pointer
bool (Shape::*mf2)() const  = &Shape::validate;	// 


int main ()
{
      Circle circ;
      Shape *pShape   ;
      (pShape->*mf2)(); 	// call Shape::validate
      (circ.*mf2)()   		// same

      mf2 = &Shape::draw; 	// draw is virtual
      (pShape->*mf2)(); 	// call Circle::draw
      
      return (0) ;
} 




      void Shape::moveTo( Point newLocation ){
	
      }
      
      bool Shape::validate() const{
	
      }
      
      bool Circle::draw() const{
	
      }
      
      