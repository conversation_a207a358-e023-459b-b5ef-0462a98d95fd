# Evaluation of cost funtions: f1->total amount of tranfert data, and f2 -> total mnumber of message
set terminal png transparent nocrop enhanced font arial 8 size 1000,800
set output 'bouth.1.png'
set yrange [1:4]
set xrange [1:4]
set view 60, 30, 0.85, 1.1
set samples 20, 20
set isosamples 21, 21
set contour base
set title "Evaluation of cost funtions: total amount of tranfert data, and total mnumber of message"
set xlabel "npx axis"
set ylabel "npy axis"
#set zlabel "total amount of tranfert data, total mnumber of message"
set zlabel  offset character 1, 0, 0 font "" textcolor lt -1 norotate
splot 2.0 *( 100*( x -1.0) + 200*( y - 1.0) )/800 ,  2.0 *( x *( y - 1.0)  +  y*( x -1.0) )/8 ;
set contour surface
set cntrparam order 8
replot
