#include "Container.h"

using std::cout;
using std::cerr;
using std::endl;

//==============================================================================
// Container Class Methods
//==============================================================================




//==============================================================================
/*!
  @name int FindVertexIndex( Point & a )
  @brief find the index for the given vertex in the Point-list (VVPoint)
  @param Point& a
  @return int index
*/
//------------------------------------------------------------------
/*inline*/
template<typename Config>
/*inline*/ int Container<Config>::FindVertexIndex(const Point & a )
{
	std::vector<Point >::const_iterator current = VVPoint.begin();
//	std::vector<Point >::const_iterator current = VVPoint.begin();
	int ret =  0 ;

	while(current != VVPoint.end() )
                if (*current != a){
		 ++ret;
                 ++current;
	     }else break;
	return ret;
}





//==============================================================================
/*!
  @name void AddNewFaceNbIndex(TFace&T , int &index )
  @brief
  @param TFace& T
  @param int  & index  
  @return if true  : mean it's new vertex, which  have been add to the e lists
	  else     : mean it's exist already in the lists
  
*/
//==============================================================================
template<typename Config>
/*inline*/ void Container<Config>::AddNewFaceNbIndex(TFace&T ,const int &index )
{
std::vector<int >::iterator current = T.facet_list_Of_nb.begin();
while (current != T.facet_list_Of_nb.end() )
 {//if(binary_search(T.facet_list_Of_nb.begin(),T.facet_list_Of_nb.end(),index))
  if ( *find(T.facet_list_Of_nb.begin(),T.facet_list_Of_nb.end(),index)!= index)
   {
	T.facet_list_Of_nb.push_back (index);
	break;
     }else{
	break;
   }
   ++current;
 }
}


// =============================================================================
/*!   @name void Containerr::RemoveDuplicatesFacetIndices(TFace& T )
      @brief this function will remove those repeated element ids in the
	    * facet_list_Of_nb
      @param TFace& T
*/
// =============================================================================
template<typename Config>
/*inline*/ void Container<Config>::RemoveDuplicatesFacetIndices(TFace& T )
{
	std::sort(T.facet_list_Of_nb.begin(), T.facet_list_Of_nb.end());

	std::vector<int >::iterator new_end =
             std::unique(T.facet_list_Of_nb.begin(), T.facet_list_Of_nb.end()) ;

        T.facet_list_Of_nb.erase( new_end  ,  T.facet_list_Of_nb.end());
}



// =============================================================================
/*!
  @name void Containerr::RemoveDuplicatesVertexNbIndices(Vertex& V)
  @brief this function will remove those repeated element ids in the vertices
 * list Of Nb
  @param Vertex& T
  
*/
// =============================================================================
template<typename Config>
/*inline*/ void Container<Config>::RemoveDuplicatesVertexNbIndices(Vertex& V)
{
    std::sort(V.V_nb.begin(), V.V_nb.end());
    std::vector<int >::iterator new_end = std::unique(V.V_nb.begin(), V.V_nb.end()) ;
    V.V_nb.erase(new_end  ,  V.V_nb.end());
}




// =============================================================================
/*!
  @name bool ItsInVertexNeighborbsList(int Vertex_id, int index)
  @brief search if "" is int the vertex_id-neighborbs or not
  @param int Vertex_id  
  @param int index
  @return bool
  
*/
// =============================================================================
// inline
template<typename Config>
bool Container<Config>::ItsInVertexNeighborbsList(const int Vertex_id, const int index)
{
     std::vector<int >::iterator current = VVertices[Vertex_id]->V_nb.begin();
     bool r = true ;
     while (current != VVertices[Vertex_id] -> V_nb .end() )
     {//if(binary_search(T.facet_list_Of_nb.begin(),T.facet_list_Of_nb.end(),index))
  	if (*find(VVertices[Vertex_id]->V_nb.begin(),
                  VVertices[Vertex_id]->V_nb.end()  ,
                  index)!= index)
	{
	  r = false ;
	  break;
	} else {
	  break;
	}
	++current;
      }
   return r;
}




// =============================================================================
/*!
  @name void Containerr::BuildFaceVertexMeshesRepresentation()
  @brief set up the facet_list for eatch vertex
  
*/
// =============================================================================
template<typename Config>
/*inline*/ void Container<Config>::BuildFaceVertexMeshesRepresentation()
{
	for (int i=0 ;i<VVPoint.size();++i) VVertices.push_back(new Vertex  );

	for (int i=0 ;i<VVTriangles.size();++i)
	{
	   VVertices[VVTriangles[i].v_indices[0] ]-> facet_list.push_back(i) ;
	   VVertices[VVTriangles[i].v_indices[1] ]-> facet_list.push_back(i) ;
	   VVertices[VVTriangles[i].v_indices[2] ]-> facet_list.push_back(i) ;
	}
}




// -------------------------------------------------------------------
/*!
  @name void SeTFacetItsConnectedFacetList()
  @brief set up the facet_list for eatch vertex
  
*/
// -------------------------------------------------------------------
template<typename Config>
/*inline*/ void Container<Config>::SeTFacetItsConnectedFacetList()
{
 std::cerr << "Container::SeTFacetItsConnectedFacetList()-->\n	-> pls chack" << std::endl;
 for (int i=0 ;i<VVTriangles.size();++i  )
 {
   TFace  T = VVTriangles[i] ;
   int tmp0 = T.v_indices[0] ;
   int tmp1 = T.v_indices[1] ;
   int tmp2 = T.v_indices[2] ;

   for (int ii = 0; ii < VVertices[tmp0]->facet_list.size(); ++ ii ){
     if(VVertices[tmp0]->facet_list[ii] !=i )
     VVTriangles[i].facet_list_Of_nb.push_back(VVertices[tmp0]->facet_list[ii]);
   }

   for (int ii = 0; ii< VVertices[tmp1]->facet_list.size(); ++ ii ){
     if(VVertices[tmp1]->facet_list[ii]!=i )
     VVTriangles[i].facet_list_Of_nb.push_back(VVertices[tmp1]->facet_list[ii]);
   }
   for (int ii = 0; ii< VVertices[tmp2]->facet_list.size(); ++ ii ){
     if(VVertices[tmp2]->facet_list[ii]!=i )
     VVTriangles[i].facet_list_Of_nb.push_back(VVertices[tmp2]->facet_list[ii]);
   }

  }
// Remove duplicated facet indices
	for (int i=0 ;i<VVTriangles.size();++i  )
	{
	  RemoveDuplicatesFacetIndices(VVTriangles[i] );
	}
}





// =============================================================================
/*!
  @name void BuildVertexVertexMeshesRepresentation()
  @brief this function fnd and set the vertex neighborbs-vertices, that meane for earch
 * vertex, fill the vertex-neigborbs list

  
*/
// =============================================================================

template<typename Config>
/*inline*/ void Container<Config>::BuildVertexVertexMeshesRepresentation()
{
  for (int i=0 ;i<VVPoint.size();++i)
  {
    for (int ii=0;ii < VVertices[i]-> facet_list.size (); ++ii  )
    {
       int tmp = VVertices[i]-> facet_list [ii];

	if(VVTriangles[tmp].v_indices[0] != i )VVertices[i]->V_nb.push_back(VVTriangles[tmp].v_indices[0]);
	if(VVTriangles[tmp].v_indices[1] != i )VVertices[i]->V_nb.push_back(VVTriangles[tmp].v_indices[1]);
	if(VVTriangles[tmp].v_indices[2] != i )VVertices[i]->V_nb.push_back(VVTriangles[tmp].v_indices[2]);
    }

    RemoveDuplicatesVertexNbIndices(*VVertices[i] ) ;
   } // enf if VVPoint forloop
}



template<typename Config>
/*inline*/ void Container<Config>::Check()
{
    std::cout<< "\n" ;
    std::cout<< "===========================================================\n";
    std::cout<< "START -> Container::Check() -> Results \n";
    std::cout<< "===========================================================\n";
    std::cout << "number of facet is = "   << VVTriangles.size() << "\n";

     for (int i=0; i < VVTriangles.size();  ++i ){
	cout <<"T ["<<i<< "]=  <"   << VVTriangles[i].v_indices[0]<< " , ";
	cout <<VVTriangles[i].v_indices[1]<< " , ";
	cout <<VVTriangles[i].v_indices[2]<< ">  , ";
	cout <<"facet_list_Of_nb.size = "
             << VVTriangles[i].facet_list_Of_nb.size()<<" ,contains <";
	for (int ii = 0; ii < VVTriangles[i].facet_list_Of_nb.size(); ++ii){
		cout << VVTriangles[i].facet_list_Of_nb [ii] << " , " ;
	}cout << ">\n";

     }

    std::cout << "\n----------------------\n number of Points is = "
         <<VVPoint.size()<< "\n";

	for (int i=0; i < VVPoint.size() ; ++i )
              std::cout <<  "VVPoint ["<< i << "]" << VVPoint[i] << std::endl;
	      std::cout << "\n----------------------\n number of vertices is = "
                   << VVertices.size()   << "\n";

		for (int i=0; i <VVertices.size() ; ++i )
                {
		   std::cout <<"VVertices ["<<i<<"]"<< "  have facet_list of size = "
                        <<VVertices[i]->facet_list.size() << " ,contains <";
			for (int ii=0; ii<VVertices[i]->facet_list.size();++ii){
				cout <<   VVertices[i]->facet_list[ii]<< " , ";
			}cout <<">\n";
		}
 		cout << "\n----------------------\n";

		for (int i=0; i <VVertices.size() ; ++i )
		{
		  std::cout <<  "VVertices ["<<i << "]"   << " Nb_list of size = "
                       <<  VVertices[i]->V_nb.size() << " ,contains <";
		       
		       for (int  ii=0; ii<VVertices[i]->V_nb.size(); ++ii )
		       {
			 std::cout <<  VVertices[i]->V_nb[ii]<< " , ";
		       }
		  std::cout <<">\n";
		}

                std::cout << "\n----------------------\n";
                std::cout << " Normals \n";
		for (int i=0; i< VNormals.size() ; ++i )
		{
			cout <<  VNormals[i]<< "\n";
                }

                std::cout<< "the Bounding box are  \n";
                std::cout<< "pMin = " << pMin << "\n";
                std::cout<< "pMax = " << pMax << "\n";
    std::cout<< "===========================================================\n";		
    std::cout<< "END -> Container::Check() -> Results \n";
    std::cout<< "===========================================================\n";
}







