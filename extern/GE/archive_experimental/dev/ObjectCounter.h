#ifndef OBJECTCOUNTER_HH
#define OBJECTCOUNTER_HH

#include <stddef.h> 
 
// If we want to m_count the number of live (that is, not yet destroyed)
//  objects for a certain class type, 
// it suffices to derive any class from this ObjectCounter template.
 
template <typename CountedType> 
class ObjectCounter 
{
	public: 
		//!<- return number of existing objects: 
		inline static size_t live(){
			return ObjectCounter<CountedType>::m_count; 
		}

	protected: 
		//!<- default constructor 
		ObjectCounter() { 
			++ObjectCounter<CountedType>::m_count;
		}
	
		//!<- copy constructor 
		ObjectCounter (ObjectCounter<CountedType> const&) { 
			++ObjectCounter<CountedType>::m_count; 
		}
		
		//!<- destructor 
		~ObjectCounter() { 
			--ObjectCounter<CountedType>::m_count; 
		}
		
	private: 
		int		    m_id   ;  //!< the object id
		static size_t	    m_count;  //!< number of existing objects
}; 
 
//!<- initialize m_counter with zero 
template <typename CountedType> size_t ObjectCounter<CountedType>::m_count = 0;
 
 
 
#endif
 
 
