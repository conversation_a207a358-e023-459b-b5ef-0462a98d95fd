// =============================================================================
// includes
// =============================================================================
#include <iostream>
#include <vector>
#include <functional>
#include <algorithm>
#include <cassert>
#include <boost/bind.hpp>
#include <boost/ref.hpp>
// =============================================================================




// =============================================================================
// version 0
// =============================================================================
template<typename T>
struct F2
{
	T s;
	typedef void result_type;
	inline void operator ()(const T& dt){
		s += dt ; 
	}
};
// =============================================================================


// =============================================================================
// version 1
// =============================================================================
template<typename T>
struct F3
{
	F3 (const T& s_) : s (s_) {}
	typedef void result_type;
	inline void operator ()(const T& dt){
		s += dt ; // this->S() += dt;
	}
	// get functions
	inline const T& S()const{return s ;}
	inline T& S(){return s ;}
  private:
	T s;
};
// =============================================================================




// =============================================================================
// version 2
// =============================================================================
template<typename T>
struct F4
{
	explicit F4 (T* s_) : s (s_) {}
	typedef void result_type;
	inline void operator ()(const T& dt){
 	  (*s) += dt ; // this->S() += dt;
	}
	// get functions
	inline const T* S()const{return s ;}
	//inline T& S(){return s ;}
  private:
	T* s;// rb 
  private:// forbiden operators
	F4 (const F4& ) ;
	void operator = (const F4& ) ;
};
// =============================================================================


// =============================================================================
// version 2
// =============================================================================
template<typename T>
struct SimRotation
{
	explicit SimRotation (T* s_) : s (s_) {}
	typedef void result_type;
	inline void operator ()(const T& dt)
	{
 	  (*s).Pos()  = 5.0 * dt ; // this->S() += dt;
	}
	// get functions
	inline const T* S()const{return s ;}
	//inline T& S(){return s ;}
  private:
	T* s;// rb 
  private:// forbiden operators
	SimRotation (const SimRotation& ) ;
	void operator = (const SimRotation& ) ;
};
// =============================================================================



// configure test 
struct config_t
{
	typedef float	real ;
	typedef int	object_type;
};


template<typename object_type>
class sim ;

template<typename configure>
struct rb
{
	typedef typename configure::real real ;
	typedef typename configure::object_type object_type ;
// 	explicit rb (sim<object_type>* sim_ ): m_sim(sim_){}
	
	inline real& Pos(){return m_com;}
	inline const real& Pos()const{return m_com;}
	// update
private :
	real m_com ;
// 	sim<object_type>*	m_sim;// function update ---> operator () basically*/
};







int main ()
{
	// first version :
	// =====================================================================
	F2<int> f2 = { 0 };
	int a[] = { 1, 2, 3 };
	std::for_each( a, a+3, bind( boost::ref(f2), _1 ) );
	assert( f2.s == 6 );
	
	
	// second version :
	// =====================================================================
	F3<int> f3 ( 0 );
	//int a[] = { 1, 2, 3 };
	std::for_each( a, a+3, bind( boost::ref(f3), _1 ) );
	assert( f3.S() == 6 );
	
	// =====================================================================
	// third version :
	// =====================================================================
	int tmp =0 ;
	F4<int> f4 (&tmp );
	//int a[] = { 1, 2, 3 };
	std::for_each( a, a+3, bind( boost::ref(f4), _1 ) );
	std::cerr<<"f4.S() : " << *(f4.S()) << "\n";
	assert( *f4.S() == 6 );
	
	
	
	rb < config_t > my_object( )  ;
// 	SimRotation<rb <config_t> > Sim ( my_object) ;
	
	
	return (0) ;
}