#ifndef LISTSAMPLE_HH
#define LISTSAMPLE_HH



#include "List.h"
#include "../configure/Config.h"
#include "../gmath/Point3D.h"
#include "../accel/Sector.h"

// =============================================================================
// config List Sample : 
// =============================================================================

template<typename ElementType >
struct ListSample
{
    //1. config first the basic type
	 typedef ElementType 		 		ObjectType ;
      	 typedef const ObjectType         		ObjectArgType  ;
	 
      	 typedef EmptyCopier<ObjectArgType>    		Copier; // or MonomorphicCopier, or 
	 //typedef PolymorphicCopier<ObjectType>	Copier; //
      
	 //typedef ElementDestructor ; //
      	 typedef EmptyDestructor   <ObjectType>   	Destructor ;
	 typedef DynamicTypeChecker<ObjectType>   	TypeChecker;// or
	 // typedef EmptyTypeChecker               	TypeChecker;
	 
	 typedef boost::ptr_vector<ObjectType>  	ContainerType ;	 
      	 typedef std::less<ObjectType>   	  	Compare ;// i will use fo accosiative container	 

	 typedef List <ListSample> 	 		FinalListType ;
};

#endif 
