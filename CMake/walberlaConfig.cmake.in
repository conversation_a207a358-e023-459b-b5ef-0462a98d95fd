# Tell the user project where to find our headers and libraries

SET (walberla_INCLUDE_DIRS
  "@walberla_SOURCE_DIR@/src"
  )

SET (walberla_LIBRARY_DIRS
  "@walberla_BINARY_DIR@/bin"
  )


# Tell the user project where to find our build settings and library dependencies

SET (walberla_BUILD_SETTINGS_FILE
  "@walberla_BINARY_DIR@/bin/walberlaBuildSettings.cmake"
  )

INCLUDE (
  "@walberla_BINARY_DIR@/bin/walberlaLibraryDepends.cmake" OPTIONAL
  )


# Tell the user project wjere to find the "USE" file.
# This file uses the above settings to configure the
# user project.

SET (walberla_USE_FILE
  "@walberla_BINARY_DIR@/bin/walberlaUse.cmake"
  )


# Import pecore's build settings

INCLUDE (CMakeImportBuildSettings)

CMAKE_IMPORT_BUILD_SETTINGS(
  ${walberla_BUILD_SETTINGS_FILE}
  )


# Tell the compiler where to find pe's header files

INCLUDE_DIRECTORIES(${walberla_INCLUDE_DIRS})


# Tell the linker where to find pe's libraries

LINK_DIRECTORIES(${walberla_LIBRARY_DIRS})


