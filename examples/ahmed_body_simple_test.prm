// Simplified Ahmed Body LES Test
// This tests our turbulence models with a basic flow setup

simdata {
    // Domain size (small for testing)
    domainX     64;       // Streamwise
    domainY     32;       // Vertical  
    domainZ     32;       // Spanwise
    dx          0.001;    // Grid spacing
    
    // Simulation control
    timesteps   200;      // Short test run
    nu          1.0e-4;   // Viscosity
    rho         1.0;      // Density
    maxPermittedVel 0.15; // Maximum velocity
    
    dump;  // Output all computed parameters
}

// Test our Dynamic Smagorinsky turbulence model
turbulence {
    model               dynamic_smagorinsky;  // Use our Dynamic Smagorinsky model
    filterWidth         2.0;                  // Test filter width = 2*dx
    strainRateMethod    nonequilibrium;       // Non-equilibrium method
    dynamicSmagorinskyAveraging lagrangian;   // Lagrangian averaging
    maxDynamicCs        0.3;                  // Limit Cs for stability
}

// Simple channel flow setup (no curved boundaries for now)
init {
    // Inlet (west)
    west {
        vel_in {
            y_L     1..'domainY'-1;
            z_L     1..'domainZ'-1;
            ux_L    0.08;  // Inlet velocity in lattice units
            uy_L    0.0;
            uz_L    0.0;
        }
    }
    
    // Outlet (east)
    east {
        prs_nils {
            y_L     1..'domainY'-1;
            z_L     1..'domainZ'-1;
            prs_L   0.0;  // Zero gauge pressure
        }
    }
    
    // Ground (bottom) - no slip
    bottom {
        noslip {
            x_L     0..'domainX'+1;
            z_L     0..'domainZ'+1;
        }
    }
    
    // Top boundary - slip wall
    top {
        freeslip {
            x_L     0..'domainX'+1;
            z_L     0..'domainZ'+1;
        }
    }
    
    // Side walls - slip
    north {
        freeslip {
            x_L     0..'domainX'+1;
            y_L     0..'domainY'+1;
        }
    }
    
    south {
        freeslip {
            x_L     0..'domainX'+1;
            y_L     0..'domainY'+1;
        }
    }
}

// Output configuration
paraview {
    filename    ahmed_simple_test;
    xstart      0;
    xend        64;
    ystart      0;
    yend        32;
    zstart      16;
    zend        16;       // Single plane through center
    writevelocity  1;
    writepressure  1;
    writeeddyvisc  1;     // Output eddy viscosity to verify turbulence model
    interval       50;    // Output every 50 steps
    physical       1;     // Use physical units
}

// Monitor turbulence quantities
simoutput {
    TURBULENT_KINETIC_ENERGY {
        time 25;  // Output TKE every 25 steps
    }
    EDDY_VISCOSITY_STATS {
        time 25;  // Output eddy viscosity statistics
    }
}

// Logging configuration
logging {
    logfile ahmed_simple_test.log;
    append  0;
}
