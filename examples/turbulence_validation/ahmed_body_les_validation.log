

-- RELEASE -- SVN Revision 134 -- LOG BEGIN , Tuesday, 17.June 2025, 12:57---------------------------------


 INFO:    wa<PERSON><PERSON><PERSON><PERSON> called with parameter file: /home/<USER>/walberla/examples/turbulence_validation/ahmed_body/parameter_files/ahmed_body_les_validation.prm
 INFO:    Domain.cpp::InitSimData:  SimData.h:Parameters:
---------------------
 dt      0.00166667
 dx      0.001
 tau     0.75
 omega   1.33333
 nu      5e-05
 rho     1
 Re      32
 gx      0
 gy      0
 gz      0
 xLength 0.08
 yLength 0.04
 zLength 0.02
---------------------
 dt_L    1
 dx_L    1
 nu_L    0.0833333
 gx_L    0
 gy_L    0
 gz_L    0
 domainX 80
 domainY 40
 domainZ 20
 numFluidCells 0
---------------------

 INFO:    PatchField.cpp::Create:  Patch<PERSON>ield.cpp:Parameters:
---------------------
 xNumPatches 1
 yNumPatches 1
 zNumPatches 1
---------------------

 WARNING: Domain.h::SetBC: Lattice density for prs_nils boundary condition at north side has extreme value: 0. Adjust resolution!
 WARNING: Domain.cpp::InitializeBoundaries:
 Even GE is activated through CMake config, there is no blocks named 'stl' in parameter file. You are not used the STL GeometricEngine !


Simulation Output , Tuesday, 17.June 2025, 12:57---------------------------------

Output data: 
AVG_DENS

------------------------------------------------------------------------
SIMULATION OUTPUT
------------------------------------------------------------------------
Average Density: 0.99184
------------------------------------------------------------------------
