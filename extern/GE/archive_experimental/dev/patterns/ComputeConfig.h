#ifndef COMPUTECONFIG_HH
#define COMPUTECONFIG_HH

#include "StructuredConfigRepositor.h"

struct Precise{
	typedef double Element ;
};

struct Compact{
	typedef float Element ;
};


template <int Size = 100, class Precision = Compact>
struct ComputeConfig{
	struct ForVector{
		typedef typename Precision::Element Element;
		typedef int size_type ;
		enum {initial_value =  0};
		enum {size = Size};
		typedef Vector<ForVector> vector ;
	};
};


template<class Precision=Compact>
struct Mass{
	typedef typename Precision::Element ElementType;
  private:
    ElementType m_mass; 	  // mass by default 1.1  	[kg]
    Point3D<float>  m_pos;    	  // position      		[m]
    Vector3D<float> m_velocity  ; // m_velocity    		[m/s]
    Vector3D<float> m_aforce    ; // aplying force 		[N]
    // =========================================================================
public:

    // constructor
    explicit Mass (const FLOAT& _m ):m_mass(_m){ Init ();}
    explicit Mass (){ m_mass=1.1 ;  Init (); }
    // Init Functions
    inline void Init  ();
    inline void InitPosition ();
    inline void InitForce ();
    inline void InitVelocity ();

    inline void AplyForce (Vector3D<float> F);

    inline void Simulate(const FLOAT& dt);

    inline friend std::ostream& operator<<(std::ostream& , const Mass& m);

    inline void ComputeVelocity(const uint32& p1,const uint32 & p2);
    inline void ComputeForce   (const uint32& p1,const uint32 & p2);
    
//  Get functions
    inline Point3D<float> 
};
// helpl function

// Init
    inline void Mass::Init(){
      InitPosition() ;      
      InitForce() ;
      InitVelocity() ;
    }
    
    inline void Mass::InitPosition(){
        m_pos.m_x = m_pos.m_y = m_pos.m_z =  0.0f ;
    }    
    inline void Mass::InitForce(){
        m_aforce[0] = m_aforce[1] = m_aforce[2] =0. ;
    }

    inline void Mass::InitVelocity(){
        m_velocity[0] = m_velocity[1] = m_velocity[2] =0. ;
    }
//

    inline void Mass::AplyForce (Vector3D<float> F){
        m_aforce +=  F;
    }


   inline void Mass::Simulate(const FLOAT& dt)
   {
       m_velocity += (m_aforce / m_mass) * dt;	// Change In Velocity Is Added To The Velocity.
                                        // The Change Is Proportinal With The Acceleration (force / m) And Change In Time.
       m_pos += m_velocity * dt;		// Change In Position Is Added To The Position.
                                        // Change In Position Is Velocity Times The Change In Time.
   }


inline std::ostream& operator<<(std::ostream& out , const Mass& m)
{
      out << "mass point have\n"<< "position -> \t " << m.m_pos << "\n";
      out << "velocity ->  \t "<< m.m_velocity << "\n";
      out << "AForce   ->  \t "<< m.m_aforce   << "\n" ;
      out << "mass   ->  \t "  << m.m_mass   << "\n" ;
      return out  ;
}

inline void Mass::ComputeVelocity (const uint32& p1,const uint32 & p2)
{

}

inline void Mass::ComputeForce   (const uint32& p1,const  uint32 & p2){

}

};




#endif
