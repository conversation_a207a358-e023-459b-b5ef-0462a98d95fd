
/*!
// 
//  Helper class used to show execution time
//  platform independant windows/Linux systems
// 
*/

/*!
\example :    
      void tstart(); \n

      void tend();   \n

      double tval(); \n
*/

// ===============================================================================

#if defined (_WIN32) || defined(__WIN32__)

// ===============================================================================
//   LARGE_INTEGER tim, FREQ;
//      double seconds;
//      QueryPerformanceCounter(&tim);
//	...
//      QeryPerformanceFrequency(&FREQ);
//      seconds = (double)tim / (double) FREQ

	    static LARGE_INTEGER m_start, m_end;
	    static LARGE_INTEGER FREQ;

	    inline void tstart(void)
	    {
	      static int first = 1;
	      if(first) {       
		QueryPerformanceFrequency(&FREQ);
		first = 0;   
	      }
		QueryPerformanceCounter(&m_start);
	    }

	    inline void tend(void)  {  QueryPerformanceCounter(&m_end);   }
	    
	    inline double tval()    {
		return ((double)m_end.QuadPart -
			    (double)m_start.QuadPart)/((double)FREQ.QuadPart);
	    }

// ===============================================================================
    
 #elif defined(linux) || defined(__linux)
    
// ===============================================================================    
	  
	    #include <sys/time.h>
	    #include <sys/types.h>
	    #include <stdlib.h>
	    #include <sys/resource.h>

	    static struct timeval m_start, m_end;
	    static struct timezone tz;

	    inline void tstart(void)  { gettimeofday(&m_start, &tz); }

	    inline void tend(void)    { gettimeofday(&m_end,&tz);    }

	    inline double tval()
	    {
		double t1, t2;
		t1 =  (double)m_start.tv_sec + (double)m_start.tv_usec/(1000*1000);
		t2 =  (double)m_end.tv_sec   + (double)m_end.tv_usec/(1000*1000);
		return t2-t1;
	    }

 #endif