#ifndef GVERTEX_HH
#define GVERTEX_HH

// #include "Vector.h"
#include <gmath/Point3D.h> // #include "Vector3D.h"
#include <gmath/Vector4D.h> //#include <math/Vector.h>
#include <vector>




namespace ge
{

	//==========================================================================
	// Defining the Vertex Parts, vertex should be any combination of these part
	//==========================================================================
	typedef enum vertex_part_usage
	{
	    usage_position_3f, // positions
	    usage_faceIndex ,
	    usage_color_4b,
	    usage_texcoord0_2f,
	    usage_texcoord1_2f,
	    usage_position_4f // special positions designed for sse2-purpose
	} vertex_part_usage;
	//======================================================================
	
	
	
	
	
	
	template <vertex_part_usage usage> 
	struct vertex_part; // forward declaration

	template < >
	struct vertex_part<usage_position_3f>
	{
		Point3D<float> m_pos ;      //    Vector3<float> m_pos;
	};
 
    
	template < >
	struct vertex_part<usage_faceIndex> //  color 
	{
	    unsigned int m_face_index ;
	};
    
	template < >
	struct vertex_part<usage_color_4b> //  color 
	{
	    unsigned int m_color;
	};

	template < >
	struct vertex_part<usage_texcoord0_2f> //texture mapping
	{
	  float m_u ; // double are not allowed
	  float m_v ;
	};
    

	template < >
	struct vertex_part<usage_position_4f>
	{
	    Vector4D<float> m_pos; // sse alignement , to 16bites 
	};


	
	
	
	
	// the rest is underdevelopemnt
	
	
	//======================================================================  
	//!@brief Each vertex node contains a usage and an offset in bytes
	//======================================================================
	struct vertex_node
	{
	      vertex_part_usage usage;
	      std::size_t offset;
	};
	
	
	//!@brief The vertex format contains a collection of these nodes, along with the
	//!@brief stride (the total distance from one vertex to the next). Using this,
	//!@brief we can tell the graphics API how many streams of vertex information there are,
	//!@brief where each stream starts, and how each stream is to be used.
	struct vertex_format
	{
	      std::vector<vertex_node> usage;
	      std::size_t stride;
	};

	
	
	template <vertex_part_usage usage>
	std::ostream &operator<<(std::ostream &output, const vertex_part<usage_position_3f> & pos)
	{
	      output<<"Position ="<< pos.m_pos<< std::endl ;
	      return output ;
	}
	  
	  
	template <vertex_part_usage usage>
	std::ostream &operator<<(std::ostream &output, const vertex_part<usage_faceIndex> & pos)
	{
	      output<<"faceIndex ="<< pos.m_face_index<< std::endl ;
	      return output ;
	}
	   

	template <vertex_part_usage usage>
	std::ostream &operator<<(std::ostream &output, const vertex_part<usage_color_4b> & pos)
	{
	      output<<"color  ="<< pos.m_color<< std::endl ;
	      return output ;
	}

	template <vertex_part_usage usage>
	std::ostream &operator<<(std::ostream &output, const vertex_part<usage_texcoord0_2f> & pos) {
	      output<<"u  ="<< pos.m_u << std::endl ;
	      output<<"v  ="<< pos.m_v << std::endl ;
	      return output ;
	}

// TODO compiler error 
// 	template <vertex_part_usage usage>
// 	std::ostream &operator<<(std::ostream &output, const vertex_part<usage_position_4f>& pos)
// 	{
// 	      output<<"Position ="<< pos.m_pos<< std::endl ;
// 	}




} // end namepace ge
    
    
#endif



