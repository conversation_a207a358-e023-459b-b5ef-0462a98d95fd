//=================================================================================================
/*!
//  \file Domain.cpp
//  \brief Source file for the domain.
//  \author Jan <PERSON>
 */
//=================================================================================================




//*************************************************************************************************
// Includes
//*************************************************************************************************

#include "Domain.h"
#include "SimOutput.h"
#include "XmGraceWriter.h"
#include "SweepStruct.h"
#include "ApplicationDefinitions.h"
#include "Communication.h"
#include "MIFunctions.h"
#include "MixData.h"
#include "FSInterfaceCells.h"
#include "FSGeometry.h"
#include "WalberlaMath.h"
#ifndef NO_PE
#include <pe/util/Random.h>
#endif


#ifdef USE_STL_GE
#include <boost/progress.hpp>
#endif



#define DEBUG_TURB_DECAY



static double d2r(double d) {
  return (d / 180.0) * ((double) M_PI);
}


namespace walberla
{

#ifdef USE_STL_GE

//      typedef /*typename*/ ge::geScene<>::aabb_type aabb_type ;

//      inline static aabb_type* GetProcessAaabb(const MPIData& m_mpi, const SimData& m_sim, PatchField& m_patchField)
//      {
//	      aabb_type  *final = new aabb_type  ;
//	      final -> Empty() ;
//	      for(Uint k=0;k<m_sim.zNumPatches;++k) {
//		for(Uint j=0;j<m_sim.yNumPatches;++j) {
//		  for(Uint i=0;i<m_sim.xNumPatches;++i)
//		  {
//				 if (!m_patchField.IsAllocated(i,j,k) )
//					 continue;
//				 final->Union ( m_patchField.GetAABB( i,j,k) [0],m_patchField.GetAABB( i,j,k) [1],m_patchField.GetAABB( i,j,k) [2],
//						m_patchField.GetAABB( i,j,k) [3],m_patchField.GetAABB( i,j,k) [4],m_patchField.GetAABB( i,j,k) [5]  ) ;
//		  }
//		}
//	      }
//	      return final ;//
//      }



// for dev/debugginig only
inline void PrintInfo (const MPIData& m_mpi, const SimData& m_sim, PatchField& m_patchField)
{
        std::ofstream		file;
        std::stringstream	fileName ;
        fileName.str("");
        fileName <<"Pro"<< m_mpi.worldRank <<".dat"; // fileName <<"Pro"<< mpi_.fluidWorkersRank <<".dat";
        file.open(fileName.str().c_str() );//     file.open(fileName.str().c_str() /*, ofstream::out | ofstream::trunc*/ );
        file<<"GetProcesdpMin function \n";
        file<<"I'm proc with worldRank : " << m_mpi.worldRank << "\n";
        file<<"I'm proc with fluidWorkersRank : " << m_mpi.fluidWorkersRank << "\n";
        file<< "they are in total :numprocsWorld : "<< m_mpi.numprocsWorld <<", and numprocsFluidWorkers : "<< m_mpi.numprocsFluidWorkers<<std::endl;

        for(Uint k=0;k<m_sim.zNumPatches;++k) {
                for(Uint j=0;j<m_sim.yNumPatches;++j) {
                        for(Uint i=0;i<m_sim.xNumPatches;++i)
                        {
                                if (!m_patchField.IsAllocated(i,j,k) )
                                        continue;
                                file<<"Abbb : \n";
                                file<< ( m_patchField.GetAABB( i,j,k) ) ;
                                file<<"\n---> GetXSize,  GetXSize, GetXSize  : \t  ("
                                   << m_patchField.GetXSize(i,j,k)<<","
                                   << m_patchField.GetYSize(i,j,k)<<","
                                   << m_patchField.GetZSize(i,j,k)<<")\n";
                                file<<"=================================\n";
                                file<< "\n	<" <<
                                       ( m_patchField.GetAABB( i,j,k) )[0] << ","  <<
                                       ( m_patchField.GetAABB( i,j,k) )[1] << ","  <<
                                       ( m_patchField.GetAABB( i,j,k) )[2] << ","  <<
                                       ( m_patchField.GetAABB( i,j,k) )[3] << ","  <<
                                       ( m_patchField.GetAABB( i,j,k) )[4] << ","  <<
                                       ( m_patchField.GetAABB( i,j,k) )[5] << ">\n";

                                typedef /*typename*/ ge::geScene<>::aabb_type aabb_type ;
                                aabb_type  tmp ( m_patchField.GetAABB( i,j,k) [0],m_patchField.GetAABB( i,j,k) [1],m_patchField.GetAABB( i,j,k) [2],
                                                 m_patchField.GetAABB( i,j,k) [3],m_patchField.GetAABB( i,j,k) [4],m_patchField.GetAABB( i,j,k) [5] );

                                file << tmp << std::endl ;
                                //			  file<<"m_patchField.GetBeginLocalPatches() : \n"<<
                                //				m_patchField. << "\n";
                        }
                }
        }


        //       file<<" npx =     "<<  m_mpi.dims[X]  <<   "\n";
        //       file<<" npy =     "<<  m_mpi.dims[Y]  <<   "\n";
        //       file<<" npz =     "<<  m_mpi.dims[Z]  <<   "\n";
        //       file<<" i =:       "<<  i <<   "\n";
        //       file<<" j =:       "<<  j <<   "\n";
        //       file<<" k =:       "<<  k <<   "\n";
        file.close();
}



#endif






//=================================================================================================
//
// Constructor
//
//=================================================================================================

//*************************************************************************************************
/*!\fn void Domain::Domain()
   // \brief Does nothing so far
   //
    */
Domain::Domain():autoPlace_(0),peInstance_(0),tStart_(0),wantMOObstacles_(false){
        sim_.maxNumObstacleID=0;
#     ifdef WALBERLA_USE_MPI
        mpi_.requestsPe=NULL;
        mpi_.statiPe=NULL;
#     endif
     use_turbModel=false;
     use_LBGKModel=false;
     use_MRTModel =false;
     use_TRTModel =false;
}
Domain::Domain(const Domain &d){}
//=================================================================================================
//
// Destructor
//
//=================================================================================================

//*************************************************************************************************
/*!\fn void Domain::~Domain()
   // \brief
   //
    */
Domain::~Domain(){
        LOG_INFO3("Domain.cpp::~Domain:In Domain Destructor\n");

#ifdef WALBERLA_USE_MPI
        //Delete all buffers in the ComPatches if MPI
        std::vector<ComPatchID>::iterator itComPatches;
        for(itComPatches=comPatches_.begin();itComPatches!=comPatches_.end();++itComPatches)
        {
                delete (*itComPatches);
        }

        // Cherif
        //if ( mpi_.stati != NULL ) delete mpi_.stati;
        //if ( mpi_.requests != NULL ) delete mpi_.requests;
        // else cherif
        delete mpi_.stati;
        delete mpi_.requests;

#endif
#ifdef  USE_STL_GE
        delete m_geScene ;
#endif
        //      std::cout << "Finished deleting Domain from process " << mpi_.worldRank << std::endl;
        LOG_INFO3("Domain.cpp::~Domain:Out Domain Destructor\n");
}


//=================================================================================================
//
// Create function
//
//=================================================================================================

//*************************************************************************************************
/*!\fn void Domain::Create(FileReader &fileReader)
   // \brief Sets the members and creates the fields
   //
   // \param fileReader reference to the file reader of the simulation
   // \return void
    */
void Domain::Create(FileReader &fileReader){

        fileReader_=&fileReader; // Save the pointer for later use in ParamContainer

        // TODO: Here only a dirty hack to control communication of PDF directions - will be moved elsewhere
        comm::communicateAllPDFDirs=0;

#ifdef OLD_WALBERLA_COMM
        LOG_ERROR("USING OLD WALBERLA COMM\n");
#endif
#ifdef WALBERLA_USE_MPI
        //Get MPI information
        MPI_Comm_size( MPI_COMM_WORLD, &mpi_.numprocsWorld);
        MPI_Comm_rank(MPI_COMM_WORLD, &mpi_.worldRank);
        if(mpi_.worldRank==0)
        {
                std::cout << "We have " << mpi_.numprocsWorld << " Processes" << std::endl;
        }
#endif
        InitFieldsForApplications();



        InitRestartForApplications();

        // Check whether this simulation is to be run with geometries from file
        //InitGeometryInfo(fileReader);
        geometry_.InitGeometryInfo(fileReader);
        // Create the patch grid and fill the simdata accordingly
        InitSimData(fileReader);

        // Configure restart
        Restart::Configure(fileReader,sim_,&mpi_);
#ifndef NO_PE
        movingObstacles_.MOConfigureRestart(Restart::GetRestartData().directory,Restart::GetRestartData().filename);
        InitParticles(fileReader);
#endif
        mix::MixData::Configure(fileReader,sim_);
        patchField_.Create(fileReader,sim_,mpi_); //Has to create the field to fill in geometry_.CalcFluidCellsFromFiles with number of fluid cells
        bool haveGeometryFiles=geometry_.CalcFluidCellsFromFiles(patchField_,sim_);
        patchField_.DetermineNeededPatches(haveGeometryFiles,sim_);
        patchField_.Allocate(sim_);

        LOG_INFO2("Domain.cpp::Create: Start to place and allocate patches.\n");
        PlaceAllocatePatches();

#ifndef NO_PE
        //Initialize pe-structures
        LOG_INFO2("Domain.cpp::Create: Initializing PE.\n");
        movingObstacles_.InitializePe(mpi_,sim_);
#ifndef OBSTACLES_IGNORE_GRAVITY
        movingObstacles_.SetGravity(sim_.gx_L, sim_.gy_L, sim_.gz_L);
#endif
#endif

        LOG_INFO2("Domain.cpp::Create: Initializing SC.\n");
        InitShanChen(fileReader);

        // Initialize povray writing
        LOG_INFO2("Domain.cpp::Create: Initializing Povray.\n");
        ActivatePovray(fileReader);

        //Initialize Boundaries and inner block
        LOG_INFO2("Domain.cpp::Create: Initializing Boundaries.\n");
        InitializeBoundaries(fileReader);



    //InitVelField() ;


        // Initialize communication buffers (after boundaries, because of periodic stuff)
        commWorld_.Create(patchField_,&mpi_);
#ifndef NO_PE
#     ifdef FAST_PE
        //      int count =0;
        //      pe::World::CastIterator<pe::Union> begin1=movingObstacles_.GetPeWorld()->begin<pe::Union>();
        //      pe::World::CastIterator<pe::Union> end1=movingObstacles_.GetPeWorld()->end<pe::Union>();
        //      if(mpi_.worldRank==0)
        //      for(;begin1!=end1;++begin1)
        //      {
        //         pe::Union::CastIterator<pe::Sphere> a = (*begin1)->begin<pe::Sphere>();
        //         pe::Union::CastIterator<pe::Sphere> b   = (*begin1)->end<pe::Sphere>();
        //         if(a!=b)
        //         {
        //            std::cout << "Before Synchronize:On proces " << mpi_.worldRank << " Object is " << a->getID() << " with position " << a->getPosition() << std::endl;
        //            //std::cout << "On process " << mpi_.worldRank << " we have object with id " << begin->getID() << std::endl;
        //            //std::cout << "On process " << mpi_.worldRank << " we have object with system id " << begin->getSystemID() << std::endl;
        //         }
        //      }
        //Synchronize the PE
        pe::WorldID world = pe::theWorld();
        world->synchronize();

        //      //Copy one all obstacles to the local map after synchronization
        //      pe::World::CastIterator<pe::Union> begin=movingObstacles_.GetPeWorld()->begin<pe::Union>();
        //      pe::World::CastIterator<pe::Union> end=movingObstacles_.GetPeWorld()->end<pe::Union>();
        //      for(;begin!=end;++begin)
        //      {
        //         pe::Union::CastIterator<pe::Plane> a = (*begin)->begin<pe::Plane>();
        //         pe::Union::CastIterator<pe::Plane> b   = (*begin)->end<pe::Plane>();
        //         if(a==b)
        //         {
        //            movingObstacles_.GetObstacles()[begin->getID()]=*begin;
        //            ++count;
        //            //std::cout << "On process " << mpi_.worldRank << " we have object with id " << begin->getID() << std::endl;
        //            //std::cout << "On process " << mpi_.worldRank << " we have object with system id " << begin->getSystemID() << std::endl;
        //         }
        ////         pe::Union::CastIterator<pe::Sphere> c = (*begin)->begin<pe::Sphere>();
        ////         pe::Union::CastIterator<pe::Sphere> d   = (*begin)->end<pe::Sphere>();
        ////         if(c!=d)
        ////         {
        ////            std::cout << "After Synchronize:On proces " << mpi_.worldRank << " Object is " << c->getID() << " with position " << c->getPosition() << std::endl;
        ////            //std::cout << "On process " << mpi_.worldRank << " we have object with id " << begin->getID() << std::endl;
        ////            //std::cout << "On process " << mpi_.worldRank << " we have object with system id " << begin->getSystemID() << std::endl;
        ////         }
        //      }
        //std::cout << "After synchronizing i am having " << count << " objects on process " << mpi_.worldRank << std::endl;

#     endif
#endif
        ActivateBrownianMotion(fileReader);

        InitGravity(fileReader);

        // POSTPROCESSING FREE SURFACES
        // Note: Has to be done after gravity-aware initialization because interface cells have to get correct mass
        InitFreeSurfaceData(fileReader);

        // Init the neighbourhood structure for the patches
        InitNeighbourHoods();

        // Setup request buffers for MPI
        InitRequestMem();


#ifndef NO_PE
        // Configure simulation output
        SimOutput::Configure(fileReader,sim_,&mpi_,patchField_,movingObstacles_.GetObstacles());
        movingObstacles_.InitializePovRay();
#else
        // Configure simulation output
        ObstacleMap obsts;
        SimOutput::Configure(fileReader,sim_,&mpi_,patchField_,obsts);
#endif
        InitMixtures(fileReader);

        InitMonitoredCells(fileReader);

        // Init all data output writers here -> for example paraview writers
        if(!peInstance_||mpi_.worldRank!=mpi_.rankPeInstance) //This is not done by pe instance
                dataOut_.InitParaview(fileReader,sim_,patchField_,mpi_);

        sim_.numFluidCells=patchField_.CountFluidCells();

        // AS A DIRTY HACK: Set all periodic walls again, since the Init*-routines may have changed some states
        patchField_.SetPeriodicWall(20);

#ifndef NO_PE
        // prepare Global Declarations file needed for povray output
        if(PovRayVis::IsActive() && !Restart::GetRestartData().isRestartFile)
                PovRayVis::Instance().FinalizeGlobals();
#endif


#ifdef USE_STL_GE

        // start paralle test
        //	   ge::ParaTest ( m_geScene-> GetMpiData() , m_geScene->GetSimData()  ) ;
        //	*( ge::geScene<>::GetProceAabb( *m_geScene) ) ;
        // PrintInfo (mpi_ , sim_ , patchField_ ) ;

#ifdef GE_USE_MPI
        // ge::PrintParallelInfo(*m_geScene) ;
#endif

        if ( STL_BLOCK_EXIST_IN_PARM )
        {
                // const Uint InsideFlage = 256 , OusideFlage = 2 , BorderFlage = 600 ;

                const double wT_start =  WcTiming() ;

                // ===================================================================
                // voxelization real testing started now
                // ===================================================================
                //m_geScene -> RecomputeNormals();

#ifdef WALBERLA_USE_MPI
                if(mpi_.worldRank==0){
                        std::cerr << "NOTE: Voxelization started\n \n" ;

                        std::cerr << "      step1) ChangeLiquidToNoSlipBC()...\n";
                }
#else
                std::cerr << "NOTE: Voxelization started\n \n" ;
                std::cerr << "      step1) ChangeLiquidToNoSlipBC()...\n";
#endif



                // all objects
                m_geScene -> InitInnerPartLiquidToNoSlipBC();//m_geScene -> InitInnerPartLiquidToNoSlipBC(InsideFlage) ;




                //ge::ChangeLiquidToNoSlipBC (patchField_) ;//  as static function
                const double wT_FillInnertBench =  WcTiming() - wT_start ;


                // /////////////////////////////////////////////////////////////////////////////////
                // /////////////////////////////////////////////////////////////////////////////////

#ifdef WALBERLA_USE_MPI
                MPI_Barrier ( MPI_COMM_WORLD ) ;
#endif



                // parallel
                // /////////////////////////////////////////////////////////////////////////////////
#ifdef WALBERLA_USE_MPI
                if(mpi_.worldRank==0){std::cerr << "\n      step2) Detect the Border()...\n";}
#else
                std::cerr << "\n      step2) Detect the Border()...\n";
#endif

                // /////////////////////////////////////////////////////////////////////////////////
                const double wT_BorderDetectStart =  WcTiming() ;



                                m_geScene -> VoxelizeTrianglesMeshObjects( walberla::TMP_FLOODFILL );



                const double wT_BorderDetectEnd =  WcTiming() - wT_BorderDetectStart;







                // /////////////////////////////////////////////////////////////////////////////////
#ifdef WALBERLA_USE_MPI
                if(mpi_.worldRank==0)
                {
                        std::cerr << "\n      step3) FillOutSideObject()...\n";
                }
#else
                std::cerr << "\n      step3) FillOutSideObject()...\n";
#endif
                // /////////////////////////////////////////////////////////////////////////////////
                const double wT_FloodFillStart =  WcTiming() ;





                //today
                m_geScene -> FillOutSideObjects( ) ;// old implementation -> //  m_geScene -> FillOutSideObject(NewFlag,OldFlag)




                const double wT_FloodFillEnd =  WcTiming() - wT_FloodFillStart;


                // /////////////////////////////////////////////////////////////////////////////////
#ifdef WALBERLA_USE_MPI
                if(mpi_.worldRank==0){
                        std::cerr << "\n      step4) Swap Object border flags ()...\n";
                }
#else
                std::cerr << "\n      step4) Swap Object border flags ()...\n";
#endif

                // /////////////////////////////////////////////////////////////////////////////////
                const double wT_ObjectSwapBorderStart =  WcTiming() ;





                //today
                m_geScene -> SwapInsideBorderObjectsFlag();




                // swap  celles with flag = 3 to liquid,
                // its not recommnanded to  call this function, unless you know what u are doing !!!
                //	m_geScene -> SwapOutsideBorderObjectsFlag(); // change it in cas you need, and you know what you are diong :)








                const double wT_ObjectSwapBorderEnd =  WcTiming() - wT_ObjectSwapBorderStart;


                const double wT_all =  WcTiming() - wT_start ;


#ifdef WALBERLA_USE_MPI
                if(mpi_.worldRank==0)
                {
                        std::cerr<< "\n=====================\n processor rank : " << mpi_.worldRank	<<
                            "\n=====================\n"<<
                            "\t-----> wT_FillInnertBench : "<< wT_FillInnertBench<<" sec\n" <<
                            "\t-----> wT_Border : "<< wT_BorderDetectEnd <<" sec\n" <<
                            "\t-----> FloodFill outside : "<< wT_FloodFillEnd <<" sec\n"<<
                            "\t-----> Swap Object Border : "<< wT_ObjectSwapBorderEnd <<" sec\n";
                }
#else
                std::cerr<< "		----->  : wT_FillInnertBench : "<< wT_FillInnertBench<<" sec\n";
                std::cerr<< "		----->  : wT_Border : "<< wT_BorderDetectEnd <<" sec\n";
                std::cerr<< "		----->  : FloodFill outside : "<< wT_FloodFillEnd <<" sec\n";
                std::cerr<< "		----->  : Swap Object Border : "<< wT_ObjectSwapBorderEnd <<" sec\n";
#endif




#ifdef WALBERLA_USE_MPI
                if(mpi_.worldRank==0)
                {
                        std::cerr<< "         ----->  : wT_FloodFill : "<< wT_ObjectSwapBorderEnd <<" sec\n";
                        std::cerr << "\n===================================================\n Statistic : Master processor\n" <<
                                     "===================================================\n";
                        std::cerr << "InitFillInnerInsideObjectPart (%) :	"   <<  100. * (wT_FillInnertBench/wT_all) << "\n";
                        std::cerr << "Detect Border (%) :			"   <<  100. * (wT_BorderDetectEnd/wT_all) << "\n";
                        std::cerr << "FloodFill outside (%) :			"   <<  100. * (wT_FloodFillEnd/wT_all) << "\n";
                        std::cerr << "Swap Object Border (%) :		"   <<  100. * (wT_ObjectSwapBorderEnd/wT_all) << "\n";
                        std::cerr << "the total running time is\t\t" << wT_all <<" sec"<<std::endl;
                        std::cerr << "===================================================\n";
                }
#else
                std::cerr<< "         ----->  : wT_FloodFill : "<< wT_ObjectSwapBorderEnd <<" sec\n";

                std::cerr << "\n===================================================\n Statistic :\n" <<
                             "===================================================\n";
                std::cerr << "InitFillInnerInsideObjectPart (%) :	"	<<  100. * (wT_FillInnertBench/wT_all) << "\n";
                std::cerr << "Detect Border (%) :			"	<<  100. * (wT_BorderDetectEnd/wT_all) << "\n";
                std::cerr << "FloodFill outside (%) :			"	<<  100. * (wT_FloodFillEnd/wT_all) << "\n";
                std::cerr << "Swap Object Border (%) :		"		<<  100. * (wT_ObjectSwapBorderEnd/wT_all) << "\n";
                std::cerr << "the total running time is\t\t" << wT_all <<" sec"<<std::endl;
                std::cerr << "===================================================\n";
#endif
                //m_geScene -> ComputeForces() ;
                m_geScene -> RbForcesAndTorquesInfo() ;

                //#ifdef DEBUG
                //	m_geScene -> RbForcesAndTorquesInfo() ;
                //#endif

#ifdef WALBERLA_USE_MPI
                MPI_Barrier ( MPI_COMM_WORLD ) ;
#endif


                // /////////////////////////////////////////////////////////////////////////////////
                // /////////////////////////////////////////////////////////////////////////////////

                //	  Final ()  ; // its fine but not optimized version
                //
                //	  if (  sim_. NumberOfSeeds != 0 ) // 0 mean no floodfill
                // 	  {
                // 	    std::cerr << "		FloodFill()...  " ;
                // 		Uint INSIDE_FLAGE  = sim_.insideflag;
                // 		Uint OUTSIDE_FLAGE = sim_.outsideflag;
                // 		Uint BORDER_FLAGE  = sim_.boundaryflag;
                // 		std::cerr << " & seed point<x.y,z>  = " <<  sim_.Seed_x << "," << sim_.Seed_y << "," << sim_.Seed_z  << ">\n" ;
                // 		floodFill4 (  sim_.Seed_x,  sim_.Seed_y , sim_.Seed_z  , OUTSIDE_FLAGE , INSIDE_FLAGE );
                // 	    std::cerr << "		End FloodFill()...  " ;
                // 	  } else{
                // 		std::cerr << "		NO  FloodFill.   " ;
                // 	  }
                //
                // 	  std::cerr << "\nNOTE: End Voxelization \n" ;

                // 	  if ( !sim_.TMeshToRowfileName .empty() )// only if you have specify this in ".par" file
                // 	  {
                // 	      cerr << "\nNOTE:Start export to waLBerla Row file\n		..." ;
                // 	      //!@note please use this function only one you have 1 pache in each direction
                // 	      if (sim_.xNumPatches == 1 && sim_.yNumPatches == 1 && sim_.zNumPatches == 1 )
                // 	      {
                // 		ConvertSTL2WarlabaRowFile () ;
                // 	      }else
                // 	      {
                // 		throw std::runtime_error("when you want to to export to row file, you can't use more than 1 patch in each direction.");
                // 	      }
                //
                // 	      std::cerr << "\nNOTE: End Export :) \n" ;
                // 	  }

                // 	  STLInsideCheckWithOneCell (3,4,4, 0) ;




        } // end if STL_BLOCK_EXIST_IN_PARM = true
        else {
#ifdef WALBERLA_USE_MPI
                if(mpi_.worldRank==0)
                    std::cerr<<"GE is activated from cmake, but there is no stl Block in the given parameter file\n";

#else

                    std::cerr<<"GE is activated from cmake, but there is no stl Block in the given parameter file\n";
#endif

    }

#endif  // USE_STL_GE


// #ifdef WALBERLA_USE_MPI
// 	if(mpi_.worldRank==0)
// 	{
// 		InitVelField() ;
// 	}
// #endif



}









//*************************************************************************************************
/*!\fn void Domain::InitFreeSurfaceData(FileReader &fileReader)
   // \brief Configures the FSConfig data struct with values given in parameter file
   // \return void
    */
void Domain::InitFreeSurfaceData(FileReader &fileReader)
{
        FileReader::Blocks blocks;
        fileReader.GetBlocks("freesurface",blocks);
        if (blocks.size()==0) return;
        if (blocks.size()>1) {
                throw std::runtime_error("Domain.cpp::Init: Too many blocks named 'FreeSurface' in parameter file. Only one supported!\n");
        }
        FileReader::BlockHandle block=blocks[0];
        FileReader::Block pcBlock;
        pcBlock = block.CloneBlock();
        fsConfig_.SetDataWithFileBlock(pcBlock,sim_);
        if (blocks[0].IsDefined("dump")) {
                Logging::Instance().LogInfo("Domain.cpp::InitFreeSurfaceData:\n" + fsConfig_.ToString() +"\n");
#if LOGGING>=1
                LOG_INFO1("Domain.cpp::InitFreeSurfaceData: Overview of bubbles follows...\n");
                for(Uint k=0;k<sim_.zNumPatches;++k) {
                        for(Uint j=0;j<sim_.yNumPatches;++j) {
                                for(Uint i=0;i<sim_.xNumPatches;++i){
                                        std::string bubbleNums="";
                                        if (!patchField_.IsAllocated(i,j,k)) continue;
                                        if (patchField_.IsActive(i,j,k,FREE_SURFACE)) {
                                                freeSurface::FSBubbles& bubbles = patchField_.GetPatch(i,j,k)->GetBubbles();
                                                for (Uint i=0; i<=bubbles.GetNumBubbles(); i++) {
                                                        freeSurface::Bubble* bubble=bubbles.GET_BUBBLE_DATA(i);
                                                        if (bubble!=NULL) bubbleNums+=STR(bubble->ID)+", ";
                                                }
                                        }
                                        if (bubbleNums!="") {
                                                bubbleNums = bubbleNums.substr(0,bubbleNums.size()-2);
                                        } else {
                                                bubbleNums = "none";
                                        }
                                        LOG_INFO1("Domain.cpp::InitFreeSurfaceData: Patch "+STR(patchField_.GetPatchStruct(i,j,k).GetPatchUID())+" has bubbleIDs: "+bubbleNums+"\n");
                                }
                        }
                }
                LOG_INFO1("Domain.cpp::InitFreeSurfaceData: End of overview of bubbles.\n");
#endif
        }

        //! set collision model to compressible (important, for free obstacles, otherwise jan's buoyancy will be used)
        sim_.compressible = 1;

#ifndef NO_PE
        //////////////////////////
        // POVRAY VISUALIZATION //
        //////////////////////////
        if(blocks[0].IsDefined("povvistype")) {
                Uint type = blocks[0].GetParameter<Uint>("povvistype");
                if(PovRayVis::IsActive()) {
                        PovRayVis::Instance().SetFreeSurfaceVisType(type);
                        PovRayVis::Instance().SetFreeSurfaceVis(true);
                }
        }

        //Check if surface rendering should be smoothed
        if(blocks[0].IsDefined("smoothSurface"))
        {
                Real isoValue = blocks[0].GetParameter<Real>("smoothSurface");
                if(isoValue<=0.0 || isoValue>=1.0) {
                        LOG_WARNING("Domain.cpp::InitFreeSurfaceData: smoothSurface - please specity a value between 0.0 and 1.0!\n");
                }
                else {
                        if(PovRayVis::IsActive())PovRayVis::Instance().EnableSmoothFreeSurface(isoValue);
                }
        }
#endif

        std::set<PatchUID> fsPatches;
        std::string capillary = ConvertToLowerCase(block.GetParameter<std::string>("capillarymodel"));
        bool cfModel = ((capillary != "none") && (capillary != ""));
        bool cfVarAngles = false;
        if (cfModel) {
                FileReader::Blocks geoBlocks;
                fileReader.GetBlocks("fsgeometry",geoBlocks);
                if (geoBlocks.size()!=0) {
                        if (capillary=="potential") THROW_LOG_ERROR("Domain.cpp::InitFreeSurfaceData: Variable contact angles are not yet supported with 'potential' model!\n");
                        cfVarAngles=true;
                }
        }


        for(Uint k=0;k<sim_.zNumPatches;++k) {
                for(Uint j=0;j<sim_.yNumPatches;++j) {
                        for(Uint i=0;i<sim_.xNumPatches;++i){

                                // TODO: Here all patches are activated for FREE_SURFACE. We should make it possible without!
                                patchField_.AddApp(i,j,k,FREE_SURFACE);
                                if (cfModel) {
                                        patchField_.AddApp(i,j,k,FREE_SURFACE_CAPFO);
                                        if (cfVarAngles) patchField_.AddApp(i,j,k,FREE_SURFACE_CAPFO_VAR);
                                }
#              ifdef WALBERLA_USE_MPI
                                if(mpi_.worldRank==0)
#              endif
                                        std::cout<<"Active application on patch ("<<i<<","<<j<<","<<k<<"): "<< patchField_.GetState(i,j,k)<<std::endl;

                                if (patchField_.IsActive(i,j,k,FREE_SURFACE)) {
                                        PatchUID tmp(i,j,k);
                                        fsPatches.insert(tmp);
                                        if (patchField_.IsAllocated(i,j,k)) freeSurface::FixInitialInterfaceLayer(*(patchField_.GetPatch(i,j,k)));
                                }

                                if (patchField_.IsActive(i,j,k,FREE_SURFACE_CAPFO_VAR) && patchField_.IsAllocated(i,j,k)) {
                                        // set the contact angle field to default contact angle
                                        CalcPatch& patch = *(patchField_.GetPatch(i,j,k));
                                        ScalarField<Real>& angles = patch.GetField<ScalarField<Real> >(FIELD_FS_CONTACT_ANGLES);

                                        for(Uint z=0; z<patch.GetZSize(); ++z) {
                                                for(Uint y=0; y<patch.GetYSize(); ++y) {
                                                        for(Uint x=0; x<patch.GetXSize(); ++x) {
                                                                angles.SET_SCALAR(x,y,z,fsConfig_.defaultTheta.rad);
                                                        }
                                                }
                                        }
                                }

                        }
                }
        }

        // Read contact angle geometries
        if (cfVarAngles) {
                FileReader::Blocks geomBlocks;
                fileReader.GetBlocks("fsgeometry",geomBlocks);
                for (FileReader::Blocks::size_type i=0; i<geomBlocks.size(); ++i) {
                        freeSurface::FSGeometry::ReadFSGeometry(geomBlocks[i], patchField_, sim_, fsConfig_);
                }
        }


        if (fsConfig_.bubbleCommunicationScheme != freeSurface::BCOMSTEFANLOCAL) {
                // for all other bubble communication types every patch has to know all bubbles
                // therefore, add all (other) patches to the bubbles, too
                LOG_INFO1("Domain.cpp::InitFreeSurfaceData:: Patch begins special initialization for bubble com type "+STR(fsConfig_.bubbleCommunicationScheme)+"\n");
                for(Uint k=0;k<sim_.zNumPatches;++k) {
                        for(Uint j=0;j<sim_.yNumPatches;++j) {
                                for(Uint i=0;i<sim_.xNumPatches;++i){
                                        if (patchField_.IsActive(i,j,k,FREE_SURFACE) && patchField_.IsAllocated(i,j,k)) {
                                                freeSurface::FSBubbles& bubbles = patchField_.GET_PATCH3(i,j,k)->GetBubbles();
                                                for (Uint b=0; b<=bubbles.GetNumBubbles(); b++) {
                                                        freeSurface::Bubble* bubble = bubbles.GET_BUBBLE_DATA(b);
                                                        if (bubble==NULL) continue;
                                                        std::set<PatchUID>& bubblePatches = bubble->patches;
                                                        for (std::set<PatchUID>::iterator p = fsPatches.begin(); p!=fsPatches.end(); ++p) {
                                                                if (bubblePatches.find(*p)==bubblePatches.end()) {
                                                                        LOG_INFO1("Domain.cpp::InitFreeSurfaceData:: Patch "+STR(patchField_.GET_PATCH3(i,j,k)->GetPatchUID())+" adding patch "+STR(*p)+" to bubble "+STR(bubble->ID)+"\n");
                                                                        bubble->diffAddPatches.insert(*p);
                                                                }
                                                        }
                                                }
                                        }
                                }
                        }
                }
                LOG_INFO1("Domain.cpp::InitFreeSurfaceData:: Patch finished special initialization for bubble com type "+STR(fsConfig_.bubbleCommunicationScheme)+"\n");
        }
}


//*************************************************************************************************
/*!\fn void Domain::InitGravity
   // \brief Initializes gravity for the PDF source field, depending on position of atmosphere (if it exists)
   // Note: Has to be done after InitializeBoundaries since gravity initialization is depending
   // on height of water surface to atmosphere
   // \param fileReader The fileReader object containing the simdata block
   // \return void
    */
void Domain::InitGravity(FileReader &fileReader) {
        if ( sim_.gx_L != 0 || sim_.gy_L != 0 || sim_.gz_L != 0)
        {
                // check if user wants his domain initialized with gravity ...
                FileReader::Blocks blocks;
                fileReader.GetBlocks("simdata",blocks);
                FileReader::Block sdBlock;
                sdBlock = blocks[0].CloneBlock();

                if (!sdBlock.IsDefined("g_init") && !sdBlock.IsDefined("g_init_l"))
                {
                        // no pdf-initialization due to gravity is done, output a warning as this was probably not intentioned
                        LOG_INFO1("Domain.cpp::Create: parameter 'g_init' was not defined, although a gravity is specified! If gravity is used, Patches' PDFs should be initialized with gravity. Please specify either just 'g_init;' or 'g_init <x,y,z>;'\n");
                }
                else
                {
                        // initialize fluid-domain with gravity, hopefully the user has specified the waterlevel
                        Vector3<Real> level; // vector that holds the coordinates of a point on water-surface
                        Vector3<Real> grav(sim_.gx_L, sim_.gy_L, sim_.gz_L); // gravity-vector


                        // if specified in simdata-block, use given coordinate-vector as mark for waterlevel
                        // note that cells above that point in <-gx,-gy,-gz> - direction won't be affected by initialization
                        PhysicalCheck pc("Domain.cpp::InitGravity");
                        // create an empty block with only this parameter to avoid the overwrite warnings of PhysicalCheck
                        Convert(pc.CheckAndCompleteParam("g_initX_L",0,&PhysicalCheck::CheckGZ,-1.0),level[X]);
                        Convert(pc.CheckAndCompleteParam("g_initY_L",0,&PhysicalCheck::CheckGZ,-1.0),level[Y]);
                        Convert(pc.CheckAndCompleteParam("g_initZ_L",0,&PhysicalCheck::CheckGZ,-1.0),level[Z]);
                        if (level[X]!=-1.0 && level[Y]!=-1.0 && level[Z]!=-1.0) {
                                LOG_INFO1("Domain.cpp::InitGravity: Waterlevel mark is manually set to "+STR(level)+".\n");
                                LOG_MONITOR("Domain.cpp::InitGravity: Waterlevel mark is manually set to "+STR(level)+".\n");
                                //std::cerr << "waterlevel mark: " << level << std::endl;
                        } else {
                                // if no vector was given, use 'highest' cell in domain as level mark
                                // note that cells above that point in <-gx,-gy,-gz> - direction wont be affected by initialization
                                //TODO: maybe better to search for the highest fluid(!)-cell in the domain
                                if(grav[0]>=0) level[X]=0.0; else level[X]=sim_.domainX;
                                if(grav[1]>=0) level[Y]=0.0; else level[Y]=sim_.domainY;
                                if(grav[2]>=0) level[Z]=0.0; else level[Z]=sim_.domainZ;

                                //check for a valid fsConfig_.initialSurfacePos{X,Y,Z}
                                if(fsConfig_.initialSurfacePosX<=sim_.domainX
                                                && fsConfig_.initialSurfacePosY<=sim_.domainY
                                                && fsConfig_.initialSurfacePosZ<=sim_.domainZ)
                                {
                                        if(grav[X]!=0.0) level[X]=fsConfig_.initialSurfacePosX;
                                        if(grav[Y]!=0.0) level[Y]=fsConfig_.initialSurfacePosY;
                                        if(grav[Z]!=0.0) level[Z]=fsConfig_.initialSurfacePosZ;
                                }
                                LOG_INFO1("Domain.cpp::InitGravity: Waterlevel mark is automatically set to "+STR(level)+".\n");
                                LOG_MONITOR("Domain.cpp::InitGravity: Waterlevel mark is automatically set to "+STR(level)+".\n");
                                //std::cerr << "waterlevel mark (auto):"<< level << std::endl;
                        }
                        //std::cerr << "initial-fs-pos: " << Vector3<Real>(fsConfig_.initialSurfacePosX, fsConfig_.initialSurfacePosY, fsConfig_.initialSurfacePosZ) << "\n";

                        // Initialize liquid cells with gravity if they lie under a certain waterlevel, marked by level
                        PatchIter patchIt=patchField_.GetBeginLocalPatches();
                        PatchIter patchEnd=patchField_.GetEndLocalPatches();
                        for(;patchIt!=patchEnd;++patchIt)
                        {
                                CalcPatch& patch = *(patchIt->GetPatch());
                                // for all patch-cells ..
                                for(Uint z=1; z<patch.GetZSize()-1; ++z)
                                {
                                        // std::cerr << "init_z: " << patch.GetZStart()+ z << ", flag: "<< patch.GetFlagField().Get(10,10,z) << "\n";
                                        for(Uint y=1; y<patch.GetYSize()-1; ++y)
                                        {
                                                for(Uint x=1; x<patch.GetXSize()-1; ++x)
                                                {
                                                        // calculate global cell coordinates and distance to watersurface (-0.5 (cellcenter) better for free surf)
                                                        Vector3<Real> cell(patch.GetXStart()+ x, patch.GetYStart()+ y, patch.GetZStart()+ z);
                                                        Real depth_factor = (cell - level)*grav; // is equals h*g in formula

                                                        if(depth_factor>=0)
                                                        {
                                                                //if(x==10 && y==10)
                                                                //std::cerr << "initializing z=" << Vector3<Real>(x,y,z) << "("<< FlagToStr(patch.GetFlagField().Get(x,y,z)) << ")"<< " with depth " << cell-level << "*" << grav <<" = "<<(cell - level)*grav<<"\n";
                                                                // current cell is under water-surface, so there is higher pressure
                                                                Real rho = exp(3.0*depth_factor); // see Diploma thesis of Simon Bogner

                                                                //!TODO: Treat boundary cells right (maybe)

                                                                //walberla::Equilibrium(patch.GetSrcField(), patch.GetDestField(), x,y,z, 1.0 + 3.0*depth_factor, 0,0,0, grav); // old version according to Tom
                                                                walberla::EquilibriumCompressible(patch.GetSrcField(), patch.GetDestField(), x,y,z, rho, 0,0,0, grav); // new version (slightly better)

                                                                // calc macroscopic values
                                                                Vector3<Real> vel;
                                                                CalcMacroCompressible(patch.GetSrcField(), x,y,z, rho, vel);
                                                                patch.GetVelField().SET_VEL_VEC(x,y,z,vel);
                                                        } //end if
                                                }
                                        }
                                }
                        } // end for all patches



                        /* START: old gravity-initialization
            //!TODO throw old one out, if the new one is 'good' ;-)
            // Initialize liquid cells with gravity like Tom did for Free Surfaces
            // CAUTION: Currently only in z-direction!!
            PatchIter patchIt=patchField_.GetBeginLocalPatches();
            PatchIter patchEnd=patchField_.GetEndLocalPatches();
            for(;patchIt!=patchEnd;++patchIt){
                CalcPatch& patch = *(patchIt->GetPatch());
                for(Uint z=1; z<patch.GetZSize()-1; ++z) {
                for(Uint y=1; y<patch.GetYSize()-1; ++y) {
                   for(Uint x=1; x<patch.GetXSize()-1; ++x) {
                      // Theretically it is possible to specify an atmosphere that is not as wide as the domain, like this:
                      // ------------------------------------
                      // | L   L   L   L | G G G G G G G G G |
                      // |		|-------------------|  y-dim
                      // |   L   L   L   L   L   L   L  L  L |
                      // |				 |
                      // -------------------------------------
                      //            x-dim
                      // For y above the atmosphere level, the gravity in x-direction has to respect the lower surface.
                      // For y below the atmosphere level, the gravity in x-direction has to count domainX cells.
                      // Therefore, we have to compute the three components for gravity separately and add them up.

                      // CAUTION: currently only for z-direction implemented!
                      Uint globalX = patch.GetXStart()+ x;
                      Uint globalY = patch.GetYStart()+ y;
                      Uint globalZ = patch.GetZStart()+ z;
                      PDFField& srcPDF = patch.GetSrcField();
                      Real rho=1.0;

                      if ((globalX >= fsConfig_.initialSurfacePosX) && (globalY >= fsConfig_.initialSurfacePosY)) {
                          if (globalZ<fsConfig_.initialSurfacePosZ) {
                                  rho += 3.0 * sim_.gz_L * (fsConfig_.initialSurfacePosZ - globalZ);
                          }
                      }else{
                          rho += 3.0 * sim_.gz_L * (sim_.domainZ - globalZ);
                      }
                      rho = max(1.0, rho);
                      for (Uint l=0; l<D3Q19::Cellsize; l++) {
                          srcPDF.SET_PDF(x,y,z,l, D3Q19::w[l] * rho);
                      }
                   }
                }
                }
            }// end for all patches
            // END: old gravity-initialization */
                }
        }//end initialize with gravity
}

//*************************************************************************************************
/*!\fn void Domain::InitShanChen(FileReader &read)
   // \brief A method to initialize the multi-phases and -components model according Shan and Chen
   // \param read File Reader
   // \return void
    */
void Domain::InitShanChen(FileReader &read){

        //////////////////////
        // Parse Input file //
        //////////////////////

        // Are there any blocks in the input file for phases
        FileReader::Blocks blocks;
        read.GetBlocks("ShanChen",blocks);
        // there may only be one block
        if(blocks.size()>1){
                LOG_ERROR("Specification of too many Phases blocks in the input file!");
                throw std::runtime_error( "Specification of too many Phases blocks in the input file!");
        }
        // if there no block return
        if(blocks.size()==0)return;

        LOG_WARNING("Domain.cpp::InitShanChen: SHAN_CHEN application is going to be activated. This application is still in highly experimental state!\n");

        FileReader::BlockHandle &handle=blocks[0];

        // throw error if none of the parameters is specified
        if ( (!handle.IsDefined("molmass1")) && (!handle.IsDefined("molmass2")) ) {
                throw std::runtime_error("Domain.cpp::InitShanChen:Molecular masses are not specified!");
        }

        if ( handle.IsDefined("tau1") ) {
                scConfig_.SetTau1(handle.GetParameter<Real>("tau1"));
        }else{
                scConfig_.SetTau1(sim_.tau);
        }
        if ( handle.IsDefined("tau2") ) {
                scConfig_.SetTau2(handle.GetParameter<Real>("tau2"));
        }else{
                scConfig_.SetTau2(sim_.tau);
        }

        scConfig_.SetMolMass1(handle.GetParameter<Real>("molmass1"));
        if(scConfig_.GetMolMass1() <= 0){
                throw std::runtime_error( "Domain.cpp::InitShanChen:Molecular masses smaller than zero!\nCorrect the value 'molmass1' in block 'phases'" );
        }
        scConfig_.SetMolMass2(handle.GetParameter<Real>("molmass2"));
        if(scConfig_.GetMolMass2() <= 0){
                throw std::runtime_error( "Domain.cpp::InitShanChen:Molecular masses smaller than zero!\nCorrect the value 'molmass2' in block 'phases'" );
        }

        if ( handle.IsDefined("g00") &&  handle.IsDefined("g01") &&  handle.IsDefined("g10") &&  handle.IsDefined("g11")  ) {
                scConfig_.SetGreens(handle.GetParameter<Real>("g00"), handle.GetParameter<Real>("g01"),  handle.GetParameter<Real>("g10"), handle.GetParameter<Real>("g11"));
        }
        //////////////////////////
        // Activision of Phases //
        //////////////////////////
        for(Uint k=0;k<sim_.zNumPatches;++k)
                for(Uint j=0;j<sim_.yNumPatches;++j)
                        for(Uint i=0;i<sim_.xNumPatches;++i){
                                patchField_.AddApp(i,j,k,SHAN_CHEN);
                        }

}


// activate Brownian motion
void Domain::ActivateBrownianMotion(FileReader &fileReader){

        // parameters for Brownian motion model
        Real phi = 0.0; // needed for bulk and shear value computations
        std::string ghost;   // information on ghost modes
        std::string bulk;   // information on bulk mode


        // Block parameter
        FileReader::Blocks blocks;
        fileReader.GetBlocks("brownian_motion",blocks);
        if(blocks.size()>1)
                throw std::runtime_error("Domain.cpp::ActivateBrownianMotion: Too many blocks named 'brownian_motion' in parameter file. Only one supported!\n");
        if(blocks.size()==1){
                std::cerr << "Initialise Brownian motion simulation..." << std::endl;

                brown_ = BrownianData();
                FileReader::BlockHandle &handle=blocks[0];

                // read in temperature and/or mass of fluid molecule

                // throw error if none of the parameters is specified
                if ( (!handle.IsDefined("temp")) && (!handle.IsDefined("mass")) && (!handle.IsDefined("mass_L")))
                {
                        throw std::runtime_error("Domain.cpp::ActivateBrownianMotion:Temperature and mass not specified!");
                }

                // get the temperature
                if ( handle.IsDefined("temp"))
                {
                        brown_.SetTemp(handle.GetParameter<Real>("temp"));
                        if(brown_.GetTemp() <=0){
                                throw std::runtime_error( "Domain.cpp::ActivateBrownianMotion:Temperature smaller than zero!\nCorrect the value 'temp' in block 'brownian_motion'" );
                        }
                }

                /////////////////////////
                // A FOR VAN DER WAALS //
                /////////////////////////
                if ( handle.IsDefined("a")&& handle.IsDefined("a_l"))
                        THROW_LOG_ERROR("a and a_l have been defined ... just specify one\n");
                if ( !(handle.IsDefined("a")||handle.IsDefined("a_l")))
                        THROW_LOG_ERROR("Neither a or a_l have been defined ... specify one of them\n");

                if ( handle.IsDefined("a"))
                        brown_.SetAFromA(handle.GetParameter<Real>("a"), sim_);
                if ( handle.IsDefined("a_l"))
                        brown_.SetAFromA_L(handle.GetParameter<Real>("a_l"), sim_);



                // get the mass
                if( (handle.IsDefined("mass_L")) && (handle.IsDefined("mass")) )
                {
                        Real m = handle.GetParameter<Real>("mass");
                        Real mL = handle.GetParameter<Real>("mass_L");
                        if (m != sim_.rho*sim_.dx*sim_.dx*sim_.dx*mL)
                        {
                                throw std::runtime_error("Domain.cpp::ActivateBrownianMotion:'mass' and 'mass_L' are both specified, but are not equivalent!");
                        }
                        brown_.SetMassFromMassL(mL, sim_);

                        if (brown_.GetMassL() <= 0){
                                throw std::runtime_error( "Domain.cpp::ActivateBrownianMotion:Molecule mass smaller than zero!\nCorrect the value 'mass' and/or 'mass_L' in block 'brownian_motion'" );
                        }
                } else {
                        if ( handle.IsDefined("mass_L") )
                        {
                                brown_.SetMassFromMassL( handle.GetParameter<Real>("mass_L"), sim_);

                                if (brown_.GetMassL() <= 0){
                                        throw std::runtime_error( "Domain.cpp::ActivateBrownianMotion:Molecule mass smaller than zero!\nCorrect the value 'mass' and/or 'mass_L' in block 'brownian_motion'" );
                                }
                        } else if (handle.IsDefined("mass"))
                        {
                                brown_.SetMassFromMass(handle.GetParameter<Real>("mass"), sim_);

                                if (brown_.GetMassL() <= 0){
                                        throw std::runtime_error( "Domain.cpp::ActivateBrownianMotion:Molecule mass smaller than zero!\nCorrect the value 'mass' and/or 'mass_L' in block 'brownian_motion'" );
                                }
                        }
                }

                if ( (brown_.GetMassL() == 0.0) && (brown_.GetTemp() == 0.0) )
                {
                        throw std::runtime_error("Domain.cpp::ActivateBrownianMotion: No molecule mass and temperature defined!\n Please specify 'mass'/'mass_L' or 'temp' in block 'brownian_motion'");
                }

                // read in either Boltzmann constant or nondimensional Boltzmann constant
                // -> if both are defined, calculate either temperature (if not defined in the file) or test the constants on equivalency
                // -> if none is defined, the Boltzmann constant is set to its default value: k_B = 1.38e-23
                // -> due to the nondimensionalisation, the definition of for example k_B_L and mass_L is not sufficient to determine k_B; in this
                //    case, give a warning and go on (k_B is not necessary for computations on the lattice)
                if( (handle.IsDefined("k_B")) && (handle.IsDefined("k_B_L")) )
                {
                        Real kB = handle.GetParameter<Real>("k_B");
                        Real kBL = handle.GetParameter<Real>("k_B_L");
                        if (brown_.GetTemp() != 0.0)
                        {
                                if ( kBL != (kB*brown_.GetTemp()*sim_.dt*sim_.dt/(sim_.rho*sim_.dx*sim_.dx*sim_.dx*sim_.dx*sim_.dx)) )
                                {
                                        throw std::runtime_error("Domain.cpp::ActivateBrownianMotion:Non-equivalent expressions for 'k_B', 'temp' and 'k_B_L'!");
                                }
                                // otherwise: determine temperature
                        } else {
                                brown_.SetTemp(kBL*sim_.rho*sim_.dx*sim_.dx*sim_.dx*sim_.dx*sim_.dx/(kB*sim_.dt*sim_.dt));
                        }
                } else
                {
                        if ( handle.IsDefined("k_B") )
                        {
                                brown_.SetKB(handle.GetParameter<Real>("k_B"));
                                if(brown_.GetKB() <=0){
                                        throw std::runtime_error( "Domain.cpp::ActivateBrownianMotion:Boltzmann constant smaller than zero!\nCorrect the value 'k_B' in block 'brownian_motion'" );
                                }

                                // determine k_B_L from either temperature or mass
                                if (brown_.GetTemp() != 0.0)
                                {
                                        brown_.CalcKBLFromTemp(sim_);
                                } else {
                                        brown_.CalcKBLFromMass();
                                }
                                std::cerr<<"Boltzmann constant read in: k_B=" << brown_.GetKB() << ", k_B_L=" << brown_.GetKBL() << std::endl;
                        } else if( handle.IsDefined("k_B_L") )
                        {
                                brown_.SetKBL(handle.GetParameter<Real>("k_B_L"));

                                if(brown_.GetKBL() <=0){
                                        throw std::runtime_error( "Domain.cpp::ActivateApplications:Boltzmann constant smaller than zero!\nCorrect the value 'k_B_L' in block 'brownian_motion'" );
                                }

                                // if possible, compute k_B; otherwise give warning
                                if (brown_.GetTemp() != 0.0)
                                {
                                        brown_.CalcKB(sim_);
                                } else {
                                        std::cerr << "Domain.cpp::ActivateBrownianMotion:Warning:Cannot determine k_B from k_B_L without temperature definition!" << std::endl;
                                }
                                std::cerr<<"Lattice Boltzmann constant read in: k_B=" << brown_.GetKB() << ", k_B_L=" << brown_.GetKBL() << std::endl;
                        } else {
                                brown_.SetKB(1.38e-23);
                                if (brown_.GetTemp() != 0.0)
                                {
                                        brown_.CalcKBLFromTemp(sim_);
                                } else {
                                        brown_.CalcKBLFromMass();
                                }
                                std::cerr<<"Boltzmann constant was set to default: k_B=1.38e-23, k_B_L=" << brown_.GetKBL() << std::endl;
                        }
                }

                // complete temperature if possible; otherwise, give warning
                if ( (brown_.GetTemp() == 0.0) && (brown_.GetKB() != 0.0) )
                {
                        brown_.CalcTemp(sim_);
                } else {
                        if (brown_.GetTemp() == 0.0)
                        {
                                std::cerr << "Domain.cpp::ActivateBrownianMotion:Warning:Temperature cannot be determined since k_B is missing!" << std::endl;
                        }
                }

                // complete mass values if they were not defined (since k_B_L is read in until here, this is definitely possible)
                if ( (!handle.IsDefined("mass")) && (!handle.IsDefined("mass_L")))
                {
                        brown_.CalcMass(sim_);
                }

                // for the case that mass and temperature were defined: check gas law on lattice level
                if (!brown_.CheckGasLaw())
                {
                        throw std::runtime_error( "Domain.cpp::ActivateBrownianMotion:Ideal gas law not fulfilled!\nCorrect the value 'temp' and/or 'mass' in block 'brownian_motion' or skip one of both definitions!" );
                }
                std::cerr << "temperature: " << brown_.GetTemp() << " mass: " << brown_.GetMass();
                std::cerr << " mass_L: " << brown_.GetMassL() << std::endl;



                if( (handle.IsDefined("bulk_viscosity")) && (handle.IsDefined("bulk_viscosity_L")) )
                {
                        Real b = handle.GetParameter<Real>("bulk_viscosity");
                        Real bL = handle.GetParameter<Real>("bulk_viscosity_L");
                        if ( b != bL*sim_.dx*sim_.dx/sim_.dt)
                        {
                                throw std::runtime_error("Domain.cpp::ActivateBrownianMotion: expressions for 'bulk_viscosity' and 'bulk_viscosity_L' are not equivalent!");
                        }

                        if (bL <= 0.0)
                        {
                                throw std::runtime_error("Domain.cpp::ActivateBrownianMotion: bulk viscosity not bigger than zero!");
                        }

                        brown_.SetBulkViscFromBulkViscL(bL,sim_);
                } else
                {
                        if ( handle.IsDefined("bulk_viscosity") )
                        {
                                brown_.SetBulkViscFromBulkVisc(handle.GetParameter<Real>("bulk_viscosity"), sim_);
                                if(brown_.GetBulkViscL() <=0.0){
                                        throw std::runtime_error( "Domain.cpp::ActivateBrownianMotion:bulk viscosity smaller than zero!\nCorrect the value 'bulk_viscosity' in block 'brownian_motion'" );
                                }
                                std::cerr<<"bulk viscosity read in: bulk_visc_L=" << brown_.GetBulkViscL() << std::endl;
                        } else if( handle.IsDefined("bulk_viscosity_L") )
                        {
                                brown_.SetBulkViscFromBulkViscL(handle.GetParameter<Real>("bulk_viscosity_L"), sim_);
                                if(brown_.GetBulkViscL() <= 0.0){
                                        throw std::runtime_error( "Domain.cpp::ActivateBrownianMotion:bulk viscosity smaller than zero!\nCorrect the value 'bulk_viscosity_L' in block 'brownian_motion'" );
                                }
                                std::cerr<<"bulk viscosity read in: bulk_visc_L=" << brown_.GetBulkViscL() <<  std::endl;
                        } else {
                                throw std::runtime_error("Domain.cpp::ActivateBrownianMotion: bulk viscosity not defined! Please specify either 'bulk_viscosity' or 'bulk_viscosity_L' in block 'brownian_motion'");
                        }
                }

                // compute gammas for shear and bulk modes and check them
                brown_.CalcGammas(sim_);
                if ( (brown_.GetGamma(BULK) < -1.0) || (brown_.GetGamma(BULK) > 1.0) )
                {
                        throw std::runtime_error("Domain.cpp::ActivateBrownianMotion: bulk viscosity of wrong order! Stability is lost!");
                }
                if ( (brown_.GetGamma(SHEAR) < -1.0) || (brown_.GetGamma(SHEAR) > 1.0) )
                {
                        throw std::runtime_error("Domain.cpp::ActivateBrownianMotion: shear viscosity of wrong order! Stability is lost!");
                }

                // read in gamma values for ghost modes
                // -> if it was specified, read in all values and calculate phi values
                // -> if not specified, set gamma and phi to zero for ghost modes
                if ( handle.IsDefined("gamma_ghost") )
                {
                        ghost = handle.GetParameter<std::string>("gamma_ghost");
                        if ( !(brown_.GetGammaGhost(ghost)) )
                        {
                                throw std::runtime_error( "Domain.cpp::ActivateBrownianMotion: Wrong input data for 'gamma_ghost' !\nPlease specify 'gamma_ghost' in block 'brownian_motion' in the following form:\ngamma_ghost [val1 val2 val3 val4]\nRemember that each value has to lie in the range [-1.0,1.0]!" );
                        }
                        // compute the phi values
                        brown_.CalcPhi();

                } else {

                        for (Uint i = THIRD_ORDER1; i < LENGTH_GAMMA; i++)
                        {
                                brown_.SetGamma(0.0,i);
                                brown_.SetPhi(0.0,i);
                        }

                        phi = sqrt(1.0 - brown_.GetGamma(BULK)*brown_.GetGamma(BULK));
                        brown_.SetPhi(phi,BULK);
                        phi = sqrt(1.0 - brown_.GetGamma(SHEAR)*brown_.GetGamma(SHEAR));
                        brown_.SetPhi(phi,SHEAR);

                        std::cerr << "'gamma_ghost' not specified..." << std::endl;
                }

                std::cerr << "gamma and phi read in: gamma=" << std::endl;
                for (Uint i = 0; i < LENGTH_GAMMA; i++)
                {
                        std::cerr << brown_.GetGamma(i) << " ";
                }
                std::cerr << std::endl;
                std::cerr << "phi=" << std::endl;
                for (Uint i = 0; i < LENGTH_PHI; i++)
                {
                        std::cerr << brown_.GetPhi(i) << " ";
                }
                std::cerr << std::endl;

                // compute the variational expression mu
                brown_.CalcMu();
                std::cerr << "mu_L=" << brown_.GetMu() << std::endl;

                Uint seed=10000;
                if(!handle.IsDefined("fixedseed"))
                        seed = (Uint) time(NULL);


                // read in simulation mode if specified
                if ( handle.IsDefined("sim_mode") )
                {
                        bulk = handle.GetParameter<std::string>("sim_mode");
                        if ( bulk.compare("NO_NOISE") == 0 )
                        {
                                brown_.SetSimMode(NO_NOISE);
                                std::cerr<<"'sim_mode' set to NO_NOISE." << std::endl;
                        } else if ( bulk.compare("NO_GHOST_INFLUENCE") == 0 )
                        {
                                brown_.SetSimMode(NO_GHOST_INFLUENCE);
                                std::cerr<<"'sim_mode' set to NO_GHOST_INFLUENCE. Additional settings for 'gamma_ghost' are ignored!" << std::endl;

                                // initialise the random number calculator
                                gaussian_ = Gaussian();
                                gaussian_.Create(seed, 2);
                                //gaussian_.Create(100000, 2);
                        } else if ( bulk.compare("GHOST_NOISE") == 0 )
                        {
                                brown_.SetSimMode(GHOST_NOISE);
                                // initialise the random number calculator
                                gaussian_ = Gaussian();
                                gaussian_.Create(seed, 2);
                        } else {
                                throw std::runtime_error( "Domain.cpp::ActivateBrownianMotion: Wrong input data for 'sim_mode' !\nIf 'sim_mode' in block 'brownian_motion' is specified, please set it\nto NO_NOISE, NO_GHOST_INFLUENCE or GHOST_NOISE!" );
                        }
                        // if no simulation mode was specified, set it to NO_GHOST_INFLUENCE, if the ghost modes
                        // were also not specified at all; otherwise, set the simulation mode to the "normal"
                        // setting GHOST_NOISE
                } else {
                        std::cerr<< "'sim_mode' not specified" << std::endl;

                        if ( (brown_.GetGamma(THIRD_ORDER1) == 0.0) && (brown_.GetGamma(THIRD_ORDER2) == 0.0)
                                        && (brown_.GetGamma(FOURTH_ORDER1) == 0.0) && (brown_.GetGamma(FOURTH_ORDER2) == 0.0)
                                        && (brown_.GetPhi(THIRD_ORDER1) == 0.0) && (brown_.GetPhi(THIRD_ORDER2) == 0.0)
                                        && (brown_.GetPhi(FOURTH_ORDER1) == 0.0) && (brown_.GetPhi(FOURTH_ORDER2) == 0.0) )
                        {
                                brown_.SetSimMode(NO_GHOST_INFLUENCE);
                                std::cerr<<"'sim_mode' is set to NO_GHOST_INFLUENCE." << std::endl;
                        } else {
                                brown_.SetSimMode(GHOST_NOISE);
                                std::cerr<<"'sim_mode' is set to GHOST_NOISE." << std::endl;
                        }
                }

                // read in generation mode for random numbers:
                // - if rand_mode is set to POLAR, use Polar method
                // - if rand_mode is set to FASTNORM3, use the FastNorm3 method from Wallace
                // - if not specified, use FastNorm3 method
                if ( handle.IsDefined("rand_mode") )
                {
                        bulk = handle.GetParameter<std::string>("rand_mode");

                        if ( bulk.compare("POLAR") == 0 )
                        {
                                brown_.SetRandMode(POLAR);
                                std::cerr<<"'rand_mode' is set to POLAR." << std::endl;
                        } else if ( bulk.compare("FASTNORM3") == 0)
                        {
                                brown_.SetRandMode(FASTNORM3);
                                std::cerr<<"'rand_mode' is set to FASTNORM3." << std::endl;
                        } else {
                                throw std::runtime_error( "Domain.cpp::ActivateBrownianMotion: Unknown generation mode for random numbers!\nPlease specify 'rand_mode' (POLAR/FASTNORM3) in block 'brownian_motion' in the correct way" );
                        }
                } else {
                        brown_.SetRandMode(FASTNORM3);
                        std::cerr<<"'rand_mode' not specified. 'rand_mode' is set to FASTNORM3." << std::endl;
                }


                // set all to brownian motion
                for(Uint k=0;k<sim_. zNumPatches;++k)
                        for(Uint j=0;j<sim_. yNumPatches;++j)
                                for(Uint i=0;i<sim_. xNumPatches;++i){
                                        patchField_.AddApp(i,j,k,BROWNIAN_MOTION);
                                }
        }
}


void Domain::ActivatePovray(FileReader &fileReader)
{
#ifndef NO_PE
        /////////////
        // POVRAY? //
        /////////////
        Uint spacing=1;

        FileReader::Blocks blocks,cameras,visTypes;
        fileReader.GetBlocks("povray",blocks);
        if(blocks.size()>1)
                THROW_LOG_ERROR("In Function Domain::ActivatePovray(FileReader &fileReader):\n Too many\"povray\" blocks!\n");
        if(blocks.size()==0)return;
        FileReader::BlockHandle &povray = blocks[0];

        /////////////
        // Spacing //
        /////////////
        if(povray.IsDefined("spacing"))
                spacing=povray.GetParameter<Uint>("spacing");

        PovRayVis::Configure(spacing,&patchField_,&sim_,&mpi_);
        PovRayVis &pov = PovRayVis::Instance();
        //////////
        // File //
        //////////
        if (povray.IsDefined("filename")) {
                bool overwrite = true;
                int nProcsPerFile = 1;

                // check for optional
                if (povray.IsDefined("nProcsPerFile")) {
                        nProcsPerFile = povray.GetParameter<Uint> ("nProcsPerFile");
                }

                // check if files should be overwritten
                if (povray.IsDefined("overwrite") && povray.GetParameter<Uint> (
                                        "overwrite") == 0)
                        overwrite = false;
                pov.SetFileName(povray.GetParameter<std::string> ("filename"),
                                overwrite, nProcsPerFile);

        } else {
                THROW_LOG_ERROR("Domain::ActivatePovray: No parameter 'filename' specified!\n");
        }

        //////////////
        // Resources //
        //////////////
        FileReader::Blocks includeBlocks;
        povray.GetBlocks("resource",includeBlocks);
        Uint size = includeBlocks.size();
        for(Uint i = 0;i<size;++i){


                if(!includeBlocks[i].IsDefined("file"))
                        THROW_LOG_ERROR("Domain.cpp: resource block defined in povray block but no \"file\" name\n");
                //std::cerr << "block " << i <<" - " << std::string(includeBlocks[i].GetParameter<std::string>("file"))<<"\n";
                pov.AddIncludeFile(includeBlocks[i].GetParameter<std::string>("file"));
        }

        /////////////
        // Cameras //
        /////////////
        povray.GetBlocks("camera",cameras);
        size = cameras.size();
        if(size==0)THROW_LOG_ERROR("Domain::ActivatePovray: No camera specified\n");

        for(Uint i = 0;i<size;++i)
                pov.AddCamera(cameras[i]);


        //////////////
        // VisTypes //
        //////////////
        povray.GetBlocks("povvistype",visTypes);
        size = visTypes.size();
        if(size==0)THROW_LOG_ERROR("Domain::ActivatePovray: No PovRayVisType specified\n");
        for(Uint i = 0;i<size;++i)
                pov.AddVisType(visTypes[i]);

        ////////////////
        // Cellfields //
        ////////////////
        FileReader::Blocks cellfields;
        povray.GetBlocks("cellfield", cellfields);
        size = cellfields.size();
        for(Uint i=0; i<size; i++) {
                pov.AddCellFieldVis(cellfields[i]);
        }

        ////////////////
        // Densfields //
        ////////////////
        FileReader::Blocks densfields;
        povray.GetBlocks("densfield", densfields);
        for(Uint i=0; i<densfields.size(); i++) {
                pov.AddDensFieldVis(densfields[i]);
        }


#ifdef WALBERLA_USE_MPI
        MPI_Barrier(MPI_COMM_WORLD);
#endif
#endif
}

//*************************************************************************************************
/*!\fn void Domain::InitMonitoredCells(FileReader &fileReader)
   // \brief Configures the Patches' Fields for monitoring the cells specified by the user in "monitorCell"-Blocks
   // \return void
    */
void Domain::InitMonitoredCells(FileReader &fileReader){
        FileReader::Blocks monitorBlocks;
        fileReader.GetBlocks("monitorcell",monitorBlocks);
        if (monitorBlocks.size()==0) return;
#     ifndef LOGGING_MONITOR
#     ifdef WALBERLA_USE_MPI
        if(mpi_.worldRank==0)
#     endif
                LOG_WARNING("Domain.cpp::InitMonitoredCells: The parameter file specifies cells to monitor, but waLBerla is compiled without -DLOGGING_MONITOR! No cells will be monitored!\n");
        return;
#     endif
        for (FileReader::Blocks::const_iterator block=monitorBlocks.begin(); block!=monitorBlocks.end(); block++) {
                if (!block->IsDefined("cell") && !block->IsDefined("cell_L")) THROW_LOG_ERROR("Domain.cpp::InitMonitoredCells: One of the 'monitorCell' blocks does not contain a parameter 'cell' or 'cell_L' to describe the global position.\n");
                //if (!block->IsDefined("fields")) THROW_LOG_ERROR("Domain.cpp::InitMonitoredCells: One of the 'monitorCell' blocks does not contain a parameter 'fields' with a comma-separated list of the FieldIDs (or the special value 'all').\n");

                PhysicalCheck::ParameterUnitRelation pu;
                pu["cell"]="m";
                FileReader::Block pcBlock = block->CloneBlock();
                PhysicalCheck pc("Domain.cpp::InitMonitoredCells");
                pc.LearnInformation(pcBlock,pu);
                Uint x,y,z;
                x = pc.CheckAndCompleteParam<Uint>("cellX_L",0,&PhysicalCheck::CheckGZInt);
                y = pc.CheckAndCompleteParam<Uint>("cellY_L",0,&PhysicalCheck::CheckGZInt);
                z = pc.CheckAndCompleteParam<Uint>("cellZ_L",0,&PhysicalCheck::CheckGZInt);
                if ((x>sim_.domainX+1) || (y>sim_.domainY+1) || (z>sim_.domainZ+1)) THROW_LOG_ERROR("Domain.cpp::InitMonitoredCells: The cell (x,y,z)=("+STR(x)+","+STR(y)+","+STR(z)+") lies outside of the domain!\n");
                std::vector<PatchCoordinate> pCoord;
                pCoord = GetLocalPatchCoordinates(x,y,z,sim_);
                for (Uint i=0; i<pCoord.size();i++) {
                        if (patchField_.IsAllocated(pCoord[i].pX,pCoord[i].pY,pCoord[i].pZ)) {
                                std::string fields;
                                if (!block->IsDefined("fields")) {
                                        fields="all";
                                } else {
                                        fields=ConvertToLowerCase(block->GetParameter<std::string>("fields"));
                                }
                                if (fields=="all") {
                                        fields="";
                                        for (Uint f=0; f<MAXIMUM_FIELD_UID; f++) {
                                                fields+=ConvertToLowerCase(fieldsUIDtoString[f])+",";
                                        }
                                } else {
                                        fields=fields+",";
                                }
                                Cell monitoredCell(pCoord[i].x,pCoord[i].y,pCoord[i].z);
                                patchField_.GET_PATCH3(pCoord[i].pX,pCoord[i].pY,pCoord[i].pZ)->AddMonitoredCell(monitoredCell);

                                while (fields.find(",")!=string::npos) {
                                        Uint fieldID = MAXIMUM_FIELD_UID;
                                        std::string fieldIDStr = fields.substr(0,fields.find(","));
                                        for (Uint f=0; f<MAXIMUM_FIELD_UID; f++) {
                                                if (ConvertToLowerCase(fieldsUIDtoString[f])==fieldIDStr) {
                                                        fieldID = f;
                                                        break;
                                                }
                                        }
                                        if (fieldID == MAXIMUM_FIELD_UID) THROW_LOG_ERROR("Domain.cpp::InitMonitoredCells: Invalid FieldUID: "+fieldIDStr+".\n");
                                        CalcPatch* patch = patchField_.GET_PATCH3(pCoord[i].pX,pCoord[i].pY,pCoord[i].pZ);
                                        if (patch->IsFieldCreated(static_cast<FieldUID>(fieldID))) {
                                                FieldInterface* field = patch->GetFieldInterface(static_cast<FieldUID>(fieldID));
                                                field->AddMonitoredCell(monitoredCell);
                                                LOG_INFO2("Domain.cpp::InitMonitoredCells: Adding Monitoring of cell ("+STR(pCoord[i].x)+","+STR(pCoord[i].y)+","+STR(pCoord[i].z)+") in field "+fieldIDStr+" for patch ("+STR(pCoord[i].pX)+","+STR(pCoord[i].pY)+","+STR(pCoord[i].pZ)+").\n");
                                        }
                                        fields=fields.substr(fields.find(",")+1);
                                }
                        }
                }
        }
}

//*************************************************************************************************
/*!\fn void Domain::InitSimData(FileReader &fileReader)
   // \brief Configures the SimData struct
   // \return void
    */
void Domain::InitSimData(FileReader &fileReader){
        FileReader::Blocks simDataBlocks;
        fileReader.GetBlocks("simdata",simDataBlocks);
        if (simDataBlocks.size()==0) THROW_LOG_ERROR("Domain.cpp::InitSimData: No 'simdata' block found in parameter file!\n");
        if (simDataBlocks.size()>1) THROW_LOG_ERROR("Domain.cpp::InitSimData: Too many 'simdata' blocks specified in parameter file. Please specify only one!\n");

#ifdef DEACTIVATE_PHYSIC_CHECKER_TURBULENCE

#ifdef WALBERLA_USE_MPI
        if(mpi_.worldRank==0)
        {
                std::cerr << " DEACTIVATED THE PHYSICAL CHECKER  \n" ;
        }
#else
        std::cerr << " DEACTIVATED THE PHYSICAL CHECKER  \n" ;
#endif // mpi




        // need for  flows simulation
        PhysicalCheck::Configure(simDataBlocks[0], geometry_.GetGeometryInfo());
        PhysicalCheck pc("Domain::InitSimData");
        pc.CheckAndCompleteAllParamsTurbulence(sim_);
#else
        PhysicalCheck::Configure(simDataBlocks[0], geometry_.GetGeometryInfo());
        PhysicalCheck pc("Domain::InitSimData");
        pc.CheckAndCompleteAllParams(sim_);
#endif


#if defined (TURB_DECAY) && !defined (DNS_DECAY)
    this->LogErrorMessage ( " RUN TURBULENCE DECAY SINARIO  \n" );
#endif

#if defined (DNS_DECAY) && !defined (TURB_DECAY)
    this->LogErrorMessage ( " RUN DNS DECAY SINARIO  \n" );
#endif


#     ifdef WALBERLA_USE_MPI
        if(mpi_.worldRank==0)
#     endif
                std::cout << "The domain size is set to:" << sim_.domainX << "," << sim_.domainY << "," << sim_.domainZ << std::endl;
        ASSERT(sim_.domainX>0&&sim_.domainY>0&&sim_.domainZ>0,"Domain.cpp::InitSimData: Size of the domain smaller 0");
        FileReader::Blocks blocks;
        fileReader.GetBlocks("simdata",blocks);
        if (blocks[0].IsDefined("dump")) {
                Logging::Instance().LogInfo("Domain.cpp::InitSimData: " + sim_.ToString() +"\n");
        }
        if (blocks[0].IsDefined("itaniumOptimizationLevel")) {
                sim_.itaniumOptimizationLevel=blocks[0].GetParameter<int>("itaniumOptimizationLevel");
                if(mpi_.worldRank==0)
                        std::cout << "Itanium optimization is set to " << sim_.itaniumOptimizationLevel << std::endl;
        }
}

void Domain::InitParticles(FileReader &fileReader){
#ifndef NO_PE
        //******************************************
        //First check if there is an object block
        FileReader::Blocks objectBlock;
        // to do so, first get the init block, where the particles should be
        fileReader.GetBlocks("object",objectBlock);
        if(objectBlock.size()>1)
                throw std::runtime_error( "Domain.cpp::InitParticles: Specification of too many object blocks in the input file! Only one block allowed" );

        if(objectBlock.size()==1)
        {
                FileReader::BlockHandle &objectHandle=objectBlock[0];

                //========================================================
                if ( objectHandle.IsDefined("distance") )
                {
                        movingObstacles_.SetDistance(objectHandle.GetParameter<Real>("distance"));
                }
                //========================================================
                if ( objectHandle.IsDefined("radius") )
                {
                        movingObstacles_.SetRadius(objectHandle.GetParameter<Real>("radius"));
                }
                //========================================================
                if ( objectHandle.IsDefined("ObstaclePatches") )
                {
                        wantMOObstacles_=true;
                }
                else
                {
                        wantMOObstacles_=false;
                }
                //========================================================
                if ( objectHandle.IsDefined("useHalfForce") )
                {
                        movingObstacles_.useHalfForce=objectHandle.GetParameter<int>("useHalfForce");
                }
                //========================================================
                if ( objectHandle.IsDefined("useFillLevels") )
                {
                        movingObstacles_.useFillLevels=objectHandle.GetParameter<int>("useFillLevels");
                }
                //========================================================
                if ( objectHandle.IsDefined("useDetectGap") )
                {
                        movingObstacles_.useDetectGap_=objectHandle.GetParameter<int>("useDetectGap");
                }
                //========================================================
                if ( objectHandle.IsDefined("useCheckObjects") )
                {
                        movingObstacles_.useCheckObjects_=objectHandle.GetParameter<int>("useCheckObjects");
                }
                //========================================================
                if ( objectHandle.IsDefined("useAutomaticForceCorrection") )
                {
                        movingObstacles_.useAutomaticForceCorrection=objectHandle.GetParameter<int>("useAutomaticForceCorrection");
                }
                //========================================================
                if ( objectHandle.IsDefined("useBoundaryObjects") )
                {
                        movingObstacles_.useBoundaryObjects_=objectHandle.GetParameter<int>("useBoundaryObjects");
                }
                //========================================================
                if ( objectHandle.IsDefined("useZPeriodic") )
                {
                        movingObstacles_.useZPeriodic_=objectHandle.GetParameter<int>("useZPeriodic");
                }
                //========================================================
                if ( objectHandle.IsDefined("densObjects") )
                {
                        movingObstacles_.densObjects_=objectHandle.GetParameter<Real>("densObjects");
                }
                //========================================================
                if ( objectHandle.IsDefined("densDuck") )
                {
                        movingObstacles_.densDuck_=objectHandle.GetParameter<Real>("densDuck");
                }
                //========================================================
                if ( objectHandle.IsDefined("resizeVel") )
                {
                        movingObstacles_.resizeVel_=objectHandle.GetParameter<int>("resizeVel");
                }
                //========================================================
                if ( objectHandle.IsDefined("resizePDF") )
                {
                        movingObstacles_.resizePDF_=objectHandle.GetParameter<int>("resizePDF");
                }
                //========================================================
                if ( objectHandle.IsDefined("setToSolid") )
                {
                        movingObstacles_.setToSolid_=objectHandle.GetParameter<int>("setToSolid");
                }
                //========================================================
                if ( objectHandle.IsDefined("addDuck") )
                {
                        movingObstacles_.addDuck_=objectHandle.GetParameter<int>("addDuck");
                        if(movingObstacles_.addDuck_) std::cout << "Adding duck!" << std::endl;
                }
                //========================================================
                if ( objectHandle.IsDefined("particleSegregation") )
                {
                        movingObstacles_.particleSegregation_=objectHandle.GetParameter<int>("particleSegregation");
                        if(movingObstacles_.particleSegregation_ && mpi_.worldRank==0) std::cout << "Using particle segregation!" << std::endl;
                }
                //========================================================
                if ( objectHandle.IsDefined("compressible") )
                {
                        sim_.compressible=objectHandle.GetParameter<int>("compressible");
                }
                //========================================================
                if ( objectHandle.IsDefined("testForce") )
                {
                        movingObstacles_.testForce_=objectHandle.GetParameter<Real>("testForce");
                        std::cout << "Test Force is:" << movingObstacles_.testForce_ << std::endl;
                }
                //========================================================
                if ( objectHandle.IsDefined("useLubrication") )
                {
                        movingObstacles_.useLubrication_=objectHandle.GetParameter<int>("useLubrication");
                }
                //========================================================
                if ( objectHandle.IsDefined("outputPos") )
                {
                        movingObstacles_.outputPos_=objectHandle.GetParameter<int>("outputPos");
                }
                //========================================================
                if ( objectHandle.IsDefined("sphereBatterie") )
                {
                        movingObstacles_.sphereBatterie_=objectHandle.GetParameter<int>("sphereBatterie");
                }
                //========================================================
                if ( objectHandle.IsDefined("initPressureGradient") )
                {
                        movingObstacles_.initPressureGradient_=objectHandle.GetParameter<int>("initPressureGradient");
                        movingObstacles_.prsMin_=objectHandle.GetParameter<Real>("prsMin");
                        movingObstacles_.prsMax_=objectHandle.GetParameter<Real>("prsMax");
                        sim_.prsMin_=objectHandle.GetParameter<Real>("prsMin");
                        sim_.prsMax_=objectHandle.GetParameter<Real>("prsMax");
                }
                //========================================================
                if ( objectHandle.IsDefined("numPeStepsPerLBMStep") )
                {
                        movingObstacles_.numPeStepsPerLBMStep_=objectHandle.GetParameter<int>("numPeStepsPerLBMStep");
                }
                //========================================================
                if ( objectHandle.IsDefined("numLBMStepsPerPeStep") )
                {
                        movingObstacles_.numLBMStepsPerPeStep_=objectHandle.GetParameter<int>("numLBMStepsPerPeStep");
                }
                //========================================================

                if ( objectHandle.IsDefined("numTimestepsPerObjectCreate") )
                {
                        movingObstacles_.numTimestepsPerObjectCreate_=objectHandle.GetParameter<int>("numTimestepsPerObjectCreate");
                }
                //========================================================
                if ( objectHandle.IsDefined("objectEntryVel") )
                {
                        movingObstacles_.objectEntryVel_=objectHandle.GetParameter<Real>("objectEntryVel");
                }
                //========================================================
                if ( objectHandle.IsDefined("numObjectsYDirection") )
                {
                        movingObstacles_.numObjectsYDirection_=objectHandle.GetParameter<Uint>("numObjectsYDirection");
                }
                //========================================================


                if ( objectHandle.IsDefined("timesPerPulse") )
                {
                        movingObstacles_.timesPerPulse_=objectHandle.GetParameter<Real>("timesPerPulse");
                }
                //========================================================
                if ( objectHandle.IsDefined("displacement") )
                {
                        movingObstacles_.displacement_=objectHandle.GetParameter<Real>("displacement");
                }
                //========================================================
                //========================================================
                if ( objectHandle.IsDefined("amplitude") )
                {
                        movingObstacles_.amplitude_=objectHandle.GetParameter<Real>("amplitude");
                }
                //========================================================

                //========================================================
                //check settings for particles
                //========================================================
                if(sim_.g_init && !sim_.compressible )
                {
                        throw std::runtime_error("Domain.cpp::InitParticles:When using g_init, it is only allowed to use the compressible method!");
                }
                if(movingObstacles_.sphereBatterie_ && movingObstacles_.addDuck_)
                {
                        throw std::runtime_error("Domain.cpp::InitParticles:Only one setting allowed, either Spherebatterie or AddDuck!");
                }
                if( (movingObstacles_.GetRadius()==0.0 || movingObstacles_.GetDistance()==0) && (movingObstacles_.sphereBatterie_ || movingObstacles_.addDuck_))
                {
                        throw std::runtime_error("Domain.cpp::InitParticles:When adding Spherebatterie or AddDuck, you have to specify the radius and the distance!");
                }
                if( (movingObstacles_.GetRadius()!=0.0 || movingObstacles_.GetDistance()!=0) &&  movingObstacles_.densObjects_==0.0)
                {
                        throw std::runtime_error("Domain.cpp::InitParticles:When adding objects, you have to specify the density!");
                }
                if( movingObstacles_.addDuck_ &&  movingObstacles_.densDuck_==0.0)
                {
                        throw std::runtime_error("Domain.cpp::InitParticles:When adding duck, you have to specify the density!");
                }
                if( (movingObstacles_.GetRadius()!=0.0 && movingObstacles_.GetDistance()==0 && !movingObstacles_.useBoundaryObjects_) || (movingObstacles_.GetRadius()==0.0 && movingObstacles_.GetDistance()!=0 && !movingObstacles_.useBoundaryObjects_)  )
                {
                        throw std::runtime_error("Domain.cpp::InitParticles:Either radius or distance of objects is 0!");
                }


                std::ostringstream oss;
                oss << " Particle Parameters:" << std::endl;
                oss << "---------------------" << std::endl;
                //========================================================
                //output all settings for particles
                //========================================================
                if(sim_.compressible)
                {
                        oss << " We are using compressible model!" << std::endl;
                }
                else
                {
                        oss << " We are using incompressible model!" << std::endl;
                }
                //========================================================
                if(movingObstacles_.GetRadius()>0.0)
                {
                        oss << " The radius for the objects is " << movingObstacles_.GetRadius() << std::endl;
                }
                //========================================================
                if(movingObstacles_.GetDistance()>0.0)
                {
                        oss << " The distance for the objects is " << movingObstacles_.GetDistance() << std::endl;
                }
                //========================================================
                if(movingObstacles_.densObjects_>0.0)
                {
                        oss << " We are using a density per object of " << movingObstacles_.densObjects_ << std::endl;
                }
                //========================================================
                if(movingObstacles_.densDuck_>0.0)
                {
                        oss << " We are using a density for the duck of " << movingObstacles_.densDuck_ << std::endl;
                }
                //========================================================
                oss << " We are using " << movingObstacles_.numPeStepsPerLBMStep_ << " PE steps per LBM time step" << std::endl;
                //========================================================
                oss << " We are using " << movingObstacles_.numLBMStepsPerPeStep_ << " LBM steps per Pe time step" << std::endl;
                //========================================================
                if(movingObstacles_.useHalfForce)
                {
                        oss << " We are using half force method" << std::endl;
                }
                else
                {
                        oss << " We are NOT using half force method" << std::endl;
                }
                //========================================================
                if(movingObstacles_.useFillLevels)
                {
                        oss << " We are using fill method. BE AWARE! THIS IS EXPERIMENTAL!!!" << std::endl;
                }
                else
                {
                        oss << " We are NOT using fill method" << std::endl;
                }
                //========================================================
                if(movingObstacles_.useDetectGap_)
                {
                        oss << " We are detecting gaps between objects. This will reduce performance" << std::endl;
                }
                else
                {
                        oss << " We are NOT detecting gaps between objects" << std::endl;
                }
                //========================================================
                if(movingObstacles_.useCheckObjects_)
                {
                        oss << " We are checking if all objects inside the domain. This will reduce performance" << std::endl;
                }
                else
                {
                        oss << " We are NOT checking if all objects inside the domain" << std::endl;
                }
                //========================================================
                if(movingObstacles_.useAutomaticForceCorrection)
                {
                        oss << " We are using automatic force correction" << std::endl;
                }
                else
                {
                        oss << " We are NOT using automatic force correction" << std::endl;
                }
                //========================================================
                if(movingObstacles_.useLubrication_)
                {
                        oss << " We are using lubrication. BE AWARE! ONLY IMPLEMENTED FOR SPHERES YET!" << std::endl;
                }
                else
                {
                        oss << " We are NOT using lubrication" << std::endl;
                }
                //========================================================
                if(movingObstacles_.sphereBatterie_)
                {
                        oss << " We are using sphereBatterie" << std::endl;
                }
                //========================================================
                if(movingObstacles_.resizeVel_)
                {
                        oss << " We are resizing the velocity if larger than 0.1" << std::endl;
                }
                else
                {
                        oss << " We are NOT resizing the velocity if larger than 0.1" << std::endl;
                }
                //========================================================
                if(movingObstacles_.resizePDF_)
                {
                        oss << " We are resizing the PDF if density larger than 1.2" << std::endl;
                }
                else
                {
                        oss << " We are NOT resizing the PDF if density larger than 1.2" << std::endl;
                }
                //========================================================
                if(movingObstacles_.setToSolid_)
                {
                        oss << " We are setting the cell to solid if density larger than 1.2" << std::endl;
                }
                else
                {
                        oss << " We are NOT setting the cell to solid if density larger than 1.2" << std::endl;
                }
                //========================================================
                if(movingObstacles_.outputPos_)
                {
                        oss << " We are outputting the position" << std::endl;
                }
                else
                {
                        oss << " We are NOT outputing the position" << std::endl;
                }
                //========================================================
                Logging::Instance().LogInfo("Domain.cpp::InitParticles: " + oss.str() +"\n");

        }

#     ifdef WALBERLA_USE_MPI

        //Check if the simulation contains particles and we need the pe instance
        FileReader::Blocks blocks;
        FileReader particleFileReader;
        blocks.clear();

        // to do so, first get the init block, where the particles should be
        // obstacle restart data is now read solely from include files
        fileReader.GetBlocks("init",blocks);


        if(blocks.size()>1)
                throw std::runtime_error( "Domain.cpp::Init: Specification of too many init blocks in the input file! Only one block allowed" );
        if(blocks.size()==0)
                throw std::runtime_error( "Domain.cpp::Init: Specification of no init block in the input file! See documentation for usage!" );

        //Now take the inner block in the init block. Here the particles should be
        FileReader::BlockHandle &initHandle=blocks[0];
        FileReader::Blocks innerBlocks;
        initHandle.GetBlocks("inner",innerBlocks);

        //We can have many inner blocks, so iterate about them
        for (Uint numInnerBlock=0; numInnerBlock<innerBlocks.size(); numInnerBlock++) {

                // For each block look for objects specified...
                FileReader::Blocks objectBlocks;
                innerBlocks[numInnerBlock].GetBlocks(objectBlocks);
                //Now we shall have unions, or primitives (spheres, boxes, etc)
                //so check if we have moving obstacles and not only fixed ones
                for (Uint objBlock=0; objBlock<objectBlocks.size(); objBlock++)
                {
                        if (
                                        (objectBlocks[objBlock].GetKey()=="union")   ||
                                        (objectBlocks[objBlock].GetKey()=="sphere")  ||
                                        (objectBlocks[objBlock].GetKey()=="box")     ||
                                        (objectBlocks[objBlock].GetKey()=="capsule") ||
                                        (objectBlocks[objBlock].GetKey()=="plane")
                                        )
                        {
                                if (objectBlocks[objBlock].IsDefined("fixed")) //union is fixed, so no moving obstacles till now
                                        continue;
                                else  //Check all primitives in the union if they are fixed
                                {
                                        // parse all the primitive object contained in the union
                                        FileReader::Blocks primitiveBlocks;
                                        objectBlocks[objBlock].GetBlocks(primitiveBlocks);
                                        for (Uint i=0; i<primitiveBlocks.size(); i++)
                                        {
                                                if(primitiveBlocks[i].IsDefined("fixed"))
                                                        continue;
                                                else
                                                {
                                                        if(mpi_.numprocsWorld>1)
#			 ifdef FAST_PE
                                                                peInstance_=0;
#			 else
                                                                peInstance_=1;
#			 endif
                                                        break;
                                                }
                                        }
                                }
                        }
                }
        }
        //******************************************
#     endif

        if(peInstance_)
        {
                LOG_INFO1("Domain.cpp::Init:We will need a pe Instance\n");
        }
        else
        {
                LOG_INFO1("Domain.cpp::Init:We will need no pe Instance\n");
        }

#endif
}



//TODO Funktion kommt in COMMUNICATION
//TODO Fuer den seriellen Fall im PatchField impl

//Place the patches to the processors and allocates fields in the patches
//then put the patches which shold be calculated to the calcPatch vector
void Domain::PlaceAllocatePatches(){
        LOG_INFO2("Domain.cpp::Placing the patches to the processes\n");
        FileReader::Blocks patchBlocks;
        fileReader_->GetBlocks("patches",patchBlocks);
        if(patchBlocks.size()>0)
                if (patchBlocks[0].IsDefined("autoplace")) autoPlace_=1;
#ifdef WALBERLA_USE_MPI


        mpi_.ndims = 3;
        mpi_.reorder = true;

        mpi_.periods[X] = false;
        mpi_.periods[Y] = false;
        mpi_.periods[Z] = false;

        mpi_.dims[X]=0;
        mpi_.dims[Y]=0;
        mpi_.dims[Z]=0;

        //************************************************************************
        //Create a new communicator for fluid workers only, so without pe-instance
        MPI_Group fluidWorkersGroup, worldGroup;

        //Create the world communicator
        MPI_Comm_group(MPI_COMM_WORLD, &worldGroup);

        if(peInstance_)
        {
                mpi_.rankPeInstance=mpi_.numprocsWorld-1; //Last process is the pe-instance

                //Take all ranks, but not the pe instance
                int* ranks=new int[mpi_.numprocsWorld-1];
                int count=0;
                for(int i=0;i<mpi_.numprocsWorld;++i)
                {
                        if(i!=mpi_.rankPeInstance)
                        {
                                ranks[count]=i;
                                ++count;
                        }
                }
                //Include all ranks without the pe instance
                MPI_Group_incl(worldGroup, mpi_.numprocsWorld-1, ranks, &fluidWorkersGroup);
                MPI_Comm_create(MPI_COMM_WORLD, fluidWorkersGroup, &mpi_.comm_cart);

                //The pe process has to leave here
                if(mpi_.worldRank==mpi_.rankPeInstance)
                {
                        mpi_.fluidWorkersRank=-1;
                        mpi_.numprocsFluidWorkers=mpi_.numprocsWorld-1;
                        return;
                }
        }
        else //We dont have pe instance
        {
                mpi_.rankPeInstance=-1;
                mpi_.comm_cart=MPI_COMM_WORLD;
        }

        //Get the number of processes
        //std::cerr << "I am fluid worker with rank:" << mpi_.fluidWorkersRank <<std::endl;
        MPI_Comm_size(mpi_.comm_cart,&mpi_.numprocsFluidWorkers);
        MPI_Dims_create( mpi_.numprocsFluidWorkers, mpi_.ndims, mpi_.dims);
        MPI_Cart_create( mpi_.comm_cart, mpi_.ndims, mpi_.dims, mpi_.periods, mpi_.reorder, &mpi_.comm_cart );
        //Get the fluid workers rank
        MPI_Comm_rank(mpi_.comm_cart, &mpi_.fluidWorkersRank);

        //************************************************************************

        if(sim_.xNumPatches*sim_.yNumPatches*sim_.zNumPatches<(Uint)mpi_.numprocsFluidWorkers)
        {
                LOG_WARNING("Domain.cpp::Create:We have more processes ("+Convert<std::string>(mpi_.numprocsFluidWorkers)+") than patches, I cannot divide a patch in two. Please check setup or reduce number of processes!");
        }

        if(patchField_.autoPlace_)
        {
                if(mpi_.fluidWorkersRank==0)
                        std::cout << "Simulation uses " << mpi_.numprocsFluidWorkers << " Processors for the patches and " << peInstance_ << " processes for pe-instance(s)" << std::endl;
                LOG_INFO2("Domain.cpp::PlaceAllocatePatches:Simulation uses "+ Convert<std::string>(mpi_.numprocsFluidWorkers) + " Processors for the patches and "+ Convert<std::string>(peInstance_)+" processes for pe-instance(s)\n");
        }
        else
        {
                if(mpi_.fluidWorkersRank==0)
                        std::cout << "Domain MPI split in " << mpi_.dims[X] << "," << mpi_.dims[Y] << "," << mpi_.dims[Z] << " for " << mpi_.numprocsFluidWorkers << " Processors for the patches and " << peInstance_ << " processes for pe-instance(s)" << std::endl;
                LOG_INFO2("Domain.cpp::PlaceAllocatePatches:Domain MPI split in "+Convert<std::string>(mpi_.dims[X]) + "," + Convert<std::string>(mpi_.dims[Y]) + "," + Convert<std::string>(mpi_.dims[Z]) + " for " + Convert<std::string>(mpi_.numprocsFluidWorkers) + " Processors for the patchesand "+ Convert<std::string>(peInstance_)+" processes for pe-instance(s)\n");
        }

        //Min and max values where the domain local patches begin and end
        Uint xMin=sim_.domainX+1, yMin=sim_.domainY+1, zMin=sim_.domainZ+1;
        Uint xMax=0.0, yMax=0.0, zMax=0.0;

        if(patchField_.autoPlace_)
        {
                //LOG_ERROR("GES NUM PATCHES "+STR(patchField_.GetGesNumUsedPatches())+"\n");
                double patchesPerProcess=1.0*patchField_.GetGesNumUsedPatches()/(mpi_.numprocsFluidWorkers);
                int patchNum=0;

                if(mpi_.fluidWorkersRank==0) std::cout << "Each patch process has " << patchesPerProcess << " patches" << std::endl;

#        ifdef FAST_PE
                if(patchesPerProcess>1)
                        THROW_LOG_ERROR("In case of the fast physics engine and autoplace, only one patch per process is supported at the moment! (UsedPatches: "+STR(patchField_.GetGesNumUsedPatches())+", processes: "+STR(mpi_.numprocsFluidWorkers)+")");
#        endif

                //Create all patches
                for(Uint z=0;z<sim_.zNumPatches;++z)
                {
                        for(Uint y=0;y<sim_.yNumPatches;++y)
                        {
                                for(Uint x=0;x<sim_.xNumPatches;++x)
                                {
                                        //Start with the first process, which treats patches. NOTE: pe processes dont treat patches
                                        int numProc=static_cast<Uint>(patchNum/patchesPerProcess);
                                        patchField_.GetWorldRank(x,y,z)=numProc;
                                        patchField_.GetFluidRank(x,y,z)=numProc;
                                        if(patchField_.IsNeeded(x,y,z))
                                        {
                                                patchNum++;

                                                if(numProc==mpi_.fluidWorkersRank)
                                                {
                                                        //Allocate all LBM fileds and create patch
                                                        patchField_.AllocPatch(x+1,y+1,z+1,sim_);

                                                        xMin=patchField_.GetPatch(x,y,z)->GetXStart()+1;
                                                        yMin=patchField_.GetPatch(x,y,z)->GetYStart()+1;
                                                        zMin=patchField_.GetPatch(x,y,z)->GetZStart()+1;
                                                        xMax=patchField_.GetPatch(x,y,z)->GetXEnd()+1;
                                                        yMax=patchField_.GetPatch(x,y,z)->GetYEnd()+1;
                                                        zMax=patchField_.GetPatch(x,y,z)->GetZEnd()+1;
                                                        //std::cout << "On process " << numProc << ":MinXYZ, MaxXYZ: " << xMin << "," << yMin << "," << zMin << "," << xMax << "," << yMax << "," << zMax << std::endl;
                                                        LOG_INFO2("Domain.cpp::PlaceAllocatePatches:Patch " + Convert<std::string>(patchField_.GetPatchNumber(x,y,z)) + " has " +  Convert<std::string>(patchField_.GetNumFluidCells(x,y,z)) + " fluid cells and will be processed\n");

                                                        LOG_INFO2("Domain.cpp::PlaceAllocatePatches:Adding patch ("+Convert<std::string>(x)+","+Convert<std::string>(y)+","+Convert<std::string>(z)+") to process "+Convert<std::string>(mpi_.worldRank)+"\n");

                                                }
                                                else
                                                {
                                                        LOG_INFO2("Domain.cpp::PlaceAllocatePatches:Patch: " +
                                                                  Convert<std::string>(patchField_.GetPatchNumber(x,y,z))+" is not allocated\n");
                                                }
                                        }
                                }
                        }
                }
        }
        else
        {
                //Get coordinates of all processes
                for(int numProc=0;numProc<mpi_.numprocsFluidWorkers;++numProc)
                {
                        MPI_Cart_coords(mpi_.comm_cart, numProc, mpi_.ndims, mpi_.coord);

                        //if(mpi_.fluidWorkersRank==0)std::cout << "Coordinates of processor " << numProc << " are " << mpi_.coord[X] << "," << mpi_.coord[Y] << "," << mpi_.coord[Z] << std::endl;
                        MPE_Decomp1d(sim_.xNumPatches,mpi_.dims[X],mpi_.coord[X],&mpi_.XStartPatchIndex,&mpi_.XEndPatchIndex);
                        MPE_Decomp1d(sim_.yNumPatches,mpi_.dims[Y],mpi_.coord[Y],&mpi_.YStartPatchIndex,&mpi_.YEndPatchIndex);
                        MPE_Decomp1d(sim_.zNumPatches,mpi_.dims[Z],mpi_.coord[Z],&mpi_.ZStartPatchIndex,&mpi_.ZEndPatchIndex);


                        if(mpi_.fluidWorkersRank==0)  std::cout << "NumProc startXYZ endXYZ "<< numProc <<" "
                                << mpi_.XStartPatchIndex<<" "<<mpi_.XEndPatchIndex<<" "
                                << mpi_.YStartPatchIndex<<" "<<mpi_.YEndPatchIndex<<" "
                                << mpi_.ZStartPatchIndex<<" "<<mpi_.ZEndPatchIndex<<" "<< std::endl;

                        //Be aware -> MPE_Decomp1d starts at 1
                        mpi_.XStartPatchIndex-=1;
                        mpi_.YStartPatchIndex-=1;
                        mpi_.ZStartPatchIndex-=1;
                        mpi_.XEndPatchIndex-=1;
                        mpi_.YEndPatchIndex-=1;
                        mpi_.ZEndPatchIndex-=1;

                        //Create all patches
                        for(Uint z= mpi_.ZStartPatchIndex;z<= (Uint)mpi_.ZEndPatchIndex;++z)
                        {
                                for(Uint y= mpi_.YStartPatchIndex;y<= (Uint)mpi_.YEndPatchIndex;++y)
                                {
                                        for(Uint x= mpi_.XStartPatchIndex;x<= (Uint)mpi_.XEndPatchIndex;++x)
                                        {
                                                patchField_.GetWorldRank(x,y,z)=numProc;
                                                patchField_.GetFluidRank(x,y,z)=numProc;

                                                if(numProc==mpi_.fluidWorkersRank && patchField_.IsNeeded(x,y,z))
                                                {

                                                        //Allocate all LBM fileds and create patch
                                                        patchField_.AllocPatch(x+1,y+1,z+1,sim_);

                                                        if(mpi_.fluidWorkersRank==0) std::cout << "Adding patch (" << x << "," << y << "," << z << ") to process " << mpi_.worldRank << std::endl;

                                                        LOG_INFO2("Domain.cpp::PlaceAllocatePatches:Patch " +
                                                                  Convert<std::string>(patchField_.GetPatchNumber(x,y,z)) + " has " +  Convert<std::string>(patchField_.GetNumFluidCells(x,y,z)) + " fluid cells and will be processed\n");

                                                        LOG_INFO2("Domain.cpp::PlaceAllocatePatches:Adding patch ("+Convert<std::string>(x)+","+Convert<std::string>(y)+","+Convert<std::string>(z)+") to process "+Convert<std::string>(mpi_.worldRank)+"\n");

                                                }
                                                else
                                                {
                                                        LOG_INFO2("Domain.cpp::PlaceAllocatePatches:Patch: " + Convert<std::string>(patchField_.GetPatchNumber(x,y,z))+" is not allocated\n");
                                                }
                                        }
                                }
                        }

                        //Create the DOMAIN neighborhood
                        //First calculate the min and max values
                        if(numProc==mpi_.fluidWorkersRank)
                        {
                                for(int z= mpi_.ZStartPatchIndex;z<= mpi_.ZEndPatchIndex;++z)
                                {
                                        for(int y= mpi_.YStartPatchIndex;y<= mpi_.YEndPatchIndex;++y)
                                        {
                                                for(int x= mpi_.XStartPatchIndex;x<= mpi_.XEndPatchIndex;++x)
                                                {
                                                        //Min
                                                        if(patchField_.GetPatch(x,y,z)->GetXStart()+1 < xMin)
                                                                xMin=patchField_.GetPatch(x,y,z)->GetXStart()+1;
                                                        if(patchField_.GetPatch(x,y,z)->GetYStart()+1 < yMin)
                                                                yMin=patchField_.GetPatch(x,y,z)->GetYStart()+1;
                                                        if(patchField_.GetPatch(x,y,z)->GetZStart()+1 < zMin)
                                                                zMin=patchField_.GetPatch(x,y,z)->GetZStart()+1;
                                                        //Max
                                                        if(patchField_.GetPatch(x,y,z)->GetXEnd()+1 > xMax)
                                                                xMax=patchField_.GetPatch(x,y,z)->GetXEnd()+1;
                                                        if(patchField_.GetPatch(x,y,z)->GetYEnd()+1 > yMax)
                                                                yMax=patchField_.GetPatch(x,y,z)->GetYEnd()+1;
                                                        if(patchField_.GetPatch(x,y,z)->GetZEnd()+1 > zMax)
                                                                zMax=patchField_.GetPatch(x,y,z)->GetZEnd()+1;
                                                }
                                        }
                                }
                                //std::cout << "On process " << numProc << ":MinXYZ, MaxXYZ: " << xMin << "," << yMin << "," << zMin << "," << xMax << "," << yMax << "," << zMax << std::endl;
                        }
                }
        }


#     ifdef FAST_PE
        pe::theMPISystem()->setComm(mpi_.comm_cart);
        double sqrt_2=sqrt(2.0);
        double sqrt_3=sqrt(3.0);
        //std::cout << "sqrt(2.0)=" << sqrt_2 << std::endl;

        //Now check the neighbors
        //TESTA
        //East
        if(xMax+1<sim_.domainX)
        {
                PatchCoordinate pc = GetOneLocalExistingPatchCoordinate(xMax+1,yMin,zMin,sim_);
                pe::Vec3 norm(1.0, 0.0, 0.0);
                pe::Vec3 point(xMax,yMin-1,zMin-1);

                //std::cout << "On process " << mpi_.fluidWorkersRank <<":In East direction we have neighbor " << patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) << std::endl;
                pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,norm, point, 1.0 );
        }

        //South
        if(yMin>1)
        {
                PatchCoordinate pc = GetOneLocalExistingPatchCoordinate(xMax,yMin-1,zMin,sim_);
                pe::Vec3 norm(0.0, -1.0, 0.0);
                pe::Vec3 point(xMax,yMin-1,zMin-1);

                //std::cout << "On process " << mpi_.fluidWorkersRank <<":In South direction we have neighbor " << patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) << std::endl;
                pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,norm, point, 1.0 );

        }
        //Bottom
        if(zMin>1)
        {
                PatchCoordinate pc = GetOneLocalExistingPatchCoordinate(xMax,yMin,zMin-1,sim_);
                pe::Vec3 norm(0.0, 0.0, -1.0);
                pe::Vec3 point(xMax,yMin-1,zMin-1);

                //std::cout << "On process " << mpi_.fluidWorkersRank <<":In Bottom direction we have neighbor " << patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) << std::endl;
                pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,norm, point, 1.0 );

        }
        //South-East OK
        if(xMax+1<sim_.domainX && yMin>1)
        {
                PatchCoordinate pc = GetOneLocalExistingPatchCoordinate(xMax+1,yMin-1,zMin,sim_);
                //pe::Vec3 norm(1.0, -1.0, 0.0);
                pe::Vec3 norm1(1.0, 0.0, 0.0);
                pe::Vec3 norm2(0.0, -1.0, 0.0);

                pe::Vec3 point(xMax,yMin-1,zMin-1);

                //std::cout << "On process " << mpi_.fluidWorkersRank <<":In South-East direction we have neighbor " << patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) << std::endl;
                //pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,norm, point, sqrt_2 );
                pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) , pe::intersect(pe::HalfSpace(norm1, point, sqrt_2 ),pe::HalfSpace(norm2, point, sqrt_2 )));
        }

        //Bottom-East OK
        if(xMax+1<sim_.domainX && zMin>1)
        {
                PatchCoordinate pc = GetOneLocalExistingPatchCoordinate(xMax+1,yMin,zMin-1,sim_);
                //pe::Vec3 norm(1.0, 0.0, -1.0);
                pe::Vec3 norm1(1.0, 0.0, 0.0);
                pe::Vec3 norm2(0.0, 0.0, -1.0);
                pe::Vec3 point(xMax,yMin-1,zMin-1);

                //std::cout << "On process " << mpi_.fluidWorkersRank <<":In Bottom-East direction we have neighbor " << patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) << std::endl;
                //pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,norm, point, sqrt_2 );
                pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,pe::intersect(pe::HalfSpace(norm1, point, sqrt_2 ),pe::HalfSpace(norm2, point, sqrt_2 )));

        }
        //Bottom-South OK
        if(yMin>1 && zMin>1)
        {
                PatchCoordinate pc = GetOneLocalExistingPatchCoordinate(xMax,yMin-1,zMin-1,sim_);
                //pe::Vec3 norm(0.0, -1.0, -1.0);
                pe::Vec3 norm1(0.0, -1.0, 0.0);
                pe::Vec3 norm2(0.0, 0.0, -1.0);
                pe::Vec3 point(xMax,yMin-1,zMin-1);

                //std::cout << "On process " << mpi_.fluidWorkersRank <<":In Bottom-South direction we have neighbor " << patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) << std::endl;
                //pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,norm, point, sqrt_2 );
                pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,pe::intersect(pe::HalfSpace(norm1, point, sqrt_2 ),pe::HalfSpace(norm2, point, sqrt_2 )));
        }
        //Bottom-South-East OK
        if(xMax+1<sim_.domainX && yMin>1 && zMin>1)
        {
                PatchCoordinate pc = GetOneLocalExistingPatchCoordinate(xMax+1,yMin-1,zMin-1,sim_);
                //pe::Vec3 norm(1.0, -1.0, -1.0);
                pe::Vec3 norm1(0.0, 0.0, -1.0);
                pe::Vec3 norm2(0.0, -1.0, 0.0);
                pe::Vec3 norm3(1.0, 0.0, 0.0);
                pe::Vec3 point(xMax,yMin-1,zMin-1);

                //std::cout << "On process " << mpi_.fluidWorkersRank <<":In Bottom-South-East direction we have neighbor " << patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) << std::endl;
                //pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,norm, point , sqrt_3);
                pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,pe::intersect(pe::HalfSpace(norm1, point, sqrt_3 ),pe::HalfSpace(norm2, point, sqrt_3 ),pe::HalfSpace(norm3, point, sqrt_3 )));
        }
        //TESTB
        //West
        if(xMin>1)
        {
                PatchCoordinate pc = GetOneLocalExistingPatchCoordinate(xMin-1,yMin,zMin,sim_);
                pe::Vec3 norm(-1.0, 0.0, 0.0);
                //pe::Vec3 point(xMin-1,0.0,0.0);
                pe::Vec3 point(xMin-1,yMin-1,zMin-1);

                //std::cout << "On process " << mpi_.fluidWorkersRank <<":In West direction we have neighbor " << patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) << std::endl;
                pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,norm, point, 1.0 );

                //pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) , -1.0, 0.0, 0.0, xMin-1 );
                //std::cout << "On process " << mpi_.fluidWorkersRank <<":In West direction we have neighbor " << patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) << std::endl;
        }
        //South-West OK
        if(xMin>1 && yMin>1)
        {
                PatchCoordinate pc = GetOneLocalExistingPatchCoordinate(xMin-1,yMin-1,zMin,sim_);
                //pe::Vec3 norm(-1.0, -1.0, 0.0);
                pe::Vec3 norm1(0.0, -1.0, 0.0);
                pe::Vec3 norm2(-1.0, 0.0, 0.0);
                pe::Vec3 point(xMin-1,yMin-1,zMin-1);

                //std::cout << "On process " << mpi_.fluidWorkersRank <<":In South-West direction we have neighbor " << patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) << std::endl;
                //pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,norm, point, sqrt_2 );
                pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,pe::intersect(pe::HalfSpace(norm1, point, sqrt_2 ),pe::HalfSpace(norm2, point, sqrt_2 )));
        }
        //Bottom-West OK
        if(xMin>1 && zMin>1)
        {
                PatchCoordinate pc = GetOneLocalExistingPatchCoordinate(xMin-1,yMin,zMin-1,sim_);
                //pe::Vec3 norm(-1.0, 0.0, -1.0);
                pe::Vec3 norm1(-1.0, 0.0, 0.0);
                pe::Vec3 norm2(0.0, 0.0, -1.0);
                pe::Vec3 point(xMin-1,yMin-1,zMin-1);

                //std::cout << "On process " << mpi_.fluidWorkersRank <<":In Bottom-West direction we have neighbor " << patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) << std::endl;
                //pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,norm, point, sqrt_2 );
                pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,pe::intersect(pe::HalfSpace(norm1, point, sqrt_2 ),pe::HalfSpace(norm2, point, sqrt_2 )));
        }
        //Bottom-South-West OK
        if(xMin>1 && yMin>1 && zMin>1)
        {
                PatchCoordinate pc = GetOneLocalExistingPatchCoordinate(xMin-1,yMin-1,zMin-1,sim_);
                //pe::Vec3 norm(-1.0, -1.0, -1.0);
                pe::Vec3 norm1(0.0, 0.0, -1.0);
                pe::Vec3 norm2(0.0, -1.0, 0.0);
                pe::Vec3 norm3(-1.0, 0.0, 0.0);
                pe::Vec3 point(xMin-1,yMin-1,zMin-1);

                //std::cout << "On process " << mpi_.fluidWorkersRank <<":In Bottom-South-West direction we have neighbor " << patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) << std::endl;
                //pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,norm, point, sqrt_3 );
                pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,pe::intersect(pe::HalfSpace(norm1, point, sqrt_3 ),pe::HalfSpace(norm2, point, sqrt_3 ),pe::HalfSpace(norm3, point, sqrt_3 )));
        }
        //TestC
        //Top
        if(zMax+1<sim_.domainZ)
        {
                PatchCoordinate pc = GetOneLocalExistingPatchCoordinate(xMax,yMax,zMax+1,sim_);
                pe::Vec3 norm(0.0, 0.0, 1.0);
                //pe::Vec3 point(0.0,0.0,zMax);
                pe::Vec3 point(xMax,yMax,zMax);

                //std::cout << "On process " << mpi_.fluidWorkersRank <<":In Top direction we have neighbor " << patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) << std::endl;
                pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,norm, point, 1.0 );

                //            pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) , 0.0, 0.0, 1.0, zMax  );
                //            std::cout << "On process " << mpi_.fluidWorkersRank <<":In Top direction we have neighbor " << patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) << std::endl;
        }
        //North
        if(yMax+1<sim_.domainY)
        {
                PatchCoordinate pc = GetOneLocalExistingPatchCoordinate(xMax,yMax+1,zMax,sim_);
                pe::Vec3 norm(0.0, 1.0, 0.0);
                //pe::Vec3 point(0.0,yMax,0.0);
                pe::Vec3 point(xMax,yMax,zMax);

                //std::cout << "On process " << mpi_.fluidWorkersRank <<":In North direction we have neighbor " << patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) << std::endl;
                pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,norm, point, 1.0 );

                //            pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) , 0.0, 1.0, 0.0, yMax  );
                //            std::cout << "On process " << mpi_.fluidWorkersRank <<":In North direction we have neighbor " << patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) << std::endl;
        }

        //Top-North OK
        if(yMax+1<sim_.domainY && zMax+1<sim_.domainZ)
        {
                PatchCoordinate pc = GetOneLocalExistingPatchCoordinate(xMax,yMax+1,zMax+1,sim_);
                //pe::Vec3 norm(0.0, 1.0, 1.0);
                pe::Vec3 norm1(0.0, 0.0, 1.0);
                pe::Vec3 norm2(0.0, 1.0, 0.0);
                pe::Vec3 point(xMax,yMax,zMax);

                //std::cout << "On process " << mpi_.fluidWorkersRank <<":In Top-North direction we have neighbor " << patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) << std::endl;
                //pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,norm, point, sqrt_2 );
                pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,pe::intersect(pe::HalfSpace(norm1, point, sqrt_2 ),pe::HalfSpace(norm2, point, sqrt_2 )));
        }
        //North-East OK
        if(yMax+1<sim_.domainY && xMax+1<sim_.domainX)
        {
                PatchCoordinate pc = GetOneLocalExistingPatchCoordinate(xMax+1,yMax+1,zMax,sim_);
                //pe::Vec3 norm(1.0, 1.0, 0.0);
                pe::Vec3 norm1(0.0, 1.0, 0.0);
                pe::Vec3 norm2(1.0, 0.0, 0.0);
                pe::Vec3 point(xMax,yMax,zMax);

                //std::cout << "On process " << mpi_.fluidWorkersRank <<":In North-East direction we have neighbor " << patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) << std::endl;
                //pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,norm, point, sqrt_2 );
                pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,pe::intersect(pe::HalfSpace(norm1, point, sqrt_2 ),pe::HalfSpace(norm2, point, sqrt_2 )));
        }
        //Top-North-East OK
        if(yMax+1<sim_.domainY && xMax+1<sim_.domainX && zMax+1<sim_.domainZ)
        {
                PatchCoordinate pc = GetOneLocalExistingPatchCoordinate(xMax+1,yMax+1,zMax+1,sim_);
                //pe::Vec3 norm(1.0, 1.0, 1.0);
                pe::Vec3 norm1(0.0, 0.0, 1.0);
                pe::Vec3 norm2(0.0, 1.0, 0.0);
                pe::Vec3 norm3(1.0, 0.0, 0.0);
                pe::Vec3 point(xMax,yMax,zMax);

                //std::cout << "On process " << mpi_.fluidWorkersRank <<":In Top-North-East direction we have neighbor " << patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) << std::endl;
                //pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,norm, point, sqrt_3 );
                pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,pe::intersect(pe::HalfSpace(norm1, point, sqrt_3 ),pe::HalfSpace(norm2, point, sqrt_3 ),pe::HalfSpace(norm3, point, sqrt_3 )));
        }
        //Top-East OK
        if(xMax+1<sim_.domainX && zMax+1<sim_.domainZ)
        {
                PatchCoordinate pc = GetOneLocalExistingPatchCoordinate(xMax+1,yMax,zMax+1,sim_);
                //pe::Vec3 norm(1.0, 0.0, 1.0);
                pe::Vec3 norm1(0.0, 0.0, 1.0);
                pe::Vec3 norm2(1.0, 0.0, 0.0);
                pe::Vec3 point(xMax,yMax,zMax);

                //std::cout << "On process " << mpi_.fluidWorkersRank <<":In Top-East direction we have neighbor " << patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) << std::endl;
                //pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,norm, point, sqrt_2 );
                pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,pe::intersect(pe::HalfSpace(norm1, point, sqrt_2 ),pe::HalfSpace(norm2, point, sqrt_2 )));
        }
        //TestD
        //Top-West OK
        if(xMin>1 && zMax+1<sim_.domainZ)
        {
                PatchCoordinate pc = GetOneLocalExistingPatchCoordinate(xMin-1,yMax,zMax+1,sim_);
                //pe::Vec3 norm(-1.0, 0.0, 1.0);
                pe::Vec3 norm1(0.0, 0.0, 1.0);
                pe::Vec3 norm2(-1.0, 0.0, 0.0);
                pe::Vec3 point(xMin-1,yMax,zMax);

                //std::cout << "On process " << mpi_.fluidWorkersRank <<":In Top-West direction we have neighbor " << patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) << std::endl;
                //pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,norm, point, sqrt_2 );
                pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,pe::intersect(pe::HalfSpace(norm1, point, sqrt_2 ),pe::HalfSpace(norm2, point, sqrt_2 )));
        }
        //Top-North-West OK
        if(xMin>1 && yMax+1<sim_.domainY && zMax+1<sim_.domainZ)
        {
                PatchCoordinate pc = GetOneLocalExistingPatchCoordinate(xMin-1,yMax+1,zMax+1,sim_);
                //pe::Vec3 norm(-1.0, 1.0, 1.0);
                pe::Vec3 norm1(0.0, 0.0, 1.0);
                pe::Vec3 norm2(0.0, 1.0, 0.0);
                pe::Vec3 norm3(-1.0, 0.0, 0.0);
                pe::Vec3 point(xMin-1,yMax,zMax);

                //std::cout << "On process " << mpi_.fluidWorkersRank <<":In Top-North-West direction we have neighbor " << patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) << std::endl;
                //pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,norm, point, sqrt_3 );
                pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,pe::intersect(pe::HalfSpace(norm1, point, sqrt_3 ),pe::HalfSpace(norm2, point, sqrt_3 ),pe::HalfSpace(norm3, point, sqrt_3 )));
        }
        //North-West OK
        if(xMin>1 && yMax+1<sim_.domainY)
        {
                PatchCoordinate pc = GetOneLocalExistingPatchCoordinate(xMin-1,yMax+1,zMax,sim_);
                //pe::Vec3 norm(-1.0, 1.0, 0.0);
                pe::Vec3 norm1(0.0, 1.0, 0.0);
                pe::Vec3 norm2(-1.0, 0.0, 0.0);
                pe::Vec3 point(xMin-1,yMax,zMax);

                //std::cout << "On process " << mpi_.fluidWorkersRank <<":In North-West direction we have neighbor " << patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) << std::endl;
                //pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,norm, point, sqrt_2 );
                pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,pe::intersect(pe::HalfSpace(norm1, point, sqrt_2 ),pe::HalfSpace(norm2, point, sqrt_2 )));
        }
        //TestE
        //Bottom-North OK
        if(yMax+1<sim_.domainY && zMin>1)
        {
                PatchCoordinate pc = GetOneLocalExistingPatchCoordinate(xMax,yMax+1,zMin-1,sim_);
                //pe::Vec3 norm(0.0, 1.0, -1.0);
                pe::Vec3 norm1(0.0, 0.0, -1.0);
                pe::Vec3 norm2(0.0, 1.0, 0.0);
                pe::Vec3 point(xMax,yMax,zMin-1);

                //std::cout << "On process " << mpi_.fluidWorkersRank <<":In Bottom-North direction we have neighbor " << patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) << std::endl;
                //pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,norm, point, sqrt_2 );
                pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,pe::intersect(pe::HalfSpace(norm1, point, sqrt_2 ),pe::HalfSpace(norm2, point, sqrt_2 )));
        }
        //Bottom-North-East OK
        if(xMax+1<sim_.domainX && yMax+1<sim_.domainY && zMin>1)
        {
                PatchCoordinate pc = GetOneLocalExistingPatchCoordinate(xMax+1,yMax+1,zMin-1,sim_);
                //pe::Vec3 norm(1.0, 1.0, -1.0);
                pe::Vec3 norm1(0.0, 0.0, -1.0);
                pe::Vec3 norm2(0.0, 1.0, 0.0);
                pe::Vec3 norm3(1.0, 0.0, 0.0);
                pe::Vec3 point(xMax,yMax,zMin-1);

                //std::cout << "On process " << mpi_.fluidWorkersRank <<":In Bottom-North-East direction we have neighbor " << patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) << std::endl;
                //pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,norm, point, sqrt_3 );
                pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,pe::intersect(pe::HalfSpace(norm1, point, sqrt_3 ),pe::HalfSpace(norm2, point, sqrt_3 ),pe::HalfSpace(norm3, point, sqrt_3 )));
        }
        //TestF
        //Top-South OK
        if(zMax+1<sim_.domainZ && yMin>1)
        {
                PatchCoordinate pc = GetOneLocalExistingPatchCoordinate(xMin,yMin-1,zMax+1,sim_);
                //pe::Vec3 norm(0.0, -1.0, 1.0);
                pe::Vec3 norm1(0.0, 0.0, 1.0);
                pe::Vec3 norm2(0.0, -1.0, 0.0);
                pe::Vec3 point(xMin-1,yMin-1,zMax);

                //std::cout << "On process " << mpi_.fluidWorkersRank <<":In Top-South direction we have neighbor " << patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) << std::endl;
                //pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,norm, point, sqrt_2 );
                pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,pe::intersect(pe::HalfSpace(norm1, point, sqrt_2 ),pe::HalfSpace(norm2, point, sqrt_2 )));
        }
        //Top-South-West OK
        if(zMax+1<sim_.domainZ && yMin>1 && xMin>1)
        {
                PatchCoordinate pc = GetOneLocalExistingPatchCoordinate(xMin-1,yMin-1,zMax+1,sim_);
                //pe::Vec3 norm(-1.0, -1.0, 1.0);
                pe::Vec3 norm1(0.0, 0.0, 1.0);
                pe::Vec3 norm2(0.0, -1.0, 0.0);
                pe::Vec3 norm3(-1.0, 0.0, 0.0);
                pe::Vec3 point(xMin-1,yMin-1,zMax);

                //std::cout << "On process " << mpi_.fluidWorkersRank <<":In Top-South-West direction we have neighbor " << patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) << std::endl;
                //pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,norm, point, sqrt_3 );
                pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,pe::intersect(pe::HalfSpace(norm1, point, sqrt_3 ),pe::HalfSpace(norm2, point, sqrt_3 ),pe::HalfSpace(norm3, point, sqrt_3 )));
        }
        //TestG
        //Bottom-North-West OK
        if(yMax+1<sim_.domainY && xMin>1 && zMin>1)
        {
                PatchCoordinate pc = GetOneLocalExistingPatchCoordinate(xMin-1,yMax+1,zMin-1,sim_);
                //pe::Vec3 norm(-1.0, 1.0, -1.0);
                pe::Vec3 norm1(0.0, 0.0, -1.0);
                pe::Vec3 norm2(0.0, 1.0, 0.0);
                pe::Vec3 norm3(-1.0, 0.0, 0.0);
                pe::Vec3 point(xMin-1,yMax,zMin-1);

                //std::cout << "On process " << mpi_.fluidWorkersRank <<":In Bottom-North-West direction we have neighbor " << patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) << std::endl;
                //pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,norm, point, sqrt_3 );
                pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,pe::intersect(pe::HalfSpace(norm1, point, sqrt_3 ),pe::HalfSpace(norm2, point, sqrt_3 ),pe::HalfSpace(norm3, point, sqrt_3 )));
        }
        //TestH
        //Top-South-East OK
        if(xMax+1<sim_.domainX &&  zMax+1<sim_.domainZ && yMin>1)
        {
                PatchCoordinate pc = GetOneLocalExistingPatchCoordinate(xMax+1,yMin-1,zMax+1,sim_);
                //pe::Vec3 norm(1.0, -1.0, 1.0);
                pe::Vec3 norm1(0.0, 0.0, 1.0);
                pe::Vec3 norm2(0.0, -1.0, 0.0);
                pe::Vec3 norm3(1.0, 0.0, 0.0);
                pe::Vec3 point(xMax,yMin-1,zMax);

                //std::cout << "On process " << mpi_.fluidWorkersRank <<":In Top-South-East direction we have neighbor " << patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) << std::endl;
                //pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,norm, point, sqrt_3 );
                pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,pe::intersect(pe::HalfSpace(norm1, point, sqrt_3 ),pe::HalfSpace(norm2, point, sqrt_3 ),pe::HalfSpace(norm3, point, sqrt_3 )));
        }

        if(movingObstacles_.useZPeriodic_)
        {
                if(sim_.xNumPatches!=1 || sim_.yNumPatches!=1)
                {
                        throw std::runtime_error( "Domain.cpp::PlaceAllocatePatches: For using z-Periodic only one patch in x and y direction allowed!" );
                }
                //Bottom
                if(zMin==1)
                {
                        PatchCoordinate pc = GetOneLocalExistingPatchCoordinate(xMax,yMin,sim_.domainZ-1,sim_);
                        pe::Vec3 norm(0.0, 0.0, -1.0);
                        pe::Vec3 point(xMax,yMin-1,0);

                        std::cout << "On process " << mpi_.fluidWorkersRank <<":In Bottom direction we have neighbor " << patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) << " with norm " << norm << " and point " << point << std::endl;
                        pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,norm, point, 1.0 );

                }
                //Top
                if((zMax+1)>=sim_.domainZ)
                {
                        PatchCoordinate pc = GetOneLocalExistingPatchCoordinate(xMax,yMax,0,sim_);
                        pe::Vec3 norm(0.0, 0.0, 1.0);
                        //pe::Vec3 point(0.0,0.0,zMax);
                        pe::Vec3 point(xMax,yMax,zMax);

                        std::cout << "On process " << mpi_.fluidWorkersRank <<":In Top direction we have neighbor " << patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) << " with norm " << norm << " and point " << point << std::endl;
                        pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) ,norm, point, 1.0 );

                        //            pe::connect(patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) , 0.0, 0.0, 1.0, zMax  );
                        //            std::cout << "On process " << mpi_.fluidWorkersRank <<":In Top direction we have neighbor " << patchField_.GetWorldRank(pc.pX,pc.pY,pc.pZ) << std::endl;
                }
        }


        //      //Check the MPI system for PE
        //      pe::MPISystemID mpiSystem = pe::theMPISystem();
        //      try{
        //         mpiSystem->checkProcesses();
        //      }
        //      catch (std::runtime_error e) {
        //         LOG_ERROR("Domain.h::Problem with MPI communication for PE:\n"+STR(+e.what())+"\n");
        //         MPI_Abort(mpi_.comm_cart,-1);
        //      }


        //   {
        //   pe::MPISystemID mpisystem=pe::theMPISystem();
        //   for(pe::MPISystem::Iterator it=mpisystem->begin();it!=mpisystem->end();++it)
        //   {
        //      std::cout << *it << std::endl;
        //   }
        //   }


#        endif


        if(mpi_.fluidWorkersRank==0)
        {
                std::cout << "Domain.cpp:PlaceAllocatePatches: Patches are aligned to processes. Distribution with processnumber follows" << std::endl;
                for(Uint z=0;z<sim_.zNumPatches;++z)
                {
                        for(Uint y=0;y<sim_.yNumPatches;++y)
                        {
                                for(Uint x=0;x<sim_.xNumPatches;++x)
                                {
                                        if(!patchField_.IsNeeded(x,y,z))
                                                std::cout << " x" << " ";
                                        else
                                        {
                                                if(patchField_.GetWorldRank(x,y,z)<10)
                                                        std::cout << " " << patchField_.GetWorldRank(x,y,z) << " ";
                                                else
                                                        std::cout << patchField_.GetWorldRank(x,y,z) << " ";
                                        }
                                }
                                cout<<"\n";
                        }
                        cout<<"\n";
                }
        }
#endif

#ifndef NO_PE
#if LOGGING>=1
#ifdef WALBERLA_USE_MPI
        if(mpi_.fluidWorkersRank==0)
#endif
        {
                for(Uint z=0;z<sim_.zNumPatches;++z)  {
                        for(Uint y=0;y<sim_.yNumPatches;++y)  {
                                for(Uint x=0;x<sim_.xNumPatches;++x) {
                                        std::cout<<"+ --------------------------- +";
                                }
                                std::cout<<std::endl;
                                for(Uint x=0;x<sim_.xNumPatches;++x) {
                                        PatchStruct& pStruct = patchField_(x,y,z);
                                        pe::RigidBody::AABB& aabb = pStruct.GetAABB();
                                        std::cout<<"| "<<setw(3)<<aabb[0]<<","<<setw(3)<<aabb[1]<<","<<setw(3)<<aabb[2]<<" --- "<<setw(3)<<aabb[3]<<","<<setw(3)<<aabb[1]<<","<<setw(3)<<aabb[2]<<" |";
                                }
                                std::cout<<std::endl;
                                for(Uint x=0;x<sim_.xNumPatches;++x) {
                                        std::cout<<"|				 |";
                                }
                                std::cout<<std::endl;
                                for(Uint x=0;x<sim_.xNumPatches;++x) {
                                        PatchStruct& pStruct = patchField_(x,y,z);
                                        pe::RigidBody::AABB& aabb = pStruct.GetAABB();
                                        std::cout<<"| "<<setw(3)<<aabb[0]<<","<<setw(3)<<aabb[4]<<","<<setw(3)<<aabb[2]<<" --- "<<setw(3)<<aabb[3]<<","<<setw(3)<<aabb[4]<<","<<setw(3)<<aabb[5]<<" |";
                                }
                                std::cout<<std::endl;
                                for(Uint x=0;x<sim_.xNumPatches;++x) {
                                        std::cout<<"+ --------------------------- +";
                                }
                                std::cout<<std::endl;
                        }
                        for(Uint x=0;x<sim_.xNumPatches;++x) {
                                std::cout<<"###############################";
                        }
                        std::cout<<std::endl;
                }
        }
#endif
#endif
}

void Domain::InitNeighbourHoods(){
        LOG_INFO2("Domain.cpp:Initialize the neighborhood structures\n");

        //Initialize the neighbor structure of the patches
        PatchIter patchIt;

        for(patchIt=patchField_.GetBeginLocalPatches();patchIt!=patchField_.GetEndLocalPatches();++patchIt)
        {
                CalcPatchID patch=patchIt->GET_PATCH();
                Uint xPatch, yPatch, zPatch;

                xPatch=patch->GetPatchCoordinate()[X];
                yPatch=patch->GetPatchCoordinate()[Y];
                zPatch=patch->GetPatchCoordinate()[Z];

                //std::cout << "Patch coordinates:" << xPatch << "," << yPatch << "," << zPatch << std::endl;

                IPatch::neighbor nb;

                IPatch::startEnd sourceInterval; //Where to get the data of the CalcPatch
                IPatch::startEnd targetInterval; //Where to put the data
                IPatch::startEnd calcPatchToComPatch; //Where to put the data via MPI

                for(int d=1;d<D3Q19::Cellsize;++d)
                {
#ifdef WALBERLA_USE_MPI
                        ComPatchID comPatch=NULL;
#endif

                        //Check all boundaries
                        //Check B
                        if(zPatch==0 && cz[d]==-1) //We look across boundary -> no neighbor
                                continue;
                        //Check T
                        if(zPatch==sim_.zNumPatches-1 && cz[d]==1) //We look across boundary -> no neighbor
                                continue;
                        //Check S
                        if(yPatch==0 && cy[d]==-1) //We look across boundary -> no neighbor
                                continue;
                        //Check N
                        if(yPatch==sim_.yNumPatches-1 && cy[d]==1) //We look across boundary -> no neighbor
                                continue;
                        //Check W
                        if(xPatch==0 && cx[d] ==-1) //We look across boundary -> no neighbor
                                continue;
                        //Check E
                        if(xPatch==sim_.xNumPatches-1 && cx[d]==1) //We look across boundary -> no neighbor
                                continue;
                        //Check if there is a patch
                        if(!patchField_.IsNeeded(xPatch+cx[d],yPatch+cy[d],zPatch+cz[d]))
                                continue;

                        //Set the pointer to myself
                        nb.myself=patchField_.GET_PATCH3(xPatch,yPatch,zPatch);

                        //Set pointer to neighbor, if neighbor is on same subdomain
                        if(patchField_.GetWorldRank(xPatch+cx[d],yPatch+cy[d],zPatch+cz[d])==patchField_.GetWorldRank(xPatch,yPatch,zPatch)){
                                nb.nbPatch=patchField_.GET_PATCH3(xPatch+cx[d],yPatch+cy[d],zPatch+cz[d]);
                                nb.targetProcessNumber=patchField_.GetWorldRank(xPatch,yPatch,zPatch);
                        }
                        else
                        {
#ifdef WALBERLA_USE_MPI
                                comPatch=new ComPatch();
                                comPatch->comm_cart_=mpi_.comm_cart;
                                // The comPatch has the same number as hast local calcPatch -> This is used for the identifier in MPI
                                comPatch->SetPatchNumber(patchField_.GetPatchNumber(xPatch,yPatch,zPatch));
                                nb.nbPatch=comPatch;

                                //Set the process number to communicate to
                                nb.targetProcessNumber=patchField_.GetWorldRank(xPatch+cx[d],yPatch+cy[d],zPatch+cz[d]);

                                //Set the patchnumber where we have to communicate to
                                nb.targetPatchNumber=patchField_.GetPatchNumber(xPatch+cx[d],yPatch+cy[d],zPatch+cz[d]);
#else
                                throw std::runtime_error( "Domain.cpp::InitNeighbourHoods: Something went wrong, you have different process numbers, but no MPI!" );
#endif

                        }

                        nb.direction=d;

                        //Initialize all sizes with max values
                        nb.xSize=patchField_.GetXSize(xPatch,yPatch,zPatch);
                        nb.ySize=patchField_.GetYSize(xPatch,yPatch,zPatch);
                        nb.zSize=patchField_.GetZSize(xPatch,yPatch,zPatch);

                        //Calc the buffer size and starting/end points
                        //Check E-W
                        nb.bufferSize=1;
                        if(cx[d]==0) //We look not direction E-W
                        {
                                //Could be edge or rectangle, so multiply with length
                                nb.bufferSize*=patchField_.GetXSize(xPatch,yPatch,zPatch)-2;
                                //Complete x range from source
                                sourceInterval.xStart=1;
                                sourceInterval.xEnd=patchField_.GetXSize(xPatch,yPatch,zPatch)-2;
                                calcPatchToComPatch.xStart=1;
                                calcPatchToComPatch.xEnd=patchField_.GetXSize(xPatch,yPatch,zPatch)-2;
                                //Complete x range from target
                                targetInterval.xStart=1;
                                targetInterval.xEnd=patchField_.GetXSize(xPatch+cx[d],yPatch+cy[d],zPatch+cz[d])-2;
                        }
                        else if(cx[d]==1) //We look direction E
                        {
                                sourceInterval.xStart=patchField_.GetXSize(xPatch,yPatch,zPatch)-2; //Source is last real layer
                                sourceInterval.xEnd=patchField_.GetXSize(xPatch,yPatch,zPatch)-2; //Source is last real layer
                                calcPatchToComPatch.xStart=patchField_.GetXSize(xPatch,yPatch,zPatch)-1; //ComPatch aim is last ghost layer
                                calcPatchToComPatch.xEnd=patchField_.GetXSize(xPatch,yPatch,zPatch)-1; //ComPatch aim is last ghost layer
                                targetInterval.xStart=0; //Target is first layer -> ghost
                                targetInterval.xEnd=0;   //Target is first layer -> ghost
                                nb.xSize=1;
                        }
                        else //We look direction W
                        {
                                sourceInterval.xStart=1; //Source is first real layer
                                sourceInterval.xEnd=1; //Source is first real layer
                                calcPatchToComPatch.xStart=0; //ComPatch aim is first ghost layer
                                calcPatchToComPatch.xEnd=0; //ComPatch aim is first ghost layer
                                targetInterval.xStart=patchField_.GetXSize(xPatch+cx[d],yPatch+cy[d],zPatch+cz[d])-1; //Target is last layer -> ghost of neighboring patch
                                targetInterval.xEnd=patchField_.GetXSize(xPatch+cx[d],yPatch+cy[d],zPatch+cz[d])-1;   //Target is last layer -> ghost of neighboring patch
                                nb.xSize=1;
                        }

                        //Check N-S
                        if(cy[d]==0) //We look not direction N-S
                        {
                                //Could be edge or rectangle, so multiply with length
                                nb.bufferSize*=patchField_.GetYSize(xPatch,yPatch,zPatch)-2;
                                //Complete y range from source
                                sourceInterval.yStart=1;
                                sourceInterval.yEnd=patchField_.GetYSize(xPatch,yPatch,zPatch)-2;
                                calcPatchToComPatch.yStart=1;
                                calcPatchToComPatch.yEnd=patchField_.GetYSize(xPatch,yPatch,zPatch)-2;
                                //Complete y range from target
                                targetInterval.yStart=1;
                                targetInterval.yEnd=patchField_.GetYSize(xPatch+cx[d],yPatch+cy[d],zPatch+cz[d])-2;
                        }
                        else if(cy[d]==1) //We look direction N
                        {
                                sourceInterval.yStart=patchField_.GetYSize(xPatch,yPatch,zPatch)-2; //Source is last real layer
                                sourceInterval.yEnd=patchField_.GetYSize(xPatch,yPatch,zPatch)-2; //Source is last real layer
                                calcPatchToComPatch.yStart=patchField_.GetYSize(xPatch,yPatch,zPatch)-1; //ComPatch aim is last ghost layer
                                calcPatchToComPatch.yEnd=patchField_.GetYSize(xPatch,yPatch,zPatch)-1; //ComPatch aim is last ghost layer
                                targetInterval.yStart=0; //Target is first layer -> ghost
                                targetInterval.yEnd=0;   //Target is first layer -> ghost
                                nb.ySize=1;
                        }
                        else //We look direction S
                        {
                                sourceInterval.yStart=1; //Source is first real layer
                                sourceInterval.yEnd=1; //Source is first real layer
                                calcPatchToComPatch.yStart=0; //ComPatch aim is first ghost layer
                                calcPatchToComPatch.yEnd=0; //ComPatch aim is first ghost layer
                                targetInterval.yStart=patchField_.GetYSize(xPatch+cx[d],yPatch+cy[d],zPatch+cz[d])-1; //Target is last layer -> ghost of neighboring patch
                                targetInterval.yEnd=patchField_.GetYSize(xPatch+cx[d],yPatch+cy[d],zPatch+cz[d])-1;   //Target is last layer -> ghost of neighboring patch
                                nb.ySize=1;
                        }
                        //Check T-B
                        if(cz[d]==0) //We look not direction T-B
                        {
                                //Could be edge or rectangle, so multiply with length
                                nb.bufferSize*=patchField_.GetZSize(xPatch,yPatch,zPatch)-2;
                                //Complete z range from source
                                sourceInterval.zStart=1;
                                sourceInterval.zEnd=patchField_.GetZSize(xPatch,yPatch,zPatch)-2;
                                calcPatchToComPatch.zStart=1;
                                calcPatchToComPatch.zEnd=patchField_.GetZSize(xPatch,yPatch,zPatch)-2;
                                //Complete x range from target
                                targetInterval.zStart=1;
                                targetInterval.zEnd=patchField_.GetZSize(xPatch+cx[d],yPatch+cy[d],zPatch+cz[d])-2;
                        }
                        else if(cz[d]==1) //We look direction T
                        {
                                sourceInterval.zStart=patchField_.GetZSize(xPatch,yPatch,zPatch)-2; //Source is last real layer
                                sourceInterval.zEnd=patchField_.GetZSize(xPatch,yPatch,zPatch)-2; //Source is last real layer
                                calcPatchToComPatch.zStart=patchField_.GetZSize(xPatch,yPatch,zPatch)-1; //ComPatch aim is last ghost layer
                                calcPatchToComPatch.zEnd=patchField_.GetZSize(xPatch,yPatch,zPatch)-1; //ComPatch aim is last ghost layer
                                targetInterval.zStart=0; //Target is first layer -> ghost
                                targetInterval.zEnd=0;   //Target is first layer -> ghost
                                nb.zSize=1;
                        }
                        else //We look direction B
                        {
                                sourceInterval.zStart=1; //Source is first real layer
                                sourceInterval.zEnd=1; //Source is first real layer
                                calcPatchToComPatch.zStart=0; //ComPatch aim is first ghost layer
                                calcPatchToComPatch.zEnd=0; //ComPatch aim is first ghost layer
                                targetInterval.zStart=patchField_.GetZSize(xPatch+cx[d],yPatch+cy[d],zPatch+cz[d])-1; //Target is last layer -> ghost of neighboring patch
                                targetInterval.zEnd=patchField_.GetZSize(xPatch+cx[d],yPatch+cy[d],zPatch+cz[d])-1;   //Target is last layer -> ghost of neighboring patch
                                nb.zSize=1;
                        }
                        //Save start and end points to neighbor struct
                        nb.sourceInterval=sourceInterval;
                        nb.targetInterval=targetInterval;

                        //Buffer stores 5 values, if E,W,S,N,T,B
                        //else 1 value so multiply with 5 or 1
                        if(d==D3Q19::E || d==D3Q19::W || d==D3Q19::N || d==D3Q19::S || d==D3Q19::T || d==D3Q19::B)
                                nb.bufferSize*=5; //E,W,S,N,T,B
                        else
                                nb.bufferSize*=1; //diagonal directions

                        //            //NOTE: For testing
                        //            nb.bufferSize*=19;

                        //Some checks
                        ASSERT(nb.sourceInterval.xEnd-nb.sourceInterval.xStart == nb.targetInterval.xEnd-nb.targetInterval.xStart,"Domain.cpp::Create:Source and target patch does not match in x direction\n");
                        ASSERT(nb.sourceInterval.yEnd-nb.sourceInterval.yStart == nb.targetInterval.yEnd-nb.targetInterval.yStart,"Domain.cpp::Create:Source and target patch does not match in y direction\n");
                        ASSERT(nb.sourceInterval.zEnd-nb.sourceInterval.zStart == nb.targetInterval.zEnd-nb.targetInterval.zStart,"Domain.cpp::Create:Source and target patch does not match in z direction\n");

                        //Allocate the buffer
                        nb.buffer=new Real[nb.bufferSize];
                        ASSERT(nb.buffer!=NULL,"Domain.cpp::Create:Out of memory while allocating buffer in CalcPatch\n");
#ifdef WALBERLA_USE_MPI
                        PatchStructID pStruct=patchIt->GetStruct();
                        //We have a ComPatch as neighbor
                        if(comPatch!=NULL)
                        {
                                IPatch::neighbor nbCalcPatch;

                                //The sizes are the same as in the CalcPatch
                                nbCalcPatch.bufferSize=nb.bufferSize;
                                nbCalcPatch.buffer=new Real[nbCalcPatch.bufferSize];
                                ASSERT(nbCalcPatch.buffer!=NULL,"Domain.cpp::Create:Out of memory while allocating buffer in ComPatch\n");
                                nbCalcPatch.xSize=nb.xSize;
                                nbCalcPatch.ySize=nb.ySize;
                                nbCalcPatch.zSize=nb.zSize;
                                //The ComPatch needs also start and end points to write it back to the fields via Send in CalcPatch
                                nbCalcPatch.targetInterval=calcPatchToComPatch;

                                //Set direction of Calc
                                nbCalcPatch.direction=D3Q19::finv[d];

                                nbCalcPatch.targetProcessNumber=pStruct->GetWorldRank();

                                //Save the pointer to the CalcPatch
                                nbCalcPatch.nbPatch=patch;

                                comPatch->GetNeighbors()->push_back(nbCalcPatch);
                                // LOG_INFO2("Domain.cpp::Create:In patch (x,y,z):" +Convert<std::string>(xPatch)+","+Convert<std::string>(yPatch)+","+Convert<std::string>(zPatch)+":Added neighbor in ComPatch:" +nbCalcPatch.ToString()+"\n");

                                //comPatch->SetBufferSize(nb.bufferSize);
                                comPatches_.push_back(comPatch);
                                LOG_INFO2("Domain.cpp::Create:In comm patch belonging to patch (x,y,z):"+
                                          Convert<std::string>(xPatch)+","+Convert<std::string>(yPatch)+","+Convert<std::string>(zPatch)+":\nAdded ComPatch with interval:"+nbCalcPatch.targetInterval.ToString()+"\n");

                        }
#endif

                        (patch->GetNeighbors())->push_back(nb);

                        //Log
                        LOG_INFO2("Domain.cpp::Create:In patch (x,y,z):"  +Convert<std::string>(xPatch)+","+Convert<std::string>(yPatch)+","+Convert<std::string>(zPatch)+" with processnumber " +
                                  Convert<std::string>(patchField_.GetWorldRank(xPatch,yPatch,zPatch))
                                  + ":Added neighbor:" +nb.ToString()+" with processnumber: " +  Convert<std::string>(patchField_.GetWorldRank(xPatch+cx[d],yPatch+cy[d],zPatch+cz[d]))+"\n");

                }
        }

        //Initialize the domain neighbor vector, which holds all processes adjacent to the local process
        for(Uint z=0;z<sim_.zNumPatches;++z)
        {
                for(Uint y=0;y<sim_.yNumPatches;++y)
                {
                        for(Uint x=0;x<sim_.xNumPatches;++x)
                        {
                                //Only check neighbors if patch is on local processor
                                if(patchField_.GetFluidRank(x,y,z)==mpi_.fluidWorkersRank)
                                {
                                        //Init all start and end search coordinates with actual patch coordinates
                                        Uint xs=x,ys=y,zs=z,xe=x,ye=y,ze=z;

                                        //Get start and end index for the checking of the neighbors
                                        //X Start
                                        if(x!=0)
                                                xs=x-1;
                                        //Y Start
                                        if(y!=0)
                                                ys=y-1;
                                        //Z Start
                                        if(z!=0)
                                                zs=z-1;
                                        //X End
                                        if(x!=sim_.xNumPatches-1)
                                                xe=x+1;
                                        //Y End
                                        if(y!=sim_.yNumPatches-1)
                                                ye=y+1;
                                        //Z End
                                        if(z!=sim_.zNumPatches-1)
                                                ze=z+1;
                                        //  std::cout << "Going from " << xs << "," << ys << "," << zs << " to " << xe  << "," << ye << "," << ze << std::endl;
                                        //Now check all neighbors
                                        for(Uint nbz=zs;nbz<=ze;++nbz)
                                        {
                                                for(Uint nby=ys;nby<=ye;++nby)
                                                {
                                                        for(Uint nbx=xs;nbx<=xe;++nbx)
                                                        {
                                                                if(patchField_.GetFluidRank(nbx,nby,nbz)!=mpi_.fluidWorkersRank)
                                                                {
                                                                        //The patch neighbor is on another processor, so we have to send to this neighbor
                                                                        //So check if we already put the processnumber to the vector
                                                                        std::map<int,int>::iterator it;

                                                                        it=processNeighbors_.find(patchField_.GetWorldRank(nbx,nby,nbz));
                                                                        if(it==processNeighbors_.end()) //We have not found the process number
                                                                        {
                                                                                processNeighbors_.insert(std::pair<int,int>(patchField_.GetFluidRank(nbx,nby,nbz),1));

                                                                                //std::cout << "Adding neighbor "<< patchField_.GetFluidRank(nbx,nby,nbz)<< " to process " << mpi_.worldRank << std::endl;
                                                                        }
                                                                }
                                                        }
                                                }
                                        }
                                }
                        }
                }
        }

        //Print all process numbers for debugging
        //      std::cout << "Process " << mpi_.worldRank << " has neighbors:";
        //      std::string procNeighbors="";
        //
        //      std::map<int,int>::iterator it;
        //      for(it=processNeighbors_.begin();it!=processNeighbors_.end();++it)
        //      {
        //         std::cout << it->first << ",";
        //         if(procNeighbors=="")
        //            procNeighbors+=Convert<std::string>(it->first);
        //         else
        //            procNeighbors+=(","+Convert<std::string>(it->first));
        //      }
        //      std::cout << std::endl;
        //      LOG_INFO2("Process "+Convert<std::string>(mpi_.worldRank)+" has neighbors:"+procNeighbors+"\n");
}

//Init all MPI structures for ISend and IRecv
void Domain::InitRequestMem(){

#ifdef WALBERLA_USE_MPI
        LOG_INFO2("Domain.cpp:Initialize the MPI structures\n");
        LOG_INFO3("Domain.cpp::Create:We have "+Convert<std::string>(2*comPatches_.size())+" MPI requests\n");
        //We need two times the requests we have ComPatches
        mpi_.requests=new MPI_Request[2*comPatches_.size()];
        mpi_.stati=new MPI_Status[2*comPatches_.size()];
        for(Uint i=0;i<comPatches_.size();++i)
        {
                comPatches_[i]->SetRequestStructure(mpi_.requests+2*i);
        }
#endif
}





//#ifdef


//#include  "SOLVE_DecayCase.h"


//#else



//=================================================================================================
//
// Solve
//
//=================================================================================================

//*************************************************************************************************
/*!\fn void Domain::Solve()
   // \brief Solve is doing the setup of the domain and the time loop. Benchmarking and Timing can be done by setting compilerflags \c BENCHMARKING and \c TIMING respectively.
   // \return void
    */
void Domain::Solve()
{
        Uint t; // The time step
        int stopSimulation=0; //Stop for restart
        Uint restartNumTimestep=0; // The timesteps executed before restart

        //Timing and benchmarking initializations

        unsigned int tMlups = Timing::Register();
#ifdef USE_OMP
        std::map< Apps ,std::vector<PatchIter> > testVec;
#endif


#ifdef BELTRANI_FLOW
    std::cerr << "   Case of Beltrani flow case\n";
#else
    	#ifdef TAYLOR_GREEN_VORTEX
    	std::cerr << "   Case of Taylor Green vortex\n";
    		#ifdef TAYLOR_GREEN_VORTEX_2D
		    	std::cerr << "   Case of 2D Taylor Green vortex\n";
		#endif
	#endif
#endif



#     ifdef WALBERLA_USE_MPI
        //Start the pe instance if necessary
        //NOTE: The instance does NOT return from the loop before all other processes finished the whole simulation
        if(peInstance_ && mpi_.worldRank==mpi_.rankPeInstance)
        {
                movingObstacles_.PeLoop();
                return;
        }
#     endif

        tStart_=Restart::Instance().RestartSimulation(patchField_); //If it is a restart file, then restart simulation from values from file and set the starting time step

        // Struct for the order of the sweeps in the time loop
    SweepStruct sweeps ;

        //!TODO: this is for testing MOVING_OBJECTS, do one OBJECT_SWEEP first
        // movingobstacle::BCObstacles(movingObstacles_.GetObstacles(),*(patchField_.GetBeginLocalPatches())->GET_PATCH(),sim_);

        //Start of benchmarking
#     ifdef BENCHMARKING
#     ifdef WALBERLA_USE_MPI
        if(mpi_.fluidWorkersRank==0)
#     endif
        {
                std::cout << "Running benchmark."<< std::endl<<std::flush;
        }
        if (sim_.timesteps<5) THROW_LOG_ERROR("Domain.cpp::Solve: Benchmarking starts not before the third time step. Please specify at least 10 time steps for proper measurements!\n");
#     endif

#ifdef USE_STL_GE // progress bar



#ifdef WALBERLA_USE_MPI
        const int ntime_steps =  sim_.timesteps+1;
        boost::progress_display *prog_bar ;
        if(mpi_.worldRank==0)
        {
                prog_bar = new boost::progress_display ( ntime_steps  , std::cerr);
        }
#else
        const int ntime_steps =  sim_.timesteps+1;
        boost::progress_display prog_bar( ntime_steps  , std::cerr);
#endif // mpi
#endif

std::cerr<<"\n";

#ifdef USE_STL_GE
    if( sim_.gx_L != 0.0 || sim_.gy_L != 0.0 || sim_.gz_L != 0.0 )
    {

#ifdef WALBERLA_USE_MPI
        if(mpi_.fluidWorkersRank==0)
        {
            std::cout<< "  Add Gravitaional field :\t (sim_.gx_L  , sim_.gy_L , sim_.gz_L) : ("<<  sim_.gx_L <<" , "<<sim_.gy_L<<","<< sim_.gz_L <<")\n";
            std::cout<< "  Add Gravitaional field :\t (sim_.gx    , sim_.gy   , sim_.gz ) : ("<<  sim_.gx  <<" , "<<sim_.gy <<","<< sim_.gz <<")\n";
        }
#else
        std::cout<< "  Add Gravitaional field :\t (sim_.gx_L  , sim_.gy_L , sim_.gz_L) : ("<<  sim_.gx_L <<" , "<<sim_.gy_L<<","<< sim_.gz_L <<")\n";
        std::cout<< "  Add Gravitaional field :\t (sim_.gx    , sim_.gy   , sim_.gz ) : ("<<  sim_.gx  <<" , "<<sim_.gy <<","<< sim_.gz <<")\n";
#endif
        m_geScene ->  AddGravitationalField (sim_.gx_L , sim_.gy_L , sim_.gz_L) ;
    }
#endif






#ifdef USE_STL_GE

#ifdef GE_USE_MPI
        std::stringstream fnKe , fnEpsilon, fNvel, fMaxNvel , fnSgsKe ;
        fnKe      << "ke_"          <<  "proc" << mpi_.fluidWorkersRank <<".dat" ;// Average kinetic energy
        fNvel     << "velocity_"    <<  "proc" << mpi_.fluidWorkersRank <<".dat" ;// average velocity
        fMaxNvel  << "maxVelocity_" <<  "proc" << mpi_.fluidWorkersRank <<".dat" ;// max velocity
        std::ofstream fke        ( fnKe.str().c_str()        ) ;
        std::ofstream avevel     ( fNvel.str().c_str()       ) ;
        std::ofstream maxvel     ( "maxVelocity.dat"    ) ;

        // Declare turbulence output streams for all ranks
        fnSgsKe   << "Sgske_"       <<  "proc" << mpi_.fluidWorkersRank <<".dat" ;// subgrid turb kinetic energy
        fnEpsilon << "sgsEpsilon_"  <<  "proc" << mpi_.fluidWorkersRank <<".dat" ;// subgrid turb dessipation energy
        std::ofstream fSgske     ( fnSgsKe.str().c_str()     ) ;
        std::ofstream EpStream   ( fnEpsilon.str().c_str()   ) ;

if(mpi_.fluidWorkersRank==0)
{
        if (use_turbModel )
        {
            if(fSgske.fail() )       throw runtime_error("Could not open output file.");
            if(EpStream.fail() )     throw runtime_error("Could not open output file.");

            fSgske << "# Subgrid turb Kinetic energy (lbm)\n" ;
            fSgske << "# time\t sgsKE\n";
            EpStream << "# Subgrid turb Dissipation energy(lbm)\n" ;
            EpStream << "# time\t sgsEpsilo\n";
        }


        if(fke.fail() )
            throw runtime_error("Could not open output file.");
        if(maxvel.fail() )
            throw runtime_error("Could not open output file.");


        #ifdef TAYLOR_GREEN_VORTEX
               fke << "#============================================================================\n#\n#\n";
               fke << "#\tTaylor Green Validation\n" ;
               fke << "#\tError cmpare to the analytical solution \n";
               fke << "#\tAverage kinetic Energy is the average kinetic energy, f (t)\n";
               fke << "#\txp,yp and zp are in global coordinates, exact solution\n";
               fke << "#\n#\n#\n# ============================================================================\n";
              // fke << "#time\t \tError"<< "AvrkineticEnergy(lbm)"<< "\t dimless Reynolds number\n" ;
               fke << "#time\t Error(theo)"<< "\t ke(lbm)"<< "\tMax Vel:Length"  << "\tmax(Ux) "  << "\t max(Uy) "  << "\t max(Uz) Cell\t x ,\ty ,\tz : " <<
        "\txp.\typ.\tzp" << "\tU_exact_mag" <<   "\tU_exact [0]"   <<   "\tU_exact[1]"     << "\tU_exact [2]\n" ;

        #else
            //fke << "#time\t"<< "ke(lbm)"<< "\tMax Vel:Length"  << "\tmax(Ux) "  << "\t max(Uy) "  << "\t max(Uz) Cell\t x ,\ty ,\tz :\n" ;
             fke << "#time\t [s]"<< "KE(phy)"<< "\tAverageMagSij "  << "\tratio(tubVisc/laminar) \n" ;
        #endif


        maxvel << "time[s]\tMax Velocity: Length: \t Ux,Uy,Uz,  \tCell (x,y,z) : " <<std::endl;


}
#else
        std::stringstream fnKe , fNvel , fMaxNvel   ;
        fnKe      << "ke"               << ".dat" ;// Average kinetic energy
        fNvel     << "averageVelocity"  << ".dat" ;// average velocity

        fMaxNvel  << "maxVelocity"<< ".dat" ;

        std::ofstream fke        ( fnKe.str().c_str()        ) ;

        //if (use_turbModel )
        //{
            std::stringstream fnEpsilon, fnSgsKe ;

            fnSgsKe   << "Sgske"            << ".dat" ;// subgrid turb kinetic energy
            fnEpsilon << "sgsEpsilon"       << ".dat" ;// subgrid turb dessipation energy

            std::ofstream fSgske     ( fnSgsKe.str().c_str()     ) ;
            std::ofstream EpStream   ( fnEpsilon.str().c_str()   ) ;

            if(fSgske.fail() )       throw runtime_error("Could not open output file.");
            if(EpStream.fail() )     throw runtime_error("Could not open output file.");

            // new , cherif
            std::ofstream SkewnessFlatness ( "SkewnessFlatness.dat" ) ;
            if(SkewnessFlatness.fail() )
                 throw runtime_error("Could not open SkewnessFlatness file.");

            std::ofstream TauEff ( "AvrTauEffec.dat" ) ;
             if(TauEff.fail() )     throw runtime_error("Could not open SkewnessFlatness file.");

            SkewnessFlatness << "#time\t(s)"<< "Skewness \t Flatness \tSkewness_d \t Flatness_d\n";
            TauEff << "#time\t(s)"<< "tau_eff(lam+turb)\n";

            fSgske << "# Subgrid turb Kinetic energy (lbm)\n" ;
            fSgske << "# time\t sgsKE\n";
            EpStream << "# Subgrid turb Dissipation energy(lbm)\n" ;

            EpStream << "# time\t sgsEpsilo\n";
        //}



        // incase of Taylor-Green case, this file report error with respect to  Analytical solution.
        #ifdef TAYLOR_GREEN_VORTEX
            std::ofstream VelEroor        (  "VelociyErrorAnalytical.dat"       ) ;
                if(VelEroor.fail() )          throw runtime_error("Could not open output file VelEroor.");
        #endif


        std::ofstream avevel     ( fNvel.str().c_str()       ) ;
        std::ofstream maxvel     (  fMaxNvel.str().c_str()   ) ;



        if(fke.fail() )          throw runtime_error("Could not open output file.");

        if(maxvel.fail() )          throw runtime_error("Could not open output maxvel.");

        maxvel << "time[s]\tMax Velocity: Length: \t Ux,Uy,Uz,  \tCell (x,y,z) : " <<std::endl;


        #ifdef TAYLOR_GREEN_VORTEX
               VelEroor << "#============================================================================\n#\n#\n";
               VelEroor << "#\tTaylor Green Validation\n" ;
               VelEroor << "#\tError cmpare to the analytical solution \n";
               VelEroor << "#\tAverage kinetic Energy is the average kinetic energy, f (t)\n";
               VelEroor << "#\txp,yp and zp are in global coordinates, exact solution\n";
               VelEroor << "#\n#\n#\n# ============================================================================\n";
              // fke << "#time\t \tError"<< "AvrkineticEnergy(lbm)"<< "\t dimless Reynolds number\n" ;
               VelEroor << "#time\t Error(theo)"<< "\t ke(lbm)"<< "\tMax Vel:Length"  << "\tmax(Ux) "  << "\t max(Uy) "  << "\t max(Uz) Cell\t x ,\ty ,\tz : " <<
        "\txp.\typ.\tzp" << "\tU_exact_mag" <<   "\tU_exact [0]"   <<   "\tU_exact[1]"     << "\tU_exact [2]\n" ;

        #else
            //fke << "#time\t"<< "ke(lbm)"<< "\tMax Vel:Length"  << "\tmax(Ux) "  << "\t max(Uy) "  << "\t max(Uz) Cell\t x ,\ty ,\tz :\n" ;
             fke << "#time\t [s]"<< "KE(phy)"<< "\tAverageMagSij "  << "\tratio(tubVisc/laminar) \n" ;
        #endif


#endif








#ifdef TAYLOR_GREEN_VORTEX

    this ->TaylorGreenVortex( 0. ) ;
 
    //DivergenceCheck (t);
 
    #include "InitialConditions.h"
    dataOut_.Write(0, mpi_, sim_);

#endif


        // Initial condition regardless LES or not
        InitVelField() ;

        #include "InitialConditions.h"



#if defined (DNS_DECAY) && !defined (TURB_DECAY)

        // InitVelField() ;

        // #include "InitialConditions.h"

        WriteVelocityRowdebug ( "0.0");

#endif





#ifdef TURB_DECAY

        if (this->m_keepVelocityConstDuringTheInitialBCs)
                  LogErrorMessage (" keep Velocity Const During The Initialization of the BCs\n");
        else
                  LogErrorMessage (" The Velocity is not Const During The Initialization routines\n");

    #ifndef TAYLOR_GREEN_VORTEX


               // InitVelField() ;

                //#ifdef DEBUG_TURB_DECAY
                 dataOut_.Write(0, mpi_, sim_);
                //#endif

                // #if defined (DEBUG_TURB_DECAY)  && defined ( WALBERLA_USE_MPI)
                //MPI_Barrier(mpi_.comm_cart);

        //        WriteVelocityRowdebug ( "beforICs");

                //MPI_Barrier(mpi_.comm_cart);
                //#endif

                // run only in the earlier first time,
                // Its Initial BCs routines,
                // I implemented them for turb decaying case, and
                // for Taylor-Green vortex case.



 std::cout<<"\n\t\tu_mean :\t "<< GetOverallAverageVelocity ()    << "  " ;
 std::cout<<"\tt(phyical) : "  <<  0    <<  "\t Ek : " << GetOverallAverageKineticEnergy()  ;

                #include "InitialConditions.h"
                
                for(int initialTime=0 ;initialTime < 4  ; ++ initialTime )
                {

                }

        // just for info,
        std::cout << "\n" ;
        GetMaxVelocity( tStart_  );
        WriteVelocityRowdebug ( "0.0");
     //   DivergenceCheck ( 0 );
    #endif // ifndef TAYLOR_GREEN_VORTEX


    #if defined (DEBUG_TURB_DECAY)  && defined ( WALBERLA_USE_MPI)
        MPI_Barrier(mpi_.comm_cart);
    #endif

#endif

// Print out some information
# ifdef WALBERLA_USE_MPI // parallel mode
        if(mpi_.fluidWorkersRank==0)
        {
            #ifndef WALE_MODEL
            
                #ifdef VREMAN_LES_SGS
                        std::cerr << "      VREMAN model,  sim.csmag =   " <<  sim_.csmag  << "\n";

                #else
                        std::cerr << "      Smagorinsky model,  sim.csmag =   " <<  sim_.csmag  << "\n";

                #endif

            #else
                std::cerr << "      WALE  model,  sim.c_W =   " <<  0.325  << "\n";
            #endif
            #ifdef FINITE_DIFFERENCE
                std::cerr << "      use finite difference to calculate rate stres-tensor   \n";
            #else
                std::cerr << "      no finite difference\n";
            #endif
        }
#else // serial mode
        #ifndef WALE_MODEL

                #ifdef VREMAN_LES_SGS
                        std::cerr << "      VREMAN model,  sim.csmag =   " <<  sim_.csmag  << "\n";

                #else
                        std::cerr << "      Smagorinsky model,  sim.csmag =   " <<  sim_.csmag  << "\n";

                #endif
        #else
            std::cerr << "      WALE  model,  sim.c_W =   " <<  0.325  << "\n";
        #endif
        #ifdef FINITE_DIFFERENCE
            std::cerr << "      use finite difference to calculate rate stres-tensor   \n";
        #else
            std::cerr << "      no finite difference\n";
        #endif
#endif
// end   Print out

                //Real Epsilon_energy;




#if defined(TAYLOR_GREEN_VORTEX)||defined(TURB_DECAY)
        dataOut_.Write(1, mpi_, sim_);
        tStart_ += 1 ;
#endif

#endif // end if defined GE

        for(t=tStart_; t<sim_.timesteps; ++t)
        {
                if (t==tStart_+2) START_MFLUPS( tMlups );

#ifdef USE_STL_GE // progress bar
#ifdef WALBERLA_USE_MPI
                if(mpi_.worldRank==0)
                {
                        ++(*prog_bar) ;
                }
#else
                ++ prog_bar  ;
#endif // mpi

#endif



//#if defined (TURB_DECAY) // DEBUG ONLY
//        if (t==tStart_+1){
//            WriteVelocityRowdebug ( "DecayTStart");
//            exit (1);
//        }
//#endif
                
#ifndef BENCHMARKING                
// check continuity error
if ( (t%100)==0    )  //ceck if the velocity field is divergance free or not?
    //DivergenceCheck (t);
#endif 

#        ifdef WALBERLA_USE_MPI
                // TODO: BRAUCHEN WIR DIE WIRKLICH ?
                MPI_Barrier(mpi_.comm_cart); //Waiting for all processes to finish the previous steps
#        if (LOGGING>=1) || (LOGGING_MONITOR)
                Logging::Instance().LogInfo("Domain.cpp::Solve:---------------------------------------\n");
                Logging::Instance().LogInfo("Domain.cpp::Solve:Running timestep " + Convert<std::string>(t+1)+ " of "+ Convert<std::string>(sim_.timesteps)+"\n");
                Logging::Instance().LogInfo("Domain.cpp::Solve:---------------------------------------\n");
#        endif
                if(mpi_.fluidWorkersRank==0)
                {
#           ifndef BENCHMARKING
                        //std::cout << "Domain.cpp::Solve: Running timestep " << t+1 << " of " << sim_.timesteps <<".\n" << std::flush;
#           endif
                }
#        else
#           if (LOGGING>=1) || (LOGGING_MONITOR)
                Logging::Instance().LogInfo("Domain.cpp::Solve:---------------------------------------\n");
                Logging::Instance().LogInfo("Domain.cpp::Solve:Running timestep " + Convert<std::string>(t+1)+ " of "+ Convert<std::string>(sim_.timesteps)+"\n");
                Logging::Instance().LogInfo("Domain.cpp::Solve:---------------------------------------\n");
#           endif
#        ifndef BENCHMARKING
                std::cout << "\tSolve: Running timestep " << t+1 << " of " << sim_.timesteps <<"."<< std::flush;
#        endif
                //std::cout << "\r Domain.cpp::Solve: Running timestep " << t+1 << " of " << sim_.timesteps <<"."<< std::flush;
#        endif



                //		   SweepIt sweepEndIt=sweeps.End();
                /*
#ifdef WALBERLA_USE_MPI
        if(mpi_.fluidWorkersRank==0)
        std::cout << "     -> THE total number of sweeps : " << sweeps.GetNumSweeps() << "\n";

#else
        std::cout << "     -> THE total number of sweeps : " << sweeps.GetNumSweeps() << "\n";
#endif
*/




// #ifdef WALBERLA_USE_MPI
#ifdef GE_USE_MPI
        if(mpi_.fluidWorkersRank==0)
        {
            const Vector3<Real>  u_mean  = GetOverallAverageVelocity () ;
            const Real rho_mean  =  GetOverallAverageRho ();
            const Real Ek = GetOverallAverageKineticEnergy();// / (4.0 * PI*PI *3.0 *sim_.dx );


            if (use_turbModel )
            {
                const Real Avg_magS_ij = GetOverallAverageMagSij(); // average mag rate stress tensor
                const Real turbViscLaminarRatio= GetOverallAverageTubToLaminarViscosity ();
                std::cout<<"\n\ttimestep " << t+1 <<"\tu_mean :\t "<< u_mean<< "\t u_meanMag\t"  << u_mean.sqrLength()  <<  " ,\trho_mean  " << rho_mean << "\n";
                std::cout<<"\tt(phyical) : "  <<  (t)*sim_.dt   <<  "\t Ek : " << Ek <<  "\t Avg (magS_ij) : " <<  Avg_magS_ij
                         <<"\t ratio(tubVisc/laminar) : " << turbViscLaminarRatio ;
                fke << t*sim_.dt  << "\t" << GetOverallAverageKineticEnergy() <<
                       "\t" << GetOverallAverageMagSij() <<
                       "\t" << turbViscLaminarRatio << "\n";

            }else{ // laminar
                std::cout<<"\n\ttimestep " << t+1 <<"\tu_mean :\t "<< u_mean<< "\t u_meanMag\t"  << u_mean.sqrLength()  <<  " ,\trho_mean  " << rho_mean << "\n";
                std::cout<<"\tt(phyical) : "  <<  (t)*sim_.dt   <<  "\t Ek : " << Ek  ;
                fke << t*sim_.dt  << "\t" << GetOverallAverageKineticEnergy()   <<  "\n";
            }


            GetMaxVelocity( t );
            GetMaxVelocity( t , maxvel );// usefull Poissouile-flow

            if( ( rho_mean ) >= 2.0      ){
                throw std::runtime_error("Domain.cpp: rho_mean have reached hight values (crash) !");
            }

        }
#else
        const Vector3<Real>  u_mean  = GetOverallAverageVelocity () ;
        const Real rho_mean  =  GetOverallAverageRho ();
        const Real Ek = GetOverallAverageKineticEnergy();// / (4.0 * PI*PI *3.0 *sim_.dx );

        if (use_turbModel )
        {
            const Real Avg_magS_ij = GetOverallAverageMagSij(); // average mag rate stress tensor
            const Real turbViscLaminarRatio= GetOverallAverageTubToLaminarViscosity ();

            const Real SgsKineticEnergy = GetOverallAverageSGSKineticEnergy () ;
            fSgske <<   t  <<  "\t"  <<  SgsKineticEnergy  << "\n" ; 

            // subgrid turb dissipation energy
            const Real EpsilonSgs = GetOverallAverageSGSDissipationEnergy () ;
            EpStream << t << "\t" <<   EpsilonSgs << "\n" ; //  "\n";
            
            std::cout<<"\n\t\tu_mean :\t "<< u_mean<< "\t u_meanMag\t"  << u_mean.sqrLength()  <<  " ,\trho_mean  " << rho_mean << "\n";
            std::cout<<"\tt(phyical) : "  <<  (t+1)*sim_.dt   <<  "\t Ek : " << Ek <<  "\t Avg (magS_ij) : " <<  Avg_magS_ij
                     <<"\t ratio(tubVisc/laminar) : " << turbViscLaminarRatio ;
            fke << t*sim_.dt  << "\t" << GetOverallAverageKineticEnergy() <<
                   "\t" <<  GetOverallAverageMagSij() <<
                   "\t" << turbViscLaminarRatio << "\n";

        }else // laminar
        {
            std::cout<<"\n\t\tu_mean :\t "<< u_mean<< "\t u_meanMag\t"  << u_mean.sqrLength()  <<  " ,\trho_mean  " << rho_mean << "\n";
            std::cout<<"\tt(phyical) : "  <<  (t)*sim_.dt   <<  "\t Ek : " << Ek   ;

            fke << t*sim_.dt  << "\t" << GetOverallAverageKineticEnergy()   <<  "\n";
        }

        GetMaxVelocity( t );

        GetMaxVelocity( t , maxvel ); // usefull Poissouile-flow

        if( ( rho_mean ) >= 2.0      ){
            throw std::runtime_error("Domain.cpp: rho_mean have reached hight values (crash) !");
        }






#endif







                for(SweepIt sweepIt=sweeps.Begin() ; sweepIt != sweeps.End()  ;++sweepIt)
                {


                        ////////////////////////////////////////////////////////
                        // Test if the sweep is required
                        ////////////////////////////////////////////////////////
                        /*const*/ Sweep &current=*sweepIt;
                        const std::vector<Apps>& apps = current.GetApps();
                        Uint size=apps.size();
                        if((current.GetCombinedApps()&patchField_.GetCombinedPatchState())==0)continue;
                        ///////////////////////////////////////////////////////

                        ///////////////////////////////////////////////////////
                        // Repeat as often as necessary
                        ///////////////////////////////////////////////////////
                        bool newIteration = true;
                        Uint sweepIteration = 0;
                        //Logging::Instance().LogError("Domain.cpp::Solve:Running sweep "+sweepUIDtoString[current.GetID()]+"\n");
//#ifdef WALBERLA_USE_MPI
//        if(mpi_.fluidWorkersRank==0)
//        	std::cout << "Domain.cpp::Solve:Running sweep " << sweepUIDtoString[current.GetID()] << "\n";
//#else
//        std::cout << "Domain.cpp::Solve:Running sweep " << sweepUIDtoString[current.GetID()] << "\n";
//#endif


#ifdef USE_OMP
                        for(int i=0;i<size;++i){
                                PatchIter patchIt=patchField_.GetBeginLocalPatches(apps[i]);
                                PatchIter patchEnd=patchField_.GetEndLocalPatches(apps[i]);
                                for(;patchIt!=patchEnd;++patchIt){
                                        testVec[apps [i] ].push_back(patchIt);
                                }
                        }
#endif // end USE_OMP
                        while (newIteration)
                        {
                                newIteration = false;
                                
                                //std::cout << "Domain.cpp::Solve:Running sweep "+sweepUIDtoString[current.GetID()]+" in iteration "+STR(sweepIteration)+"\n";
#if (LOGGING>=1) || (LOGGING_MONITOR)
                                Logging::Instance().LogInfo("Domain.cpp::Solve:Running sweep "+sweepUIDtoString[current.GetID()]+" in iteration "+STR(sweepIteration)+"\n");
#endif // end (LOGGING>=1) ...
                                ///////////////////////////////////////////////////////
                                // Communicate all required fields
                                ///////////////////////////////////////////////////////

                                const SFPairVec&  commField=current.GetCommFields();

                                //NOTE: Just a workaround till Christian is doing a
                                //possibility for communicating "non-field" sweeps
                                if(commField.size()||current.GetID()==MO_DUMMY_SWEEP||current.GetID()==FS_UPDATE_BUBBLES_SWEEP||current.GetID()==FS_MERGE_BUBBLES_SWEEP){
#ifdef OLD_WALBERLA_COMM
                                        Communicate_PDF();
#else
                                        newIteration |= commWorld_.ClearCommunicationBuffers(sim_,current,sweepIteration);

                                        //////////////////
                                        // Free-Surface //
                                        //////////////////
                                        newIteration |= commWorld_.FSBubbleCommunication(patchField_,sim_,fsConfig_,current,t,sweepIteration);


                                        //////////////////////////
                                        // Local LBM Operations //
                                        //////////////////////////
                                        if (! ((patchField_.GetCombinedPatchState()&FREE_SURFACE) && (sweepIteration>0) )) {
                                                // for FREE_SURFACE, do field communication only once per timestep
                                                newIteration |= commWorld_.LocalFieldCommunication(patchField_,current,sweepIteration);
                                        }
#ifndef NO_PE
                                        ///////////////////
                                        // Van der Waals //
                                        ///////////////////
                                        newIteration |= commWorld_.VanDerWaalsCommunication(patchField_,current,sweepIteration,vanObjects_,movingObstacles_.GetObstacles());

                                        ////////////////
                                        // Objects-Pe //
                                        ////////////////
#ifndef           FAST_PE
                                        if(current.GetID()==MO_DUMMY_SWEEP && sweepIteration==0) newIteration |= commWorld_.AddObjectsToMessage(movingObstacles_);
#endif// end FAST_PE
#endif //NO_PE
                                        /////////
                                        // MPI //
                                        /////////
                                        newIteration |= commWorld_.MPICommunication(patchField_,current,sweepIteration,t,movingObstacles_,vanObjects_);
#endif // OLD_WALBERLA_COMM
                                }
                                ///////////////////////////////////////////////////////
                                PatchIter patchIt;
                                switch(current.GetType())
                                {
                                case PATCH_SWEEP:

                                        ///////////////////////////////////////////////////////
                                        // Go over all patches this sweep requires
                                        ///////////////////////////////////////////////////////
#ifdef USE_OMP
                                        //					#pragma omp parallel for private(i) schedule (static)

                                        //					for(int i =0;i<size;++i){

                                        ////						std::cerr << "Counter of i : " << i ;
                                        ////						std::cerr << " ----> Running sweep " << sweepUIDtoString[ i ] << "\n";

                                        //						for (int j=0; j<testVec[ apps [i] ].size(); j++)
                                        //						{
                                        ////							std::cerr << " Debugging output : 2  with counter of j = " << j << std::endl;

                                        //							PatchIter patchIt = testVec[ apps [i] ][j];
                                        //							///////////////////////////////////////////////////////
                                        //							// Set restrictions
                                        //							///////////////////////////////////////////////////////

                                        //							current.SetFieldRestrictions(patchIt->GET_PATCH());
                                        ////							std::cerr << " Debugging output : 4 "<<std::endl;
                                        //							///////////////////////////////////////////////////////
                                        //							// Call sweep function
                                        //							///////////////////////////////////////////////////////

                                        //							newIteration |= (this->*current.GetSweepFunction())(patchIt->GET_PATCH(),apps[i],t,sweepIteration);
                                        //							//std::cerr<< " Debugging output : 5 "<<std::endl;
                                        //						}
                                        //					}



                                        for(Uint i=0;i<size;++i)
                                        {
#pragma omp parallel private (patchIt)
                                                {
                                                        for( patchIt = patchField_.GetBeginLocalPatches(apps[i]);
                                                             patchIt !=  patchField_.GetEndLocalPatches(apps[i]) ; ++  patchIt)
                                                        {
                                                                ///////////////////////////////////////////////////////
                                                                // Set restrictions
                                                                ///////////////////////////////////////////////////////

                                                                // FIXME uncomment this later!!!
                                                                current.SetFieldRestrictions(patchIt->GET_PATCH());

                                                                ///////////////////////////////////////////////////////
                                                                // Call sweep function
                                                                ///////////////////////////////////////////////////////
#pragma omp single nowait

                                                                newIteration |= (this->*current.GetSweepFunction())(patchIt->GET_PATCH(),apps[i],t,sweepIteration);

                                                                ///////////////////////////////////////////////////////
                                                        }
                                                }
                                        }


#else //NO openMP


                                        for(Uint i=0;i<size;++i){
                                                PatchIter patchIt=patchField_.GetBeginLocalPatches(apps[i]);
                                                PatchIter patchEnd=patchField_.GetEndLocalPatches(apps[i]);
                                                for( ; patchIt !=  patchEnd ; ++  patchIt)
                                                {
                                                        ///////////////////////////////////////////////////////
                                                        // Set restrictions
                                                        ///////////////////////////////////////////////////////

                                                        // FIXME uncomment this later!!!
                                                        //current.SetFieldRestrictions(patchIt->GET_PATCH());

                                                        ///////////////////////////////////////////////////////
                                                        // Call sweep function
                                                        ///////////////////////////////////////////////////////

                                                        newIteration |= (this->*current.GetSweepFunction())(patchIt->GET_PATCH(),apps[i],t,sweepIteration);

                                                        ///////////////////////////////////////////////////////
                                                }
                                        }
#endif // end USE_OMP
                                        break;
                                case DOMAIN_SWEEP:
                                        newIteration |= (this->*current.GetSweepFunction())(NULL,apps[0],t,sweepIteration);
                                        break;
                                }

                                sweepIteration++;



                        }//while(newIteration)



                }//for all sweeps




                // debug free obstacles
                if (false && (t%10==0))
                {
                        pe::World::CastIterator<pe::Union> iter = movingObstacles_.GetPeWorld()->begin<pe::Union>();
                        pe::World::CastIterator<pe::Union> end   = movingObstacles_.GetPeWorld()->end<pe::Union>();
                        while (iter!=end)
                        {
                                if(iter->getID() == 394) {
                                        pe::Union::CastIterator<pe::Sphere> sphereIt= (*iter)->begin<pe::Sphere>();
                                        PatchIter patchIt=patchField_.GetBeginLocalPatches();
                                        PatchIter patchEnd=patchField_.GetEndLocalPatches();

                                        for(;patchIt!=patchEnd;++patchIt){
                                                CalcPatch &cp = *patchIt->GetPatch();
                                                Real patchX0, patchX1, patchY0, patchY1, patchZ0, patchZ1;
                                                Uint x0, x1, y0, y1, z0, z1;

                                                movingobstacle::DetermineBB(cp, sim_, *sphereIt, patchX0, patchX1, patchY0, patchY1, patchZ0, patchZ1, x0, x1, y0, y1, z0,
                                                                            z1);

                                                // The bounding box of the object
                        pe::RigidBody::AABB objectAABB=sphereIt->getAABB();

                        // The bounding box of the patch
                        pe::detection::coarse::BoundingBox<pe::real> patchAABB(patchX0,patchY0,patchZ0,patchX1,patchY1,patchZ1);

                        if(!objectAABB.overlaps(patchAABB,1.0))continue;

                                                movingobstacle::DumpToHtml(cp, true, t, x0, x1, y0, y1, z0, z1, "flag");
                                                movingobstacle::DumpObstCellRelToHtml(cp, true, t, x0, x1, y0, y1, z0, z1, "obst");
                                        }
                                        break;
                }
                                iter++;
                        }
                }

                // Swap all the patches' grids
                PatchIter patchIt=patchField_.GetBeginLocalPatches();
                PatchIter patchEnd=patchField_.GetEndLocalPatches();
                for(;patchIt!=patchEnd;++patchIt)
                  patchIt->GET_PATCH()->Swap();

        // add by cherif, need for turbulence HIT, and for Taylor-Green vortex decaying case.
#ifdef USE_STL_GE

#ifdef GE_USE_MPI
        if(mpi_.fluidWorkersRank==0)
        {

            Real  EpsilonSgs, SgsKineticEnergy;
            //std::flush(fke  ); std::flush(avevel  ); std::flush(fSgske  ); std::flush(EpStream  );
            #ifdef TAYLOR_GREEN_VORTEX
                 Real VelErrorValue = VelErrorAnalyticWithTaylorGreenError (t) ;
                 std::cerr << t << "\t error(% theory) \t"<< VelErrorValue << "\t" << "\n";

                 VelEroor << t  << "\t" << VelErrorValue << "\n";
                 // subgrid turb kinetic energy
//                 #ifdef TURB_DECAY
//                 SgsKineticEnergy = GetOverallAverageSGSKineticEnergy () ;
//                 fSgske <<   t  <<  "\t"  <<  SgsKineticEnergy  << "\n" ; // "\n";

//                 // subgrid turb dissipation energy
//                 EpsilonSgs = GetOverallAverageSGSDissipationEnergy () ;
//                 EpStream << t << "\t" <<   EpsilonSgs << "\n" ; //  "\n";
//                 #endif
            #endif
          //  #ifdef TURB_DECAY
                  //ketmp =   GetOverallAverageKineticEnergy () ;
                  //double magSij_avg = GetOverallAverageMagSij();
                  //fke << t  << "\t" << ketmp  << "\t" << magSij_avg  <<  "\n";
                  // max Velocity,  here maxvel is std::ofstream
                  GetMaxVelocity( t, maxvel );
                  maxvel << "\n";
                  // Average Velocity
                  const Vector3<Real> averageVel=  GetOverallAverageVelocity() ;
                  avevel << t << "\t" <<   averageVel  <<  "\n"  ; // << std::flush
            // #endif //TURB_DECAY
        }
#else // serial mode
            Real EpsilonSgs, SgsKineticEnergy;
            std::flush(fke  );
            std::flush(avevel  );
            std::flush(fSgske  ); 
            std::flush(EpStream  );

//            if ( use_turbModel )
//            {
//                std::flush(fSgske  ); std::flush(EpStream  );
//            }
            #ifdef TAYLOR_GREEN_VORTEX
                 Real VelErrorValue = VelErrorAnalyticWithTaylorGreenError (t) ;
                 std::cerr << t  << "\t error(% theory) \t"  << VelErrorValue << "\t" << "\n";
                 VelEroor  << t  << "\t" << VelErrorValue  << "\n";


                 // subgrid turb kinetic energy
//                 SgsKineticEnergy = GetOverallAverageSGSKineticEnergy () ;
//                 fSgske <<   t  <<  "\t"  <<  SgsKineticEnergy  << "\n" ; // "\n";

//                 // subgrid turb dissipation energy
//                 EpsilonSgs = GetOverallAverageSGSDissipationEnergy () ;
//                 EpStream << t << "\t" <<   EpsilonSgs << "\n" ; //  "\n";
            #endif

                  //ketmp =   GetOverallAverageKineticEnergy () ;
                  //fke << t  << "\t" << ketmp  << "\n";
                  // const Real magSij_avg = GetOverallAverageMagSij();
                  //fke << t  << "\t" << ketmp  << "\t" << magSij_avg  <<  "\n";

           // max Velocity,  here maxvel is std::ofstream
           GetMaxVelocity( t, maxvel );
           maxvel << "\n";
           // Average Velocity
           Vector3<Real> averageVel=  GetOverallAverageVelocity() ;
           avevel << t << "\t" <<   averageVel  <<  "\n"  ; // << std::flush

           #ifdef TURB_DECAY
           #ifndef TAYLOR_GREEN_VORTEX


                  if (use_turbModel )
                  {
                          // subgrid turb kinetic energy
                          SgsKineticEnergy = GetOverallAverageSGSKineticEnergy () ;
                          fSgske <<   t  <<  "\t"  <<  SgsKineticEnergy  << "\n" ; // "\n";

                          // subgrid turb dissipation energy
                          EpsilonSgs = GetOverallAverageSGSDissipationEnergy () ;
                          EpStream << t << "\t" <<   EpsilonSgs << "\n" ; //  "\n";

                          SkewnessFlatness << t*sim_.dt <<  "\t"
                                           << GetOverallAverageSkewness() << "\t"
                                           << GetOverallAverageFlatness() << "\t"
                                           << GetOverallAverageDerivativesSkewness() << "\t"
                                           << GetOverallAverageDerivativesFlatness() << "\n" ;
                          TauEff << t*sim_.dt <<  "\t" << GetOverallAverageTauEffect ()  << "\n";
                 }
                #endif // TAYLOR_GREEN_VORTEX
             #endif //TURB_DECAY

#endif // GE_USE_MPI
#endif// USE_STL_GE

                //Call all output writers -> Paraview, etc
                dataOut_.Write(t, mpi_, sim_);
                //dataOut_.WriteRectilinear(t, mpi_, sim_);

//#if defined (TURB_DECAY) && !defined (TAYLOR_GREEN_VORTEX)
//#ifdef TURB_DECAY
#if defined (DNS_DECAY) && !defined (TURB_DECAY)
        WriteVelocityRow (t);
#endif

#if defined (TURB_DECAY) && !defined (DNS_DECAY)
                #ifndef TAYLOR_GREEN_VORTEX
                      WriteVelocityRow (t);
                #endif
#endif

                ////////////////
                // Pov Output //
                ////////////////
                //if(PovRayVis::IsActive()&&t+1<sim_.timesteps)PovRayVis::Instance().SyncPe();

#ifndef NO_PE
                ///////////////////////
                // Moving Pov Output //
                ///////////////////////
                if(PovRayVis::IsActive()) PovRayVis::Instance().Write(t);
#endif
                // JAN: Das sollte hier nicht so drin stehen -> bitte aufraeumen.
                //Pov Ray on pe instance
                /*		if(t%40==0 && peInstance_)
                         {
#           ifdef WALBERLA_USE_MPI
MPI_Barrier(mpi_.comm_cart); //Waiting for all processes to finish the previous steps
         // Example: Process 0 has nothing to do and don't sends obstacles to pe.
         // Thus, process 0 sends VISUALIZE to pe instance, which starts the Visualize function and cannot
         // receive obstacles for the last pe step before the visualization!!!!
         // So we wait for all processes to come here. This means, that all processes finished their pe step!
         //First fluid worker activates visualization on the pe instance
         if(mpi_.fluidWorkersRank==0)
         {
         int data[2];
         data[0]=movingobstacle::VISUALIZE;
         data[1]=0;
         //TODO: IS it the right request????
         MPI_Ssend(&data,2,MPI_INT,mpi_.rankPeInstance,movingobstacle::TAG_TASK,MPI_COMM_WORLD);
         }
         movingObstacles_.SendToPe(movingobstacle::TAG_VISUALIZATION_DATA, true);
#           endif
          */
                SimOutput::Instance().WriteData(t);

                stopSimulation=Restart::Instance().WriteData(t,patchField_,movingObstacles_);
                if(stopSimulation)
                {
                        restartNumTimestep=t;
                        t=sim_.timesteps;
                }



#ifdef USE_STL_GE

#ifdef GE_USE_MPI
                // syncronize forces  between all processors.
                m_geScene -> SyncronizeForcesAllReduce() ;
#endif
                //#idfef DEBUG
#     ifdef WALBERLA_USE_MPI
        if(mpi_.fluidWorkersRank==0)
#     endif
        {
                m_geScene -> RbForcesAndTorquesInfoToFile (t) ;
                m_geScene -> RbForcesAndTorquesInfo() ;
        }
                //#endif


                if ( STL_BLOCK_EXIST_IN_PARM )
                {
                        const int ctime_step  =  t ;
                        if( ctime_step %50 == 0  )
                        {
                                m_geScene -> PlotPressureCoefficient (ctime_step) ;
                                m_geScene -> ComputeDragCoefficient ( ctime_step) ;
                                m_geScene -> ComputeLiftCoefficient ( ctime_step) ;
                                if ( m_geScene -> ComputedDeltaPressure (ctime_step , this->sim_.dx , this-> sim_.dt )  )
                                        std::cout<< "there is no block named  delta_pressure in your prm file\n";
                        }
                }



                //std::cerr<<"\n";

                // Reset all force to zero
                m_geScene -> ResetForceAndMomentAllObjects();


#endif



        }//End timeloop


#ifdef USE_STL_GE
//        #ifdef GE_USE_MPI
//            if(mpi_.fluidWorkersRank==0)
//            {
//                fke.close();
//                fSgske.close();
//                EpStream.close();
//                avevel.close();
//                maxvel.close();
//            }
//        #else
        #ifndef GE_USE_MPI
                fke.close();
//                if (use_turbModel )
//                {
//                    fSgske.close();
//                    EpStream.close();
//                }
                avevel.close();
                maxvel.close();
        #endif // GE_USE_MPI
#endif

        //Update timesteps when restart with wallclock time took place
        if(stopSimulation)
        {
                t=restartNumTimestep;
        }


        //Finalize all output writers and the pe instance
        dataOut_.Finalize(sim_,mpi_);
#ifndef NO_PE
        movingObstacles_.ShutdownInstance(peInstance_!=0);
#endif
        //Stop of benchmarking
        STOP_MFLUPS( tMlups );

#     ifdef WALBERLA_USE_MPI
        if(mpi_.fluidWorkersRank==0)
#     endif
                std::cout << std::endl;


        //End of benchmarking
#     if BENCHMARKING
        int gesNumFluid=sim_.numFluidCells; //Set because of serial processing
        double CPUTime=Timing::GetCPUTotal(tMlups);
        double WCTime=Timing::GetWCTotal(tMlups);
        double maxCPUTime=CPUTime; //Set because of serial processing
        double minCPUTime=CPUTime;
        double maxWCTime=WCTime; //Set because of serial processing
        double minWCTime=CPUTime;

#     ifdef WALBERLA_USE_MPI
        MPI_Reduce(&CPUTime, &maxCPUTime, 1, MPI_DOUBLE, MPI_MAX, 0, mpi_.comm_cart );
        MPI_Barrier(mpi_.comm_cart);
        MPI_Reduce(&CPUTime, &minCPUTime, 1, MPI_DOUBLE, MPI_MIN, 0, mpi_.comm_cart);
        MPI_Barrier(mpi_.comm_cart);
        if(mpi_.fluidWorkersRank==0)
        {
                std::cout << "The maximum CPU time is " << maxCPUTime << "!" << std::endl;
                std::cout << "The minimum CPU time is " << minCPUTime << "!" << std::endl;
        }
        //      LOG_INFO1("Domain.cpp::The CPU time of process"+Convert<std::string>(mpi_.worldRank)+" is "+ Convert<std::string>(maxCPUTime)+"!\n");
        MPI_Reduce(&WCTime, &maxWCTime, 1, MPI_DOUBLE, MPI_MAX, 0, mpi_.comm_cart);
        MPI_Barrier(mpi_.comm_cart);
        MPI_Reduce(&WCTime, &minWCTime, 1, MPI_DOUBLE, MPI_MIN, 0, mpi_.comm_cart);
        MPI_Barrier(mpi_.comm_cart);
        if(mpi_.fluidWorkersRank==0)
        {
                std::cout << "The maximum WC time is " << maxWCTime << "!" << std::endl;
                std::cout << "The minimum WC time is " << minWCTime << "!" << std::endl;
        }
        //      std::cout << "The WC time of process " << mpi_.worldRank << " is " << maxWCTime << "!" << std::endl;

        LOG_INFO1("Domain.cpp::The WC time of process"+Convert<std::string>(mpi_.worldRank)+" is "+ Convert<std::string>(maxWCTime)+"!\n");
        MPI_Reduce(&sim_.numFluidCells, &gesNumFluid, 1, MPI_UNSIGNED_LONG, MPI_SUM, 0, mpi_.comm_cart);
        MPI_Barrier(mpi_.comm_cart);
        //std::cout << "The domain of process " << mpi_.worldRank <<" has " << sim_.numFluidCells << " fluid cells" << std::endl;
        LOG_INFO1("Domain.cpp::The domain of process"+Convert<std::string>(mpi_.worldRank)+"has:"+ Convert<std::string>(sim_.numFluidCells)+" fluid cells\n");
        if(mpi_.fluidWorkersRank==0)
        {
                std::cout << "The difference between min CPU time and max CPU time is " << 100.0*(maxCPUTime-minCPUTime)/maxCPUTime << "%" << std::endl;
                std::cout << "The difference between min WC time and max WC time is " << 100.0*(maxWCTime-minWCTime)/maxWCTime << "%" << std::endl;
        }

#     endif

        if( gesNumFluid!=0)
#     ifdef WALBERLA_USE_MPI
                if(mpi_.fluidWorkersRank==0)
#     endif
                {
#ifdef USE_OMP
                        if ( omp_get_thread_num() == 0 ) // thread id == 0 , master thread
                        {
                                std::cout <<
                                             "OPENMP is \t\t" << _OPENMP <<
                                             "\nNumber of processors available:\t" << omp_get_num_procs()   <<
                                             "\nMAX number of OpenMP threads:  \t" << omp_get_max_threads() <<
                                             "\nnumber of threads : \t\t"          << omp_get_num_threads() <<
                                             std::endl;
                        }
#endif
                        std::cout << "The complete domain has " << gesNumFluid << " fluid cells" << std::endl;
                        LOG_INFO1("Domain.cpp::The complete domain has:"+ Convert<std::string>(gesNumFluid)+" fluid cells\n");
                        std::cout << "Total CPU-Time:     " << maxCPUTime << std::endl;
                        LOG_INFO1("Domain.cpp::Solve:Total CPU-Time:"+ Convert<std::string>(maxCPUTime)+"\n");
                        std::cout << "Total WC-Time:      " << maxWCTime << std::endl;
                        LOG_INFO1("Domain.cpp::Solve:Total WC-Time:"+ Convert<std::string>(maxWCTime)+"\n");

                        double MFLUPS_tot=(1.0*(t-3)*(sim_.domainX+2)*(sim_.domainY+2)*(sim_.domainZ+2)/1.0e6)/maxWCTime;

                        std::cout << "MFLUPS(total):      " << MFLUPS_tot << std::endl;
                        LOG_INFO1("Domain.cpp::Solve:MFLUPS(total):"+Convert<std::string>(MFLUPS_tot)+"\n");
                        std::cout << "MFLUPS:             " << (1.0*(t-3)*gesNumFluid/1.0e6)/maxWCTime << std::endl;
                        LOG_INFO1("Domain.cpp::Solve:MFLUPS:"+ Convert<std::string>((1.0*(t-3)*gesNumFluid/1.0e6)/maxWCTime)+"\n");
                        std::cout << "Domain.cpp::Estimated memory bandwidth for 524 bytes transfered: " << MFLUPS_tot*524.0/1000.0 << " GB/s" << std::endl;
                        LOG_INFO1("Domain.cpp::Estimated memory bandwidth for 524 bytes transfered: "+Convert<std::string>(MFLUPS_tot*524.0/1000.0)+" GB/s\n");
                        std::cout << "Domain.cpp::Estimated performance for 213 flops per cell: " << MFLUPS_tot*213.0/1000.0 << " GFLOP/s" << std::endl;
                        LOG_INFO1("Domain.cpp::Estimated performance for 213 flops per cell: "+Convert<std::string>(MFLUPS_tot*213.0/1000.0)+" GFLOP/s\n");
                }
#     endif


#ifdef WALBERLA_USE_MPI
        if(mpi_.worldRank==0)
        {
#ifdef USE_STL_GE
                delete prog_bar ;
#endif
        }
#endif
        //Comment this in for volume test in case of Jans drag test
        if(0)
                std::cout << "The domain of process " << mpi_.worldRank <<" has " << sim_.numFluidCells << " fluid cells" << std::endl;



    #ifdef TAYLOR_GREEN_VORTEX
    fke.close();
    #endif
}
//*************************************************************************************************


//#endif



//=================================================================================================
//
// Internal init functions
//
//=================================================================================================


//*************************************************************************************************
/*!\fn void Domain::InitializeBoundaries(FileReader& fileReader)
      // \brief Initializes the boundaries of the domain according to the information in fileReader "init" block
      // \param fileReader Reference to the FileReader object
      // \return void
      //
      // The function first searches for "scenario" or "filename" keywords in the init blocks and
      // issues the appropriate sub routines.
      // After that, it looks for manual boundary descriptions (which will be applied ADDITIONALLY!).
      // It is possible to define several blocks for the same direction, and several similar or
      // different boundary types in the same direction block.\n
      // The function throws an error when the boundary description (in its whole, i.e. including
      // any possibly given secanrio or file) is inconsistent: In the case one cell is touched twice,
      // and in the case a cell was forgotten, the program will break.\n
      // For that, all boundaries of the domain are initialized with \c UNDEFINED first. If after
      // processing the boundary descriptions still \c UNDEFINED cells exist, either an error is
      // thrown, or, if the keyword "fill" is given in the "init" block, they will be defined as
      // \c NOSLIP.\n
      // Together with the help function Domain::SetBC all necessary initializations for the
      // boundaries are done: The PrsObst and VelObst vectors are filled as well as the \c NEAR_OBST
      // flag is set.
       */

void Domain::InitializeBoundaries(FileReader& fileReader)
{


        // Init field to liquid cells, ghost of domain to UNDEFINED|GHOST, ghost of patches to
        // LIQUID and GHOST
        PatchIter patchIt=patchField_.GetBeginLocalPatches();
        for(;patchIt!=patchField_.GetEndLocalPatches();++patchIt)
        {
                CalcPatchID patch=patchIt->GET_PATCH();
                PatchStructID pStruct=patchIt->GetStruct();
                Uint xSize = pStruct->GetXSize();
                Uint ySize = pStruct->GetYSize();
                Uint zSize = pStruct->GetZSize();
                FlagField<Flag>& flags = patch->GetFlagField();
                for(Uint z=0; z<zSize; ++z) {
                        for(Uint y=0; y<ySize; ++y) {
                                for(Uint x=0; x<xSize; ++x) {

                                        if (
                                                        ((patch->GetXStart()==0) && (x==0)) ||
                                                        ((patch->GetYStart()==0) && (y==0)) ||
                                                        ((patch->GetZStart()==0) && (z==0)) ||
                                                        ((patch->GetXEnd()==sim_.domainX-1) && (x==xSize-1)) ||
                                                        ((patch->GetYEnd()==sim_.domainY-1) && (y==ySize-1)) ||
                                                        ((patch->GetZEnd()==sim_.domainZ-1) && (z==zSize-1))
                                                        )
                                        {
                                                flags.SET_FLAG(x,y,z,UNDEFINED|GHOST);
                                        } else {
                                                flags.SET_FLAG(x,y,z,LIQUID);
                                                if(x==0||y==0||z==0||x==xSize-1||y==ySize-1||z==zSize-1){
                                                        flags.ADD_FLAG(x,y,z,GHOST);
                                                }
                                        }
                                }
                        }
                }
        }


        bool virginBounds = true;
        FileReader::Blocks initBlocks;
        fileReader.GetBlocks("init",initBlocks);
        if(initBlocks.size()>1)
                throw std::runtime_error( "Domain.cpp::InitializeBoundaries: Specification of too many init blocks in the input file! Only one block allowed" );
        if(initBlocks.size()==0)
                throw std::runtime_error( "Domain.cpp::InitializeBoundaries: Specification of no init block in the input file! See documentation for usage!" );

        FileReader::BlockHandle &initHandle=initBlocks[0];
        bool fill=false;

        if(initHandle.IsDefined("scenario"))
        {
                std::string scenario=ConvertToLowerCase(initHandle.GetParameter<std::string>("scenario"));
                if(scenario.compare("liddrivencavity")==0)
                {
                        ScenarioLidDrivenCavity();
                        virginBounds=false;
                } else if(scenario.compare("backwardface")==0)
                {
                    const double Lz =initHandle.GetParameter<double>("ZLength_L");
                    BackwardFace( Lz  );
                    virginBounds=false;

                }else if(scenario.compare("doublyperiodicshearlayer")==0)
                {
                    const double rho  =initHandle.GetParameter<double>("rho");
                    const double delta  =initHandle.GetParameter<double>("delta");
                    DoublyPeriodicShearLayer( rho , delta  );
                    virginBounds=false;

                }
                else if(scenario.compare("canal")==0)
                {
                    ScenarioCanal();
                    virginBounds=false;
                } else if(scenario.compare("freesurface")==0)
                {
                    ScenarioFreeSurface();
                    virginBounds=false;
                }
                else if(scenario.compare("freeshear")==0)
                {
                    ScenarioFreeShear();
                    virginBounds=false;
                }
                else if(scenario.compare("shear")==0) {
                    ScenarioShear();
                    virginBounds=false;
                }
                else {
                        throw std::runtime_error( "Domain.cpp::InitializeBoundaries: Scenario "+scenario+" not known!" );
                }
        }

        //         for (ParamContainer::ParamIterator myBlock=geometryInfo_.ParamsBegin(); myBlock!=geometryInfo_.ParamsEnd(); myBlock++)
        //         {
        //            if ((myBlock->first).find("_filename")!=string::npos)
        //            {
        //		InitWithFile((std::string)myBlock->second,(std::string)(ReplaceSubStr(myBlock->first,"_filename","")),geometryInfo_);
        //		virginBounds=true;
        //            }
        //         }

        virginBounds=geometry_.InitWithGeometries(patchField_, sim_);

        //BLOOD INIT
        int isBlood=0;
        if(initHandle.IsDefined("blood"))
        {
                isBlood=initHandle.GetParameter<int>("blood");
        }
        //For blood switch patches to blood patches and use pulse for time dependency
        if(isBlood)
        {
                PatchIter patchIt;
                for(patchIt=patchField_.GetBeginLocalPatches();patchIt!=patchField_.GetEndLocalPatches();++patchIt)
                {
                        patchIt->GET_PATCH()->GetVelObst().SetTimeBehavior(new BloodPulse(1,sim_.timesteps));
                }
                // set all patches to blood
                for(Uint k=0;k<sim_.zNumPatches;++k)
                        for(Uint j=0;j<sim_.yNumPatches;++j)
                                for(Uint i=0;i<sim_.xNumPatches;++i){
                                        patchField_.RemoveApp(i,j,k,FAST);
                                        patchField_.AddApp(i,j,k,BLOOD);
                                }
        }




        // look if there are manual boundary descriptions
        for (Uint side=D3Q19::N; side<=D3Q19::B; side++) {
                std::string blockName;
                switch(side) {
                case D3Q19::N: blockName="north";  break;
                case D3Q19::S: blockName="south";  break;
                case D3Q19::T: blockName="top";    break;
                case D3Q19::B: blockName="bottom"; break;
                case D3Q19::E: blockName="east";   break;
                case D3Q19::W: blockName="west";   break;
                default: THROW_LOG_ERROR("Domain.cpp::InitializeBoundaries: Invalid branch in code taken when selecting the name of D3Q19 side "+STR(side)+". Made any changes to the directions enum?\n");
                }
                FileReader::Blocks boundaryBlocks;
                initHandle.GetBlocks(blockName,boundaryBlocks);
                if (boundaryBlocks.size()==0) {
                        if (virginBounds) {
                                throw std::runtime_error("Domain.cpp::InitializeBoundaries: No information for "+blockName+" boundary given in init block!");
                        } else {
                                LOG_INFO2("Domain.cpp:InitializeBoundaries: No information for "+blockName+" boundary given in init block!");
                        }
                }
                for (Uint block=0; block<boundaryBlocks.size(); block++) {
                        // For each block look for conditions specified...

                        FileReader::BlockHandle &boundaryHandle=boundaryBlocks[block];
                        if (boundaryHandle.GetNumBlocks()==0) {
                                if (!boundaryHandle.IsDefined("periodic")) {
                                        LOG_WARNING("Domain.cpp:InitializeBoundaries: '"+blockName+"' block "+Convert<std::string>(block)+" is empty!\n");
                                } else {
                                        // configure PatchField
                                        patchField_.SetPeriodicWall(side);
                                        // call SetBC to set the flags
                                        FileReader::Block dummy("periodic");
                                        FileReader::BlockHandle handle(&dummy);
                                        if (!SetBC(fileReader,handle,blockName)) {
                                                throw std::runtime_error("Domain.cpp::InitializeBoundaries: Error ocurred in execution of SetBC with periodic for "+blockName+" boundaries!\n");
                                        }
                                }
                        } else {
                                if (boundaryHandle.IsDefined("periodic")) {
                                        THROW_LOG_ERROR("Domain.cpp::InitializeBoundaries: '"+blockName+"' block contains both 'periodic' and other boundary types, which is prohibited!\n");
                                }
                                for (Uint bcType=0; bcType<11; bcType++) {
                                        std::string bcName;
                                        switch (bcType) {
                                        case 0: bcName="noslip";        break;
                                        case 1: bcName="vel_in";        break;
                                        case 2: bcName="prs_nils";      break;
                                        case 3: bcName="acc";           break;
                                        case 4: bcName="bouzidi";       break;
                                        case 5: bcName="freeslip";      break;
                                        case 6: bcName="partslip";      break;
                                        case 7: bcName="zero_prs_grad"; break;
                                        case 8: bcName="prs_grad";      break;
                                        case 9: bcName="prs_comp";      break;
                                        case 10:bcName="mass_in";       break;
                                        default: continue;
                                        }

                                        FileReader::Blocks bcTypeBlocks;
                                        boundaryHandle.GetBlocks(bcName,bcTypeBlocks);

                                        for (Uint bcCount=0; bcCount<bcTypeBlocks.size(); bcCount++) {
                                                FileReader::BlockHandle &bcTypeHandle=bcTypeBlocks[bcCount];
                                                if (!SetBC(fileReader,bcTypeHandle,blockName)) {
                                                        throw std::runtime_error("Domain.cpp::InitializeBoundaries: Error ocurred in execution of SetBC with "+bcName+" block number "+Convert<string>(bcCount)+" for "+blockName+" boundaries!\n");
                                                }
                                        }
                                }
                        }
                }
        }


        // ===================================================================
        //
        // for 3D geometrie using STLfile, add by Cherif
        //
        // ===================================================================

#ifdef  USE_STL_GE

#ifdef WALBERLA_USE_MPI // if inside parallel region
        std::cout << "Proc [" << mpi_.worldRank << "] -> InitializeBoundaries  "<<std::endl;
#else // serial part
        std::cout <<"InitializeBoundaries  "<<std::endl;
#endif


#ifdef WALBERLA_USE_MPI
        m_geScene = new ge::geScene<>(patchField_,sim_,mpi_);
#else
        m_geScene = new ge::geScene<>(patchField_,sim_);
#endif

        FileReader::Blocks stlBlock;

        initHandle.GetBlocks("Triangle_mesh_scene",stlBlock);

        if (stlBlock.size()== 0)
        {
#ifdef WALBERLA_USE_MPI
                if(mpi_.worldRank==0)
                {
                    LOG_WARNING("Domain.cpp::InitializeBoundaries:\n Even GE is activated through CMake config, there is no blocks named 'stl' in parameter file. You are not used the STL GeometricEngine !\n");
                }
#else
        LOG_WARNING("Domain.cpp::InitializeBoundaries:\n Even GE is activated through CMake config, there is no blocks named 'stl' in parameter file. You are not used the STL GeometricEngine !\n");
#endif // mpi

                STL_BLOCK_EXIST_IN_PARM  = false ;

        } else if (stlBlock.size()>1)
        {
                // TODO improve to add meany file at one time
                throw std::runtime_error("Domain.cpp::Init: Too many blocks named 'stl' in parameter file. Only one supported!\n");
        }else
        {
                STL_BLOCK_EXIST_IN_PARM = true ;
                // ===============================================================================
                // create instance from geInterface :
                // ===============================================================================


                ge::geParamBlockReader<>::ConfigureGE (stlBlock,*m_geScene, sim_, mpi_ ) ;
                // Get information, with and without MPI
        }

#endif //USE_STL_GE


#ifdef  USE_STL_GE

    // default values
    this-> my_compressible=false;
    this-> m_EquilibriumInit=false;
    this-> m_EquilibriumCompressible=false;
    this-> m_EquilibriumIncompressible= false;



    FileReader::Blocks TurbDecayingBlock;
    initHandle.GetBlocks("decayingTurbulent",TurbDecayingBlock);

    this-> my_decayingTurbulent = false;
    this-> m_keepVelocityConstDuringTheInitialBCs = false;


    if(TurbDecayingBlock.size()>1)
    {
            throw std::runtime_error("Domain.cpp::InitializeBoundaries:An error occured: More than one decayingTurbulent block in paramter file!\n");
    }else if (TurbDecayingBlock.size()==1 )
    {

        this->my_decayingTurbulent = true;

        std::string InitFile , keepVeloConst ;

        if (TurbDecayingBlock[0].IsDefined("InitialBCsFile"))
        {
                InitFile=TurbDecayingBlock[0].GetParameter<std::string>("InitialBCsFile");
        }

        this->my_InitialBCsFile = InitFile ;

        double scale_factor_x =1.0 , scale_factor_y =1.0 ,  scale_factor_z =1.0 ;

        if (TurbDecayingBlock[0].IsDefined("scale_x"))
        {
                scale_factor_x=TurbDecayingBlock[0].GetParameter<double>("scale_x");
        }

        this->m_scale_factor_x=scale_factor_x ;



        if (TurbDecayingBlock[0].IsDefined("scale_y"))
        {
                scale_factor_y=TurbDecayingBlock[0].GetParameter<double>("scale_y");
        }
        this->m_scale_factor_y=scale_factor_y ;

        if (TurbDecayingBlock[0].IsDefined("scale_z"))
        {
                scale_factor_z=TurbDecayingBlock[0].GetParameter<double>("scale_z");
        }
        this->m_scale_factor_z=scale_factor_z ;



        if (TurbDecayingBlock[0].IsDefined("keepInitVelocityconstDuringInitialization"))
        {
                keepVeloConst=TurbDecayingBlock[0].GetParameter<std::string>("keepInitVelocityconstDuringInitialization");
        }

        if(keepVeloConst=="on")
        {
                this->m_keepVelocityConstDuringTheInitialBCs = true;
        }


        std::string compressiblE ;
        if (TurbDecayingBlock[0].IsDefined("compressible"))
        {
               compressiblE =TurbDecayingBlock[0].GetParameter< std::string >("compressible");

               if(compressiblE == "on")
               {
                   this->my_compressible = true ;
               }

#ifdef WALBERLA_USE_MPI
        if(mpi_.worldRank==0)
        {
            if (my_compressible)
                std::cerr<< " // **** COMPRESSIBLE  ****  \n" ;
            else
                std::cerr<< " // **** NOT COMPRESSIBLE  ****  \n" ;
        }
#else
            if (my_compressible)
                   std::cerr<< " // **** COMPRESSIBLE  ****  \n" ;
            else
                   std::cerr<< " // **** NOT COMPRESSIBLE  ****  \n" ;
#endif
        }

        std::string EquilibriumInit ;
        if (TurbDecayingBlock[0].IsDefined("EquilibriumInit"))
        {
               EquilibriumInit =TurbDecayingBlock[0].GetParameter< std::string >("EquilibriumInit");
               if(EquilibriumInit == "on")
               {
                   this-> m_EquilibriumInit=true;
                   if (my_compressible)
                   {
                        std::cerr<< " // **** INITIALIZATION COMPRESSIBLE  ****  \n" ;
                        this-> m_EquilibriumCompressible=true;
                        this-> m_EquilibriumIncompressible= false;
                   } else{
                        std::cerr<< " // **** INITIALIZATION INCOMPRESSIBLE  ****  \n" ;
                        this-> m_EquilibriumCompressible=false;
                        this-> m_EquilibriumIncompressible= true;
                   };
               }else //if(EquilibriumInit == "off")
               {
                   this-> m_EquilibriumInit=false;
                   this-> m_EquilibriumCompressible= false;
                   this-> m_EquilibriumIncompressible= false;
               }
        }
 }




    if (this->my_decayingTurbulent)
    {
#ifdef WALBERLA_USE_MPI
        if(mpi_.worldRank==0)
        {
                std::cerr << "Decaying Turbulent Research!!!\n" ;
        }
#else
                std::cerr << "Decaying Turbulent Research!!!\n" ;
#endif
    }


        //Init time switcher of the turbulent flows and which LBM-models
        FileReader::Blocks AerodynamicBlock;
        initHandle.GetBlocks("Aerodynamics",AerodynamicBlock);

        // the default scenario is laminar LBGK model:
         this->use_turbModel =  false;
         this->use_LBGKModel =  true;
         this->use_MRTModel  =  false;
         this->use_TRTModel  =  false;





        Real csmag = 0.03  ;// 0.04
        Real cD = 0.1  ;// normaly this is constant
        Real cE = 0.9  ;// range [0.9, 1.1]

        if(AerodynamicBlock.size()>1)
        {
                throw std::runtime_error("Domain.cpp::InitializeBoundaries:An error occured: More than one AerodynamicBlock block in paramter file!\n");
        }
        else if (AerodynamicBlock.size()==1 )
        {
                std::string switcher ;


                std::cerr << "AerodynamicBlock block was found\n";

                if (AerodynamicBlock[0].IsDefined("turbulent"))
                {
                        switcher=AerodynamicBlock[0].GetParameter<std::string>("turbulent");
                }



                if (AerodynamicBlock[0].IsDefined("csmag"))
                {
                       csmag =AerodynamicBlock[0].GetParameter< Real >("csmag");
                }

                if (AerodynamicBlock[0].IsDefined("cD"))
                {
                       cD =AerodynamicBlock[0].GetParameter< Real >("cD");
                }

                if (AerodynamicBlock[0].IsDefined("cE"))
                {
                       cE =AerodynamicBlock[0].GetParameter< Real >("cE");
                }

                if( cE < 0.9 || cE > 1.0 )
                {
                        throw std::runtime_error("Domain.cpp::InitializeBoundaries:An error occured: cE must be in range [0.9 - 1.1] !\n");
                }

                /*
                if( csmag < 0.01 || csmag > 0.04 )
                {
                        throw std::runtime_error("Domain.cpp::InitializeBoundaries:An error occured: csmag must be in range [0.01 - 0.04 ] !\n");
                }
                */

                //std::cerr << "switcher ---> " << switcher << std::endl ;




                if(switcher=="on")
                {
                        use_turbModel = true;
                }else if(switcher=="off")
                {
                        use_turbModel = false;
                }


                std::string LBMmodel;

                if (AerodynamicBlock[0].IsDefined("model"))
                {
                        LBMmodel=AerodynamicBlock[0].GetParameter<std::string>("model");
                }

                if(LBMmodel=="LBGK")
                {
                        use_LBGKModel =  true;
                        use_MRTModel  =  false;
                        use_TRTModel  =  false;
                }else if(LBMmodel=="MRT")
                {
                        use_LBGKModel =  false;
                        use_MRTModel  =  true;
                        use_TRTModel  =  false;
                }else if(LBMmodel=="TRT")
                {
                        use_LBGKModel =  false;
                        use_MRTModel  =  false;
                        use_TRTModel  =  true;
                }else
                {
                    throw std::runtime_error("Domain.cpp::InitializeBoundaries:An error occured: Only LBGK, MRT or TRT  ara avaible!\n");
                }

                if ( use_TRTModel  && use_MRTModel  )
                    throw std::runtime_error("Domain.cpp::InitializeBoundaries:An error occured: Only LBGK, MRT or TRT  ara avaible!\n");
                if ( use_TRTModel  && use_LBGKModel  )
                    throw std::runtime_error("Domain.cpp::InitializeBoundaries:An error occured: Only LBGK, MRT or TRT  ara avaible!\n");
                if ( use_MRTModel  && use_LBGKModel  )
                    throw std::runtime_error("Domain.cpp::InitializeBoundaries:An error occured: Only LBGK, MRT or TRT  ara avaible!\n");
        }

        this->sim_.csmag = csmag   ; //0.03
        this->sim_.cD    = cD   ; // 0.1
        this->sim_.cE    = cE   ; // 0.9 <= cE <= 1.1


         if ( use_turbModel ) // turbulence - Smagorinsky LES model
         {
            if (use_LBGKModel )
            {
                //std::cerr << " LBGK-Turbulence lbm model will be used!\n";

                #ifdef WALBERLA_USE_MPI
                                if(mpi_.worldRank==0)
                                {
                                        std::cerr << "LBGK-Turbulence lbm model will be used!!!\n" ;
                                        std::cerr << "Application id : \t LBGK_TURB_EXTERNFLOW  or " <<   LBGK_TURB_EXTERNFLOW << "\n";
                                }
                #else
                                std::cerr << "LBGK-Turbulence lbm model will be used!!!\n" ;
                                std::cerr << "Application id : \t LBGK_TURB_EXTERNFLOW  or " <<   LBGK_TURB_EXTERNFLOW << "\n";
                #endif // mpi


                                // set all patches to turbulence
                                for(Uint k=0;k<sim_.zNumPatches;++k)
                                        for(Uint j=0;j<sim_.yNumPatches;++j)
                                                for(Uint i=0;i<sim_.xNumPatches;++i){
                                                        patchField_.RemoveApp(i,j,k,FAST);
                                                        patchField_.AddApp(i,j,k,LBGK_TURB_EXTERNFLOW);
                                                }

            }
            if (use_MRTModel )
            {
                #ifdef WALBERLA_USE_MPI
                                if(mpi_.worldRank==0)
                                {
                                        std::cerr << "MRT-Turbulence lbm model will be used!!!\n" ;
                                }
                #else
                                std::cerr << "MRT-Turbulence lbm model will be used!!!\n" ;
                #endif // mpi

                                // set all patches to turbulence
                                for(Uint k=0;k<sim_.zNumPatches;++k)
                                        for(Uint j=0;j<sim_.yNumPatches;++j)
                                                for(Uint i=0;i<sim_.xNumPatches;++i){
                                                        patchField_.RemoveApp(i,j,k,FAST);
                                                        patchField_.AddApp(i,j,k,MRT_TURB_EXTERNFLOWS);
                                                }

            }



//             if (use_TRTModel)
//             {
//                 #ifdef WALBERLA_USE_MPI
//                                 if(mpi_.worldRank==0)
//                                 {
//                                         std::cerr << "TRT-Turbulence lbm model will be used!!!\n" ;
//                                 }
//                 #else
//                                 std::cerr << "TRT-Turbulence lbm model will be used!!!\n" ;
//                 #endif // mpi
//
//
//                                 // set all patches to turbulence
//                                 for(Uint k=0;k<sim_.zNumPatches;++k)
//                                         for(Uint j=0;j<sim_.yNumPatches;++j)
//                                                 for(Uint i=0;i<sim_.xNumPatches;++i){
//                                                         patchField_.RemoveApp(i,j,k,FAST);
//                                                         patchField_.AddApp(i,j,k,TRT_TURB_EXTERNFLOWS);
//                                                 }
//
//             }
         }else  // Laminar flow
        {
            if (use_LBGKModel)
            {
                #ifdef WALBERLA_USE_MPI
                    if(mpi_.worldRank==0)
                    {
                         std::cerr << "LBGK-Laminar lbm model will be used!!!\n" ;
                    }
                #else
                         std::cerr << "LBGK-Laminar lbm model will be used!!!\n" ;
                #endif // mpi


                                // set all patches to turbulence
                                for(Uint k=0;k<sim_.zNumPatches;++k)
                                        for(Uint j=0;j<sim_.yNumPatches;++j)
                                                for(Uint i=0;i<sim_.xNumPatches;++i){
                                                        patchField_.RemoveApp(i,j,k,FAST);
                                                        patchField_.AddApp(i,j,k,LBGK_LAMINAR_EXTERNFLOWS);
                                                }

            }
           /* if (use_MRTModel)
            {
                #ifdef WALBERLA_USE_MPI
                                if(mpi_.worldRank==0)
                                {
                                        std::cerr << "MRT-Laminar lbm model will be used!!!\n" ;
                                }
                #else
                                std::cerr << "MRT-Laminar lbm model will be used!!!\n" ;
                #endif // mpi


                                // set all patches to turbulence
                                for(Uint k=0;k<sim_.zNumPatches;++k)
                                        for(Uint j=0;j<sim_.yNumPatches;++j)
                                                for(Uint i=0;i<sim_.xNumPatches;++i){
                                                        patchField_.RemoveApp(i,j,k,FAST);
                                                        patchField_.AddApp(i,j,k,MRT_LAMINAR_EXTERNFLOWS);
                                                }

            }*/
//             if (use_TRTModel)
//             {
//                 #ifdef WALBERLA_USE_MPI
//                                 if(mpi_.worldRank==0)
//                                 {
//                                         std::cerr << "TRT-Laminar lbm model will be used!!!\n" ;
//                                 }
//                 #else
//                                 std::cerr << "TRT-Laminar lbm model will be used!!!\n" ;
//                 #endif // mpi
//
//
//                                 // set all patches to turbulence
//                                 for(Uint k=0;k<sim_.zNumPatches;++k)
//                                         for(Uint j=0;j<sim_.yNumPatches;++j)
//                                                 for(Uint i=0;i<sim_.xNumPatches;++i){
//                                                         patchField_.RemoveApp(i,j,k,FAST);
//                                                         patchField_.AddApp(i,j,k,TRT_LAMINAR_EXTERNFLOWS);
//                                                 }
//
//             }
        }


#endif


        //Init time dependency of the inflow
        FileReader::Blocks timeDepBlocks;
        initHandle.GetBlocks("timedependency",timeDepBlocks);

        //Init all blocks to no time dependency
        if(timeDepBlocks.size()==0)
        {
                PatchIter patchIt;
                for(patchIt=patchField_.GetBeginLocalPatches();patchIt!=patchField_.GetEndLocalPatches();++patchIt)
                {
                        patchIt->GET_PATCH()->GetVelObst().SetTimeBehavior(new NoTimeDepFactor());
                }
        }
        else if(timeDepBlocks.size()>1)
        {
                throw std::runtime_error("Domain.cpp::InitializeBoundaries:An error occured: More than one time dependency block in paramter file!\n");
        }
        else
        {
                std::string dependency;

                if (timeDepBlocks[0].IsDefined("dependency"))
                {
                        dependency=timeDepBlocks[0].GetParameter<std::string>("dependency");
                }
                else
                {
                        throw std::runtime_error("Domain.cpp::InitializeBoundaries:An error occured: No dependency found in timedependency block of paramteter file! See examples for details.\n");
                }

                if(dependency=="ramp")
                {
                        if(mpi_.worldRank==0)
                                std::cerr << "Domain.cpp::InitializeBoundaries:Set up a ramp!" << std::endl;
                        if(!timeDepBlocks[0].IsDefined("length"))
                                throw std::runtime_error("Domain.cpp::InitializeBoundaries:An error occured: No length found in ramp block of timedependency block of paramteter file! See examples for details.\n");
                        int length=timeDepBlocks[0].GetParameter<int>("length");
                        PatchIter patchIt;
                        for(patchIt=patchField_.GetBeginLocalPatches();patchIt!=patchField_.GetEndLocalPatches();++patchIt)
                        {
                                patchIt->GET_PATCH()->GetVelObst().SetTimeBehavior(new Ramp(length,sim_.timesteps));
                        }
                }
                if(dependency=="pulse")
                {
                        if(mpi_.worldRank==0)
                                std::cerr << "Domain.cpp::InitializeBoundaries:Set up a pulse!" << std::endl;
                        if(!timeDepBlocks[0].IsDefined("numpulses"))
                                throw std::runtime_error("Domain.cpp::InitializeBoundaries:An error occured: No number of pulses  found in pulse block of timedependency block of paramteter file! See examples for details.\n");
                        int numPulses=timeDepBlocks[0].GetParameter<int>("numpulses");
                        if(!timeDepBlocks[0].IsDefined("numTimestepsZeroPerPulse"))
                                throw std::runtime_error("Domain.cpp::InitializeBoundaries:An error occured: No numTimestepsZeroPerPulse found in pulse block of timedependency block of paramteter file! See examples for details.\n");
                        int numTimestepsZeroPerPulse=timeDepBlocks[0].GetParameter<int>("numTimestepsZeroPerPulse");
                        PatchIter patchIt;
                        for(patchIt=patchField_.GetBeginLocalPatches();patchIt!=patchField_.GetEndLocalPatches();++patchIt)
                        {
                                patchIt->GET_PATCH()->GetVelObst().SetTimeBehavior(new Pulse(numPulses,sim_.timesteps,numTimestepsZeroPerPulse));
                        }
                }
        }





        // What's with an inner block?
        FileReader::Blocks innerBlocks;
        FileReader particleFileReader;
        FileReader::Blocks initBlocks2;

        // changed: all obstacle data now read solely from include files
        initHandle.GetBlocks("inner",innerBlocks);

        for (Uint block=0; block<innerBlocks.size(); block++) {
                // For each block look for objects specified...
                FileReader::Blocks objectBlocks;
                innerBlocks[block].GetBlocks(objectBlocks);
                for (Uint objBlock=0; objBlock<objectBlocks.size(); objBlock++) {
                        if ((objectBlocks[objBlock].GetKey()=="bubble")||(objectBlocks[objBlock].GetKey()=="atmosphere")) {
                                if (!SetFreeSurfaceObject(fileReader,objectBlocks[objBlock])) {
                                        throw std::runtime_error("Domain.cpp::InitializeBoundaries: Error ocurred in execution of SetFreeSurfaceObject with inner block number "+Convert<string>(block)+"!\n");
                                }
                        } else if (objectBlocks[objBlock].GetKey()=="drop") {
                                LOG_ERROR("Domain.cpp::InitializeBoundaries: You must not specify a drop inside the liquid region but only inside the atmosphere or a bubble! (inner block number "+Convert<string>(block)+" has a 'drop' block.)\n");
                                throw std::runtime_error("Domain.cpp::InitializeBoundaries: You must not specify a drop inside the liquid region but only inside the atmosphere or a bubble! (inner block number "+Convert<string>(block)+" has a 'drop' block.)\n");
                        } else {
#ifndef NO_PE
                                if(sim_.maxNumObstacleID%100==0 && mpi_.worldRank==0) std::cout << "Processing union number " << sim_.maxNumObstacleID << std::endl;
                                if (!SetPEObject(fileReader,objectBlocks[objBlock])) {
                                        throw std::runtime_error("Domain.cpp::InitializeBoundaries: Error ocurred in execution of SetPEObject with inner block number "+Convert<string>(block)+"!\n");
                                }
#else
                                LOG_ERROR("Domain.cpp::InitializeBoundaries: waLBerla is compiled with NO_PE, therefore no support for objects!\n");
#endif
                        }
                }
                //Check if Bouzidi for moving obstacles should be done
                if(innerBlocks[block].IsDefined("mobouzidi"))
                {
                        sim_.MOBouzidi = true;
                }
        }

#ifndef NO_PE
        //If specified in paameter file here we add objects in a regular mannar with given distance and radius
        if(movingObstacles_.GetDistance()!=0 && movingObstacles_.GetRadius()!=0.0)
        {
                if(movingObstacles_.testForce_>0.0)
                        TwoSphereDensTest();
                else
                {
                        if(movingObstacles_.sphereBatterie_)
                                AddSphereBatterie();
                        else if(movingObstacles_.addDuck_)
                                AddObjectsDuck();
                        else if(movingObstacles_.particleSegregation_)
                                SetParticleSegregation();
                        else
                                AddObjects();
                }
        }
#endif

        // shall I fill the rest?
        fill=initHandle.IsDefined("fill");
        std::string undefinedCells="";
        // do post-processing

        for(patchIt=patchField_.GetBeginLocalPatches();patchIt!=patchField_.GetEndLocalPatches();++patchIt)
        {
                CalcPatchID patch=patchIt->GET_PATCH();
                PatchStructID pStruct=patchIt->GetStruct();
                FlagField<Flag> &flag =patch->GetFlagField();
                Uint zSize=patch->GetZSize();
                Uint ySize=patch->GetYSize();
                Uint xSize=patch->GetXSize();

                // check if all boundaries are set (not UNDEFINED any more)
                for(Uint z=0;z<zSize;++z) {
                        for(Uint y=0;y<ySize;++y) {
                                for(Uint x=0;x<xSize;++x){
                                        if (flag.IS_FLAG(x,y,z,UNDEFINED)) {
                                                // periodic boundaries aftermath:
                                                // if two adjacent planes with PERIODIC left an UNDEFINED edge, set it to PERIODIC
                                                // also, if a corner was left over, surrounded by PERIODIC, set it to PERIODIC
                                                if (
                                                                // check SW edge (including SWT and SWB corners)
                                                                ((x==0 && y==0) && (
                                                                         (flag.IS_FLAG(1,0,z,PERIODIC) && flag.IS_FLAG(0,1,z,PERIODIC)) ||
                                                                         (z==0 && flag.IS_FLAG(1,0,1,PERIODIC) && flag.IS_FLAG(0,1,1,PERIODIC) && flag.IS_FLAG(1,1,0,PERIODIC)) ||
                                                                         (z==zSize-1 && flag.IS_FLAG(1,0,zSize-2,PERIODIC) && flag.IS_FLAG(0,1,zSize-2,PERIODIC) && flag.IS_FLAG(1,1,zSize-1,PERIODIC))
                                                                         ))
                                                                ||
                                                                // check SE edge (including SET and SEB corners)
                                                                ((x==xSize-1 && y==0) && (
                                                                         (flag.IS_FLAG(xSize-2,0,z,PERIODIC) && flag.IS_FLAG(xSize-1,1,z,PERIODIC)) ||
                                                                         (z==0 && flag.IS_FLAG(xSize-2,0,1,PERIODIC) && flag.IS_FLAG(xSize-1,1,1,PERIODIC) && flag.IS_FLAG(xSize-2,1,0,PERIODIC)) ||
                                                                         (z==zSize-1 && flag.IS_FLAG(xSize-2,0,zSize-2,PERIODIC) && flag.IS_FLAG(xSize-1,1,zSize-2,PERIODIC) && flag.IS_FLAG(xSize-2,1,zSize-1,PERIODIC))
                                                                         ))
                                                                ||
                                                                // check NE edge (including NET and NEB corners)
                                                                ((x==xSize-1 && y==ySize-1) && (
                                                                         (flag.IS_FLAG(xSize-2,ySize-1,z,PERIODIC) && flag.IS_FLAG(xSize-1,ySize-2,z,PERIODIC)) ||
                                                                         (z==0 && flag.IS_FLAG(xSize-2,ySize-1,1,PERIODIC) && flag.IS_FLAG(xSize-1,ySize-2,1,PERIODIC) && flag.IS_FLAG(xSize-2,ySize-2,0,PERIODIC)) ||
                                                                         (z==zSize-1 && flag.IS_FLAG(xSize-2,ySize-1,zSize-2,PERIODIC) && flag.IS_FLAG(xSize-1,ySize-2,zSize-2,PERIODIC) && flag.IS_FLAG(xSize-2,ySize-2,zSize-1,PERIODIC))
                                                                         ))
                                                                ||
                                                                // check NW edge (including NWT and NWB corners)
                                                                ((x==0 && y==ySize-1)&& (
                                                                         (flag.IS_FLAG(1,ySize-1,z,PERIODIC) && flag.IS_FLAG(0,ySize-2,z,PERIODIC)) ||
                                                                         (z==0 && flag.IS_FLAG(1,ySize-1,1,PERIODIC) && flag.IS_FLAG(0,ySize-2,1,PERIODIC) && flag.IS_FLAG(1,ySize-2,0,PERIODIC)) ||
                                                                         (z==zSize-1 && flag.IS_FLAG(1,ySize-1,zSize-2,PERIODIC) && flag.IS_FLAG(0,ySize-2,zSize-2,PERIODIC) && flag.IS_FLAG(1,ySize-2,zSize-1,PERIODIC))
                                                                         ))
                                                                // check SB edge
                                                                || ((y==0 && z==0) && flag.IS_FLAG(x,0,1,PERIODIC) && flag.IS_FLAG(x,1,0,PERIODIC))
                                                                // check ST edge
                                                                || ((y==0 && z==zSize-1) && flag.IS_FLAG(x,0,zSize-2,PERIODIC) && flag.IS_FLAG(x,1,zSize-1,PERIODIC))
                                                                // check NT edge
                                                                || ((y==ySize-1 && z==zSize-1) && flag.IS_FLAG(x,ySize-1,zSize-2,PERIODIC) && flag.IS_FLAG(x,ySize-2,zSize-1,PERIODIC))
                                                                // check NB edge
                                                                || ((y==ySize-1 && z==0) && flag.IS_FLAG(x,ySize-1,1,PERIODIC) && flag.IS_FLAG(x,ySize-2,0,PERIODIC))
                                                                // check WB edge
                                                                || ((x==0 && z==0) && flag.IS_FLAG(0,y,1,PERIODIC) && flag.IS_FLAG(1,y,0,PERIODIC))
                                                                // check EB edge
                                                                || ((x==xSize-1 && z==0) && flag.IS_FLAG(xSize-1,y,1,PERIODIC) && flag.IS_FLAG(xSize-2,y,0,PERIODIC))
                                                                // check WT edge
                                                                || ((x==0 && z==zSize-1) && flag.IS_FLAG(0,y,zSize-2,PERIODIC) && flag.IS_FLAG(1,y,zSize-1,PERIODIC))
                                                                // check ET edge
                                                                || ((x==xSize-1 && z==zSize-1) && flag.IS_FLAG(xSize-1,y,zSize-2,PERIODIC) && flag.IS_FLAG(xSize-2,y,zSize-1,PERIODIC))
                                                                ) {
                                                        flag.ADD_FLAG(x,y,z,PERIODIC);
                                                        flag.REMOVE_FLAG(x,y,z,UNDEFINED);
                                                } else {
                                                        if(fill) {
                                                                flag.ADD_FLAG(x,y,z,NOSLIP);
                                                                flag.REMOVE_FLAG(x,y,z,UNDEFINED);
                                                        }
                                                        undefinedCells+="           |-> Cell flag in Patch number=("+Convert<std::string>(pStruct->GetPatchNumber())+") "
                                                                        +"at relative position (x,y,z)=("+Convert<std::string>(x)+","+Convert<std::string>(y)+","+Convert<std::string>(z)+") "
                                                                        +"(global position (x,y,z)=("+Convert<std::string>(patch->GetXStart()+x)+","+Convert<std::string>(patch->GetYStart()+y)+","+Convert<std::string>(patch->GetZStart()+z)+"))\n.";
                                                }
                                        }
                                        // now set NEAR_OBST flag. Since not yet all rows are processed, this has to run after it, shifted by (-1,-1,-1)
                                        if (x>1 && y>1 && z>1) {
                                                flag.REMOVE_FLAG(x-1,y-1,z-1,NEAR_OBST);
                                                // is it a fluid cell
                                                if(flag.IS_FLAG(x-1,y-1,z-1,FLUID)){
                                                        // then test all directions
                                                        for( Uint d=0; d<D3Q19::Cellsize; ++d){
                                                                // if the cell in the tested direction is an obstacle cell
                                                                // set the current cell to near obstacle
                                                                if(flag.IS_FLAG(x-1+cx[d],y-1+cy[d],z-1+cz[d],OBST)){
                                                                        flag.ADD_FLAG(x-1,y-1,z-1,NEAR_OBST);
                                                                        break;
                                                                }
                                                        }
                                                }
                                        }
                                }
                        }
                }
        }

#ifndef NO_PE
        /////////////////////
        // PE & POV PLANES //
        /////////////////////
        AddPlanesTo_Pe_Pov(initHandle);

        /////////////////////////////////////
        ////ACTIVISION OF MOVING OBSTACLES///
        /////////////////////////////////////

        int notfixed=0;
        ObstacleIter it;
        for(it=movingObstacles_.GetObstacles().begin();it!=movingObstacles_.GetObstacles().end();++it){
                if(!(*it).second->isFixed()){
                        notfixed=1;
                        break;
                }
        }

#        ifdef WALBERLA_USE_MPI
        int allNotFixed=0;
        MPI_Allreduce(&notfixed, &allNotFixed, 1, MPI_INT, MPI_MAX, mpi_.comm_cart );
        if(movingObstacles_.useBoundaryObjects_)
                allNotFixed=1;
        // if(movingObstacles_.useBoundaryObjects_)
        //   allNotFixed=1;
        // std::cout << "Allnotfixed is " << allNotFixed << std::endl;
#        endif

        //NOTE: Changed! Now all patches are set to moving obstacles if we have at least one object in the whole domain (not only local one)!
        // If we do the dynamic activision, then change that again
        //if(notfixed){
#     ifdef WALBERLA_USE_MPI
#        ifdef FAST_PE
        if(allNotFixed||wantMOObstacles_){
#        else
        if(peInstance_||(notfixed&&mpi_.numprocsWorld==1)){ //For the parallel case: If we have the instance or we have only one process with notfixed obstacles, then all
#        endif
#     else
        if(notfixed||wantMOObstacles_){ //For the serial case: If we have obstacles
#     endif
                LOG_INFO1("Setting all patches to moving obstacles patches!\n");

                /////////////////////
                // PE & POV PLANES //
                /////////////////////
                // AddPlanesTo_Pe_Pov(initHandle); // copied outside of if-condition, to always have boundary


                // set all to moving obstacles
                for(Uint k=0;k<sim_.zNumPatches;++k)
                        for(Uint j=0;j<sim_.yNumPatches;++j)
                                for(Uint i=0;i<sim_.xNumPatches;++i)
                                {
                                        patchField_.RemoveApp(i,j,k,FAST);
                                        patchField_.AddApp(i,j,k,MOVING_OBSTACLES);
                                }

        }

#endif


        ///////////////////////////////////////
        ////ACTIVISION OF PURE_FLUID_PATCHES///
        ///////////////////////////////////////
        patchIt = patchField_.GetBeginLocalPatches();
        for(;patchIt!=patchField_.GetEndLocalPatches();++patchIt){
                if(patchIt->GetStruct()->IsActive(FAST)&&patchIt->GetPatch()->HasNoObstacles())
                        patchField_.SetState(patchIt->GetPatch()->GetPatchUID(),PUREFLUID);
        }




        if (undefinedCells!="") {
                if (fill) {
                        LOG_INFO3("Domain.cpp::InitializeBoundaries: Your boundary description has left over some UNDEFINED boundary cells. Filled them with NOSLIP:\n"+undefinedCells);
                } else {
                        LOG_ERROR("Domain.cpp::InitializeBoundaries: Your boundary description is not consistent. There are cells with UNDEFINED state:\n"+undefinedCells);
                        throw std::runtime_error("Domain.cpp::InitializeBoundaries: Your boundary description is not consistent. There are cells with UNDEFINED state. See log file for details.");
                }
        }
#ifndef NO_PE
        //NOTE: For Jans test with many obstacles
        //AddObjects();
        std::cout << "We have " << movingObstacles_.GetObstacles().size() << " objects on process " << mpi_.worldRank << std::endl;
#endif
#     ifdef WALBERLA_USE_MPI
#ifndef NO_PE
#     ifndef FAST_PE
        //Initialize the objects, which have to be send to pe instance
        if(mpi_.worldRank!=mpi_.rankPeInstance && peInstance_)
        {
                movingObstacles_.TellInstanceData(patchField_,processNeighbors_);
        }
        if(mpi_.worldRank!=mpi_.rankPeInstance){
                MPI_Barrier(mpi_.comm_cart);
                if(PovRayVis::IsActive())PovRayVis::Instance().SyncPe();
        }
#     endif
#endif
#     endif
#ifndef NO_PE
        if(movingObstacles_.initPressureGradient_)
        {
                Real prsMax=movingObstacles_.prsMax_;
                Real prsMin=movingObstacles_.prsMin_;
                Vector3<Real> grav(sim_.gx_L, sim_.gy_L, sim_.gz_L); // gravity-vector

                // Initialize liquid cells with pressure gradient
                PatchIter patchIt=patchField_.GetBeginLocalPatches();
                PatchIter patchEnd=patchField_.GetEndLocalPatches();
                for(;patchIt!=patchEnd;++patchIt)
                {
                        CalcPatch& patch = *(patchIt->GetPatch());

                        // for all patch-cells ..
                        for(Uint z=0; z<patch.GetZSize(); ++z)
                        {
                                for(Uint y=0; y<patch.GetYSize(); ++y)
                                {
                                        for(Uint x=0; x<patch.GetXSize(); ++x)
                                        {
                                                Real rho=prsMax-(prsMax-prsMin)*(x+patch.GetXStart())/sim_.domainX;
                                                for (Uint l=0; l<D3Q19::Cellsize; l++) {
                                                        if(sim_.compressible)
                                                                walberla::EquilibriumCompressible(patch.GetSrcField(), patch.GetDestField(), x,y,z, rho, 0,0,0, grav); // for me
                                                        else
                                                                walberla::EquilibriumIncompressible(patch.GetSrcField(), patch.GetDestField(), x,y,z, rho, 0,0,0); // for me
                                                }
                                        }
                                }
                        }
                }
        }
#endif
}


//*************************************************************************************************
/*!\fn void Domain::ScenarioLidDrivenCavity()
         // \brief A method to initialize a lid driven cavity scenario.
         // \return void
          */
void Domain::ScenarioLidDrivenCavity()
{
        // South is acceleration cell
        // All others are Noslip

        LOG_INFO3("Domain.cpp::ScenarioLidDrivenCavity:Setting scenario lid driven cavity\n");

        //For all patches
        PatchIter patchIt;
        for(patchIt=patchField_.GetBeginLocalPatches();patchIt!=patchField_.GetEndLocalPatches();++patchIt) {
                CalcPatchID patch = patchIt->GET_PATCH();
                FlagField<Flag> &flag =patch->GetFlagField();

                //Save patch sizes in internal variable
                Uint xSize=flag.XSize();
                Uint ySize=flag.YSize();
                Uint zSize=flag.ZSize();


                // init north border if last patch in north direction
                if(patch->GetYEnd()>=sim_.domainY-1)
                {
                        // N
                        Uint y=sim_.domainY-patch->GetYStart()+1;
                        for(Uint z=0;z<zSize;++z)
                                for(Uint x=0;x<xSize;++x)
                                {
                                        flag.ADD_FLAG(x,y,z,NOSLIP);
                                        flag.REMOVE_FLAG(x,y,z,UNDEFINED);
                                        patch->GetSrcField().SetInvalidPDF(x,y,z);
                                }
                }

                // init west border if first patch in west direction
                if(patch->GetXStart()==0)
                {
                        // W
                        Uint x=0;
                        for(Uint z=0;z<zSize;++z)
                                for(Uint y=0;y<ySize;++y)
                                {
                                        flag.ADD_FLAG(x,y,z,NOSLIP);
                                        flag.REMOVE_FLAG(x,y,z,UNDEFINED);
                                        patch->GetSrcField().SetInvalidPDF(x,y,z);
                                }
                }

                // init east border if last patch in east direction
                if(patch->GetXEnd()>=sim_.domainX-1)
                {
                        // E
                        Uint x=sim_.domainX-patch->GetXStart()+1;
                        for(Uint z=0;z<zSize;++z)
                                for(Uint y=0;y<ySize;++y)
                                {
                                        flag.ADD_FLAG(x,y,z,NOSLIP);
                                        flag.REMOVE_FLAG(x,y,z,UNDEFINED);
                                        patch->GetSrcField().SetInvalidPDF(x,y,z);
                                }
                }

                // init top border if last patch in top direction
                if(patch->GetZEnd()>=sim_.domainZ-1)
                {
                        // T
                        Uint z=sim_.domainZ-patch->GetZStart()+1;
                        for(Uint y=0;y<ySize;++y)
                                for(Uint x=0;x<xSize;++x)
                                {
                                        flag.ADD_FLAG(x,y,z,NOSLIP);
                                        flag.REMOVE_FLAG(x,y,z,UNDEFINED);
                                        patch->GetSrcField().SetInvalidPDF(x,y,z);
                                }
                }

                // init bottom border if first patch in bottom direction
                if(patch->GetZStart()==0)
                {
                        // B
                        Uint z=0;
                        for(Uint y=0;y<ySize;++y)
                                for(Uint x=0;x<xSize;++x)
                                {
                                        flag.ADD_FLAG(x,y,z,NOSLIP);
                                        flag.REMOVE_FLAG(x,y,z,UNDEFINED);
                                        patch->GetSrcField().SetInvalidPDF(x,y,z);
                                }
                }

                // init south border if first patch in south direction
                if(patch->GetYStart()==0)
                {
                        // S
                        Uint y=0;
                        for(Uint z=0;z<zSize;++z)
                                for(Uint x=0;x<xSize;++x)
                                {
                                        // South is acceleration cell
                                        flag.ADD_FLAG(x,y,z,ACC);
                                        flag.REMOVE_FLAG(x,y,z,UNDEFINED);
                                        Vector3<Real> tmp(0.05,0.0,0.0);
                                        patch->GetVelObst().AddCell(x,y,z,tmp.length(),tmp);
                                        patch->GetSrcField().SetInvalidPDF(x,y,z);
                                }
                }
        }
        LOG_INFO3("Domain.cpp::ScenarioLidDrivenCavity:Setting scenario lid driven cavity finished\n");
}
//*************************************************************************************************






//*************************************************************************************************
/*!\fn void Domain::BackwardFace();()
         // \brief A method to initialize a Backward Face step  scenario.
            This example simulates the laminar flow over a backward facing step at a Reynolds number
            of 800 based on channel height. The results are compared with numerical as well as
            experimental results available in literature.
         // \return void
          */
void Domain::BackwardFace(const double& Lz)
{
        // South is acceleration cell
        // All others are Noslip

        LOG_INFO3("Domain.cpp::BackwardFace :Setting scenario lid driven cavity\n");

        std::cerr << "\n\n Lz = " << Lz << "\n\n";

        //For all patches
        PatchIter patchIt;
//        for(patchIt=patchField_.GetBeginLocalPatches();patchIt!=patchField_.GetEndLocalPatches();++patchIt)
//        {
//                CalcPatchID patch = patchIt->GET_PATCH();
//                FlagField<Flag> &flag =patch->GetFlagField();

//                //Save patch sizes in internal variable
//                Uint xSize=flag.XSize();
//                Uint ySize=flag.YSize();
//                Uint zSize=flag.ZSize();


//                // init north border if last patch in north direction
//                if(patch->GetYEnd()>=sim_.domainY-1)
//                {
//                        // N
//                        Uint y=sim_.domainY-patch->GetYStart()+1;
//                        for(Uint z=0;z<zSize;++z)
//                                for(Uint x=0;x<xSize;++x)
//                                {
//                                        //flag.REMOVE_FLAG(x,y,z,UNDEFINED);
//                                        flag.SET_FLAG(x,y,z,NOSLIP);

//                                        //patch->GetSrcField().SetInvalidPDF(x,y,z);
//                                }
//                }

//                // init south border if first patch in south direction
//                if(patch->GetYStart()==0)
//                {
//                        // S
//                        Uint y=0;
//                        for(Uint z=0;z<zSize;++z)
//                                for(Uint x=0;x<xSize;++x)
//                                {
//                                        // South is acceleration cell
//                                        //flag.REMOVE_FLAG(x,y,z,UNDEFINED);
//                                        flag.SET_FLAG(x,y,z,NOSLIP);

//                                        //patch->GetSrcField().SetInvalidPDF(x,y,z);
//                                }
//                }

//                // init west border if first patch in west direction
//                if(patch->GetXStart()==0)
//                {
//                        // W
//                        Uint x=0;
//                        for(Uint z=0;z<zSize;++z)
//                                for(Uint y=0;y<ySize;++y)
//                                {
//                                        //flag.REMOVE_FLAG(x,y,z,UNDEFINED);
//                                        flag.SET_FLAG(x,y,z,PERIODIC);

//                                        //patch->GetSrcField().SetInvalidPDF(x,y,z);
//                                }
//                }

//                // init east border if last patch in east direction
//                if(patch->GetXEnd()>=sim_.domainX-1)
//                {
//                        // E
//                        Uint x=sim_.domainX-patch->GetXStart()+1;
//                        for(Uint z=0;z<zSize;++z)
//                                for(Uint y=0;y<ySize;++y)
//                                {
//                                        //flag.REMOVE_FLAG(x,y,z,UNDEFINED);
//                                        flag.SET_FLAG(x,y,z,PERIODIC);

//                                        //patch->GetSrcField().SetInvalidPDF(x,y,z);
//                                }
//                }




//                // init top border if last patch in top direction
//                if(patch->GetZEnd()>=sim_.domainZ-1)
//                {
//                        // T
//                        Uint z=sim_.domainZ-patch->GetZStart()+1;
//                        for(Uint y=0;y<ySize;++y)
//                                for(Uint x=0;x<xSize;++x)
//                                {
//                                    //flag.REMOVE_FLAG(x,y,z,UNDEFINED);
//                                    flag.SET_FLAG(x,y,z,VEL_IN);

//                                    const Vector3<Real> tmp(0.05,0.0,0.0);
//                                    patch->GetVelObst().AddCell(x,y,z,tmp.length(),tmp);
//                                    //patch->GetSrcField().SetInvalidPDF(x,y,z);
//                                }
//                }

//                // init bottom border if first patch in bottom direction
//                if(patch->GetZStart()==0)
//                {
//                        // B
//                        Uint z=0;
//                        {
//                             for(Uint y=ySize/2;y<ySize;++y)
//                                for(Uint x=0;x<xSize;++x)
//                                {
//                                        //flag.REMOVE_FLAG(x,y,z,UNDEFINED);
//                                        flag.SET_FLAG(x,y,z,VEL_IN);
//                                        const Vector3<Real> tmp(0.05,0.0,0.0);
//                                        patch->GetVelObst().AddCell(x,y,z,tmp.length(),tmp);
//                                        //patch->GetSrcField().SetInvalidPDF(x,y,z);
//                                }
//                        }
//                        // second
//                        {
//                        for(Uint y=0;y<ySize/2;++y)
//                                for(Uint x=0;x<xSize;++x)
//                                {
//                                        //flag.REMOVE_FLAG(x,y,z,UNDEFINED);
//                                        flag.SET_FLAG(x,y,z,NOSLIP);
//                                        //patch->GetSrcField().SetInvalidPDF(x,y,z);
//                                }
//                        }
//                }




//        }




            // check if all boundaries are set (not UNDEFINED any more)
            for(Uint z=1;z<Lz;++z)//for(Uint z=0;z<Lz;++z)
            {
                for(Uint y=0;y< (sim_.domainY+1)/2;++y)//for(Uint y=0;y< (sim_.domainY+1)/2;++y) 
                {
                    for(Uint x =1;x< sim_.domainX;++x)//for(Uint x =0;x< sim_.domainX+1;++x) 
		    {
                        std::vector<PatchCoordinate> paCo = GetLocalPatchCoordinates(x+1,y+1,z+1,sim_);
                        for (Uint l=0; l<paCo.size(); l++)
                        {

                                if(!patchField_.IsNeeded(paCo[l].pX,paCo[l].pY,paCo[l].pZ))
                                     continue;


                                 CalcPatch& patch = *patchField_.GET_PATCH3(paCo[l].pX,paCo[l].pY,paCo[l].pZ);

                                 FlagField<Flag>& flag = patch.GetFlagField();
 //std::cerr << "--> x ,y,z = " << x << "," << y << "," << z << "\n";
                                 //flag.REMOVE_FLAG(x,y,z,ALL_FLAGS );
                                 flag.ADD_FLAG(x,y,z, NOSLIP );
                                 //flag.SET_FLAG(x,y,z, NOSLIP );

                        }
                     }
                }
            }





        std::cerr << "Domain.cpp::BackwardFace:Setting scenario lid driven cavity finished\n" ;


        LOG_INFO3("Domain.cpp::BackwardFace:Setting scenario lid driven cavity finished\n");
}
//*************************************************************************************************





//*************************************************************************************************
/*!\fn void Domain::BackwardFace();()
         // \brief A method to initialize a doubly periodic shear layer step  scenario.
        a) https://crd.lbl.gov/assets/pubs_presos/AMCS/ANAG/A115.pdf  page 17
        b )Sharp Treatment of Surface Tension and "Viscous Stresses in Multifluid Dynamics experimental results available in literature." page 21
         // \return void
          */
void Domain::DoublyPeriodicShearLayer(const double& rho, const double& delta )
{
        // South is acceleration cell
        // All others are Noslip

        LOG_INFO3("Domain.cpp::DoublyPeriodicShearLayer :Setting scenario lid driven cavity\n");

        std::cerr << "\n\n rho = " << rho << "\n\n";
        std::cerr << "\n\n delta = " << delta << "\n\n";
        const Real dx    =  sim_.dx ;
        const Real dt    =  sim_.dt ;
        const Real coeff =  (dt/dx) ;
        //const Real CurrentTtime = dt*time_step ;
        const Real Lx = sim_.xLength;

        //For all patches
        PatchIter patchIt;

         for(patchIt=patchField_.GetBeginLocalPatches();patchIt!=patchField_.GetEndLocalPatches();++patchIt)
        {
            CalcPatchID patch = patchIt->GET_PATCH();
            FlagField<Flag> &flag =patch->GetFlagField();

            //Save patch sizes in internal variable
            Uint xSize=flag.XSize();
            Uint ySize=flag.YSize();
            Uint zSize=flag.ZSize();

            // init west border if first patch in west direction
            if(patch->GetXStart()==0)
            {
                // W
                Uint x=0;
                for(Uint z=0;z<zSize;++z)
                    for(Uint y=0;y<ySize;++y)
                    {

                        flag.REMOVE_FLAG(x,y,z,UNDEFINED);
                        flag.ADD_FLAG(x,y,z,PERIODIC);
                        //patch->GetSrcField().SetInvalidPDF(x,y,z);
                    }
            }

            // init east border if last patch in east direction
            if(patch->GetXEnd()>=sim_.domainX-1)
            {
                // E
                Uint x=sim_.domainX-patch->GetXStart()+1;
                for(Uint z=0;z<zSize;++z)
                    for(Uint y=0;y<ySize;++y)
                    {

                        flag.REMOVE_FLAG(x,y,z,UNDEFINED);
                        flag.ADD_FLAG(x,y,z,PERIODIC);
                    }
            }

            // N
            // init north border if last patch in north direction
            if(patch->GetYEnd()>=sim_.domainY-1)
            {
                Uint y=sim_.domainY-patch->GetYStart()+1;
                for(Uint z=0;z<zSize;++z)
                    for(Uint x=0;x<xSize;++x)
                    {

                        flag.REMOVE_FLAG(x,y,z,UNDEFINED);
                        flag.ADD_FLAG(x,y,z,PERIODIC);
                    }
            }
            // init south border if first patch in south direction
            if(patch->GetYStart()==0)
            {
                // S
                Uint y=0;
                for(Uint z=0;z<zSize;++z)
                    for(Uint x=0;x<xSize;++x)
                    {

                        flag.REMOVE_FLAG(x,y,z,UNDEFINED);
                        flag.ADD_FLAG(x,y,z,PERIODIC);
                    }
            }

            // init bottom border if first patch in bottom direction
            // init top border if last patch in top direction
            if(patch->GetZEnd()>=sim_.domainZ-1)
            {
                // T
                Uint z=sim_.domainZ-patch->GetZStart()+1;
                for(Uint y=0;y<ySize;++y)
                    for(Uint x=0;x<xSize;++x)
                    {

                        flag.REMOVE_FLAG(x,y,z,UNDEFINED);
                        flag.ADD_FLAG(x,y,z,PERIODIC);
                    }
            }

            // init bottom border if first patch in bottom direction
            if(patch->GetZStart()==0)
            {
                // B
                Uint z=0;
                for(Uint y=0;y<ySize;++y)
                    for(Uint x=0;x<xSize;++x)
                    {

                        flag.REMOVE_FLAG(x,y,z,UNDEFINED);
                        flag.ADD_FLAG(x,y,z,PERIODIC);
                    }
            }
        }

            // check if all boundaries are set (not UNDEFINED any more)

        for(patchIt=patchField_.GetBeginLocalPatches();patchIt!=patchField_.GetEndLocalPatches();++patchIt)
        {
            VelField &velField     = patchIt->GET_PATCH()->GetField<VelField>(FIELD_VELOCITY);
            FlagField<Uint > &flag = patchIt->GET_PATCH()->GetField<FlagField<Uint> >(FIELD_FLAG_SRC);
            // velObstacles_.BCSetVelocities(velField,0);


            const int xStart = patchIt->GET_PATCH()->GetXStart();
            const int yStart = patchIt->GET_PATCH()->GetYStart();
            const int zStart = patchIt->GET_PATCH()->GetZStart();

            int count  = 0 ;
            for(Uint k = 0; k<velField.ZSize();++k)
            {
               const Real z = (k -0.5 + zStart )*dx;
               for(Uint j = 0; j<velField.YSize();++j)
               {
                  const Real y = (j -0.5 +  yStart )*dx;
                  for(Uint i = 0; i<velField.XSize();++i)
                  {
                                const Real x = (i -0.5 + xStart )*dx ;

//                                 flag.REMOVE_FLAG(i,j,k,ALL_FLAGS );
//                                 flag.ADD_FLAG(i,j,k, FLUID );

                                 double u  ;
                                 const  double v = delta * std::sin (  (2.0 * PI * (x+0.25) ) );

                                 if ( y <= 0.5 )
                                 {
                                            u = std::tanh (   ( y     - 0.25  )/rho)  ;
                                 }else if ( y   > 0.5 ){
                                            u = std::tanh (   ( 0.75   - y     )/rho)  ;
                                 }

                                 //VelField& velField = patch.GetVelField();

                                 //velField.SET_VEL(i,j,k, u*dx/dt , v*dx/dt , 0.02);
                                 //UpdateVeloctiy(i,j,k, 0.005 , 0.005  , 0.005 );

                                 UpdateVeloctiy ( i,j,k, u*dx/dt , v*dx/dt , 0.0 ) ;
//                                 std::cerr << "--> i,j,k = "    << i  << "," << j << ","<< k << "\t";
//                                 std::cerr << "\t xp ,yp,zp = " << x << "," << y << "," << z << "\t (u,v) : (" << u  << "  ,   " << v
//                                           << ")\t (u*,v*) : (" << u*dx/dt << "  ,   "  << v* dx/dt<< ")\n" ;

                  }

               }

            }
        }




        std::cerr << "Domain.cpp::BackwardFace:Setting scenario lid driven cavity finished\n" ;


        LOG_INFO3("Domain.cpp::BackwardFace:Setting scenario lid driven cavity finished\n");
}
//*************************************************************************************************








//*************************************************************************************************
/*!\fn void Domain::ScenarioCanal()
         // \brief A method to initialize a canal scenario.
         // \return void
          */
void Domain::ScenarioCanal()
{
        // South is pressure outflow, noth is velocity inflow
        // All others are Noslip
        LOG_INFO3("Domain.cpp::ScenarioCanal:Setting scenario canal\n");

        //For all patches
        PatchIter patchIt;
        for(patchIt=patchField_.GetBeginLocalPatches();patchIt!=patchField_.GetEndLocalPatches();++patchIt) {
                CalcPatchID patch = patchIt->GET_PATCH();
                FlagField<Flag> &flag =patch->GetFlagField();

                //Save patch sizes in internal variable
                Uint xSize=flag.XSize();
                Uint ySize=flag.YSize();
                Uint zSize=flag.ZSize();

                // init west border if first patch in west direction
                if(patch->GetXStart()==0)
                {
                        // W
                        Uint x=0;
                        for(Uint z=0;z<zSize;++z)
                                for(Uint y=0;y<ySize;++y)
                                {
                                        flag.ADD_FLAG(x,y,z,NOSLIP);
                                        flag.REMOVE_FLAG(x,y,z,UNDEFINED);
                                        //patch->GetSrcField().SetInvalidPDF(x,y,z);
                                }
                }

                // init east border if last patch in east direction
                if(patch->GetXEnd()>=sim_.domainX-1)
                {
                        // E
                        Uint x=sim_.domainX-patch->GetXStart()+1;
                        for(Uint z=0;z<zSize;++z)
                                for(Uint y=0;y<ySize;++y)
                                {
                                        flag.ADD_FLAG(x,y,z,NOSLIP);
                                        flag.REMOVE_FLAG(x,y,z,UNDEFINED);
                                        //patch->GetSrcField().SetInvalidPDF(x,y,z);
                                }
                }

                // init top border if last patch in top direction
                if(patch->GetZEnd()>=sim_.domainZ-1)
                {
                        // T
                        Uint z=sim_.domainZ-patch->GetZStart()+1;
                        for(Uint y=0;y<ySize;++y)
                                for(Uint x=0;x<xSize;++x)
                                {
                                        flag.ADD_FLAG(x,y,z,NOSLIP);
                                        flag.REMOVE_FLAG(x,y,z,UNDEFINED);
                                        //patch->GetSrcField().SetInvalidPDF(x,y,z);
                                }
                }

                // init bottom border if first patch in bottom direction
                if(patch->GetZStart()==0)
                {
                        // B
                        Uint z=0;
                        for(Uint y=0;y<ySize;++y)
                                for(Uint x=0;x<xSize;++x)
                                {
                                        flag.ADD_FLAG(x,y,z,NOSLIP);
                                        flag.REMOVE_FLAG(x,y,z,UNDEFINED);
                                        //patch->GetSrcField().SetInvalidPDF(x,y,z);
                                }
                }

                // N
                // init north border if last patch in north direction
                if(patch->GetYEnd()>=sim_.domainY-1)
                {
                        Uint y=sim_.domainY-patch->GetYStart()+1;
                        for(Uint z=0;z<zSize;++z)
                                for(Uint x=0;x<xSize;++x)
                                {
                                        flag.ADD_FLAG(x,y,z,VEL_IN);
                                        flag.REMOVE_FLAG(x,y,z,UNDEFINED);
                                        Vector3<Real> tmp(0.0,0.05,0.0);
                                        patch->GetVelObst().AddCell(x,y,z,tmp.length(),tmp);
                                        //patch->GetSrcField().SetInvalidPDF(x,y,z);
                                }
                }
                // init south border if first patch in south direction
                if(patch->GetYStart()==0)
                {
                        // S
                        Uint y=0;
                        for(Uint z=0;z<zSize;++z)
                                for(Uint x=0;x<xSize;++x)
                                {
                                        // South is pressure outflow
                                        flag.ADD_FLAG(x,y,z,PRS_NILS);
                                        flag.REMOVE_FLAG(x,y,z,UNDEFINED);
                                        //NOTE: FIXME
                                        Real prs=0.002;
                                        patch->GetPrsObst().AddCell(x,y,z,prs);
                                        //patch->GetSrcField().SetInvalidPDF(x,y,z);
                                }
                }

        }
        LOG_INFO3("Domain.cpp::ScenarioCanal:Setting scenario canal finished\n");
}
//*************************************************************************************************

//*************************************************************************************************
/*!\fn void Domain::ScenarioCanal()
         // \brief A method to initialize a canal scenario.
         // \return void
          */
void Domain::ScenarioShear()
{
        //For all patches
        PatchIter patchIt;
        for(patchIt=patchField_.GetBeginLocalPatches();patchIt!=patchField_.GetEndLocalPatches();++patchIt) {
                CalcPatchID patch = patchIt->GET_PATCH();
                FlagField<Flag> &flag =patch->GetFlagField();

                //Save patch sizes in internal variable
                Uint xSize=flag.XSize();
                Uint ySize=flag.YSize();
                Uint zSize=flag.ZSize();

                // init west border if first patch in west direction
                if(patch->GetXStart()==0)
                {
                        // W
                        Uint x=0;
                        for(Uint z=0;z<zSize;++z)
                                for(Uint y=0;y<ySize;++y)
                                {
                                        flag.ADD_FLAG(x,y,z,FREESLIP);
                                        flag.REMOVE_FLAG(x,y,z,UNDEFINED);
                                        //patch->GetSrcField().SetInvalidPDF(x,y,z);
                                }
                }

                // init east border if last patch in east direction
                if(patch->GetXEnd()>=sim_.domainX-1)
                {
                        // E
                        Uint x=sim_.domainX-patch->GetXStart()+1;
                        for(Uint z=0;z<zSize;++z)
                                for(Uint y=0;y<ySize;++y)
                                {
                                        flag.ADD_FLAG(x,y,z,FREESLIP);
                                        flag.REMOVE_FLAG(x,y,z,UNDEFINED);
                                        //patch->GetSrcField().SetInvalidPDF(x,y,z);
                                }
                }

                // N
                // init north border if last patch in north direction
                if(patch->GetYEnd()>=sim_.domainY-1)
                {
                        Uint y=sim_.domainY-patch->GetYStart()+1;
                        for(Uint z=0;z<zSize;++z)
                                for(Uint x=0;x<xSize;++x)
                                {
                                        flag.ADD_FLAG(x,y,z,ACC);
                                        flag.REMOVE_FLAG(x,y,z,UNDEFINED);
                                        Vector3<Real> tmp(0.0,0.0,0.0025);
                                        patch->GetVelObst().AddCell(x,y,z,tmp.length(),tmp);
                                }
                }
                // init south border if first patch in south direction
                if(patch->GetYStart()==0)
                {
                        // S
                        Uint y=0;
                        for(Uint z=0;z<zSize;++z)
                                for(Uint x=0;x<xSize;++x)
                                {
                                        flag.ADD_FLAG(x,y,z,ACC);
                                        flag.REMOVE_FLAG(x,y,z,UNDEFINED);
                                        Vector3<Real> tmp(0.0,0.0,-0.0025);
                                        patch->GetVelObst().AddCell(x,y,z,tmp.length(),tmp);
                                }
                }

                // init bottom border if first patch in bottom direction
                // init top border if last patch in top direction
                if(patch->GetZEnd()>=sim_.domainZ-1)
                {
                        // T
                        Uint z=sim_.domainZ-patch->GetZStart()+1;
                        for(Uint y=0;y<ySize;++y)
                                for(Uint x=0;x<xSize;++x)
                                {
                                        flag.ADD_FLAG(x,y,z,VEL_IN);
                                        flag.REMOVE_FLAG(x,y,z,UNDEFINED);
                                        Vector3<Real> tmp(0.0,0.0,0.005*(y-sim_.domainY/2.0)/sim_.domainY);
                                        patch->GetVelObst().AddCell(x,y,z,tmp.length(),tmp);
                                }
                }

                // init bottom border if first patch in bottom direction
                if(patch->GetZStart()==0)
                {
                        // B
                        Uint z=0;
                        for(Uint y=0;y<ySize;++y)
                                for(Uint x=0;x<xSize;++x)
                                {
                                        flag.ADD_FLAG(x,y,z,VEL_IN);
                                        flag.REMOVE_FLAG(x,y,z,UNDEFINED);
                                        Vector3<Real> tmp(0.0,0.0,0.005*(y-sim_.domainY/2.0)/sim_.domainY);
                                        patch->GetVelObst().AddCell(x,y,z,tmp.length(),tmp);
                                }
                }
        }
}



//*************************************************************************************************
/*!\fn void Domain::ScenarioCanal()
     // \brief A method to initialize a canal scenario.
     // \return void
      */
void Domain::ScenarioFreeShear()
{
    //For all patches
    PatchIter patchIt;
    for(patchIt=patchField_.GetBeginLocalPatches();patchIt!=patchField_.GetEndLocalPatches();++patchIt) {
        CalcPatchID patch = patchIt->GET_PATCH();
        FlagField<Flag> &flag =patch->GetFlagField();

        //Save patch sizes in internal variable
        Uint xSize=flag.XSize();
        Uint ySize=flag.YSize();
        Uint zSize=flag.ZSize();

        // init west border if first patch in west direction
        if(patch->GetXStart()==0)
        {
            // W
            Uint x=0;
            for(Uint z=0;z<zSize;++z)
                for(Uint y=0;y<ySize;++y)
                {
                    flag.ADD_FLAG(x,y,z,PERIODIC);
                    flag.REMOVE_FLAG(x,y,z,UNDEFINED);
                    //patch->GetSrcField().SetInvalidPDF(x,y,z);
                }
        }

        // init east border if last patch in east direction
        if(patch->GetXEnd()>=sim_.domainX-1)
        {
            // E
            Uint x=sim_.domainX-patch->GetXStart()+1;
            for(Uint z=0;z<zSize;++z)
                for(Uint y=0;y<ySize;++y)
                {
                    flag.ADD_FLAG(x,y,z,PERIODIC);
                    flag.REMOVE_FLAG(x,y,z,UNDEFINED);
                }
        }

        // N
        // init north border if last patch in north direction
        if(patch->GetYEnd()>=sim_.domainY-1)
        {
            Uint y=sim_.domainY-patch->GetYStart()+1;
            for(Uint z=0;z<zSize;++z)
                for(Uint x=0;x<xSize;++x)
                {
                    flag.ADD_FLAG(x,y,z,PERIODIC);
                    flag.REMOVE_FLAG(x,y,z,UNDEFINED);
                }
        }
        // init south border if first patch in south direction
        if(patch->GetYStart()==0)
        {
            // S
            Uint y=0;
            for(Uint z=0;z<zSize;++z)
                for(Uint x=0;x<xSize;++x)
                {
                    flag.ADD_FLAG(x,y,z,PERIODIC);
                    flag.REMOVE_FLAG(x,y,z,UNDEFINED);
                }
        }

        // init bottom border if first patch in bottom direction
        // init top border if last patch in top direction
        if(patch->GetZEnd()>=sim_.domainZ-1)
        {
            // T
            Uint z=sim_.domainZ-patch->GetZStart()+1;
            for(Uint y=0;y<ySize;++y)
                for(Uint x=0;x<xSize;++x)
                {
                    flag.ADD_FLAG(x,y,z,PERIODIC);
                    flag.REMOVE_FLAG(x,y,z,UNDEFINED);
                }
        }

        // init bottom border if first patch in bottom direction
        if(patch->GetZStart()==0)
        {
            // B
            Uint z=0;
            for(Uint y=0;y<ySize;++y)
                for(Uint x=0;x<xSize;++x)
                {
                    flag.ADD_FLAG(x,y,z,PERIODIC);
                    flag.REMOVE_FLAG(x,y,z,UNDEFINED);
                }
        }
    }



    const Real Rho = 3.0;
    const Real Delta  = 0.05;
    std::cout << " free shear flow : \n";
    std::cout << " sim_.domainX  :"<< sim_.domainX  <<" \n";
    std::cout << " sim_.domainX  :"<< sim_.domainX  <<" \n";
    for (Uint z=0; z<sim_.domainZ ; ++z) {
       for (Uint y=0; y<sim_.domainY ; ++y) {
           for (Uint x=0; x<sim_.domainX ; ++x)
           {

               const PatchCoordinate paCo = GetOneLocalExistingPatchCoordinate(x+1,y+1,z+1,sim_);
               if( ! patchField_.IsAllocated(paCo.pX,paCo.pY,paCo.pZ) )
                      continue ;

               CalcPatch& patch = *patchField_.GET_PATCH3(paCo.pX, paCo.pY, paCo.pZ);
               VelField& velocity = patch.GetVelField ();

               Real XL ;
               XL = x*sim_.dx;//x/sim_.domainX
               Real YL;
               YL = y*sim_.dx;//y/sim_.domainY

               Real  ux ;

               const Real uy =  Delta*std::sin (  ( 2.0 * 3.14159265358979323 * XL ) )    ;//uy

               if ( YL <= 0.5 )
               {
                     ux =    std::tanh( d2r (Rho* ( YL   - 0.25 )) ) ;// ux
               }else {
                     ux =    std::tanh( d2r (Rho* ( 0.75 - YL   )) ) ;// ux
               }

 std::cout << "--> XL\t"<< XL <<"\tYL,\t"<< YL<<",\tz\t"<< z<<  "\tux " << ux <<  " \t uy "<< uy << "\n" ;

              //velocity.SET_VEL ( paCo.x,paCo.y,paCo.z  ,(ux) * sim_.dt/sim_.dx  , (uy) * sim_.dt/sim_.dx  , 0.0  ) ;
                velocity.SET_VEL ( paCo.x,paCo.y,paCo.z  ,(ux)    , (uy)    , 0.0  ) ;
           }
       }
    }


    //  don't forget to add
   // #include "InitialConditions.h"

}
//*************************************************************************************************
/*!\fn void Domain::ScenarioFreeSurface()
         // \brief A method to initialize a free-surface scenario by a "dump.dat" file written from Tom's code
         // \return void
          */
void Domain::ScenarioFreeSurface()
{
        FILE* file;
#        define  ISNAN(x) ((x) != (x))
        Real fC, fN, fS, fE, fW, fT, fB, fNE, fNW, fSE, fSW, fTN, fTS, fTE, fTW, fBN, fBS, fBE, fBW;
        Uint flag;
        Real fill, mass, nX, nY, nZ, pX, pY, pZ;
        Uint noNgbrs;
        int bubbleIndex;
        Real initV, V, rhoGas;

        fC=fN=fS=fE=fW=fT=fB=fNE=fNW=fSE=fSW=fTN=fTS=fTE=fTW=fBN=fBS=fBE=fBW=0.0;
        fill=mass=nX=nY=nZ=pX=pY=pZ=0.0;
        flag=noNgbrs=0;
        initV=V=rhoGas=0.0;
        bubbleIndex=0;

        cerr.precision(12);

        file = fopen("dump.dat", "r");
        // Read dump file from TOM's code
        for (Uint z=0; z<sim_.domainZ+2; z++) {
                for (Uint y=0; y<sim_.domainY+2; y++) {
                        for (Uint x=0; x<sim_.domainX+2; x++) {
                                //fscanf(file,"%f;%f;%f;%f;%f;%f;%f;%f;%f;%f;%f;%f;%f;%f;%f;%f;%f;%f;%f;%i;%f;%f;%f;%f;%f;%f;%f;%f;%i;%f;%f;%f;%f\n",
                                fscanf(file,"%lf", &fC);
                                fscanf(file,"%lf", &fN);
                                fscanf(file,"%lf", &fS);
                                fscanf(file,"%lf", &fW);
                                fscanf(file,"%lf", &fE);
                                fscanf(file,"%lf", &fT);
                                fscanf(file,"%lf", &fB);
                                fscanf(file,"%lf", &fNW);
                                fscanf(file,"%lf", &fNE);
                                fscanf(file,"%lf", &fTN);
                                fscanf(file,"%lf", &fBN);
                                fscanf(file,"%lf", &fSW);
                                fscanf(file,"%lf", &fSE);
                                fscanf(file,"%lf", &fTS);
                                fscanf(file,"%lf", &fBS);
                                fscanf(file,"%lf", &fTW);
                                fscanf(file,"%lf", &fBW);
                                fscanf(file,"%lf", &fTE);
                                fscanf(file,"%lf", &fBE);
                                fscanf(file,"%i", &flag);
                                fscanf(file,"%lf", &fill);
                                fscanf(file,"%lf", &mass);
                                fscanf(file,"%lf", &nX);
                                fscanf(file,"%lf", &nY);
                                fscanf(file,"%lf", &nZ);
                                fscanf(file,"%lf", &pX);
                                fscanf(file,"%lf", &pY);
                                fscanf(file,"%lf", &pZ);
                                fscanf(file,"%i", &noNgbrs);
                                fscanf(file,"%i", &bubbleIndex);
                                fscanf(file,"%lf", &initV);
                                fscanf(file,"%lf", &V);
                                fscanf(file,"%lf", &rhoGas);
                                fscanf( file, "%*[^\n]s\n");
                                //std::cerr<<fN<<" "<<fSW<<" "<<flag<<" "<<fill<<" "<<bubbleIndex<<" "<<rhoGas<<std::endl;

                                std::vector<PatchCoordinate> paCo = GetLocalPatchCoordinates(x,y,z,sim_);
                                for (Uint l=0; l<paCo.size(); l++){
                                        if(!patchField_.IsNeeded(paCo[l].pX,paCo[l].pY,paCo[l].pZ)) continue;
                                        patchField_.AddApp(paCo[l].pX,paCo[l].pY,paCo[l].pZ,FREE_SURFACE);
                                        CalcPatch& patch = *patchField_.GET_PATCH3(paCo[l].pX,paCo[l].pY,paCo[l].pZ);
                                        PDFField& src = patch.GetSrcField();
                                        PDFField& dst = patch.GetDestField();
                                        FlagField<Flag>& f = patch.GetFlagField();
                                        FlagField<freeSurface::FSFlag>& fs = patch.GetFSFlags(true);
                                        freeSurface::FSBubbles &bubbles = patch.GetBubbles();
                                        if (! ISNAN(fC)) src.Set(paCo[l].x,paCo[l].y,paCo[l].z,C,fC);
                                        if (! ISNAN(fN)) src.Set(paCo[l].x,paCo[l].y,paCo[l].z,N,fN);
                                        if (! ISNAN(fS)) src.Set(paCo[l].x,paCo[l].y,paCo[l].z,S,fS);
                                        if (! ISNAN(fW)) src.Set(paCo[l].x,paCo[l].y,paCo[l].z,W,fW);
                                        if (! ISNAN(fE)) src.Set(paCo[l].x,paCo[l].y,paCo[l].z,E,fE);
                                        if (! ISNAN(fT)) src.Set(paCo[l].x,paCo[l].y,paCo[l].z,T,fT);
                                        if (! ISNAN(fB)) src.Set(paCo[l].x,paCo[l].y,paCo[l].z,B,fB);
                                        if (! ISNAN(fNW)) src.Set(paCo[l].x,paCo[l].y,paCo[l].z,NW,fNW);
                                        if (! ISNAN(fNE)) src.Set(paCo[l].x,paCo[l].y,paCo[l].z,NE,fNE);
                                        if (! ISNAN(fTN)) src.Set(paCo[l].x,paCo[l].y,paCo[l].z,TN,fTN);
                                        if (! ISNAN(fBN)) src.Set(paCo[l].x,paCo[l].y,paCo[l].z,BN,fBN);
                                        if (! ISNAN(fSW)) src.Set(paCo[l].x,paCo[l].y,paCo[l].z,SW,fSW);
                                        if (! ISNAN(fSE)) src.Set(paCo[l].x,paCo[l].y,paCo[l].z,SE,fSE);
                                        if (! ISNAN(fTS)) src.Set(paCo[l].x,paCo[l].y,paCo[l].z,TS,fTS);
                                        if (! ISNAN(fBS)) src.Set(paCo[l].x,paCo[l].y,paCo[l].z,BS,fBS);
                                        if (! ISNAN(fBW)) src.Set(paCo[l].x,paCo[l].y,paCo[l].z,BW,fBW);
                                        if (! ISNAN(fBE)) src.Set(paCo[l].x,paCo[l].y,paCo[l].z,BE,fBE);
                                        if (! ISNAN(fTW)) src.Set(paCo[l].x,paCo[l].y,paCo[l].z,TW,fTW);
                                        if (! ISNAN(fTE)) src.Set(paCo[l].x,paCo[l].y,paCo[l].z,TE,fTE);
                                        if (! ISNAN(fill)) patch.GetFillLevels(true).Set(paCo[l].x,paCo[l].y,paCo[l].z,fill);
                                        if (! ISNAN(mass)) patch.GetMass().Set(paCo[l].x,paCo[l].y,paCo[l].z,mass);
                                        if (!ISNAN(nX) && !ISNAN(nY) && !ISNAN(nZ))
                                                patch.GetSurfaceNormals().Set(paCo[l].x,paCo[l].y,paCo[l].z,nX, nY, nZ);
                                        if (!ISNAN(pX) && !ISNAN(pY) && !ISNAN(pZ))
                                                patch.GetInterfacePoints().Set(paCo[l].x,paCo[l].y,paCo[l].z,pX, pY, pZ);
                                        patch.GetInterfaceNeighbors().Set(paCo[l].x,paCo[l].y,paCo[l].z,noNgbrs);
                                        //while (bubbleIndex > bubbles.GetNumBubbles()) bubbles.AddBubble(0.0,0.0,0.0);
                                        if (! ISNAN(bubbleIndex)) bubbles.Set(paCo[l].x,paCo[l].y,paCo[l].z,bubbleIndex);
                                        if (!ISNAN(initV) && !ISNAN(V) && !ISNAN(rhoGas)) bubbles.SetBubble(patch.GetPatchUID(),bubbleIndex,initV,V,rhoGas);
                                        if (flag & 4 ) f.Set(paCo[l].x,paCo[l].y,paCo[l].z,LIQUID);
                                        if (flag & 8 ) f.Set(paCo[l].x,paCo[l].y,paCo[l].z,INTERFACE);
                                        if (flag & 16) f.Set(paCo[l].x,paCo[l].y,paCo[l].z,GAS);
                                        if (flag & 32) fs.Add(paCo[l].x,paCo[l].y,paCo[l].z,freeSurface::CONV_I_L_1);
                                        if (flag & 64) fs.Add(paCo[l].x,paCo[l].y,paCo[l].z,freeSurface::CONV_I_L_2);
                                        if (flag & 128) fs.Add(paCo[l].x,paCo[l].y,paCo[l].z,freeSurface::CONV_I_G_1);
                                        if (flag & 256) fs.Add(paCo[l].x,paCo[l].y,paCo[l].z,freeSurface::CONV_I_G_2);
                                        if (flag & 512) fs.Add(paCo[l].x,paCo[l].y,paCo[l].z,freeSurface::CONV_A_I);
                                        if (flag & 1024) fs.Add(paCo[l].x,paCo[l].y,paCo[l].z,freeSurface::RED_I_L);
                                        if (flag & 2048) fs.Add(paCo[l].x,paCo[l].y,paCo[l].z,freeSurface::RED_I_G);
                                        if (flag & 4096) fs.Add(paCo[l].x,paCo[l].y,paCo[l].z,freeSurface::DIST);
                                        if (flag & 8192) fs.Add(paCo[l].x,paCo[l].y,paCo[l].z,freeSurface::DP_A);
                                        if (flag & 16384) fs.Add(paCo[l].x,paCo[l].y,paCo[l].z,freeSurface::DP_B);
                                        if(paCo[l].x==5 && paCo[l].y==4 && paCo[l].z==9) {std::cerr<<flag<<" "<<fs.Get(paCo[l].x,paCo[l].y,paCo[l].z)<<std::endl;};
                                        for (Uint dir=0;dir<D3Q19::Cellsize;dir++) {
                                                dst.Set(paCo[l].x,paCo[l].y,paCo[l].z,dir,9999999.0);
                                        }
                                        patch.GetFillLevels(false).Set(paCo[l].x,paCo[l].y,paCo[l].z,9999999.0);
                                }
                        }
                }
        }
        fclose(file);


        return;

}

void Domain::AddPlanesTo_Pe_Pov(const FileReader::BlockHandle &initHandle){
#ifndef NO_PE
        // epsi new version
        // add 6 planes to ensure that the obstacles cannot move outside of the domain
        pe::UnionID peUnion=NULL;
        bool visible=true;

        // epsi: this is important!!! directions have to be changed, otherwise the identifiers of the walls
        // are mixed up!!!
        Uint dir[6]={D3Q19::E,D3Q19::W,D3Q19::T,D3Q19::B,D3Q19::N,D3Q19::S};

        Real
                        D[6]={	 static_cast<int>(sim_.domainX)*cx[D3Q19::W]
                                 +static_cast<int>(sim_.domainY)*cy[D3Q19::W]
                                 +static_cast<int>(sim_.domainZ)*cz[D3Q19::W],
                                 0,
                                 static_cast<int>(sim_.domainX)*cx[D3Q19::B]
                                 +static_cast<int>(sim_.domainY)*cy[D3Q19::B]
                                 +static_cast<int>(sim_.domainZ)*cz[D3Q19::B],
                                 0,
                                 static_cast<int>(sim_.domainX)*cx[D3Q19::S]
                                 +static_cast<int>(sim_.domainY)*cy[D3Q19::S]
                                 +static_cast<int>(sim_.domainZ)*cz[D3Q19::S],
                                 0};

        Uint i=0;

        if (movingObstacles_.useBoundaryObjects_)
        {
                i=2;
        }

        if(movingObstacles_.useZPeriodic_)
        {
                for(;i<6;++i)
                {
                        if(i==2 || i==3)
                                continue;

                        pe_GLOBAL_SECTION{
                                peUnion = pe::createUnion(sim_.maxNumObstacleID++,visible);
                                peUnion->add(pe::createPlane(sim_.maxNumObstacleID++,-cx[dir[i]],-cy[dir[i]],-cz[dir[i]],
                                                             +D[i],
                                                             pe::createMaterial("planes"+Convert<std::string>(i),1.0,0.5,0.1,0.1),visible));
                        }
                        peUnion->setFixed(true);

                        // epsi: get plane information for boundaries
                        if (PovRayVis::IsActive())
                        {
                                std::string blockName;
                                pe::Vec3 camPos(0,0,0);
                                FileReader::Blocks boundaryBlocks,
                                                povBlocks,
                                                appBlocks;


                                switch(dir[i]) {
                                case E: blockName="east";   break;
                                case W: blockName="west";   break;
                                case T: blockName="top";    break;
                                case B: blockName="bottom"; break;
                                case N: blockName="north";  break;
                                case S: blockName="south";  break;
                                }
                                initHandle.GetBlocks(blockName,boundaryBlocks);
                                for (Uint numBlocks = 0; numBlocks < boundaryBlocks.size(); numBlocks++)
                                {

                                        //////////////
                                        // Visible? //
                                        //////////////
                                        if(boundaryBlocks[numBlocks].IsDefined("povvistype")){
                                                // If defined set the appearance of the plane
                                                Uint type =
                                                                boundaryBlocks[numBlocks].GetParameter<Uint>("povvistype");
                                                PovRayVis::Instance().SetObjectVisType( peUnion->getID(),type);
                                                PovRayVis::Instance().SetObjectVisType(peUnion->begin<pe::Plane>()->getID(),type);
                                                //		      peUnion->setVisible(true);
                                                //		      std::cout << "peUnion->getID() is: " << peUnion->getID() << std::endl;
                                                //		      std::cout << "peUnion->begin<pe::Plane>()->getID() is: " << peUnion->begin<pe::Plane>()->getID() << std::endl;
                                                //		      std::cout << "Type is: " << type << std::endl;

                                        }
                                        else
                                        {
                                                peUnion->setVisible(false); //Neue PE!
                                                //peUnion->setInvisible(); //Alte PE!
                                        }

                                }
                        }
                }
        }
        else
        {

                for(;i<6;++i){


                        pe_GLOBAL_SECTION{
                                peUnion = pe::createUnion(sim_.maxNumObstacleID++,visible);
                                peUnion->add(pe::createPlane(sim_.maxNumObstacleID++,-cx[dir[i]],-cy[dir[i]],-cz[dir[i]],
                                                             +D[i],
                                                             pe::createMaterial("planes"+Convert<std::string>(i),1.0,0.5,0.1,0.1),visible));
                        }
                        peUnion->setFixed(true);

                        // epsi: get plane information for boundaries
                        if (PovRayVis::IsActive())
                        {
                                std::string blockName;
                                pe::Vec3 camPos(0,0,0);
                                FileReader::Blocks boundaryBlocks,
                                                povBlocks,
                                                appBlocks;


                                switch(dir[i]) {
                                case E: blockName="east";   break;
                                case W: blockName="west";   break;
                                case T: blockName="top";    break;
                                case B: blockName="bottom"; break;
                                case N: blockName="north";  break;
                                case S: blockName="south";  break;
                                }
                                initHandle.GetBlocks(blockName,boundaryBlocks);
                                for (Uint numBlocks = 0; numBlocks < boundaryBlocks.size(); numBlocks++)
                                {

                                        //////////////
                                        // Visible? //
                                        //////////////
                                        if(boundaryBlocks[numBlocks].IsDefined("povvistype")){
                                                // If defined set the appearance of the plane
                                                Uint type =
                                                                boundaryBlocks[numBlocks].GetParameter<Uint>("povvistype");
                                                PovRayVis::Instance().SetObjectVisType( peUnion->getID(),type);
                                                PovRayVis::Instance().SetObjectVisType(peUnion->begin<pe::Plane>()->getID(),type);
                                                //		      peUnion->setVisible(true);
                                                //		      std::cout << "peUnion->getID() is: " << peUnion->getID() << std::endl;
                                                //		      std::cout << "peUnion->begin<pe::Plane>()->getID() is: " << peUnion->begin<pe::Plane>()->getID() << std::endl;
                                                //		      std::cout << "Type is: " << type << std::endl;

                                        }
                                        else
                                        {
                                                peUnion->setVisible(false); //Neue PE!
                                                //peUnion->setInvisible(); //Alte PE!
                                        }

                                }
                        }
                }
        }
#endif
}

bool TestCoord(const PatchCoordinate &coord,const FlagField<Uint > &flag){


        // If a cell is a NOSLIP cell and has for one main direction
        // 5 fluid nighbors (for obtaining correct mass flux)
        // Set it to REACT
        LOG_ERROR("FLAG "+STR(flag.GET_FLAG(coord.x,coord.y,coord.z))+"\n");
        if(flag.IS_FLAG(coord.x,coord.y,coord.z,NOSLIP)){
                Uint faceCount=0,
                                dirCount=0;
                // First test all lattice dirs, we may only have 5 fluid neighbours
                for(Uint d=0;d<19;++d)
                        if(flag.IS_FLAG(coord.x+cx[d],coord.y+cy[d],coord.z+cz[d],FLUID))++dirCount;
                LOG_ERROR("Dir count is "+STR(dirCount)+"\n");
                if(dirCount!=5) return false;
                // Next we have to ensure that they all belond to the same face
                for(Uint d=1;d<7;++d){
                        Uint di=0;
                        // Foreach face test the 5 lattice dirs
                        for(;di<5;++di)
                                if(!flag.IS_FLAG(coord.x+cx[d_per_d[d][di]],coord.y+cy[d_per_d[d][di]],coord.z+cz[d_per_d[d][di]],FLUID))break;
                        if(di==5)
                                ++faceCount;
                }
                LOG_ERROR("Face count "+STR(faceCount)+"\n");
                if(faceCount==1)return true;
        }
        return false;
}

void SetToReact(PatchField &patchField_,PatchCoordinate &coord){

        PatchID patch = patchField_.GetPatch(coord.pX,coord.pY,coord.pZ);
        FlagField<Uint > &flag=patch->GetField<FlagField<Uint> >(FIELD_FLAG_SRC);
        if(flag.IS_FLAG(coord.x,coord.y,coord.z,GHOST))
                flag.SET_FLAG(coord.x,coord.y,coord.z,REACT|GHOST);
        else
                flag.SET_FLAG(coord.x,coord.y,coord.z,REACT);
        LOG_ERROR("SET to REACT "+STRB(coord.pX)+STRB(coord.pY)+STRB(coord.pZ)+STRB(coord.x)+STRB(coord.y)+STRB(coord.z)+"\n");
}


void Domain::InitMixtures(FileReader &read){



        using namespace mix;
        enum {VALIDATE,TERM,REACT};

        if(MixData::NumSpecies()==0)return;

        LOG_E("Init Mixtures\n");

        //////////////////////////
        // Random Reaction Zones //
        //////////////////////////
        Uint count=MixData::NumOuterReact();
        Real totalReactSites=MixData::NumReact();

        Vector3<Uint> lowerPoint = MixData::LowerPoint(),
                        upperPoint = MixData::UpperPoint();

        ///////////////////
        // Root Process //
        ///////////////////
        if(mpi_.fluidWorkersRank==0){
                // Set a number of totalReactSites reaction cites
                while(count<totalReactSites){

                        LOG_ERROR("mpi "+STR(mpi_.fluidWorkersRank)+"\n");
                        std::vector<PatchCoordinate> localCoords;
                        PatchCoordinate coord;

#ifndef NO_PE
                        // Generate the random position
                        Uint x = pe::rand( lowerPoint[0],upperPoint[0] );
                        Uint y = pe::rand( lowerPoint[1],upperPoint[1] );
                        Uint z = pe::rand( lowerPoint[2],upperPoint[2] );
#else
                        Uint x = 0;
                        Uint y = 0;
                        Uint z = 0;
#endif
                        // Get the patch in which the accellatation cell lies
                        coord=GetOneLocalExistingPatchCoordinate(x,y,z,sim_);

#ifdef WALBERLA_USE_MPI

                        int valid=false;
                        Uint buffer[7]={VALIDATE,coord.pX,coord.pY,coord.pZ,coord.x,coord.y,coord.z} ;

                        ///////////////
                        // Cell on 0 //
                        ///////////////
                        if(patchField_.IsAllocated(coord.pX,coord.pY,coord.pZ)){
                                PatchID patch = patchField_.GetPatch(coord.pX,coord.pY,coord.pZ);
                                FlagField<Uint > &flag=patch->GetField<FlagField<Uint> >(FIELD_FLAG_SRC);
                                valid=TestCoord(coord,flag);
                        }

                        /////////////////
                        // Cell on !0  //
                        /////////////////
                        if(!valid&&patchField_.GetFluidRank(coord.pX,coord.pY,coord.pZ)!=0){
                                // Send Coord to responsible patch
                                MPI_Send(buffer,7,MPI_UNSIGNED,patchField_.GetFluidRank(coord.pX,coord.pY,coord.pZ),999,mpi_.comm_cart);
                                MPI_Status status;
                                // Recv from patch if Coord valid
                                MPI_Recv(&valid,1,MPI_INT,patchField_.GetFluidRank(coord.pX,coord.pY,coord.pZ),999,mpi_.comm_cart,&status);
                        }

                        ///////////
                        // VALID //
                        ///////////
                        if(valid){
                                ++count;
                                localCoords=GetLocalPatchCoordinates(x,y,z,sim_);
                                Uint size = localCoords.size();
                                for(Uint i=0;i<size;++i){
                                        if(patchField_.IsAllocated(localCoords[i].pX,localCoords[i].pY,localCoords[i].pZ))
                                                SetToReact(patchField_,localCoords[i]);
                                        else{
                                                int buffer[7]={REACT,
                                                               localCoords[i].pX,localCoords[i].pY,localCoords[i].pZ,
                                                               localCoords[i].x,localCoords[i].y,localCoords[i].z};
                                                MPI_Send(buffer,7,MPI_INT,patchField_.GetFluidRank(localCoords[i].pX,localCoords[i].pY,localCoords[i].pZ),999,mpi_.comm_cart);
                                        }
                                }
                        }
#else


                        // Get the patch where the coord lies in
                        if(!patchField_.IsAllocated(coord.pX,coord.pY,coord.pZ))continue;
                        PatchID patch = patchField_.GetPatch(coord.pX,coord.pY,coord.pZ);
                        FlagField<Uint > &flag=patch->GetField<FlagField<Uint> >(FIELD_FLAG_SRC);

                        /////////////////
                        // Test Coord ///
                        /////////////////
                        bool valid = TestCoord(coord,flag);

                        ///////////
                        // Valid //
                        ///////////
                        if(valid){
                                localCoords=GetLocalPatchCoordinates(x,y,z,sim_);
                                Uint size = localCoords.size();
                                for(Uint i=0;i<size;++i)
                                        SetToReact(patchField_,localCoords[i]);
                                ++count;
                        }
#endif

                }
        }
#ifdef WALBERLA_USE_MPI
        else{

                /////////////////////////////
                // All Proccesses except 0 //
                /////////////////////////////
                bool finished=false;

                while(!finished){

                        Uint tag;
                        Uint buffer[7];
                        MPI_Status status;
                        MPI_Recv(buffer,7,MPI_UNSIGNED,0,999,mpi_.comm_cart,&status);
                        PatchCoordinate coord(&buffer[1]);

                        tag = buffer[0];
                        switch(tag){

                        case VALIDATE:
                        {
                                PatchID patch = patchField_.GetPatch(coord.pX,coord.pY,coord.pZ);
                                FlagField<Uint > &flag=patch->GetField<FlagField<Uint> >(FIELD_FLAG_SRC);
                                int valid = TestCoord(coord,flag);
                                MPI_Send(&valid,1,MPI_INT,0,999,mpi_.comm_cart);
                                break;
                        }

                        case REACT:
                                SetToReact(patchField_,coord);
                                break;

                        case TERM:
                                finished=true;
                                break;
                        }
                }
        }

        if(mpi_.fluidWorkersRank==0){
                Uint buffer[7]={TERM};
                for(int p=1;p<mpi_.numprocsFluidWorkers;++p)
                        MPI_Send(buffer,7,MPI_INT,p,999,mpi_.comm_cart);
        }
#endif



        ////////////////////
        // SETUP OUTFLOW  //
        ////////////////////
        Vector3<Real> overallVelVec(0,0,0);

        ////////////////////////
        // Overall Inflow Vel //
        ////////////////////////
        FileReader::Blocks initBlocks;
        FileReader::Blocks wallBlocks[6];
        FileReader::Blocks bcBlocks[6];
        read.GetBlocks("init",initBlocks);

        initBlocks[0].GetBlocks("north",wallBlocks[0]);
        initBlocks[0].GetBlocks("east",wallBlocks[1]);
        initBlocks[0].GetBlocks("west",wallBlocks[2]);
        initBlocks[0].GetBlocks("south",wallBlocks[3]);
        initBlocks[0].GetBlocks("bottom",wallBlocks[4]);
        initBlocks[0].GetBlocks("top",wallBlocks[5]);


        //////////////////
        // Parse Blocks //
        //////////////////
        for(Uint i = 0;i<6;++i){
                for(Uint e = 0;e<wallBlocks[i].size();++e){
                        FileReader::Blocks wallBlock;
                        wallBlocks[i][e].GetBlocks("vel_in",wallBlock);
                        bcBlocks[i].insert(bcBlocks[i].end(),wallBlock.begin(),wallBlock.end());
                }
        }


        for(Uint side = 0 ;side<6;++side){

                Uint x0, x1, y0, y1, z0, z1;
                std::string sideStr,
                                startX,startY,endX,endY;
                Real ux_L, uy_L, uz_L;
                enum InflowTypes{PARABOL,BLOCK,SHEAR};
                PhysicalCheck::ParameterUnitRelation pu;
                PhysicalCheck::Equations equ;


                //////////////
                // SETUP PC //
                //////////////
                switch(side){
                case 0: sideStr="north";startX="x0_L";endX="x1_L";startY="z0_L";endY="z1_L";break;
                case 1: sideStr="east";startX="y0_L";endX="y1_L";startY="z0_L";endY="z1_L";break;
                case 2: sideStr="west";startX="y0_L";endX="y1_L";startY="z0_L";endY="z1_L";break;
                case 3: sideStr="south";startX="x0_L";endX="x1_L";startY="z0_L";endY="z1_L";break;
                case 4: sideStr="bottom";startX="x0_L";endX="x1_L";startY="y0_L";endY="y1_L";break;
                case 5: sideStr="top";startX="x0_L";endX="x1_L";startY="y0_L";endY="y1_L";break;
                }

                pu["x"]="m";
                pu["y"]="m";
                pu["z"]="m";
                pu["ux"]="m/s";
                pu["uy"]="m/s";
                pu["uz"]="m/s";
                pu["u"]="m/s";
                equ[equ.size()]="'UInflowFac' = 'InflowVecL' / ( ( ( 'InflowVecX' ^ 2 ) + ( 'InflowVecY' ^ 2 ) + ( 'InflowVecZ' ^ 2 ) ) ^ 0.5 )";
                equ[equ.size()]="'ux'         = 'InflowVecX' * 'UInflowFac'";
                equ[equ.size()]="'uy'         = 'InflowVecY' * 'UInflowFac'";
                equ[equ.size()]="'uz'         = 'InflowVecZ' * 'UInflowFac'";


                ///////////
                // FACES //
                ///////////
                for(Uint i = 0;i<bcBlocks[side].size();++i){

                        FileReader::Block pcBlock(bcBlocks[side][i].CloneBlock());
                        Uint type=BLOCK;
                        x0=x1=y0=y1=z0=z1=0;

                        // Get the type of inflow
                        if(bcBlocks[side][i].IsDefined("type")){
                                std::string typeS =ConvertToLowerCase(bcBlocks[side][i].GetParameter<std::string>("type"));
                                if(!typeS.compare("parabol"))
                                        type=PARABOL;
                                else
                                        if(!typeS.compare("shear"))
                                                type=SHEAR;
                                        else
                                                if(!typeS.compare("block"))
                                                        type=BLOCK;
                                                else
                                                        THROW_LOG_ERROR("Domain::SetProfile: Unkown profile type "+STR(typeS)+". Please specify either 'parabol', 'shear' or 'block'.\n");
                        }

                        ////////
                        // PC //
                        ////////
                        PhysicalCheck pc("Domain.cpp::InitMixtures("+sideStr+")");
                        pc.LearnInformation(pcBlock,pu,"",&equ);

                        x0 = pc.CheckAndCompleteParam<Uint>(startX,0,&PhysicalCheck::CheckGZInt);
                        x1 = pc.CheckAndCompleteParam<Uint>(endX,0,&PhysicalCheck::CheckGZInt);
                        y0 = pc.CheckAndCompleteParam<Uint>(startY,0,&PhysicalCheck::CheckGZInt);
                        y1 = pc.CheckAndCompleteParam<Uint>(endY,0,&PhysicalCheck::CheckGZInt);
                        ux_L = pc.CheckAndCompleteParam<Real>("ux_L",0,&PhysicalCheck::CheckVelLattice);
                        uy_L = pc.CheckAndCompleteParam<Real>("uy_L",0,&PhysicalCheck::CheckVelLattice);
                        uz_L = pc.CheckAndCompleteParam<Real>("uz_L",0,&PhysicalCheck::CheckVelLattice);


                        ////////////
                        //Sum Vel //
                        ////////////
                        Vector3<Real> vel(ux_L,uy_L,uz_L);
                        Real len= vel.length();
                        if(fabs(len)>1e-12)vel/=len;
                        for (Uint y=y0; y<=y1; y++)
                                for (Uint x=x0; x<=x1; x++) {
                                        Real len_cell=len;
                                        if(type==PARABOL)
                                                len_cell= len*16.0*(x-0.5-x0****)*(x1 - x0**** + 0.5 - (x - x0 + 1.0))
                                                                *(y-0.5-y0+1)*(y1 - y0+1 + 0.5 - (y - y0+1))
                                                                /
                                                                ( (x0-1.0-x1)*(x0-1.0-x1)
                                                                  *(y0-1.0-y1)*(y0-1.0-y1) );
                                        overallVelVec+=len_cell*vel;
                                }
                }
        }

        std::cerr<<"Specie momentums\n";
        for(Uint s =0;s<MixData::NumSpecies();++s){
                //MixData::specie_[s].total_mom_In_L = overallVel * r[s];
                MixData::specie_[s].total_mom_In_L = overallVelVec.length() * MixData::Specie(s).rho_I_L;
                MixData::specie_[s].total_mom_R_L  = totalReactSites * MixData::Specie(s).mom_R_L;
                MixData::total_mom_+=MixData::Specie(s).total_mom_In_L +  MixData::Specie(s).total_mom_R_L;
                if(MixData::Specie(s).total_mom_In_L+MixData::Specie(s).total_mom_R_L<0)
                        THROW_LOG_ERROR("For Specie "+STR(s)+" there is less momentum comming in than reacting out");
                std::cerr<<s<<"| mom_in: "<<MixData::Specie(s).total_mom_In_L<<" mom_R: "<<MixData::Specie(s).total_mom_R_L<<std::endl;
                std::cerr<<s<<" overall "<<overallVelVec<<std::endl;
        }

        ////////////////////////////
        // Activision of Mixtures //
        ////////////////////////////
        for(Uint k=0;k<sim_.zNumPatches;++k)
                for(Uint j=0;j<sim_.yNumPatches;++j)
                        for(Uint i=0;i<sim_.xNumPatches;++i){
                                patchField_.AddApp(i,j,k,MIXTURES);

                                if(!patchField_.IsAllocated(i,j,k))continue;

                                // Get the current patch
                                PatchID patch = patchField_.GetPatch(i,j,k);

                                ////////////
                                // Fields //
                                ////////////
                                // Fetch the wrappers
                                MIFieldWrapper<PDFField> &pdfWrapper=
                                                patch->GetField<MIFieldWrapper<PDFField> >(FIELD_M_PDF);
                                MIFieldWrapper<DensField> &densWrapper=
                                                patch->GetField<MIFieldWrapper<DensField> >(FIELD_M_DENS);
                                // Get the field for the density and the pdf
                                // Also check that they have been created
                                PDFField **pdf=pdfWrapper.GetFields();
                                DensField **dens=densWrapper.GetFields();

                                Uint xSize=pdf[0]->XSize(),
                                                ySize=pdf[0]->YSize(),
                                                zSize=pdf[0]->ZSize();


                                for(Uint k=0;k<zSize;++k)
                                        for(Uint j=0;j<ySize;++j)
                                                for(Uint i=0;i<xSize;++i){

                                                        for(Uint s = 0;s<MixData::NumSpecies(); ++s){
                                                                //////////////////
                                                                // INIT DENSITY //
                                                                //////////////////
                                                                Real rho=1.0/MixData::NumSpecies();
                                                                if(MixData::total_mom_!=0)rho =  (MixData::Specie(s).total_mom_In_L +  MixData::Specie(s).total_mom_R_L)/MixData::total_mom_;

                                                                //MixData::Rho_O_L()*MixData::Specie(s).x_I;
                                                                dens[s]->SET_DENS(i,j,k,rho);

                                                                //////////////
                                                                // PDF INIT //
                                                                //////////////
                                                                for( unsigned int l=0; l<D3Q19::Cellsize; ++l ){
                                                                        pdf[s]->SET_PDF(i,j,k,l,Eq_s(rho,Vector3<Real>(0.0,0.0,0.0),l,MixData::Specie(s).xi));
                                                                }
                                                        }
                                                }
                        }





        ////////////////////
        // Boundary Setup //
        ////////////////////
        for(Uint k=0;k<sim_.zNumPatches;++k)
                for(Uint j=0;j<sim_.yNumPatches;++j)
                        for(Uint i=0;i<sim_.xNumPatches;++i){
                                if(!patchField_.IsAllocated(i,j,k))continue;
                                PatchID patch = patchField_.GetPatch(i,j,k);
                                patch->BuildBC();
                        }
        LOG_E("Init Mixtures End\n");

}// end -> void Domain::InitMixtures(FileReader &read)





//#ifdef TURB_DECAY

inline void Domain::UpdateVeloctiy(
                              const int& i ,
                              const int& j ,
                              const int& k ,
                              const double& ux ,
                              const double& uy ,
                              const double& uz ,
                              const double& rho
                            )
{
//#ifndef TURB_DECAY

//#endif

//	if( i < 1 ||  i >= (sim_.domainX) ||
//		j < 1 ||  j >= (sim_.domainY) ||
//		k < 1 ||  k >= (sim_.domainZ) )   return false;


// 	Log  ("----> AddToBouzidiCellsListSetNeighInsideCellFlag ----> global-Pos (i,j,k) : "+
// 		   Convert<std::string> (i) + "," +
// 		   Convert<std::string> (j) + "," +
// 		   Convert<std::string> (k) + "\n"   );

        // ================
        // Cell(i.j,k)
        // ================
// 	std::cout << "Get the patch in which the cell(i,j,k) lies\n";

        //const
        PatchCoordinate coord = GetOneLocalExistingPatchCoordinate (i+1,j+1,k+1, sim_ ) ;


        if( patchField_.IsAllocated(coord.pX,coord.pY,coord.pZ) )
	{
        	// Get the patch where the coord lies in
       	 	PatchID patch = patchField_.GetPatch(coord.pX,coord.pY,coord.pZ);
	        // added recently.
        	// ================================================================
        	// 	FlagField<Uint > &check_tmp = patch->GetFlagField();
       	 	// ================================================================
        	FlagField<Uint > &f = patch->GetFlagField();
// channel flow :
//        if ( //f.GET_FLAG (coord.x,coord.y,coord.z) == NEAR_OBST ||
//             f.GET_FLAG (coord.x,coord.y,coord.z) & 1   ||
//             f.GET_FLAG (coord.x,coord.y,coord.z) == PERIODIC  ||
//             f.GET_FLAG (coord.x,coord.y,coord.z) == GHOST     ||
//             f.GET_FLAG (coord.x,coord.y,coord.z) == NOSLIP          )
//            return false;
    		if ( //f.GET_FLAG (coord.x,coord.y,coord.z) == NEAR_OBST ||
        		 //f.GET_FLAG (coord.x,coord.y,coord.z) & 1   ||
        		 //f.GET_FLAG (coord.x,coord.y,coord.z) == PERIODIC  ||
       			 // f.GET_FLAG (coord.x,coord.y,coord.z) == GHOST     ||
         		f.GET_FLAG (coord.x,coord.y,coord.z) != NOSLIP          )
        	{

        		f.REMOVE_FLAG (coord.x,coord.y,coord.z ,ALL_FLAGS);
        		f.SET_FLAG    (coord.x,coord.y,coord.z ,walberla::FLUID   ); // ACC
        		// Get the patch where the coord lies in, and update the flag value.
        		///TODO avoid  NEAR_OBST  cells
		        VelField& fvelField  =  patch->GetVelField();
       			DensField &densField =  patch->GetDensField () ;
        		const Vector3<Real> myVect(ux , uy, uz );
        		//fvelField.SET_VEL (coord.x,coord.y,coord.z , ux , uy, uz  );
        		fvelField.SET_VEL_VEC (coord.x,coord.y,coord.z ,  myVect );
       			densField.SET_DENS    (coord.x,coord.y,coord.z ,  rho  );
		}
	}


}




inline void Domain::InitVelField()
{

    const std::string fileName = this->my_InitialBCsFile ;
    std::ifstream ifs ( (fileName).c_str()  , std::ifstream::in);
    if (FileExists(  (char* )fileName.c_str()  ) )
    {
        const double dx    =  sim_.dx ;
        const double dt    =  sim_.dt ;

        //const double coeff =  (dt/dx)*1.04466943432893 ; // channel flow
        const double coeff =  (dt/dx);


        long double ux_min  =  1000000  ; long double   uy_min  =  1000000 ; long double  uz_min  =  1000000  ;
        long double ux_max  = -1000000  ; long double   uy_max  = -1000000 ; long double  uz_max  = -1000000  ;

        long double Umin = 1000000 ;
        long double Umax = -1000000;

        double rho = sim_.rho ;

        double coeff_x = this->m_scale_factor_x ;
        double coeff_y = this->m_scale_factor_y ;
        double coeff_z = this->m_scale_factor_z ;


        if (m_scale_factor_x == 1.0 )
        //coeff_x = sim_.domainX / (2* PI_NUMBER  ) ;// valid  only for HiT case
        coeff_x = sim_.domainX / ( sim_.xLength  ) ;// valid  only for HiT case
        if (m_scale_factor_x == 1.0 )
        coeff_y = sim_.domainY / ( sim_.yLength    ) ;// valid only for HiT case
        if (m_scale_factor_x == 1.0 )
        coeff_z = sim_.domainZ / ( sim_.zLength   ) ;// valid only for HiT case

        LogMessage  ( "fileName\t" + fileName ) ;
        int count = 0;
        while   (ifs.good())
        {
                std::cout << "read fome file\n" ;

                std::string line ;


                while (  std::getline(ifs, line)  )
                {
                        // evoid empty, and comment lines i
        //              #ifdef DEBUG
                        if ( line.empty() ) continue ;
                        if ( line.find ("#")  != std::string::npos ) continue ;
                        if ( line.find ("//") != std::string::npos ) continue ;
        //                                #endif
                        std::stringstream tmp ;
                        tmp  << line  ;

                        long double x ,y ,z , ux ,uy ,uz  ;

                        tmp >> x  >> y  >> z  >> ux  >> uy  >> uz ;//>> rho    ;

                        ux_min = min ( ux_min , ux ) ;
                        uy_min = min ( uy_min , uy ) ;
                        uz_min = min ( uz_min , uz ) ;

                        ux_max = max ( ux_max , ux ) ;
                        uy_max = max ( uy_max , uy ) ;
                        uz_max = max ( uz_max , uz ) ;

                        Umin   = min ( Umin,  sqrt ( ux*ux + uy*uy + uz*uz ) ) ;
                        Umax   = max ( Umax,  sqrt ( ux*ux + uy*uy + uz*uz ) ) ;

        /*
                        if(x  == 5.52233124  && y  == 6.25864172  &&  z  == 6.25864172  )
                        {
                        std::cerr  << "line: "<< count  <<
                                "\tx\t"   <<  (x)   <<
                                "\ty\t"  <<  (y)    <<
                                "\tz\t"  <<  (z)     <<
                                "\tux\t" <<  ux  <<
                                "\tuy\t" <<  uy  <<
                                "\tuz\t" <<  uz  <<
                                "\trho\t"<<  rho <<
                                        "\n"  ;
                        }*/
        #ifdef DEBUG
                        if(ux  == 0.0  && uy  == 0.0  &&  uz  == 0.0  )
                        {
                                std::cerr  << "line: "<< count  <<
                                        std::setprecision(std::numeric_limits< double>::digits10 + 1) <<
                                        "\tx\t"  <<  (x)   <<
                                        "\ty\t"  <<  (y)    <<
                                        "\tz\t"  <<  (z)     <<
                                        "\tux\t" <<  ux  <<
                                        "\tuy\t" <<  uy  <<
                                        "\tuz\t" <<  uz  <<"\n"  ;
                        }
        #endif
                        //
                        // set the velocity according to their coord.
                        //

                        UpdateVeloctiy ( int (  (x) * coeff_x   ),
                                        int (  (y) * coeff_y   ),
                                        int (  (z) * coeff_z   ),
                                                ux*coeff ,//+ 0.13*ux*coeff,
                                                uy*coeff ,//+ 0.13*uy*coeff,
                                                uz*coeff ,//+ 0.13*uz*coeff ,
                                                rho
                        );

                        ++ count ;
                        }
                }
                // 		return true;
                std::cerr  << "================================="<<  "\n" ;
                std::cerr  << "Initialization routines for U" <<  "\n" ;
                std::cerr  << "================================="<<  "\n" ;
                std::cerr  << "      input file :\t"  << fileName <<  "\n" ;
                std::cerr  << "      scale_factor_x: \t" << m_scale_factor_x  << "\n" ;
                std::cerr  << "      scale_factor_y: \t" << m_scale_factor_y  << "\n" ;
                std::cerr  << "      scale_factor_z: \t" << m_scale_factor_z  << "\n" ;
                std::cerr  << "      coeff_x: \t " <<    coeff_x    << "\n" ;
                std::cerr  << "      coeff_y: \t " <<    coeff_y    << "\n" ;
                std::cerr  << "      coeff_z: \t " <<    coeff_z    << "\n" ;
                std::cerr  << "      rho: \t " <<     rho    << "\n" ;
                std::cerr  << "      dx: \t " <<     dx    << "\n" ;
                std::cerr  << "      dt: \t " <<     dt    << "\n" ;
                std::cerr  << "      coeff ((dt/dx)): \t " <<  coeff       << "\n" ;
                std::cerr  << "      Umin ( ux,ux,uz):  \t " <<      "(" << ux_min <<","<< uy_min << ","<< uz_min   << ")  m/s\n" ;
                std::cerr  << "      Umax ( ux,ux,uz):  \t " <<      "(" << ux_max <<","<< uy_max << ","<< uz_max   << ")  m/s\n" ;
                std::cerr  << "      Umin: \t " <<     Umin   << " m/s\n" ;
                std::cerr  << "      Umax: \t " <<     Umax   << " m/s\n" ;
                std::cerr  << "      Umin(lbm): \t " <<     Umin*coeff   << " [-]\n" ;
                std::cerr  << "      Umax(lbm): \t " <<     Umax*coeff   << " [-]\n" ;
                std::cerr  << "      file  contains: \t " <<   count  << " lines ("<<  (int) pow (count, (1.0/3.0))+1 << ") ^3  \n" ;
                #ifndef WALE_MODEL
                std::cerr << "      Smagorinsky model,  sim.csmag =   " <<  sim_.csmag  << "\n";
                #else
                std::cerr << "      WALE  model,  sim.c_W =   " <<  0.325  << "\n";
                #endif
                #ifdef FINITE_DIFFERENCE
                std::cerr << "      use finite difference to calculate rate stres-tensor   \n";
                #else
                std::cerr << "      no finite difference\n";
                #endif
                std::cerr  << "================================="<<  "\n" ;

        }
        // else {
                ifs.close();//  close  the file anywhy
                // throw runtime_error("Could not open initial velocity field data file (turb decay case!!!)");
 		//return false;
        // }

}


//
// write the whole velocity field as row format
// x y z ux uy uz
// used for post-processing --> homogenouse isotropic turbulent decay case
// the domain size should be size power of 2

 inline void Domain::WriteVelocityRow( const int time_step)
 {
      const double lbm_physic_u =  (sim_.dx/sim_.dt) ; // 0.002037183
      /*const double valiTime[17] = {
             0,
             0.001 ,
             0.002 ,
             0.06 ,
             0.13 ,
             0.20 ,
             0.28 ,
             0.36 ,
             0.45 ,
             0.55 ,
             0.60 ,
             0.77 ,
             0.89 ,
             1.01 ,
             1.15 ,
             1.45 ,
             1.5
      }  ;*/

      const double valiTime[28] = {
          0.001 ,// cherif, no dns
          0.060719,
          0.127009,
          0.199389,
          0.276189,
          0.362929,
          0.451229,
          0.549199,
          0.652549,
          0.768349,
          0.888119,
          1.013659,
          1.154109,
          1.293499,
          1.449189,
          1.619549,
          1.799409,
          1.972449,
          2.164409,
          2.364969,
          2.564929,
          2.792529,
          3.036749,
          3.281619,
          3.545939,
          3.836329,
          4.150329,
          4.461899
      };


       bool mathTimeStep = false;

       for (int i = 0 ; i < 28 ; ++i)
            if ( (time_step) == (int) (valiTime [i]/sim_.dt )   )
                mathTimeStep = true;

       if  (mathTimeStep)
       {

         std::stringstream fn;
         #ifdef WALBERLA_USE_MPI
          fn    << "data_"<< time_step*sim_.dt << "_proc" << mpi_.fluidWorkersRank <<".dat" ;
         #else
          fn    << "data_"<< time_step*sim_.dt << ".dat" ;
         #endif

         std::ofstream f( fn.str().c_str(), std::ios::out ) ;

           if(f.fail() )
             throw runtime_error("Could not open output file.");

         unsigned int cnt = 0;


//         const Real Factor_1 = (2.0*PI_NUMBER - (PI_NUMBER/128.0) ) / (sim_.domainX -0.5 ) ;
//         const Real Factor_2 = ( PI_NUMBER/128.0 ) ;


// secure
         //const Real Factor_1 = (2.0*PI_NUMBER - (PI_NUMBER/128.0) ) / (sim_.domainX  ) ;
         //const Real Factor_2 = ( PI_NUMBER/128.0 ) ;

         const Real Factor_1 = (2.0*PI_NUMBER - (PI_NUMBER/sim_.domainX ) ) / (sim_.domainX  ) ;
         const Real Factor_2 = ( PI_NUMBER/sim_.domainX ) ;

         std::cout << "Factor_1 " << Factor_1 << "\n";
         std::cout << "Factor_2 " << Factor_2 << "\n";

        // f << "#i \t j \t k \t x \t y \t z \t xp \t yp \t zp\t \tvel[x]\tvel[y]\tvel[z]\n";

//         PatchIter patchIt=patchField_.GetBeginLocalPatches();
//         PatchIter patchEnd=patchField_.GetEndLocalPatches();
//         for(;patchIt!=patchEnd;++patchIt)
//         {
//             const VelField &velField     = patchIt->GET_PATCH()->GetField<VelField>(FIELD_VELOCITY);
//             const FlagField<Uint > &flag = patchIt->GET_PATCH()->GetField<FlagField<Uint> >(FIELD_FLAG_SRC);

//             const int xStart = patchIt->GET_PATCH()->GetXStart();
//             const int yStart = patchIt->GET_PATCH()->GetYStart();
//             const int zStart = patchIt->GET_PATCH()->GetZStart();

//             for(Uint k = 0; k<velField.ZSize()-1;++k)
//             {
//                const Real z = k -0.5 + zStart ;
//                for(Uint j = 0; j<velField.YSize()-1;++j)
//                {
//                    const Real y = j -0.5 +  yStart ;
//                    for(Uint i = 0; i<velField.XSize()-1;++i)
//                    {
//                      if(flag.IS_FLAG(i,j,k,FLUID  ))
//                      {
//                          const Real x = i -0.5 + xStart ;
//                          Vector3<Real> vel ;
//                          velField.GET_VEL (i,j,k,vel) ;
//                          const Real dx = sim_.dx;
//                          f     << i << "\t" <<
//                                   j << "\t" <<
//                                   k << "\t\t" ;
//                          f     << x << "\t" <<
//                                   y << "\t" <<
//                                   z << "\t\t" ;
//                          f   << Factor_1 * (x-0.5)+ Factor_2 << "\t" <<
//                                 Factor_1 * (y-0.5)+ Factor_2 << "\t" <<
//                                 Factor_1 * (z-0.5)+ Factor_2 << "\t"  << vel [0]*lbm_physic_u << " " << vel [1]*lbm_physic_u << " " << vel [2]*lbm_physic_u << "\n" ;
//                      }
//                   }
//                }
//             }
//         }

         f << std::fixed;
         for (Uint z=0; z<sim_.domainZ ; ++z) {
            for (Uint y=0; y<sim_.domainY ; ++y) {
                for (Uint x=0; x<sim_.domainX ; ++x)
                {

                    const PatchCoordinate paCo = GetOneLocalExistingPatchCoordinate(x+1,y+1,z+1,sim_);
                    if( ! patchField_.IsAllocated(paCo.pX,paCo.pY,paCo.pZ) )
                           continue ;

                    CalcPatch& patch = *patchField_.GET_PATCH3(paCo.pX, paCo.pY, paCo.pZ);
                    const VelField&  velocity  = patch.GetVelField ();
                    const DensField& densField = patch.GetDensField();

                    Vector3<Real> vel ;
                    velocity.GET_VEL ( paCo.x,paCo.y,paCo.z  ,vel) ;
                    const Real rho = densField.GET_DENS  ( paCo.x,paCo.y,paCo.z ) ;


//                    if ( time_step ==  (int) ( 2.164409/sim_.dt )   )
//                    {
//                         f << std::setprecision(std::numeric_limits< double>::digits10 + 1) <<
//                            vel [0]*lbm_physic_u << "  " <<
//                            vel [1]*lbm_physic_u << "  " <<
//                            vel [2]*lbm_physic_u << "  " <<
//                            rho  << "\n" ;
//                    }
//                    else{
                         f << std::setprecision(std::numeric_limits< double>::digits10 + 1) <<
                             vel [0]*lbm_physic_u << "  " <<
                             vel [1]*lbm_physic_u << "  " <<
                             vel [2]*lbm_physic_u << "\n" ;
//                    }

                }
            }
         }

f.close() ;
       }


 }




 inline void Domain::WriteVelocityRowdebug ( const std::string & Msg )
 {
         const double lbm_physic_u =  (sim_.dx/sim_.dt) ; // 0.002037183

         std::stringstream fn;
         #ifdef WALBERLA_USE_MPI
          fn    << "data_"<< Msg<< "_proc" << mpi_.fluidWorkersRank <<".dat" ;
         #else
          fn    << "data_"<< Msg<< ".dat" ;
         #endif

         std::ofstream f( fn.str().c_str(), std::ios::out ) ;

           if(f.fail() )
             throw runtime_error("Could not open output file.");

         unsigned int cnt = 0;


//         const Real Factor_1 = (2.0*PI_NUMBER - (PI_NUMBER/128.0) ) / (sim_.domainX -0.5 ) ;
//         const Real Factor_2 = ( PI_NUMBER/128.0 ) ;

         //const Real Factor_1 = (2.0*PI_NUMBER - (PI_NUMBER/128.0) ) / (sim_.domainX  ) ;
         //const Real Factor_2 = ( PI_NUMBER/128.0 ) ;

         const Real Factor_1 = (2.0*PI_NUMBER - (PI_NUMBER/sim_.domainX) ) / (sim_.domainX  ) ;
         const Real Factor_2 = ( PI_NUMBER/sim_.domainX ) ;

         std::cout << "Factor_1 " << Factor_1 << "\n";
         std::cout << "Factor_2 " << Factor_2 << "\n";

    //    f << "#i \t j \t k \t x \t y \t z \t xp \t yp \t zp\t \tvel[x]\tvel[y]\tvel[z]\n";

         f << std::fixed;
         for (Uint z=0; z<sim_.domainZ ; ++z) {
            for (Uint y=0; y<sim_.domainY ; ++y) {
                for (Uint x=0; x<sim_.domainX ; ++x)
                {

                    const PatchCoordinate paCo = GetOneLocalExistingPatchCoordinate(x+1,y+1,z+1,sim_);
                    if( ! patchField_.IsAllocated(paCo.pX,paCo.pY,paCo.pZ) )
                           continue ;

                    CalcPatch& patch = *patchField_.GET_PATCH3(paCo.pX, paCo.pY, paCo.pZ);
                    const VelField& velocity = patch.GetVelField ();

                    Vector3<Real> vel ;
                    velocity.GET_VEL ( paCo.x,paCo.y,paCo.z  ,vel) ;

                    //	f << std::setprecision(10)  <<
                    //	 Factor_1 * (x)+ Factor_2 << "  " <<
                    //     Factor_1 * (y)+ Factor_2 << "  " <<
                    //     Factor_1 * (z)+ Factor_2 << "  "
                    f << std::setprecision(16) <<
                         vel [0]*lbm_physic_u << "  " <<
                         vel [1]*lbm_physic_u << "  " <<
                         vel [2]*lbm_physic_u << "\n" ;


                }
            }
         }

f.close() ;



 }


 
 
inline void Domain::DivergenceCheck(const int tStep)
{ 
    int count  = 0 ;
    double src = 0.0;

//     for (Uint z=0; z<sim_.domainZ ; ++z) {
//         for (Uint y=0; y<sim_.domainY ; ++y) {
//             for (Uint x=0; x<sim_.domainX ; ++x)
//             {
//                     const PatchCoordinate paCo = GetOneLocalExistingPatchCoordinate(x+1,y+1,z+1,sim_);
//                     if( ! patchField_.IsAllocated(paCo.pX,paCo.pY,paCo.pZ) )
//                            continue ;
// 
//                     CalcPatch& patch = *patchField_.GET_PATCH3(paCo.pX, paCo.pY, paCo.pZ);
//                     const VelField& velocity = patch.GetVelField ();
// 
//                     Vector3<Real> vel ;
//                     velocity.GET_VEL ( paCo.x,paCo.y,paCo.z  ,vel) ;
//                     
//                     const double dUx = 0.5 *(velField.GET_VEL (i+1,j,k, X) - velField.GET_VEL (i-1,j,k, X) );
//                     const double dUy = 0.5 *(velField.GET_VEL (i,j+1,k, Y) - velField.GET_VEL (i,j-1,k, Y) );
//                     const double dUz = 0.5 *(velField.GET_VEL (i,j,k+1, Z) - velField.GET_VEL (i,j,k-1, Z) );
// 
//                     src = dUx + dUy + dUz ;
//                     if (src > 1.0e-4)
//                         ++count;
// 
// 
//             }
//         }
//     }

    std::stringstream	fileName ;
    fileName.str("");
    fileName <<"Divergence_"<< tStep <<".dat";

    std::ofstream  Divergence;
    Divergence.open ( fileName.str().c_str() , std::ofstream::out ); 

    PatchIter patchIt=patchField_.GetBeginLocalPatches();
    PatchIter patchEnd=patchField_.GetEndLocalPatches();
    for(;patchIt!=patchEnd;++patchIt)
    {
        const VelField &velField     = patchIt->GET_PATCH()->GetField<VelField>(FIELD_VELOCITY);
        const FlagField<Uint > &flag = patchIt->GET_PATCH()->GetField<FlagField<Uint> >(FIELD_FLAG_SRC);
        CalcPatch& patch = *(patchIt->GetPatch());

        const int xStart = patchIt->GET_PATCH()->GetXStart();
        const int yStart = patchIt->GET_PATCH()->GetYStart();
        const int zStart = patchIt->GET_PATCH()->GetZStart();

        const Uint xSize=velField.XSize();
        const Uint ySize=velField.YSize();
        const Uint zSize=velField.ZSize();

        for(Uint k = 1; k< zSize-1 ;++k)
        {
            for(Uint j = 1; j< ySize-1;++j)
            {
                for(Uint i = 1; i< xSize-1 ;++i)
                {
                    if(flag.IS_FLAG(i,j,k,FLUID  ))
                    {
                        // LBM units !!!
                        //const Uint x = i + patch.GetXStart();
                        //const Uint y = j + patch.GetYStart();
                        //const Uint z = k + patch.GetZStart();
/*
                        //src = (u[ip1] - u[flat])/dx + (v[jp1] - v[flat])/dy + (w[kp1]-w[flat])/dz;
                        const double dUx = 0.5 *(velField.GET_VEL (i+1,j  ,k  ,X)-velField.GET_VEL(i-1,j,k, X) );
                        const double dUy = 0.5 *(velField.GET_VEL (i  ,j+1,k  ,Y)-velField.GET_VEL(i,j-1,k, Y) );
                        const double dUz = 0.5 *(velField.GET_VEL (i  ,j  ,k+1,Z)-velField.GET_VEL(i,j,k-1, Z) );
*/
                        const double dUx = (velField.GET_VEL (i+1,j  ,k  ,X))-(velField.GET_VEL(i,j,k, X) );
                        const double dUy = (velField.GET_VEL (i  ,j+1,k  ,Y))-(velField.GET_VEL(i,j,k, Y) );
                        const double dUz = (velField.GET_VEL (i  ,j  ,k+1,Z))-(velField.GET_VEL(i,j,k, Z) );
                        src = dUx + dUy + dUz ;
                        if (src > 1.0e-4){
                            Divergence  << count << " "
                                       << src  << " "
                                       << dUx << " " 
                                       << dUz << " " 
                                       << dUz << "\n";
                            ++count;
                        }
                    }
                }
            }
         }
     }
     std::cerr << "Number of cells with divergence ( " << tStep << " ) = " << count << "\n";
     Divergence.close ();
}
 
 
 
#include "TaylorGreenVortex.h"



 inline void Domain::TaylorGreenVortex( const int time_step)
 {

     std::cout << "Domain::TaylorGreenVortex( const int time_"<< time_step << ")\n";
     const Real dx    =  sim_.dx ;
     const Real dt    =  sim_.dt ;
     const Real coeff =  (dt/dx) ;

     const Real CurrentTtime = dt*time_step ;

     const Real Lx = sim_.xLength;
     const Real Ly = sim_.yLength;
     const Real Lz = sim_.yLength;

     const Real Kx =  2.0* PI /Lx;
     const Real Ky =  2.0* PI /Ly;
     const Real Kz =  2.0* PI /Lz;

     const Real K = std::sqrt  ( Kx*Kx + Ky*Ky +  Kz*Kz  ) ;

     const  Real a =  2.0* PI /sim_.domainX ;
     const  Real U0=  ( 1.0/(1000.0*a) ) ;



     const Real rho  = sim_.rho;

     const double P0  = 100 ;


     std:cout << "\nrho \t" << rho << "\n" <<
                 "a  \t" << a  <<"\n" <<
                 "U0  \t" << U0  <<"\n" <<
                 "visc physic  \t"  <<  sim_.nu    <<"\n" <<
                 "visc lattice \t"  <<  sim_.nu_L  <<"\n" <<
                  "P0  \t"          <<  P0  << "\n";

    std::ofstream  TyGreen;
    TyGreen.open ( "TyGreen.txt", std::ofstream::out );
    TyGreen  << "#x  y  z  ux\tuy\tuz\n" ;
                  
#ifdef BELTRANI_FLOW
    const double l = 1.0;
    const double m = 1.0;
    const double k = 1.0;
    const double A = 1.0;
#endif

     PatchIter patchIt=patchField_.GetBeginLocalPatches();
     PatchIter patchEnd=patchField_.GetEndLocalPatches();
     for(;patchIt!=patchEnd;++patchIt)
     {
         VelField &velField     = patchIt->GET_PATCH()->GetField<VelField>(FIELD_VELOCITY);
         FlagField<Uint > &flag = patchIt->GET_PATCH()->GetField<FlagField<Uint> >(FIELD_FLAG_SRC);
         // velObstacles_.BCSetVelocities(velField,0);

     Real ke ;

         std::cout << "velField.ZSize() :\t" << velField.ZSize() <<"\n";
         std::cout << "velField.YSize() :\t" << velField.YSize() <<"\n";
         std::cout << "velField.XSize() :\t" << velField.XSize() <<"\n";

         CalcPatch& patch = *(patchIt->GetPatch());

         const int xStart = patchIt->GET_PATCH()->GetXStart();
         const int yStart = patchIt->GET_PATCH()->GetYStart();
         const int zStart = patchIt->GET_PATCH()->GetZStart();

         const Uint xSize=velField.XSize();
         const Uint ySize=velField.YSize();
         const Uint zSize=velField.ZSize();

         int count  = 0 ;

         for(Uint k = 0; k< zSize - 1 ;++k)
         {
            for(Uint j = 0; j< ySize - 1 ;++j)
            {
               for(Uint i = 0; i< xSize -1 ;++i)
               {
                  if(flag.IS_FLAG(i,j,k,FLUID  ))
                  {
                      // LBM units !!!
                      const Uint x = i + patch.GetXStart();
                      const Uint y = j + patch.GetYStart();
                      const Uint z = k + patch.GetZStart();

                     /* Vector3 <Real> tmp (SetUx(  U0,Kx , Ky, Kz, K, visc , x , y, z, CurrentTtime ) *coeff ,
                                          SetUy(  U0,Kx , Ky, Kz, K, visc , x , y, z, CurrentTtime ) *coeff ,
                                          0.0
                                          ) ;
                      velField.SET_VEL_VEC(i,j,k,tmp); */

                      Vector3 <Real> tmp ;
                      double  Pressure ;

#ifdef BELTRANI_FLOW
                            InitialBetraniCsPhyicalUnits(
                                        A,  // rho=1.0 kg/m^3
                                        U0,   // U0=1.0 m/s
                                        l, m , k, // L=1.0
                                        P0 ,  // P0=100.0
                                        (x-0.5)*dx,  (y-0.5)*dx ,(z-0.5)*dx,
                                        tmp[0], tmp[1] , tmp[2] , // ux,uy and uz
                                        Pressure,
                                        0.0  ,// time second
                                        sim_.nu );

#else // normal Taylor Green vortex

        #if TAYLOR_GREEN_VORTEX_2D
                            // i guess im using LBM units, so becurfull with units!!!
                            InitialTaylorGreenVortex(
                                        rho,
                                        U0,
                                        a ,
                                        P0 ,
                                        x,  y ,  z,
                                        tmp[0], tmp[1] ,  Pressure );

                if ( y == 50 && z == 2 )
                      TyGreen <<  x << " " << y << " "<<  z <<
                                   "\t"<< tmp[0] << " " << tmp[1] <<" " << tmp[2]  <<"\n" ;
                            
                            tmp[2]  = 0.0 ;

        #else // 3d
                            InitialTaylorGreenVortexBCsPhyicalUnits(
                                    rho,  // rho=1.0 kg/m^3
                                    U0,   // U0=1.0 m/s
                                    1.0 , // L=1.0 // L=1.0 , FIXME , maybe its  2.0* PI
                                    P0 ,  // P0=100.0
                                    x*dx,  y*dx ,  z*dx,
                                    tmp[0], tmp[1] ,  Pressure );

        #endif

                            //tmp[2]  = 0.0 ;// w =0

#endif

                      // scale to LBM units

                      #ifndef TAYLOR_GREEN_VORTEX_2D
//                         tmp *= coeff ; //FIXME im not sure about this !!!
                      #endif

                      velField.SET_VEL_VEC(i,j,k,tmp);
                      //UpdateVeloctiy (x,y,z,tmp[0], tmp[1] , tmp[2], Pressure );

//                      std::cout << "i,j,k \t" <<   i << " " << j << " "<<  k <<
//                                    " cell:  x,y,z: "<< x << " " << y << " "<<  z <<
//                                   " cell physical Coor :  xp,yp,zp: "<< x*dx << " " << y*dx << " "<<  z*dx <<
//                                   "\t U (x,y,z ) :  ("<< tmp[0] << " " << tmp[1] <<" " << tmp[2]  <<")\n" ;

                      //ke +=  tmp.sqrLength ();
                      ++count;
                  }
               }
            }
         }
         // TyGreen << "=========================\n";

     }


 //    for (Uint z=0; z<sim_.domainZ ; ++z) {
 //       for (Uint y=0; y<sim_.domainY ; ++y) {
 //           for (Uint x=0; x<sim_.domainX ; ++x)
 //             {

 //               const PatchCoordinate paCo = GetOneLocalExistingPatchCoordinate(x,y,z,sim_);

 //               if( ! patchField_.IsAllocated(paCo.pX,paCo.pY,paCo.pZ) )
 //                   continue ;

 //               CalcPatch& patch = *patchField_.GET_PATCH3(paCo.pX, paCo.pY, paCo.pZ);

 //               FlagField<Uint > &f = patch.GetFlagField() ;
 //              /*
 //               f.REMOVE_FLAG (  paCo.x, paCo.y, paCo.z,ALL_FLAGS);
 //               f.SET_FLAG    ( paCo.x, paCo.y, paCo.z,walberla::FLUID  ); // ACC
 //               */
 //               VelField& vel = patch.GetVelField ();
 //              // if ( f.Get (paCo.x, paCo.y, paCo.z) !=  PERIODIC )
 //               vel.Set (paCo.x, paCo.y, paCo.z,
 //                        SetUx(  U0,Kx , Ky, Kz, K, visc , x , y, z, CurrentTtime ) *coeff ,
 //                        SetUy(  U0,Kx , Ky, Kz, K, visc , x , y, z, CurrentTtime ) *coeff ,
 //                        0.0  );

 //               /*
 //               UpdateVeloctiy (x , y, z ,
 //                               SetUx(  U0,Kx , Ky, Kz, K, visc , x , y, z, CurrentTtime ) *coeff ,
 //                               SetUy(  U0,Kx , Ky, Kz, K, visc , x , y, z, CurrentTtime ) *coeff ,
 //                               0.0
 //                               ) ;*/

 //               }
 //       }
 //    }
 
 
 TyGreen .close();
 }



inline void Domain::RemoveACCFLAGES()
{
    PatchIter patchIt;
    for(patchIt=patchField_.GetBeginLocalPatches();patchIt!=patchField_.GetEndLocalPatches();++patchIt)
    {
        CalcPatchID patch = patchIt->GET_PATCH();
        FlagField<Flag> &flag =patch->GetFlagField();

        Uint zSize=patch->GetZSize();
        Uint ySize=patch->GetYSize();
        Uint xSize=patch->GetXSize();

        // check if all boundaries are set (not UNDEFINED any more)
        for(Uint z=0;z<zSize;++z) {
            for(Uint y=0;y<ySize;++y) {
                for(Uint x=0;x<xSize;++x){
                        if ( flag.GET_FLAG (x,y,z) == ACC )
                        {

                             flag.REMOVE_FLAG(x,y,z,ACC );
                             flag.SET_FLAG(x,y,z, LIQUID );
                        }
                 }
            }
        }
            //patch->GetSrcField().SetInvalidPDF(x,y,z);
      }
}

//#endif



#ifdef USE_STL_GE
//=====================================================================================================
/*!
//  \fn
//  \brief basically this function write the result of voxelisation process of an triangle-mesh
//	  * to warBLaba file format (Frank-geometry), which is basically binary-row file format
//  \param Row file name include the extension, example "Myfile.row"
//  \return return true is success, else return false
//  \author Cherif, Stefan
*/
//=====================================================================================================
// inline bool Domain::ConvertSTL2WarlabaRowFile ()
// {
//         std::ofstream f( sim_.TMeshToRowfileName.c_str() , std::ios_base::out | std::ios_base::binary);
//           if(f.fail() )
//             throw runtime_error("Could not open output file.");
//
//         unsigned int xyz[3];
//         xyz[0]=sim_.domainX;
//         xyz[1]=sim_.domainY;
//         xyz[2]=sim_.domainZ;
//
//         f.write( (char*)xyz, sizeof(xyz) );
//
//         char buffer[xyz[0]*xyz[1]*xyz[2]];
//
//         unsigned int cnt = 0;
//
//         for (Uint z=0; z<sim_.domainZ ; ++z) {
//            for (Uint y=0; z<sim_.domainY ; ++y) {
//		for (Uint x=0; x<sim_.domainX ; ++x)
// 	      {
//		PatchCoordinate paCo = GetOneLocalExistingPatchCoordinate(x,y,z,sim_);
//
// 		if( ! patchField_.IsAllocated(paCo.pX,paCo.pY,paCo.pZ) ) continue ;
//
//		CalcPatch& patch = *patchField_.GET_PATCH3(paCo.pX, paCo.pY, paCo.pZ);
//
//		FlagField<Flag>& flags = patch.GetFlagField(true);
//
//		if (flags.IS_FLAG(paCo.x, paCo.y, paCo.z, OBST)) {
//		   buffer[cnt]=0;
//		} else {
//		   buffer[cnt]=1;
//		}
//		++cnt;
//		}
//            }
//         }
//
//         f.write( buffer, xyz[0]*xyz[1]*xyz[2]);
//
//         f.close() ;
// // 	return true;
// }

// inline bool Domain::ConvertSTL2WarlabaRowFile ()
// {
//     std::ofstream f( sim_.TMeshToRowfileName.c_str() , std::ios_base::out | std::ios_base::binary);
//
//     if(f.fail() )
// 	      throw runtime_error("Could not open output file.");
//
//     unsigned int xyz[3];
//        xyz[0]=sim_.domainX;
//        xyz[1]=sim_.domainY;
//        xyz[2]=sim_.domainZ;
//
//    f.write( (char*)xyz, sizeof(xyz) );
//
//    char buffer[xyz[0]*xyz[1]*xyz[2]];
//
//    unsigned int cnt = 0;
//
//     for(Uint x=0 ;x< sim_.domainX;x++){
//       for(Uint y=0 ; y< sim_.domainY ;y++){
// 	for(Uint z=0 ; z< sim_.domainZ ;z++){
//
// 	      std::vector<PatchCoordinate> paCo = GetLocalPatchCoordinates(x+1 ,y+1 ,z+1 );
//
// 	      for (Uint i=0; i<paCo.size(); i++)
// 	      { //!  Grid-wise access to the process global patches' needed status
//
// 			if( !patchField_.IsAllocated(paCo[i].pX,paCo[i].pY,paCo[i].pZ) ) continue;
//
// 			FlagField<Flag>& f = patchField_.GET_PATCH3(paCo[i].pX,paCo[i].pY,paCo[i].pZ)->GetFlagField();
//
// 			if(paCo[i].x < f.XSize()-1 && paCo[i].y < f.YSize()-1  && paCo[i].z < f.ZSize()-1)
// 			{
// 			Uint fl = f.Get(paCo[i].x,paCo[i].y,paCo[i].z) ;
//
// 			if( fl == BOUZIDI || fl == NOSLIP ) // only Bouzidi ^ No-slip
// 			{
// 				 buffer[cnt] = 0;
// 			} else {
// 				 buffer[cnt] = 1;
// 			}
// 			  ++cnt;
// 			}
// 		}
// 	}
//       }
//     }
//
//     f.write( (char*)buffer, xyz[0]*xyz[1]*xyz[2]);
//
// //     ExportToVtk<char>      (buffer ,
// // 			 "Stefan.vtk" ,
// // 			 sim_.domainX ,
// // 			 sim_.domainY ,
// // 			 sim_.domainZ ,
// // 			 1, 1 , 1  ,  // data spacing
// //			 "BINARY" );//!@note by default is acsii
//     f.close() ;
//
// }














#endif    // USE_STL_GE


} // namespace walberla
