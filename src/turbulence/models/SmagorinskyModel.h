//=================================================================================================
/*!
//  \file SmagorinskyModel.h
//  \brief Standard Smagorinsky subgrid-scale model implementation
*/
//=================================================================================================

#ifndef _WALBERLA_TURBULENCE_SMAGORINSKY_MODEL_H
#define _WALBERLA_TURBULENCE_SMAGORINSKY_MODEL_H

#include "../TurbulenceModel.h"

namespace walberla {
namespace turbulence {

//=================================================================================================
//
//  SMAGORINSKY MODEL
//
//=================================================================================================

/*!\brief Standard Smagorinsky subgrid-scale model
//
// The Smagorinsky model computes the eddy viscosity as:
// nu_t = (C_s * Delta)^2 * |S|
//
// where C_s is the Smagorinsky constant, Delta is the filter width (typically dx),
// and |S| is the magnitude of the strain rate tensor.
//
// The effective relaxation time is then:
// tau_eff = tau_0 + 3 * nu_t
// or for the alternative formulation:
// tau_eff = 0.5 * (tau_0 + sqrt(tau_0^2 + 18 * C_s^2 * |S|))
*/
class SmagorinskyModel : public TurbulenceModel
{
public:
    //**Constructor & Destructor********************************************************************
    explicit SmagorinskyModel(Real smagorinskyConstant = 0.1)
        : cs_(smagorinskyConstant), filterWidth_(1.0), useAlternativeFormulation_(false)
    {
        modelType_ = SMAGORINSKY;
    }
    
    virtual ~SmagorinskyModel() = default;
    
    //**Implementation of pure virtual functions****************************************************
    
    virtual Real computeEffectiveTau(
        const Real& tau0,
        const Uint x, const Uint y, const Uint z,
        const Real& rho,
        const Vector3<Real>& vel,
        const PDFField& pdf,
        const VelField& velField,
        const SimData& sim) const override;
    
    virtual Real computeEddyViscosity(
        const Uint x, const Uint y, const Uint z,
        const Real& rho,
        const Vector3<Real>& vel,
        const PDFField& pdf,
        const VelField& velField,
        const SimData& sim) const override;
    
    virtual Real getModelConstant() const override { return cs_; }
    virtual void setModelConstant(const Real& constant) override { cs_ = constant; }
    
    virtual std::string getModelName() const override { return "Smagorinsky"; }
    
    //**Additional configuration functions**********************************************************
    
    /*!\brief Set the filter width (default is 1.0 in lattice units)
    */
    void setFilterWidth(const Real& delta) { filterWidth_ = delta; }
    Real getFilterWidth() const { return filterWidth_; }
    
    /*!\brief Use alternative tau formulation (more stable for high strain rates)
    */
    void setUseAlternativeFormulation(bool use) { useAlternativeFormulation_ = use; }
    bool getUseAlternativeFormulation() const { return useAlternativeFormulation_; }
    
    /*!\brief Initialize from simulation data
    */
    virtual void initialize(const SimData& sim) override;

protected:
    Real cs_;                      // Smagorinsky constant
    Real filterWidth_;             // Filter width (Delta)
    bool useAlternativeFormulation_; // Use sqrt formulation for tau_eff
};

//=================================================================================================
//
//  SMAGORINSKY MODEL FOR COMPRESSIBLE FLOWS
//
//=================================================================================================

/*!\brief Smagorinsky model adapted for compressible flows
//
// This variant accounts for density variations in the eddy viscosity computation
// and uses appropriate equilibrium distributions for compressible LBM.
*/
class CompressibleSmagorinskyModel : public SmagorinskyModel
{
public:
    explicit CompressibleSmagorinskyModel(Real smagorinskyConstant = 0.1)
        : SmagorinskyModel(smagorinskyConstant) {}
    
    virtual Real computeEffectiveTau(
        const Real& tau0,
        const Uint x, const Uint y, const Uint z,
        const Real& rho,
        const Vector3<Real>& vel,
        const PDFField& pdf,
        const VelField& velField,
        const SimData& sim) const override;
    
    virtual std::string getModelName() const override { return "Compressible Smagorinsky"; }
    
protected:
    /*!\brief Compute non-equilibrium stress tensor for compressible formulation
    */
    Matrix3<Real> computeCompressibleNonEquilibriumStressTensor(
        const Uint x, const Uint y, const Uint z,
        const Real& rho,
        const Vector3<Real>& vel,
        const PDFField& pdf) const;
};

} // namespace turbulence
} // namespace walberla

#endif // _WALBERLA_TURBULENCE_SMAGORINSKY_MODEL_H