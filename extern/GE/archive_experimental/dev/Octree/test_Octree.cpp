#include "octree"

//#include "vtkObject.h" // for std STL stuff

#include <iostream>


using namespace std  ;

template< typename T_, int d_  >
inline static void PrintValues(const octree<T_, d_>& My_octree ){
  octree<int,2>::iterator it; 
  for ( it = My_octree.begin(); it != My_octree.end(); ++it )
  {
    std::cout<<"Node "<<" ("<<(it.level()) << ") "<<" = "<<it->value()<<"\n";
  }
}



// =============================================================================
//
// Octree example
//
// =============================================================================


int main()
{
  // Construct a small 3-d binary tree.
  double center[3] = { 10., 10. , 10.  };

 
  octree<int,3> my_octree( center, 10.  );
  
  octree<int,3>::iterator it;
  octree<int,3>::cursor curs( &my_octree ); 



  my_octree.root()->value() = 42;
  std::cout<< "	-> Number of leafs -> " << my_octree.size(true) << "\n";// 1 onyl root
  
  my_octree.root()->add_children();

  *(*my_octree.root())[0] = 25;
  *(*my_octree.root())[1] = 19;
  *(*my_octree.root())[2] = 8;
  *(*my_octree.root())[3] = 3;
  *(*my_octree.root())[4] = 2;
  *(*my_octree.root())[5] = 9;
  *(*my_octree.root())[6] = 16;
  *(*my_octree.root())[7] = 6;
  
  
  std::cout<< "After add Chikdren\n";
  std::cout<< "	-> Number of leafs ->  "      <<my_octree.size(true) << "\n"; // root +  4 child
  std::cout<< "	-> Number of nodes(total) -> "<<my_octree.size(false) << "\n"; // root +  4 child
  

  (*my_octree.root())[1].add_children();
  (*my_octree.root())[3].add_children();
  
  std::cout<< "After add Chikdren\n";
  std::cout<< "	-> Number of leafs ->  "      <<my_octree.size(true) << "\n"; // 
  std::cout<< "	-> Number of nodes(total) -> "<<my_octree.size(false) << "\n"; //
  

  *(*my_octree.root())[1][0] =  38;
  *(*my_octree.root())[1][1] =   5;
  *(*my_octree.root())[1][2] = -19;
  *(*my_octree.root())[1][3] =   1;
  *(*my_octree.root())[1][4] =   4;
  *(*my_octree.root())[1][5] =  78;
  *(*my_octree.root())[1][6] = -98;
  *(*my_octree.root())[1][7] =  35;

  *(*my_octree.root())[3][0] = 15;

//  (*my_octree.root())[3].remove_children();


  std::cout << "====================================================== " <<"\n";
  std::cout << "Root is " << my_octree.root()->value() << "\n";
  std::cout << "	-> Child 0 is " << (*my_octree.root())[0].value() << "\n";
  std::cout << "	-> Child 1 is " << (*my_octree.root())[1].value() << "\n";
  std::cout << "	-> Child 2 is " << (*my_octree.root())[2].value() << "\n";
  std::cout << "	-> Child 3 is " << (*my_octree.root())[3].value() << "\n";
  std::cout << "	-> Child 4 is " << (*my_octree.root())[4].value() << "\n";
  std::cout << "	-> Child 5 is " << (*my_octree.root())[5].value() << "\n";
  std::cout << "	-> Child 6 is " << (*my_octree.root())[6].value() << "\n";
  std::cout << "	-> Child 7 is " << (*my_octree.root())[7].value() << "\n";  
  std::cout << "====================================================== " <<"\n";

  std::cout << "\n\n";
  std::cout << "====================================================== " <<"\n";
  std::cout << "Print only leaf nodes.\n";
  std::cout << "====================================================== " <<"\n";
  // Now test an iterator
  //  octree<int,2>::iterator it;
  
  

  for ( it = my_octree.begin(); it != my_octree.end(); ++it )
  {
    // const double* bds = it -> center () ;
    //    double he = it->size() / 2.;
     
      std::cout
      << "	-> Node " << " (" << (it.level()) << ") "
      << " = "      << it->value() /*<<  ",  Size = "<< it->size() */
      /*
      << " [" << (bds[0] - he) << "->" << (bds[0] + he)
      << ", " << (bds[1] - he) << "->" << (bds[1] + he) << "]"
      */
      << "\n";
  }

  std::cout << "\n\n";
  std::cout << "====================================================== " <<"\n";
  std::cout << "Print all nodes\n";
  std::cout << "====================================================== " <<"\n";
  for ( it = my_octree.begin( false ); it != my_octree.end( false ); ++it )
  {
   
    // const double* bds = it -> center () ;
//    double he = it->size() / 2.;
    
    std::cout
      << "	-> Node  "<< " (" << (it.level()) << ") "
      << " = " << it->value()
      /*
      << " [" << (bds[0] - he) << "->" << (bds[0] + he)
      << ", " << (bds[1] - he) << "->" << (bds[1] + he) << "]"
      */
      << "\n";
    }
  std::cout << "====================================================== " <<"\n";
  
  
  
  std::cout << "\n\n";
  
  it = my_octree.end();
  do
    {
    --it;
    std::cout
      << "	-> Node  " << " (" << (it.level()) << ") "
      << " = " << it->value() << "\n";
    }
  while ( it != my_octree.begin() );
  std::cout << "====================================================== " <<"\n";

  std::cout << "\n\n";

  it = my_octree.end( false );
  do
    {
    --it;
    std::cout
      << "	-> Node  " 
      << " (" << (it.level()) << ") "
      << " = " << it->value() << "\n";
    }
  while ( it != my_octree.begin( false ) );
 


  std::cout << "\n\n";
  std::cout << "====================================================== " <<"\n";
  std::cout << "Now test the immediate family mode\n";
  std::cout << "====================================================== " <<"\n";
  
  it = my_octree.begin();
  ++it;
  it.immediate_family( true );
  for ( ; it != my_octree.end(); ++it )
    {
    /*
    const double* bds = it->center();
    double he = it->size() / 2.;
    */
    std::cout
      << "	-> Node  " 
      << " (" << (it.level()) << ") "
      << " = " << it->value()
      /*
      << " [" << (bds[0] - he) << "->" << (bds[0] + he)
      << ", " << (bds[1] - he) << "->" << (bds[1] + he) << "]"
      */
      << "\n";
    }

  std::cout << "\n\n";

  it = my_octree.begin( false );
  ++it;
  it.immediate_family( true );
  for ( ; it != my_octree.end( false ); ++it )
    {
    /*
    const double* bds = it->center();
    double he = it->size() / 2.;
    */
    std::cout
      << "	-> Node  0x" << hex << (&*it)
      << " (" << (it.level()) << ") "
      << " = " << it->value()
      /*
      << " [" << (bds[0] - he) << "->" << (bds[0] + he)
      << ", " << (bds[1] - he) << "->" << (bds[1] + he) << "]"
      */
      << "\n";
    }

  std::cout << "\n\n";

  
  std::cout << "\n\n";
  std::cout << "====================================================== " <<"\n";
  std::cout << "Test octree cursors.\n";
  std::cout << "====================================================== " <<"\n";
  // Test octree cursors:
//  octree<int,2>::cursor curs( &my_octree );
  curs.down( 0 );
  curs.over( 1 );
  std::cout << "Initial L2Node: " << "level " << curs.level() << " where " << curs.where() << " val " << curs->value() << "\n";
  curs.axis_partner( 1 );
  std::cout << "Axis 1 partner: " << "level " << curs.level() << " where " << curs.where() << " val " << curs->value() << "\n";
  curs.over( 1 );
  curs.axis_partner( 0 );
  std::cout << "Axis 0 partner: " << "level " << curs.level() << " where " << curs.where() << " val " << curs->value() << "\n";
  curs.over( 1 );
  curs.down( 3 );
  std::cout << "Down to level2: " << "level " << curs.level() << " where " << curs.where() << " val " << curs->value() << "\n";

  // Copy an iterator's position
  curs = my_octree.begin();
  std::cout << "level " << curs.level() << " where " << curs.where() << " val " << curs->value() << "\n";

  return 0;
}
