#ifndef EXCLASSES_HH
#define EXCLASSES_HH


//====================================================0
/*
  3  totally completely different classes

   no need to base class,
*/
//====================================================0

class Foo1
{
  public:
  ~Foo1() {
    cout << "Foo1->destructor" << endl;
  }
  void Method1() {
    cout << "Foo1->Method1()" << endl;
  }
};

class Foo2 {
  public:~Foo2() {
    cout << "Foo2->dtor" << endl;
  }
  void Method2() {
    cout << "Foo2->Method2()" << endl;
  }
};


class Foo3
{
  public:
  ~Foo3() {
    cout << "Foo3->dtor" << endl;

  }
  void Method3()
  {
    cout << "Foo3->Method3()" << endl;
  }
};


#endif 