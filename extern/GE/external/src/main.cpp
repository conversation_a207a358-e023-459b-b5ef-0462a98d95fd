//------------------------------------------------------------------------------
#ifndef UNIX
#pragma hdrstop
#include <condefs.h>
#endif

#include "src/conmin.h"
#include "Analysis.h"
#include <iostream>
#include <iomanip>
#include <cmath>
//------------------------------------------------------------------------------

#define np 4.0;


inline void InitParamF1(Conmin& ex ); // total amout of data that will be exchanaged
inline void InitParamF2(Conmin& ex ); // total number of messages.
inline void PrintFinal (Conmin& ex ); // print final results
//------------------------------------------------------------------------------

int main()
{  
        const int ndv=2, ncon=2, nacp1=3;
        
        // ====================================================================	
        // first function : total amout of data that will be exchanaged
        // ====================================================================

                //   Initialize
                Conmin ex1(ndv, ncon, nacp1);
                InitParamF1 (ex1) ;
                // Solve first optimization problem
                while( ex1.conmin() != Finished)
                        ex1.obj = f1_Analysis( ex1.x, ex1.g );
                PrintFinal(ex1) ;
                std::cout<<"f2(x,y,..)\t:"<< f2 (2,2) << "\n"; 
        // ====================================================================		
        // second function : total number of messages.
        // ====================================================================	
                //   Initialize
                Conmin ex2(ndv, ncon, nacp1);
                InitParamF2 (ex2) ;
                // Solve first optimization problem
                while( ex2.conmin() != Finished)
                        ex2.obj = f2_Analysis( ex2.x, ex2.g );
                PrintFinal(ex2) ;
                
                std::cout<<"f1(x,y,..)\t:"<< f1 (2,2) << "\n"; 
	
	
	
	return 0;
}



// ============================================================================
// 
// ============================================================================
inline void PrintFinal (Conmin& ex )
{
        std::cout<<"\n\n";
	std::cout<<"====================================================================\n";
	std::cout<<"    Final optimization information\n";
	std::cout<<"====================================================================\n";
        cout << endl << endl << "     obj =" << setw(15) << ex.obj;
        cout << endl << endl << "     Decision variables (x-vector)";
        for(int i=1; i<=ex.GetNDV(); i += 6)
        {  
                const int m1=min(ex.GetNDV(),i+5);
                std::cout << std::endl << "   " << i << ")";
                for(int j=i; j<=m1; j++) {
                        cout << setw(10) << ex.x[j] ;
                }
                std::cout << std::endl;
        }
}

// ============================================================================
// 
// ============================================================================
inline void InitParamF1(Conmin& ex )
{
	//   set parameters
	ex.iprint 	= None;
	ex.info	        = Cal_all;
	ex.itmax	= 40;
	ex.infog	= All;
	ex.nfdg	        = Fdiff;
	ex.nside	= Bounded;
	ex.icndir	= 0;
	ex.nscal	= 0;
	ex.fdch	        = 0;
	ex.fdchm	= 0.;
	ex.ct		= 0.;
	//ex1.ctmin	= 0.;// ctmin = 0.004
	ex.ctl		= 0.;
	ex.ctlmin 	= 0.;
	ex.theta 	= 0.;
	ex.phi 	        = 0.;
	// ex.delfun 	= 0.; use default value 0.0001
	ex.dabfun 	= 0.;
	ex.linear_obj 	= Linear;
	ex.itrm 	= 0;
	ex.alphax	= 0.;
	ex.abobj1	= 0;

	//   Initialize constraint identification vector, isc.
	for(int j=1; j<= ex.GetNCON(); j++){
		ex.isc[j] = 0;
	}
	//   Initialize x-vector

	ex.x[1] = 1.;
	ex.x[2] = 1.;
	
	for(int j=1; j<= ex.GetNDV(); j++)
	{
		ex.vub[j]= np;
 		ex.vlb[j]= 1.0;
	}
}


// ============================================================================
// 
// ============================================================================
inline void InitParamF2(Conmin& ex )
{
	//   set parameters
	ex.iprint 	= None;
	ex.info	        = Cal_all;
	ex.itmax	= 40;
	ex.infog	= All;
	ex.nfdg	        = Fdiff;
	ex.nside	= Bounded;
	ex.icndir	= 0;
	ex.nscal	= 0;
	ex.fdch	        = 0;
	ex.fdchm	= 0.;
	ex.ct		= 0.;
	//ex1.ctmin	= 0.;// ctmin = 0.004
	ex.ctl		= 0.;
	ex.ctlmin 	= 0.;
	ex.theta 	= 0.;
	ex.phi 	        = 0.;
	// ex.delfun 	= 0.; use default value 0.0001
	ex.dabfun 	= 0.;
	ex.linear_obj 	= Linear;
	ex.itrm 	= 0;
	ex.alphax	= 0.;
	ex.abobj1	= 0;

	//   Initialize constraint identification vector, isc.
	for(int j=1; j<= ex.GetNCON(); j++){
		ex.isc[j] = 0;
	}
	//   Initialize x-vector

	ex.x[1] = 1.;
	ex.x[2] = 1.;
	
	for(int j=1; j<= ex.GetNDV(); j++)
	{
		ex.vub[j]= np;
 		ex.vlb[j]= 1.0;
	}
}