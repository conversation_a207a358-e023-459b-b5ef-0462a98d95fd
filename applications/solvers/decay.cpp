#ifndef NO_PE
#include <pe/core.h>
#endif
#ifdef WALBERLA_USE_MPI
#include <mpi.h>
#endif
#include "Definitions.h"
#include "FileReader.h"
#include "ParaviewReader.h"
#include "ParaviewWriter.h"
#include "Convert.h"
#include "Logging.h"
#include "SimOutput.h"
#include "Domain.h"
#include "SvnRev.h"


using namespace walberla;



int main(int argc,char *argv[]){
#ifdef WALBERLA_USE_MPI
    if (MPI_Init(&argc,&argv)!=MPI_SUCCESS) {
        std::cout << "MPI_Init failed!" << std::endl;
        return -1;
    }
    try {
#endif

#ifdef WALBERLA_USE_MPI
        int rank;
        MPI_Comm_rank(MPI_COMM_WORLD, &rank);
        if(rank==0) {
#endif
            std::cout << "waLBerla decay solver. ";
#if _DEBUG
            std::cout << "DEBUG ";
#else
            std::cout << "RELEASE ";
#endif

            //std::cout << "version, Revision "<<svnRevGetRevStr()<<", LOGGING = "<<LOGGING<<".";


	
            std::cout << "version, Revision "<<  "250 "  <<", LOGGING = "<<LOGGING<<".\n";
//            std::cout << "last changd "<<  "  "  <<"\n";
#ifdef OPTIMIZED
            std::cout << " OPTIMIZED.";
#endif
            std::cout << std::flush;
#ifdef WALBERLA_USE_MPI
        }
#endif

        Domain domain;
        FileReader fileReader;

        std::string logFile;
        bool logAppend;
        FileReader::Blocks logBlocks;
        //std::string temp;
        //std::cin >> temp;

        if(argc!=2)
        {
            std::cout << "Wrong usage" << std::endl;
            std::cout << "Usage:" << argv[0] << " ParameterFile" << std::endl;
            std::cout << "The "<<argc<<" parameters you specified were:"<<std::endl;
            for (int i=1; i< argc; i++) std::cout<<i<<": "<<argv[i]<<std::endl;
            std::cout << "End of parameter list."<<std::endl;
            //fileReader.ReadParameterFile("./validate/ParticleTest_OhneParticlesInFile.prm");
        } else {
#ifdef WALBERLA_USE_MPI
#ifdef _DEBUG
#if WIN32 || WIN64
            std::string in;
            std::cout<<std::endl<<"MPI-DEBUGGING! Waiting for user input to start. Enter anything and press RETURN..."<<std::endl;
            std::cin>>in;
#endif
#endif
#endif

            fileReader.ReadParameterFile(argv[1]);
            if (fileReader.Error()!="") {
                std::cerr<<"main.cpp: FileReader returned an error reading the parameter file: "<<std::endl<<fileReader.Error()<<std::endl;
                throw std::runtime_error("main.cpp: FileReader returned an error reading the parameter file: \n"+fileReader.Error());
            }
            fileReader.GetBlocks("logging",logBlocks);
            if (logBlocks.size()>1) {
                std::cerr<<"main.cpp: Too many 'logging' blocks specified in parameter file. Please specify one or none."<<std::endl;
                throw std::runtime_error("main.cpp: Too many 'logging' blocks specified in parameter file. Please specify one or none.\n");
            }

            logFile="testoutput.txt";
            logAppend=true;

            if(logBlocks.size()!=0) {
                if (logBlocks[0].IsDefined("logfile")) {
                    logFile=(std::string)logBlocks[0].GetParameter<std::string>("logfile");
                } else {
                    logFile="testoutput.txt";
                }
                if (logBlocks[0].IsDefined("append")) {
                    if (logBlocks[0].GetParameter<Uint>("append")==0)
                        logAppend=false;
                }
            }

#ifdef WALBERLA_USE_MPI
            int rank;
            MPI_Comm_rank(MPI_COMM_WORLD, &rank);
            logFile=logFile+Convert<std::string>(rank);
#endif

            Logging::Configure(logFile,logAppend);
            Logging::Instance().LogInfo("waLBerla called with parameter file: "+STR(argv[1])+"\n");
#ifdef WALBERLA_USE_MPI
            try {
#endif

                domain.Create(fileReader);
                domain.Solve();


#ifdef WALBERLA_USE_MPI
            }
            catch (std::runtime_error e) {
                LOG_ERROR(STR("main.cpp: A std::runtime_error with the message '")+ e.what() +"' was issued.\n");
                MPI_Abort(MPI_COMM_WORLD,-1);
            }
#endif
        }



#ifdef WALBERLA_USE_MPI
    }
    catch (std::runtime_error e) {
        std::cerr<<"A std::runtime_error with the message '"<<e.what()<<"' was issued. Shutting down MPI..."<<std::endl;
        MPI_Abort(MPI_COMM_WORLD,-1);
    }
#if WIN32 || WIN64
    std::string in;
    std::cout<<std::endl<<"MPI-DEBUGGING! Waiting for user input to end. Enter anything and press RETURN..."<<std::endl;
    std::cin>>in;
#endif
    MPI_Finalize();
#endif
}
