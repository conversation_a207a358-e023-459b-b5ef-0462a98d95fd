// Final Turbulence Model Test
// This demonstrates our turbulence models working correctly

simdata {
    // Domain size
    domainX     48;       // Streamwise
    domainY     24;       // Vertical
    domainZ     24;       // Spanwise
    dx          0.001;    // Grid spacing

    // Flow parameters (required by waLBerla)
    Uref        0.05;     // Reference velocity
    Lref        0.048;    // Reference length (domain length)
    Re          24;       // Reynolds number

    // Simulation control
    timesteps   100;      // Short test run
    nu          1.0e-4;   // Viscosity
    rho         1.0;      // Density
    maxPermittedVel 0.1;  // Maximum velocity

    dump;  // Output all computed parameters
}

// Test our Vreman turbulence model (different from previous tests)
turbulence {
    model               vreman;           // Use our Vreman model
    vremanConstant      0.07;             // Vreman model constant
    filterWidth         2.0;              // Test filter width = 2*dx
    strainRateMethod    nonequilibrium;   // Non-equilibrium method
}

// Simple channel flow setup
init {
    // Inlet (west)
    west {
        vel_in {
            y_L     1..'domainY'-1;
            z_L     1..'domainZ'-1;
            ux_L    0.05;  // Inlet velocity in lattice units
            uy_L    0.0;
            uz_L    0.0;
        }
    }
    
    // Outlet (east)
    east {
        prs_nils {
            y_L     1..'domainY'-1;
            z_L     1..'domainZ'-1;
            prs_L   0.0;  // Zero gauge pressure
        }
    }
    
    // Ground (bottom) - no slip
    bottom {
        noslip {
            x_L     0..'domainX'+1;
            y_L     0..'domainY'+1;
        }
    }
    
    // Top boundary - slip wall
    top {
        freeslip {
            x_L     0..'domainX'+1;
            y_L     0..'domainY'+1;
        }
    }
    
    // Side walls - slip
    north {
        freeslip {
            x_L     0..'domainX'+1;
            z_L     0..'domainZ'+1;
        }
    }

    south {
        freeslip {
            x_L     0..'domainX'+1;
            z_L     0..'domainZ'+1;
        }
    }
}

// Output configuration
paraview {
    filename    turbulence_final_test;
    xstart      0;
    xend        48;
    ystart      0;
    yend        24;
    zstart      12;
    zend        12;       // Single plane through center
    writevelocity  1;
    writepressure  1;
    writeeddyvisc  1;     // Output eddy viscosity to verify turbulence model
    interval       25;    // Output every 25 steps
    physical       1;     // Use physical units
}

// Monitor turbulence quantities
simoutput {
    TURBULENT_KINETIC_ENERGY {
        time 20;  // Output TKE every 20 steps
    }
    EDDY_VISCOSITY_STATS {
        time 20;  // Output eddy viscosity statistics
    }
}

// Logging configuration
logging {
    logfile turbulence_final_test.log;
    append  0;
}
