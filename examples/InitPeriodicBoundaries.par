// This is an example file showing how periodic boundary conditions can be set up.
// It describes a canal with periodic boundary conditions on the left and right side (west and east)
// whereby the "outlet" and "inlet" goes through two windows at each side in order to show
// the power of the desciption possibilites. At the top boundary the fluid is accelerated.
// Note that a restriction is, that the periodic boundaries have to be on opposing walls!

init
{
  east{
    // periodic boundary description blocks have to contain an ID with
    // a numerical identifier. The ID connects the block to its
    // counterpart at the opposite wall which has to have the same ID.
    periodic{
        y_L  ('domainY'/2)+3..'domainY';
        z_L  1..'domainZ';
        ID 1;
    }
    // It is possible to specify as many periodic blocks as needed.
    // However, a requirement is, that they have different IDs.
    periodic{
        y_L  1..('domainY'/2)-2;
        z_L  1..'domainZ';
        ID 2;
    }
    noslip{
        y_L ('domainY'/2)-1..('domainY'/2)+2;
        z_L 1..'domainZ';
    }
  }
  west{
    // The range descriptions of two periodic blocks with the same
    // ID (for opposing walls) must have the same range in both
    // coordinates, while they can be located on different positions.
    periodic{
        y_L  ('domainY'/2)+3..'domainY';
        z_L  1..'domainZ';
        ID 2;
    }
    periodic{
        y_L  1..('domainY'/2)-2;
        z_L  1..'domainZ';
        ID 1;
    }
    noslip{
        y_L ('domainY'/2)-1..('domainY'/2)+2;
        z_L 1..'domainZ';
    }
  }
  top{
    acc{
        x_L  0..'domainX'+1;
        y_L  1..'domainY';
        ux_L      0.02;
        uy_L      0.0;
        uz_L      0.0;
	}
  }
  bottom{
    noslip{
        x_L  0..'domainX'+1;
        y_L  1..'domainY';
	}
  }
  north{
    noslip{
        x_L 0..'domainX'+1;
        z_L 0..'domainZ'+1;
    }
  }
  south{
    noslip{
        x_L 0..'domainX'+1;
        z_L 0..'domainZ'+1;
    }
  }
}
