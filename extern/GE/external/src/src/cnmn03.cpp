/*******************************************************************************
ConminC++, to solve constrained or unconstrained function minimization.
Copyright (C) 1999  <PERSON><PERSON> <PERSON><PERSON> Dunn

This program is free software; you can redistribute it and/or
modify it under the terms of the GNU General Public License
as published by the Free Software Foundation; either version 2
of the License, or (at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this program; if not, write to the Free Software
Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA  02111-1307, USA.
*******************************************************************************/

//---------------------------------------------------------------------------
#pragma hdrstop

#include "cnmn03.h"
#include "conmin.h"
#include <iostream>
#include <iomanip>
#include <cmath>
#ifdef UNIX
//#include <macros.h>
#else
#include <vcl.h>
#endif

using namespace std;

//---------------------------------------------------------------------------
#pragma package(smart_init)
void Conmin::cnmn03(double *x, double *s, double &slope, double & alp,
      double &fff, double &a1, double &a2, double &a3, double &a4,
      double &f1, double &f2, double &f3, double &f4, double &app,
      int *ncal, int& kount, int &jgoto)
{
//     ROUTINE TO SOLVE ONE-DIMENSIONAL SEARCH IN UNCONSTRAINED
//     MINIMIZATION USING 2-POINT QUADRATIC INTERPOLATION, 3-POINT
//     CUBIC INTERPOLATION AND 4-POINT CUBIC INTERPOLATION.
//     BY G. N. VANDERPLAATS                         APRIL, 1972.
//     NASA-AMES RESEARCH CENTER,  MOFFETT FIELD, CALIF.
//     ALP = PROPOSED MOVE PARAMETER.
//     SLOPE = INITIAL FUNCTION SLOPE = S-TRANSPOSE TIMES DF.
//     SLOPE MUST BE NEGATIVE.
//     OBJ = INITIAL FUNCTION VALUE.

//
//
//     revision history
//     v1.1, 5/1/95, sharon padula, use coding standards
//     v2.0, 6/1/98, jack dunn, converted to C++ class
//
//
double zro, ap1, ap, ff, aa, ab2, ab, ab3 ;
int jj, ii;
zro =0.;
if(jgoto == 0) goto L10;
switch(jgoto)
  {
case 1:
  goto L50;
case 2:
  goto L80;
case 3:
  goto L110;
case 4:
  goto L140;
case 5:
  goto L180;
case 6:
  goto L220;
case 7:
  goto L270;
}
//
//                     INITIAL INFORMATION  (ALPHA=0)
//

L10:
if(slope < 0.) goto L20;
alp=0.;
return;

L20:
if(iprint == Debug4) cout << endl << endl << endl << endl << endl
  << "     * * * Unconstrained one-dimensional search information * * *";
f1=fff=obj;
f3=a3=a1=ap1=0.;
ap=a2=alp;
kount=0;
//
//            MOVE A DISTANCE AP*S AND UPDATE FUNCTION VALUE
//

L30:
kount++;
for(int i=1; i<=ndv; i++) x[i] += ap*s[i];
if(iprint == Debug4)
  {
  cout << endl << "     alpha =" << setw(14) << ap
       << endl << "     x-vector";
  for(int j=1; j<=ndv; j += 6)
    {
    int m1;
    m1=min(ndv,j+5);
    cout << endl << "     ";
    for(int i=j; i<=m1; i++) cout << setw(13) << x[i];
    }
  }
ncal[1]++;
jgoto=1;
return;

L50:
f2=obj;
if(iprint == Debug4) cout << endl << "     obj =" << setw(14) << f2;
if(f2<f1) goto L120;
//
//                     CHECK FOR ILL-CONDITIONING
//
if(kount > 5) goto L60;
ff=2.*fabs(f1);
if(f2<ff) goto L90;
ff=5.*fabs(f1);
if(f2<ff) goto L60;
a2 *= .5;
ap=-a2;
alp=a2;
goto L30;

L60:
f3=f2;
a3=a2;
a2 *= .5;
//
//                 UPDATE DESIGN VECTOR AND FUNCTION VALUE
//
ap=a2-alp;
alp=a2;
for(int i=1; i<=ndv; i++) x[i] += ap*s[i];
if(iprint == Debug4)
  {
  cout << endl << "     alpha =" << setw(14) << a2
       << endl << "     x-vector";
  for(int j=1; j<=ndv; j += 6)
    {
    int m1;
    m1=min(ndv,j+5);
    cout << endl << "     ";
    for(int i=j; i<=m1; i++) cout << setw(13) << x[i];
    }
  }
ncal[1]++;
jgoto=2;
return;

L80:
f2=obj;
if(iprint == Debug4) cout << endl << "     obj =" << setw(14) << f2;
//     PROCEED TO CUBIC INTERPOLATION.
goto L160;

L90:
//
//     **********        2-POINT QUADRATIC INTERPOLATION       **********
//
jj=1;
ii=1;
cnmn04(ii,app,zro,a1,f1,slope,a2,f2,zro,zro,zro,zro);
if(alp<zro || app > a2) goto L120;
f3=f2;
a3=a2;
a2=app;
jj=0;
//
//                  UPDATE DESIGN VECTOR AND FUNCTION VALUE
//
ap=a2-app;
alp=a2;
for(int i=1; i<=ndv; i++) x[i] += ap*s[i];
if(iprint == Debug4)
  {
  cout << endl << "     alpha =" << setw(14) << a2
       << endl << "     x-vector";
  for(int j=1; j<=ndv; j += 6)
    {
    int m1;
    m1=min(ndv,j+5);
    cout << endl << "     ";
    for(int i=j; i<=m1; i++) cout << setw(13) << x[i];
    }
  }
ncal[1]++;
jgoto=3;
return;

L110:
f2=obj;
if(iprint == Debug4) cout << endl << "     obj =" << setw(14) << f2;
goto L150;

L120:
a3=2.*a2;
//
//                  UPDATE DESIGN VECTOR AND FUNCTION VALUE
//
ap=a3-alp;
alp=a3;
for(int i=1; i<=ndv; i++) x[i] += ap*s[i];
if(iprint == Debug4)
  {
  cout << endl << "     alpha =" << setw(14) << a3
       << endl << "     x-vector";
  for(int j=1; j<=ndv; j += 6)
    {
    int m1;
    m1=min(ndv,j+5);
    cout << endl << "     ";
    for(int i=j; i<=m1; i++) cout << setw(13) << x[i];
    }
  }
ncal[1]++;
jgoto=4;
return;

L140:
f3=obj;
if(iprint == Debug4) cout << endl << "     obj =" << setw(14) << f3;

L150:
if(f3<f2) goto L190;

L160:
//
//     **********       3-POINT CUBIC INTERPOLATION      **********
//
ii=3;
cnmn04(ii,app,zro,a1,f1,slope,a2,f2,a3,f3,zro,zro);
if(app<zro || app>a3) goto L190;
//
//     UPDATE DESIGN VECTOR AND FUNCTION VALUE.
//
ap1=app;
ap=app-alp;
alp=app;
for(int i=1; i<=ndv; i++) x[i] += ap*s[i];
if(iprint == Debug4)
  {
  cout << endl << "     alpha =" << setw(14) << alp
       << endl << "     x-vector";
  for(int j=1; j<=ndv; j += 6)
    {
    int m1;
    m1=min(ndv,j+5);
    cout << endl << "     ";
    for(int i=j; i<=m1; i++) cout << setw(13) << x[i];
    }
  }
ncal[1]++;
jgoto=5;
return;

L180:
if(iprint == Debug4) cout << endl << "     obj =" << setw(14) << obj;
//
//                         CHECK CONVERGENCE
//
aa=1.-app/a2;
ab2=fabs(f2);
ab3=fabs(obj);
ab=ab2;
if(ab3>ab) ab=ab3;
if(ab<1.0e-15) ab=1.0e-15;
ab=(ab2-ab3)/ab;
if(std::abs(ab)<1.0e-15 && std::fabs(aa)<.001) goto L330;
a4=a3;
f4=f3;
a3=app;
f3=obj;
if(a3>a2) goto L230;
a3=a2;
f3=f2;
a2=app;
f2=obj;
goto L230;

L190:
//
//     **********        4-POINT CUBIC INTERPOLATION       **********
//

L200:
a4=2.*a3;
//     UPDATE DESIGN VECTOR AND FUNCTION VALUE.
ap=a4-alp;
alp=a4;
for(int i=1; i<=ndv; i++) x[i] += ap*s[i];
if(iprint == Debug4)
  {
  cout << endl << "     alpha =" << setw(14) << alp
       << endl << "     x-vector";
  for(int j=1; j<=ndv; j += 6)
    {
    int m1;
    m1=min(ndv,j+5);
    cout << endl << "     ";
    for(int i=j; i<=m1; i++) cout << setw(13) << x[i];
    }
  }
ncal[1]++;
jgoto=6;
return;

L220:
f4=obj;
if(iprint == Debug4) cout << endl << "     obj =" << setw(14) << f4;
if(f4 > f3) goto L230;
a1=a2;
f1=f2;
a2=a3;
f2=f3;
a3=a4;
f3=f4;
goto L200;

L230:
ii=4;
cnmn04(ii,app,a1,a1,f1,slope,a2,f2,a3,f3,a4,f4);
if(app>a1) goto L250;
ap=a1-alp;
alp=a1;
obj=f1;
for(int i=1; i<=ndv; i++) x[i] += ap*s[i];
goto L280;

L250:
//
//                 UPDATE DESIGN VECTOR AND FUNCTION VALUE
//
ap=app-alp;
alp=app;
for(int i=1; i<=ndv; i++) x[i] += ap*s[i];
if(iprint == Debug4)
  {
  cout << endl << "     alpha =" << setw(14) << alp
       << endl << "     x-vector";
  for(int j=1; j<=ndv; j += 6)
    {
    int m1;
    m1=min(ndv,j+5);
    cout << endl << "     ";
    for(int i=j; i<=m1; i++) cout << setw(13) << x[i];
    }
  }
ncal[1]++;
jgoto=7;
return;

L270:
if(iprint == Debug4) cout << endl << "     obj =" << setw(14) << obj;

L280:
//
//                    CHECK FOR ILL-CONDITIONING
//
if(obj>f2 || obj>f3) goto L290;
if(obj<=f1) goto L330;
ap=a1-alp;
alp=a1;
obj=f1;
goto L310;

L290:
if(f2<f3) goto L300;
obj=f3;
ap=a3-alp;
alp=a3;
goto L310;

L300:
obj=f2;
ap=a2-alp;
alp=a2;

L310:
//
//                       UPDATE DESIGN VECTOR
//
for(int i=1; i<=ndv; i++) x[i] += ap*s[i];

L330:
//
//                     CHECK FOR MULTIPLE MINIMA
//
if(obj<=fff) goto L350;
//     INITIAL FUNCTION IS MINIMUM.
for(int i=1; i<=ndv; i++) x[i] -= ap*s[i];
alp=0.;
obj=fff;

L350:
jgoto=0;
return;
}

