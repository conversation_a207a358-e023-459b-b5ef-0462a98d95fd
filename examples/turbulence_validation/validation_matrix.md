# Turbulence Validation Matrix - Computational Efficiency & Reference Data

## 🎯 **Validation Strategy Overview**

This matrix organizes validation cases by computational efficiency while maximizing validation value through comparison with experimental reference data.

## 📊 **Validation Matrix**

| Priority | Case | Domain Size | Timesteps | Est. Runtime | Validation Value | Reference Data Available |
|----------|------|-------------|-----------|--------------|------------------|-------------------------|
| **1** | **Quick Channel** | 20×10×10 | 50 | ~30s | Medium | Poiseuille flow theory |
| **2** | **Circular Cylinder Re=40** | 40×20×10 | 200 | ~2min | High | <PERSON> & <PERSON> (1970) |
| **3** | **Square Cylinder Re=22** | 40×20×10 | 200 | ~2min | High | Breuer et al. (2000) |
| **4** | **Circular Cylinder Re=100** | 60×30×15 | 400 | ~5min | Very High | Williamson (1996) |
| **5** | **Square Cylinder Re=100** | 60×30×15 | 400 | ~5min | High | Sohankar et al. (1998) |
| **6** | **Ahmed Body Re=1000** | 80×40×20 | 600 | ~15min | Very High | Ahmed et al. (1984) |
| **7** | **Circular Cylinder Re=200** | 100×50×20 | 800 | ~30min | Very High | Henderson (1995) |

## 🔬 **Reference Data Sources**

### **Circular Cylinder Flow**
- **Dennis & Chang (1970)**: Re=40, steady flow, drag coefficient
- **Williamson (1996)**: Re=100, vortex shedding, Strouhal number
- **Henderson (1995)**: Re=200, wake transition, force coefficients

### **Square Cylinder Flow**
- **Breuer et al. (2000)**: Re=22, steady flow, separation length
- **Sohankar et al. (1998)**: Re=100, vortex shedding, pressure distribution

### **Ahmed Body**
- **Ahmed et al. (1984)**: Original experimental data, pressure coefficients
- **Lienhart & Becker (2003)**: PIV measurements, wake structure

## 📈 **Validation Metrics**

### **Primary Metrics**
1. **Drag Coefficient (Cd)**: ±5% of experimental value
2. **Strouhal Number (St)**: ±10% of experimental value
3. **Separation Length (Ls)**: ±15% of experimental value
4. **Pressure Coefficient (Cp)**: ±10% at key locations

### **Secondary Metrics**
1. **Wake Width**: ±20% at x/D = 2, 5, 10
2. **Velocity Profiles**: R² > 0.85 correlation
3. **Turbulent Kinetic Energy**: Qualitative comparison
4. **Eddy Viscosity Distribution**: Physical reasonableness

## ⚡ **Quick Validation Protocol**

### **Phase 1: Rapid Screening (< 5 minutes total)**
1. Quick Channel (30s) - Verify basic turbulence activation
2. Circular Cylinder Re=40 (2min) - Verify geometry handling
3. Square Cylinder Re=22 (2min) - Verify separation physics

### **Phase 2: Core Validation (< 15 minutes total)**
4. Circular Cylinder Re=100 (5min) - Vortex shedding validation
5. Square Cylinder Re=100 (5min) - Complex separation validation

### **Phase 3: Advanced Validation (< 45 minutes total)**
6. Ahmed Body Re=1000 (15min) - Automotive aerodynamics
7. Circular Cylinder Re=200 (30min) - Transitional flow validation

## 🎯 **Success Criteria**

### **Minimum Validation Requirements**
- **Phase 1**: All cases must complete successfully
- **Phase 2**: Drag coefficients within ±10% of reference
- **Phase 3**: All primary metrics within specified tolerances

### **Full Validation Requirements**
- All primary metrics within specified tolerances
- Secondary metrics show reasonable agreement
- Physical flow features correctly captured
- Turbulence models show expected behavior

## 📋 **Validation Checklist**

### **Pre-Validation**
- [ ] Reference data compiled and verified
- [ ] Parameter files optimized for efficiency
- [ ] Post-processing scripts prepared
- [ ] Convergence criteria defined

### **During Validation**
- [ ] Monitor simulation stability
- [ ] Check for unphysical behavior
- [ ] Verify output file generation
- [ ] Track computational performance

### **Post-Validation**
- [ ] Extract validation metrics
- [ ] Compare against reference data
- [ ] Generate validation report
- [ ] Update validation database

## 🔄 **Iterative Improvement**

### **Continuous Optimization**
1. **Parameter Tuning**: Adjust based on validation results
2. **Efficiency Gains**: Reduce runtime while maintaining accuracy
3. **Reference Updates**: Incorporate new experimental data
4. **Metric Refinement**: Improve validation criteria based on experience

### **Validation Database Updates**
- Track validation history for each case
- Monitor trends in validation metrics
- Identify systematic biases or improvements
- Document parameter sensitivity studies
