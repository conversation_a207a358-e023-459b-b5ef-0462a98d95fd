#ifndef BOUNDINGSPHERESTEST_HH
#define BOUNDINGSPHERESTEST_HH
	

// ref
// http://www.gamedev.net/page/resources/_/reference/programming/game-programming/collision-detection/simple-bounding-sphere-collision-detection-r1234

// Bounding-Sphere Collision Detection
namespace ge
{
	
	
	template<typename object_type>
	inline bool BoundingSpheresTest(object_type* obj1, object_type* obj2)
	{
		typedef typename object_type::vector_type vector_type ;
		typedef typename object_type::real real ;
		
		//Initialize the return value
		//*t = 0.0f;
		
		
		
		/*
		// Relative velocity
		const vector_type	dv	= obj2->V() - obj1->V();
		
		// Relative position
		const vector_type	dp	= obj2->GetCOM() - obj1->GetCOM();
		//Minimal distance squared
		const real r = obj1->Radios() + obj2->Radios();
		
		//dP^2-r^2
		const real pp = dp.x() * dp.x() + dp.y() * dp.y() + dp.z() * dp.z() - r*r;
		//(1)Check if the spheres are already intersecting
		if ( pp < 0 )
		  return true;

		//dP*dV
		const real pv	= dp.x() * dv.x() + dp.y() * dv.y() + dp.z() * dv.z();
		//(2)Check if the spheres are moving away from each other
		if ( pv >= 0 )
		  return false;

		//dV^2
		const real vv = dv.x() * dv.x() + dv.y() * dv.y() + dv.z() * dv.z();
		//(3)Check if the spheres can intersect within 1 frame
		if ( (pv + vv) <= 0 && (vv + 2 * pv + pp) >= 0 ) 
		  return false;

		//tmin = -dP*dV/dV*2
		//the time when the distance between the spheres is minimal
		const real tmin = -pv/vv;

		//Discriminant/(4*dV^2) = -(dp^2-r^2+dP*dV*tmin)
		return ( pp + pv * tmin > 0 );
	*/
	}
	
	
	
//  ============================================================================
// 2D -> ref : http://www.gamedev.net/topic/361798-intersection-of-a-line-and-a-circle/
// The standard way to derive this is to plug the line equation O+tD into the 
// circle equation |X-C|2 = r2 and solve the resulting quadratic for t. 
// The following is not the best or most robust possible code, but it does 
// illustrate the algorithm
// 
//  ============================================================================
// template<class vector_type /*= Vector2 */ >
// inline bool IntersectLineCircle(const vector_type& O,  // Line origin
// 	const vector_type& D,		// Line direction 
// 	const vector_type& C,		// Circle center 
// 	float radius,			// Circle radius 
// 	float t[2],			// Parametric values at intersection points  
// 	vector_type point[2]		// Intersection points	vector_type normal[2]) // Normals at intersection points
// {
// 	typedef typename vector_type::real real ;
// 	vector_type d = O - C;
// 	const real a = D.Dot(D);
// 	const real b = d.Dot(D); 
// 	const real c = d.Dot(d) - radius * radius;
// 	const real disc = b * b - a * c;
// 	if (disc < 0.0f) {
// 		return false;
// 	}
// 	
// 	const real sqrtDisc = Math::Sqrt(disc);
// 	const real invA = 1.0f / a;
// 	
// 	t[0] = (-b - sqrtDisc) * invA;
// 	t[1] = (-b + sqrtDisc) * invA;
// 	
// 	const real invRadius = 1.0f / radius;
// 	for (int i = 0; i < 2; ++i) {
// 		point[i] = O + t[i] * D;
// 		normal[i] = (point[i] - C) * invRadius;	
// 	} 
// 	return true;
// }
//  ============================================================================



}// end namespace ge

#endif
