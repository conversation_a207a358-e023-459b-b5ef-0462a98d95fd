#ifndef VERTEX_FORMAT_HH
#define VERTEX_FORMAT_HH 

#include <iostream>
#include "GVertex.h"

using namespace std;

namespace ge
{    
  
  	typedef unsigned char   byte;
  
  
	/// ================================================================================
	///  Combining the Vertex Parts, using complicated recusive-template desing pattern
	/// ================================================================================
	template <vertex_part_usage use, typename Next = void>
	struct vertex_info
	{
		struct vertex : public vertex_part<use>
		      , public Next::vertex {};
	};

	template <vertex_part_usage use>
	struct vertex_info <use, void>
	{
        	struct vertex: public vertex_part<use>
        	{
		
        	};
	};

}// end namespace ge     

#endif

