/*******************************************************************************
ConminC++, to solve constrained or unconstrained function minimization.
Copyright (C) 1999        <PERSON><PERSON> <PERSON><PERSON> Dunn

This program is free software; you can redistribute it and/or
modify it under the terms of the GNU General Public License
as published by the Free Software Foundation; either version 2
of the License, or (at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.        See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this program; if not, write to the Free Software
Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA        02111-1307, USA.
*******************************************************************************/

//---------------------------------------------------------------------------
#include <stdlib.h>
#pragma hdrstop

#ifdef UNIX
#include <memory.h>
// #include <macros.h>
#else
#include <vcl.h>
#include <mem.h>
#endif
#include <iostream>
#include <iomanip>
#include <cmath>
#include "conmin.h"
//---------------------------------------------------------------------------
#pragma package(smart_init)
//        Output stream function for enum type info list
ostream& operator<<(ostream&s , info_list info)
{
        static char *out[2] = { (char*)"Cal_all", (char*)"Cal_active"};

        std::cout << out[ info - info_list(0) ];

        return s;
}

//        Output stream function for enum type print list
inline ostream& operator<<(ostream&s , print_list iprint)
{
        static char *out[6] = {(char*)"None",(char*)"Init_final", (char*)"Debug1",(char*)"Debug2",
                                (char *)"Debug3", (char *)"Debug4"};
                                
        std::cout << out[ iprint - print_list(0) ];
                                
        return s ;
}

//        Output stream function for enum type grad_cal
ostream& operator<<(ostream&s , grad_cal nfdg)
{
        static char *out[3] = {(char*)"Fdiff", (char*)"External", (char*)"Combined"};

        cout << out[ nfdg - grad_cal(0) ];

        return s;
}

//        Output stream function for enum type cost_fn
ostream& operator<<(ostream&s , objective_fcn linear_obj)
{
        static char *out[2] = { (char*)" True", (char*)"False"};

        cout << out[ linear_obj - objective_fcn(0) ];

        return s;
}

//        Output stream function for enum type info_grad_list
ostream& operator<<(ostream&s , info_grad_list infog)
{
        static char *out[2] = {(char*)"All", (char*)"Partial"};

        cout << out[ infog - info_grad_list(0) ];

        return s;
}

//        Output stream function for enum type side_constraints
ostream& operator<<(ostream&s , side_constraints nside)
{
        static char *out[2] = {(char*) "Bounded", (char*)"Unbounded"};

        cout << out[ nside - side_constraints(0) ];

        return s;
}

//        Output stream function for enum type Igoto
ostream& operator<<(ostream&s , Igoto igoto)
{
        static char *out[6] = {(char*)"Finished",(char*)"Initial",(char*)"Scale", (char*)"GradientFD",
                         (char *)"SolveOneDSearch",(char*) "OneDSearch"};

        cout << out[ igoto - Igoto(0) ];

        return s;
}

// Initilize control parameters
void Conmin::InitParameters()
{
        //        if control parameter is 0, use the default
        if(itrm <= 0) itrm = 3;
        if(itmax <= 0) itmax = 20;
        int ndv1 = ndv + 1;
        if(icndir == 0) icndir = ndv1;
        if(delfun <= 0.) delfun = .0001;
        ct = - fabs(ct);
        if(ct >= 0.) ct = -.1;
        ctmin = fabs(ctmin);
        if(ctmin <= 0.) ctmin = 0.004;
        ctl = -fabs(ctl);
        if(ctl >= 0.) ctl = -.01;
        ctlmin=abs(ctlmin);
        if(ctlmin <= 0.) ctlmin = .001;
        if(theta <= 0.) theta = 1.;
        if(abobj1 <= 0.) abobj1 = .1;
        if(alphax <= 0.) alphax = .1;
        if(fdch <= 0.) fdch = .01;
        if(fdchm <= 0.) fdchm = .01;
        if(nfeasct <= 0) nfeasct = 10;

        // Add by jd
        if(phi <= 0. ) phi = 5.0;

        infog = All;
}

// Copy parameters
void Conmin::CopyParameters(const Conmin& rhs)
{
        n1 = rhs.n1;
        n2 = rhs.n2;
        n3 = rhs.n3;
        n4 = rhs.n4;
        n5 = rhs.n5;

        //        Move default values to new instance
        iprint = rhs.iprint;
        info = rhs.info;
        itmax = rhs.itmax;
        infog = rhs.infog;
        nfdg = rhs.nfdg;
        nside = rhs.nside;
        icndir = rhs.icndir;
        ndv = rhs.ndv;
        ncon = rhs.ncon;
        nscal = rhs.nscal;
        linear_obj = rhs.linear_obj;
        fdch = rhs.fdch;
        fdchm = rhs.fdchm;
        ct = rhs.ct;
        ctmin = rhs.ctmin;
        ctl = rhs.ctl;
        ctlmin = rhs.ctlmin;
        theta = rhs.theta;
        phi = rhs.phi;
        delfun = rhs.delfun;
        dabfun = rhs.dabfun;
        alphax = rhs.alphax;
        abobj1 = rhs.abobj1;
        igoto = rhs.igoto;
        nfeasct = rhs.nfeasct;
        itrm = rhs.itrm;
        // copy matrix data
        int n1x = n1*sizeof(double);
        x = new double [n1]; memcpy( x, rhs.x, n1x );
        df = new double [n1]; memcpy( df, rhs.df, n1x );
        int n2x = n2*sizeof(double);
        int n2y = n2*sizeof(int);
        g = new double [n2]; memcpy( g, rhs.g, n2x );
        isc = new int [n2]; memcpy( isc, rhs.isc, n2y );
        int n3y= n3*sizeof(int);
        ic = new int [n3]; memcpy( ic, rhs.ic, n3y );
        a = new double* [n1]; a[0] = 0;         //        set unsused pointer @ 0 to 0
        int n3x = n3*sizeof(double);
        for(int i=1; i<n1; i++) //        only assign 1 to n1-1 arrays
        {
                a[i] = new double [n3];
                //        copy all ellements even though we need only 1 to n2-1
                memcpy( a[i], rhs.a[i], n3x );
        }
        
        s = new double [n1]; memcpy( s, rhs.s, n1x );
        g1 = new double [n2]; memcpy( g1, rhs.g1, n2x );
        g2 = new double [n2]; memcpy( g2, rhs.g2, n2x );
        int n4x = n4 * sizeof(double);
        c = new double [n4]; memcpy( c, rhs.c, n4x );
        int n5y = n5 * sizeof(int);
        ms1 = new int [n5]; memcpy( ms1, rhs.ms1, n5y );
        b = new double* [n3]; b[0] = 0;         //        set unsused pointer @ 0 to 0
        for(int i=1; i<n3; i++)        //        only assign 1 to n3-1 arrays
        {
                b[i] = new double [n3];
                //        copy all ellements even though we need only 1 to n2-1
                memcpy( b[i], rhs.b[i], n3x );
        }
        
        vlb = new double [n1]; memcpy( vlb, rhs.vlb, n1x );
        vub = new double [n1]; memcpy( vub, rhs.vub, n1x );
        scal = new double [n1]; memcpy( scal, rhs.scal, n1x );
}

// Default Constructor Definition
Conmin::Conmin(int ndvs, int ncons, int maxnacs)
{
        //        Integer parameters used below for array dimensions
        //        These have been increased        by 1 from the sizes used in
        //                the FORTRAN version in order to allow for the differences
        //                in the way FORTRAN and C++ index vectors. For a vector
        //                of length n, C++ will use in index between 0 and n-1. By
        //                using vectors increased by 1, C++ can use the same index
        //                valus of 1 thru n.
        n1 = ndvs + 3;
        n2 = ncons + 2*ndvs + 1;
        n3 = maxnacs + 1;
        n4 = max(n3,ndvs + 1);        // note n3 has already been increased by 1
        n5 = 2*n4 - 1;        // note n4 as calculated is already increased by 1

        //        array allocation
        x = new double [n1];
        df = new double [n1];
        g = new double [n2];
        isc = new int [n2];
        ic = new int [n3];
        a = new double* [n1]; a[0] = 0;         //        set unsused pointer @ 0 to 0
        for(int i=1; i<n1; i++)        //        assign 1 to n1-1 arrays
                a[i] = new double [n3];
        s = new double [n1];
        g1 = new double [n2];
        g2 = new double [n2];
        c = new double [n4];
        ms1 = new int [n5];
        b = new double* [n3]; b[0] = 0;         //        set unsused pointer @ 0 to 0
        for(int i=1; i<n3; i++) //        assign 1 to n3-1 arrays
                b[i] = new double [n3];
        scal = new double [n1];
        vlb = new double [n1];
        vub = new double [n1];

        //        Set initial
        iprint = None;
        info = Cal_all;
        nfdg = Fdiff;
        nside = Bounded;
        ndv = ndvs;
        ncon = ncons;
        linear_obj = NonLinear;
        igoto = Finished;

        //        Setting these control parameters to 0 will
        //        cause InitParameters to set them to the default value
        itmax = icndir =        nscal = nfeasct =        itrm = 0;
        fdch =        fdchm =        ct =        ctmin =        ctl =        ctlmin =        theta =        phi =
        delfun =        dabfun =        alphax =        abobj1 = 0.;
        InitParameters();
}

// Destructor
Conmin::~Conmin()
{
        for(int i=n3; i<=1; i--)
                delete [] b[i];
        delete [] b; b=0; 
        delete [] scal; scal=0;
        delete [] vub; vub=0;
        delete [] vlb; vlb=0;
        delete [] ms1; ms1=0;
        delete [] c; c=0;
        delete [] g2; g2=0;
        delete [] g1; g1=0;
        delete [] s; s=0;
        for(int i=n1; i<=1; i--)
                delete [] a[i];
        delete [] a; a=0;
        delete [] ic; ic=0;
        delete [] isc; isc=0;
        delete [] g; g=0;
        delete [] df; df=0;
        delete [] x; x=0;
}

//        Copy Constructor
Conmin::Conmin(const Conmin& rhs)
{
        CopyParameters( rhs );
}

//        Assignment Operator
Conmin& Conmin::operator=(const Conmin& rhs)
{
        if( this == &rhs ) return *this;

        CopyParameters( rhs );
        return *this;
}

//                 ROUTINE TO SOLVE CONSTRAINED OR UNCONSTRAINED FUNCTION MINIMIZATION.
//                 BY G. N. VANDERPLAATS                                                                                                        APRIL, 1972.
//                 * * * * * * * * * * *         JUNE, 1979 VERSION         * * * * * * * * * * *
//                 NASA-AMES RESEARCH CENTER, MOFFETT FIELD, CALIF.
//                 REFERENCE;        Conmin - A FORTRAN PROGRAM FOR CONSTRAINED FUNCTION
//                                 MINIMIZATION:        USER'S MANUAL,        BY G. N. VANDERPLAATS,
//                                 NASA TM X-62,282, AUGUST, 1973.

//
//
//                 revision history
//                 v1.1, 5/1/95, sharon padula, use coding standards
//                 v2.0, 6/1/98, jack dunn, converted to C++ class
//
//

Igoto Conmin::conmin()
{
int m1,m2,m3,nci,mcn1;
double ctc,x1,c1,ct1,si,gi,xi,xid,sib,x12,scj,alp1,alp11,alp12,objd,objb,ff1;

//                 RE-SCALE VARIABLES IF REQUIRED.
if( nscal == 0 || igoto == Finished ) goto L20;
memcpy(&x[1], &c[1], ndv*sizeof(double));

L20:
//                 CONSTANTS.
const int ndv1 = ndv + 1;
const int ndv2 = ndv + 2;
if( igoto == Finished ) goto L40;
//
//                                                                                 CHECK FOR UNBOUNDED SOLUTION
//

//                 STOP IF OBJ IS LESS THAN -1.0D+40
if(obj > -1.0e40) goto L30;
cout << endl << endl << endl;
cout << "                 ConminC++ has achieved a solution of obj < -1.0d+40" << endl;
cout << "                 solution appears to be unbounded." << endl ;
cout << "                 Optimizaation is terminated" ;
goto L810;

L30:
switch (igoto)
        {
case Initial:
        goto L160;
case Scale:
        goto L390;
case GradientFD:
        goto L380;
case SolveOneDSearch:
        goto L670;
case OneDSearch:
        goto L690;
        }
//
//                                                                                        SAVE INPUT CONTROL PARAMETERS
//

L40:
if( iprint != None )
        {
        cout << endl << endl << endl << endl;
        cout << "      * * * * * * * * * * * * * * * * * * * * * * * * * * *" << endl;
        cout << "      *                                                                                                                                                                                                         *" << endl;
        cout << "      *                         C o n m i n C + +                                                                 *" << endl;
        cout << "      *                                                                                                                                                                                                         *" << endl;
        cout << "      *                          C++ Program for                                                                        *" << endl;
        cout << "      *                                                                                                                                                                                                         *" << endl;
        cout << "      *                  Constrained Function Minimization                                *" << endl;
        cout << "      *                                                                                                                                                                                                         *" << endl;
        cout << "      * * * * * * * * * * * * * * * * * * * * * * * * * * *" << endl;
        }
if( linear_obj == Linear || ncon > 0 && nside != Bounded ) goto L50;
// Totally unconstrained function with linear objective.
// Solution is unbounded.
cout << endl << endl << endl;
cout <<"                 Completely unconstrained function with a linear objective is specified" ;
cout << endl << endl;
cout << "         lisnear_obj = " << linear_obj << endl;
cout << "                                        ncon         = " << ncon << endl;
cout << "                                        nside        = " << nside << endl;
cout << "                 Control returned to calling program";
return igoto;

L50:
idm1 = itrm; idm2 = itmax; idm3 = icndir;
dm1 = delfun; dm2 = dabfun; dm3 = ct; dm4 = ctmin; dm5 = ctl;
dm6 = ctlmin; dm7 = theta; dm8 = phi; dm9 = fdch; dm10 = fdchm;
dm11 = abobj1; dm12 = alphax;
//
//                                                                                                                                DEFAULTS
//
InitParameters();
//
//                                                                                INITIALIZE INTERNAL PARAMETERS
//
iter=jdir=iobj=kobj=kcount=ncal[1]=ncal[2]=nac=nfeas=0;
mscal=nscal;
ct1=1./(double) itrm;
dct=pow((ctmin/fabs(ct)),ct1);
dctl=((ctlmin/fabs(ctl)),ct1);
phi=5.;
abobj=abobj1;
ncobj=0;
ctam=fabs(ctmin);
ctbm=fabs(ctlmin);
//                 CALCULATE NUMBER OF LINEAR CONSTRAINTS, NLNC.
nlnc=0;
if(ncon == 0) goto L70;
for(int i=1; i<=ncon; i++) if(isc[i] > 0 ) nlnc++;

L70:
//
//                                        CHECK TO BE SURE THAT SIDE CONSTRAINTS ARE SATISFIED
//
if(nside == Unbounded) goto L110;
        for(int i=1; i<=ndv; i++)
                {
                if(vlb[i] <= vub[i]) goto L80;
                x[i]=vlb[i]=vub[i]=.5*(vlb[i]+vub[i]);
                cout << endl << endl << endl;
                cout << "* * ConminC++ detects vlb[i] > vub[i]" << endl;
                cout << "                 fix is set x[i]=vlb[i]=vub[i] = .5*(vlb[i]+vub[i] for i =" << i;

L80:
                if( x[i] >= vlb[i] ) goto L90;
                // LOWER BOUND VIOLATED.
                cout << endl << endl << endl;
                cout << "* * ConminC++ detects initial x[i] < vlb[i]" << endl;
                cout << "                 x[i] = " << x[i] << "        vlb[i] = " << vlb[i] << endl;
                cout << "                 x[i] is set equal to vlb[i] for i = " << i;
                x[i]=vlb[i];
                continue;

L90:
                if(vub[i] >= x[i] ) continue;
                cout << endl << endl << endl;
                cout << "* * ConminC++ detects initial x[i] > vub[i]" << endl;
                cout << "                 x[i] = " << x[i] << "        vub[i] = " << vub[i] << endl;
                cout << "                 x[i] is set equal to vlb[i] for i = " << i;
                x[i]=vub[i];
                }

L110:
//
//                                                                                                INITIALIZE SCALING VECTOR, SCAL
//
if(nscal == 0) goto L150;
if( nscal < 0 ) goto L130;
for(int i=1; i<=ndv; i++) scal[i]=1.;
goto L150;

L130:
for( int i=1; i<=ndv; i++)
        {
        si=fabs(scal[i]);
        if( si < 1.e-20) si=1.e-5;
        scal[i]=si;
        x[i]=x[i]/si;
        if(nside == Unbounded) continue;
        vlb[i]=vlb[i]*si;
        vub[i]=vub[i]*si;
        }

L150:
//
//                 ***** CALCULATE INITIAL FUNCTION AND CONSTRAINT VALUES        *****
//
info = Cal_all;
ncal[1]=1;
igoto = Initial;
goto L950;

L160:
obj1 = obj;
if( dabfun <= 0.) dabfun = .001*fabs(obj);
if( dabfun < 1.e-10)        dabfun=1.e-10;
if( iprint == None ) goto L270;
//
//                                                                                PRINT INITIAL DESIGN INFORMATION
//
if( iprint == None || iprint == Init_final ) goto L230;
cout << endl << endl << endl << endl;
if(nside == Unbounded && ncon == 0)
        cout << "                 Unconstrained function minimization";
else
        cout << "                 Constrained function minimization ";
cout << endl << endl << "                 Control parameters";
cout.setf( ios::right | ios::fixed );
cout << endl << endl;
cout << setw(13)<< "iprint"
                 << setw(8) << "ndv"
                 << setw(8) << "itmax"
                 << setw(8) << "ncon"
                 << setw(12) << "nside"
                 << setw(8) << "icndir"
                 << setw(8) << "nscal"
                 << setw(10) << "nfdg" << endl
                 << setw(13) << iprint
                 << setw(8) << ndv
                 << setw(8) << itmax
                 << setw(8) << ncon
                 << setw(12) << nside
                 << setw(8) << icndir
                 << setw(8) << nscal
                 << setw(10) << nfdg << endl << endl;
cout << setw(20) << "Linear Objective"
                 << setw(8) << "itrm" << endl;
cout << setw(20) << linear_obj
                 << setw(8) << itrm << endl << endl;
cout.setf( ios::right | ios::scientific );
cout << setw(16) <<"ct"
                 << setw(16) << "ctmin"
                 << setw(16) << "ctl"
                 << setw(16) << "ctlmin" << endl;
cout << setw(16) << ct
                 << setw(16) << ctmin
                 << setw(16) << ctl
                 << setw(16) << ctlmin
                 << setw(16) << endl << endl;
cout << setw(16) << "theta"
                 << setw(16) << "phi"
                 << setw(16) << "delfun"
                 << setw(16) << "dabfun" << endl;
cout << setw(16) << theta
                 << setw(16) << phi
                 << setw(16) << delfun
                 << setw(16) << dabfun
                 << setw(16) << endl << endl;
cout << setw(16) << "fdch"
                 << setw(16) << "fdchm"
                 << setw(16) << "alphax"
                 << setw(16) << "abobj1" << endl;
cout << setw(16) << fdch
                 << setw(16) << fdchm
                 << setw(16) << alphax
                 << setw(16) << abobj1 << endl << endl;
if(nside == Unbounded) goto L190;
cout << endl << "                 Lower bounds on decision variables (vlb)";
for(int i=1; i<=ndv; i += 6)
        {
        int m1;
        m1=min(ndv,i+5);
        cout << endl << "         " << i << ")";
        for(int j=i; j<=m1; j++) cout << setw(13) << vlb[j];
        }
cout << endl << endl << "                 Uper bounds on decision variables (vub)";
for(int i=1; i<=ndv; i += 6)
        {
        int m1;
        m1=min(ndv,i+5);
        cout << endl << "         " << i << ")";
        for(int j=i; j<=m1; j++) cout << setw(13) << vub[j];
        }

L190:
if(nscal >= 0) goto L200;
cout << endl << endl << "                 Scaling vector (scal)";
for(int i=1; i<=ndv; i += 7)
        {
        int m1;
        m1=min(ndv,i+6);
        cout << endl << "         ";
        for(int j=i; j<=m1; j++) cout << setw(13) << scal[i];
        }
        
L200:
if(ncon == 0) goto L230;
if(nlnc == 0 || nlnc == ncon) goto L220;
cout << endl << endl << "                 Linear constraint identifiers (isc)"
                 << endl << endl << "                 Non-zero indicates linear constraint";
for(int i=1; i<=ncon; i += 15)
        {
        int m1;
        m1=min(ncon,i+14);
        cout << endl << "         " << i << ")";
        for(int j=i; j<=m1; j++) cout << setw(5) << isc[j];
        }
goto L230;

L220:
if(nlnc == ncon) cout << endl << endl << "                 All constraints are linear";
if(nlnc == 0 ) cout << endl << endl << "                 All constraints are non-linear";

L230:
cout << endl << endl << "                 Initial function infomation" << endl << endl
                 << "                 Obj =" << setw(15) << obj;
cout << endl << endl << "                 Decision variables (x-vector)";
for(int i=1; i<=ndv; i++)
        {
        x1=1.;
        if( nscal != 0) x1=scal[i];
        g1[i]=x[i]*x1;
        }
for(int i=1; i<=ndv; i += 6)
        {
        int m1;
        m1=min(ndv,i+5);
        cout << endl << "         " << i << ")";
        for(int j=i; j<=m1; j++) cout << setw(13) << g1[j];
        }
if(ncon == 0) goto L270;
cout << endl << endl << "                 Constraint values (g-vector)";
for(int i=1; i<=ncon; i += 6)
        {
        int m1;
        m1=min(ncon,i+5);
        cout << endl << "         " << i << ")";
        for(int j=i; j<=m1; j++) cout << setw(13) << g[j];
        }

L270:
if( iprint != None || iprint != Init_final ) cout.put('\f');
//
//                 ********************        BEGIN MINIMIZATION        ************************
//

L280:
iter++;
if (abobj1 < .0001) abobj1=.0001;
if (abobj1 > .2) abobj1=.2;
if (alphax > 1.) alphax=1.;
if (alphax < .001) alphax=.001;
//
//        THE FOLLOWING TWO LINES OF CODE WERE COMMENTED OUT ON 3/5/81
//
//                 nfeas++;
//                 if (nfeas>10) goto L810;
if( iprint == Debug2 || iprint == Debug3 || iprint == Debug4)
        cout << endl << endl << endl << endl
                         << "                 Begin iteration number " << iter;
if( iprint == Debug3 || iprint == Debug4 && ncon > 0)
        {
        cout << endl << endl << "                 ct =" << setw(16) << ct << setw(10)
                         << "ctl" << setw(16) << ctl << setw(10) << "phi"
                         << setw(16) << phi;
        }
cta=fabs(ct);
if(ncobj == 0) goto L340;
{
//
//                 NO MOVE ON LAST ITERATION.        DELETE CONSTRAINTS THAT ARE NO
//                 LONGER ACTIVE.
//
int nnac=nac;
for(int i=1; i<=nnac; i++) if(ic[i] > ncon) nac++;
if(nac <= 0) goto L420;
nnac=nac;
for(int i=1; i<=nnac; i++)
        {

L300:
        nci = ic[i];
        ct1=ct;
        if( isc[nci] > 0) ct1=ctl;
        if( g[nci] > ct1) continue;
        nac--;
        if( i > nac) goto L420;
        for(int k=i; i<=nac; k++)
                {
                int ii=k+1;
                for(int j=1; j<=ndv2; j++) a[j][k] = a[j][ii];
                ic[k] = ic[ii];
                }
        goto L300;
        }
goto L420;
}

L340:
if( mscal < nscal || nscal == 0 ) goto L360;
if( nscal < 0 && kcount < icndir ) goto L360;
mscal = kcount = 0;
//
//                                                                                                        SCALE VARIABLES
//
for(int i=1; i<=ndv; i++)
        {
        si=scal[i];
        xi=si*x[i];
        sib=si;
        if( nscal > 0) si=fabs(xi);
        if( si < 1.0e-10) continue;
        scal[i]=si;
        si=1./si;
        x[i]= xi*si;
        if(nside == Unbounded) continue;
        vlb[i]=sib*si*vlb[i];
        vub[i]=sib*si*vub[i];
        }
if( iprint == None || iprint == Init_final || iprint == Debug1 &&
                                                        (nscal <        0 && iter > 1 )) goto L360;
cout << endl << endl << "                 New scaling vector (scal)";
for(int i=1; i<=ndv; i += 7)
        {
        int m1;
        m1=min(ndv,i+6);
        cout << endl << "         ";
        for(int j=i; j<=m1; j++) cout << setw(13) << scal[i];
        }

L360:
mscal++;
nac=0;
//
//                                        OBTAIN GRADIENTS OF OBJECTIVE AND ACTIVE CONSTRAINTS
//
info=Cal_active;
ncal[2]++;
if( nfdg != External) goto L370;
igoto=Scale;
goto L950;

L370:
jgoto=0;

L380:
cnmn01( jgoto, x, df, g, isc, ic, a, g1, vlb, vub, scal, c, ncal, dx,
                                dx1, fi, xi, iii );
igoto= GradientFD;
if(jgoto > 0) goto L950;

L390:
info = Cal_all;
if(nac >= n3) goto L810;
if(nscal == 0 || nfdg == Fdiff) goto L420;
//
//                                                                                                                        SCALE GRADIENTS
//
//                 SCALE GRADIENT OF OBJECTIVE FUNCTION.
for(int i=1; i<=ndv; i++) df[i] *= scal[i];
if(nfdg == Combined || nac == 0) goto L420;
//                 SCALE GRADIENTS OF ACTIVE CONSTRAINTS.
for(int j=1; j<=ndv; j++)
        {
        scj=scal[j];
        for(int i=1; i<=nac; i++) a[j][i] *= scj;
        }
        
L420:
if(iprint == None || iprint == Init_final || iprint == Debug1 || ncon == 0)
         goto L470;

//
//                                                                                                                                         PRINT
//
//                 PRINT ACTIVE AND VIOLATED CONSTRAINT NUMBERS.
{
m1=0;
m2=n3;
if(nac == 0 ) goto L450;
for(int i=1; i<=nac; i++)
        {
        int j=ic[i];
        if(j > ncon) continue;
        gi=g[j];
        c1=ctam;
        if( isc[j] > 0) c1 = ctbm;
        gi=gi-c1;
        if(gi <= 0)        // ACTIVE CONSTRAINT.
                ms1[++m1]=j;
        else        // VIOLATED CONSTRAINT.
                ms1[++m2]=j;
        }

L450:
m3=m2-n3;
cout << endl << endl << "                 There are" << setw(5) << m1 << " active constraints.";
if(m1 == 0) goto L460;
cout << endl << "                 Constraint numbers";
for(int i=1; i<=m1; i += 15)
        {
        int l1;
        l1=min(m1,i+14);
        cout << endl << "                 ";
        for(int j=i; j<=l1; j++) cout << setw(5) << ms1[j];
        }
        
L460:
cout << endl << endl << "                 There are" << setw(5) << m3 << " violated constraints.";
if(m3 == 0) goto L470;
cout << endl << "                 Constraint numbers";
m3=n3+1;
for(int i=m3; i<=m2; i += 15)
        {
        int l1;
        l1=min(m1,i+14);
        cout << endl << "                 ";
        for(int j=i; j<=l1; j++) cout << setw(5) << ms1[j];
        }
}

L470:
//
//                                 CALCULATE GRADIENTS OF ACTIVE SIDE CONSTRAINTS
//
if( nside == Unbounded) goto L530;
{
mcn1=ncon;
m1=0;
for(int i=1; i<=ndv; i++)
        {
        //                 LOWER BOUND.
        xi=x[i];
        xid=vlb[i];
        x12=fabs(xid);
        if(x12 < 1.) x12=1.;
        gi=(xid-xi)/x12;
        if(gi <= -1.0e-6) goto L490;
        ms1[m1++]=-i;
        nac++;
        if(nac >= n3) goto L810;
        mcn1++;
        for(int j=1; j<=ndv; j++) a[j][nac]=0.;
        a[i][nac]=-1.;
        ic[nac]=mcn1;
        g[mcn1] = gi;
        isc[mcn1] = 1;

L490:
        //                 UPPER BOUND.
        xid=vub[i];
        x12=fabs(xid);
        if(x12<1.) x12=1.;
        gi=(xi-xid)/x12;
        if(gi<-1.0e-6) continue;
        m1++;
        ms1[m1]=i;
        nac++;
        if(nac >= n3) goto L810;
        mcn1++;
        for(int j=1; j<=ndv; j++) a[j][nac]=0.;
        a[i][nac]=1.;
        ic[nac]=mcn1;
        g[mcn1] = gi;
        isc[mcn1] = 1 ;
        }

//
//                                                                                                                                        PRINT
//
//                 PRINT ACTIVE SIDE CONSTRAINT NUMBERS.
if(iprint == None        || iprint == Init_final
         || iprint == Debug1 || iprint == Debug2) goto L530;
cout << endl << endl << "                 There are" << setw(5) << m1
                 << " active side constraints.";
if(m1 == 0) goto L530;
cout << endl << endl << "                 Decision variables at lower or upper bounds";
cout << endl << " (minus indicates lower bound)";
for(int i=1; i<=m1; i++) cout << ms1[i];
}

L530:
//                 PRINT GRADIENTS OF ACTIVE AND VIOLATED CONSTRAINTS.
if(iprint == None        || iprint == Init_final
         || iprint == Debug1 || iprint==Debug2) goto L570;
cout << endl << endl << "                 Gradient of obj";
for(int i=1; i<=ndv; i += 6)
        {
        int m1;
        m1=min(ndv,i+5);
        cout << endl << "         " << i << ")";
        for(int j=i; j<=m1; j++) cout << setw(13) << df[j];
        }
if(nac == 0) goto L570;
cout << endl << endl << "                 Gradients of active and violated constraints";
for(int i=1; i<=nac; i++)
        {
        m1=ic[i];
        m2=m1-ncon;
        m3=0;
        if( m2 > 0) m3=abs(ms1[m2]);
        if( m2 <= 0) cout << endl << "                 Constraint number" << setw(5) <<m1;
        if( m2 > 0) cout << endl << "                 Side constraint on variable " << setw(5) << m3;
        for(int k=1; k<=ndv; k += 6)
                {
                m1 = min(ndv,k+5);
                cout << endl << "         " << k << ")";
                for(int j=k; j<=m1; j++) cout << setw(13) << a[j][i];
                }
        cout << endl << endl;
        }

L570:
//
//                 ******************        DETERMINE SEARCH DIRECTION *******************
//
alp=1.e+20;
if(nac > 0) goto L580;
{
//
//                                                                                                UNCONSTRAINED FUNCTION
//
//                 FIND DIRECTION OF STEEPEST DESCENT OR CONJUGATE DIRECTION.
//
//        S. N. 575 ADDED ON 2/25/81
//         nvc=0

L575:
nvc=0;
nfeas=0;
kcount++;
//                 IF KCOUNT.GT.ICNDIR        RESTART CONJUGATE DIRECTION ALGORITHM.
if(kcount > icndir || iobj == 2) kcount=1;
if(kcount == 1) jdir=0;
//                 IF JDIR = 0 FIND DIRECTION OF STEEPEST DESCENT.
cnmn02(jdir,slope,dftdf1,df,s);
goto L630;
}

L580:
//
//                                                                                                        CONSTRAINED FUNCTION
//
//                 FIND USABLE-FEASIBLE DIRECTION.
kcount=0;
jdir=0;
phi *= 10.;
if(phi > 1000.) phi=1000.;
//
//        THE FOLLOWING LINE OF CODE WAS COMMENTED OUT ON 3/5/81
//
//                 IF (NFEAS.EQ.1) PHI=5.
//                 CALCULATE DIRECTION, S.
cnmn05(g,df,a,s,b,c,slope,phi,isc,ic,ms1,nvc);
//
//        THE FOLLOWING LINE WAS ADDED ON 2/25/81
//
if(nac == 0) goto L575;
//
//        THE FOLLOWING FIVE LINES WERE COMMENTED OUT ON 3/5/81
//        REASON : THEY WERE NOT IN G. VANDERPLAATS LISTING
//
//                 IF THIS DESIGN IS FEASIBLE AND LAST ITERATION WAS INFEASIBLE,
//                 SET ABOBJ1=.05 (5 PERCENT).
//                 IF (NVC.EQ.0.AND.NFEAS.GT.1) ABOBJ1=.05
//                 IF (NVC.EQ.0) NFEAS=0
if(iprint == None || iprint == Init_final || iprint == Debug1) goto L600;
cout << endl << endl << "                 Push-off factors, (theta(i), i=1,nac)";
for(int i=1; i<=nac; i += 6)
        {
        int m1;
        m1=min(nac,i+5);
        cout << endl << "         " << i << ")";
        for(int j=i; j<=m1; j++) cout << setw(13) << a[ndv1][j];
        }
cout << endl << endl << "                 Constraint parameter, beta ="
                 << setw(14) << s[ndv1];

L600:
//
//                 ****************** ONE-DIMENSIONAL SEARCH ************************
//
if(s[ndv1] < 1.0e-6 && nvc == 0) goto L710;
//
//                                                                 FIND ALPHA TO OBTAIN A FEASIBLE DESIGN
//
if(nvc == 0) goto L630;
alp=-1.;
for(int i=1; i<=nac; i++)
        {
        nci=ic[i];
        c1=g[nci];
        ctc=ctam;
        if(isc[nci] > 0) ctc=ctbm;
        if(c1 <= ctc) continue;
        alp1=0.;
        for(int j=1; j<=ndv; j++) alp1 += s[j]*a[j][i];
        alp1 *= a[ndv2][i];
        if(fabs(alp1) < 1.0e-20) continue;
        alp1=-c1/alp1;
        if(alp1>alp) alp=alp1;
        }

L630:
//
//                                                                                         LIMIT CHANGE TO ABOBJ1*OBJ
//
alp1=1.0e20;
si=fabs(obj);
if(si < .01) si=.01;
if(fabs(slope)>1.0e-20) alp1=abobj1*si/slope;
alp1=fabs(alp1);
if(nvc > 0) alp1 *= 10.;
if(alp1 < alp) alp=alp1;
//
//                                                                         LIMIT CHANGE IN VARIABLE TO ALPHAX
//
{
alp11=1.0e20;
for(int i=1; i<=ndv; i++)
        {
        si=fabs(s[i]);
        xi=fabs(x[i]);
        if(si<1.0e-10        || xi < 0.1) continue;
        alp1=alphax*xi/si;
        if(alp1<alp11) alp11=alp1;
        }
if(nvc > 0) alp11 *= 10;
if(alp11 < alp) alp=alp11;
if(alp > 1.0e20) alp=1.0e20;
if(alp <= 1.0e-20) alp=1.0e-20;
if(iprint == None || iprint == Init_final || iprint == Debug1) goto L660;
cout << endl << endl << "                 Search direction (S-vector)";
for(int i=1; i<=ndv; i += 6)
        {
        int m1;
        m1=min(ndv,i+5);
        cout << endl << "         " << i << ")";
        for(int j=i; j<=m1; j++) cout << setw(13) << s[j];
        }
cout << endl << endl << "                 One-dimensional search"
                 << endl << "                 Initial Slope =" << setw(12) << slope
                 << "                                                Proposed alpha =" << setw(12)        << alp;
}

L660:
if(ncon > 0 || nside > Unbounded) goto L680;
//
//                                         DO ONE-DIMENSIONAL SEARCH FOR UNCONSTRAINED FUNCTION
//
jgoto = 0;

L670:
cnmn03(x,s,slope,alp,fff,a1,a2,a3,a4,f1,f2,f3,f4,app,ncal,kount,jgoto);
igoto = SolveOneDSearch;
if(jgoto > 0) goto L950;
jdir=1;
//                 PROCEED TO CONVERGENCE CHECK.
goto L700;
//
//                         SOLVE ONE-DIMENSIONAL SEARCH PROBLEM FOR CONSTRAINED FUNCTION
//

L680:
jgoto=0;

L690:
cnmn06(x, vlb, vub, g, scal, df, s, g1, g2, ctam, ctbm, slope, alp,
                         a2, a3, a4, f1, f2, f3, f4, cv1, cv2, cv3, cv4, alpca, alpfes, alpln,
                         alpmin, alpnc, alpsav, alpsid, alptot, isc, ncal, nvc, icount, igood1,
                         igood2, igood3, igood4, ibest, iii, nlnc, jgoto);
igoto= OneDSearch;
if(jgoto>0) goto L950;
if(nac == 0) jdir=1;
//
//                 *******************                 UPDATE ALPHAX         **************************
//

L700:
L710:
if(alp > 1.0e19) alp=0.;
//                 UPDATE ALPHAX TO BE AVERAGE OF MAXIMUM CHANGE IN X(I)
//                 AND ALHPAX.
alp11=0.;
for(int i=1; i<=ndv; i++)
        {
        si=fabs(s[i]);
        xi=fabs(x[i]);
        if( xi < 1.0e-10) continue;
        alp1=alp*si/xi;
        if( alp1 > alp11) alp11=alp1;
        }

        alp11 = .5*(alp11+alphax);
        alp12 = 5.*alphax;
        if( alp11 > alp12) alp11=alp12;
        alphax=alp11;
        ncobj++;
        //                 ABSOLUTE CHANGE IN OBJECTIVE.
        objd=obj1-obj;
        objb=fabs(objd);
        if(objb < 1.0e-10) objb=0.;
        if(nac == 0 || objb>0.) ncobj=0;
        if(ncobj > 1) ncobj=0;
        //
        //                                                                                                                                        PRINT
        //
        //                 PRINT MOVE PARAMETER, NEW X-VECTOR AND CONSTRAINTS.
        if(iprint == None || iprint == Init_final || iprint == Debug1) goto L730;
        cout <<        endl << "                 Calculated alpha = " << setw(14) << alp;

L730:
        if(iprint == None || iprint == Init_final) goto L800;
        if(objb > 0.) goto L740;
        if(iprint == Debug1) cout << endl << endl << endl << endl <<
                 "                 Iter =" << setw(5) << iter << "                 obj =" <<
                 setw(14) << obj << "                 No change in obj";
        if(iprint == Debug2 || iprint == Debug3 || iprint == Debug4)
                 cout << endl << endl << "                 obj =" << setw(15) << obj <<
                 "                 No change in obj";
        goto        L760;

L740:
        if(iprint == Debug1) goto L750;
        cout << endl << endl << "                 obj = " << setw(15) << obj;
        goto L760;

L750:
        cout << endl << endl << endl << endl << "                 iter =" << setw(5)        << iter
                         << "                 obj =" << setw(14) << obj;

L760:
        cout << endl << endl << "                 Decision variables (x-vector)";
        for(int i=1; i<=ndv; i++)
                {
                ff1=1.;
                if(nscal !=0) ff1=scal[i];
                g1[i]=ff1*x[i];
                }
        for(int i=1; i<=ndv; i += 6)
                {
                int m1;
                m1=min(ndv,i+5);
                cout << endl << "         " << i << ")";
                for(int j=i; j<=m1; j++) cout << setw(13) << g1[j];
                }
        if(ncon == 0) goto L800;
        cout << endl << endl << "                 Constraint values (g-vector)";
        for(int i=1; i<=ncon; i += 6)
                {
                int m1;
                m1=min(ncon,i+5);
                cout << endl << "         " << i << ")";
                for(int j=i; j<=m1; j++) cout << setw(13) << g[j];
                }

L800:
//
//        THE FOLLOWING CODE WAS ADDED ON 3/5/81
//
//        IT HAD NOT BEEN REPORTED AS A FIX TO MAOB
//        BUT WAS SENT TO JEFF STROUD A YEAR AGO
//        SEE OTHER COMMENTS IN CONMIN SUBROUTINE FOR DELETIONS OF CODE
//        ON 3/5/81 PERTAINING TO THIS FIX
//
//
//                                                                         CHECK FEASIBILITY
//
if(ncon <= 0) goto L808;
nfeasct=10;
//        added by slp 11/17/94
for(int i=1; i<=ncon; i++)
        {
        c1=ctam;
        if(isc[i]>0) c1=ctbm;
        if(g[i]<=c1) continue;
        nfeas++;
        goto L806;
        }
if(nfeas > 0) abobj1=.05;
//cc
nfeas=0;
phi=5.;

L806:
if(nfeas >= nfeasct) goto L810;

L808:
//
//        END OF INSERTED FIX
//
//
//                                                                                                        CHECK CONVERGENCE
//
//                 STOP IF ITER EQUALS ITMAX.
if(iter >= itmax) goto L810;
//
//                                                                                ABSOLUTE CHANGE IN OBJECTIVE
//
objb=fabs(objd);
kobj++;
if(objb >= dabfun || nfeas > 0) kobj=0;
//
//                                                                                 RELATIVE CHANGE IN OBJECTIVE
//
if(fabs(obj1) > .10e-10) objd /= fabs(obj1);
abobj1=.5*(fabs(abobj)+fabs(objd));
abobj=fabs(objd);
iobj++;
if(nvc>0 || objd>=delfun) iobj=0;
if(iobj>=itrm || kobj>=itrm) goto L810;
obj1=obj;
//
//                                         REDUCE CT IF OBJECTIVE FUNCTION IS CHANGING SLOWLY
//
if(iobj<1 || nac==0) goto L280;
ct *= dct;
ctl *= dctl;
if(fabs(ct)<ctmin) ct=-ctmin;
if(fabs(ctl)<ctlmin) ctl=-ctlmin;
goto L280;

L810:
if(nac>=n3) cout << endl <<
                        "                 The number of active and violated constraints exceeds n3-1." <<
                        endl <<
                        "                 Dimensioned size of matrices a and b and vector ic is insufficient"
                        << endl <<
                        "                 Optimization terminated and control returned to main program.";
//
//                 ****************        FINAL FUNCTION INFORMATION        ********************
//
if(nscal == 0) goto L830;
//                 UN-SCALE THE DESIGN VARIABLES.
for(int i=1; i<=ndv; i++)
        {
        xi=scal[i];
        if(nside == Bounded)
                {
                vlb[i] *= xi;
                vub[i] *= xi;
                }
        x[i] *= xi;
        }
//
//                                                                                                         PRINT FINAL RESULTS
//

L830:
if(iprint == None || nac >= n3) goto L940;
cout.put('\f');
cout <<        endl << endl << endl << endl <<
"                Final optimization information";
cout << endl << endl << "                 obj =" << setw(15) << obj;
cout << endl << endl << "                 Decision variables (x-vector)";
for(int i=1; i<=ndv; i += 6)
        {
        int m1;
        m1=min(ndv,i+5);
        cout << endl << "         " << i << ")";
        for(int j=i; j<=m1; j++) cout << setw(13) << x[j];
        }
if(ncon == 0) goto L900;
cout << endl << endl << "                 Constraint values (g-vector)";
for(int i=1; i<=ncon; i += 6)
        {
        int m1;
        m1=min(ncon,i+5);
        cout << endl << "         " << i << ")";
        for(int j=i; j<=m1; j++) cout << setw(13) << g[j];
        }
//                 DETERMINE WHICH CONSTRAINTS ARE ACTIVE AND PRINT.
nac=0;
nvc=0;
for(int i=1; i<=ncon; i++)
        {
        cta=ctam;
        if(isc[i]>0) cta=ctbm;
        gi=g[i];
        if(gi > cta) goto L860;
        if(gi < ct && isc[i] == 0) continue;
        if(gi < ctl && isc[i] > 0) continue;
        ic[++nac]=i;
        continue;

L860:
        ms1[++nvc]=i;
        }
cout << endl << endl << "                 There are" << setw(5) << nac << " active constraints.";
if(nac == 0) goto L880;
cout << endl << endl << "                 Constraint numbers are" << endl << "                 ";
for(int j=1; j<=nac; j++)
        {
        cout << setw(5) <<        ic[j];
        if( j%15 == 0) cout << endl << "                 ";
        }

L880:
cout << endl << endl << "                 There are" << setw(5) << nvc << " violated constraints.";
if(nvc == 0) goto L890;
cout << "                 Constraint numbers are";
for(int j=1; j<=nvc; j++)
        {
        cout << setw(5) <<        ms1[j];
        if( j%15 == 0) cout << endl;
        }

L890:
L900:
if(nside == Unbounded) goto L930;
//                DETERMINE WHICH SIDE CONSTRAINTS ARE ACTIVE AND PRINT.
nac=0;
for(int i=1; i<=ndv; i++)
        {
        xi=x[i];
        xid=vlb[i];
        x12=fabs(xid);
        if(x12 < 1.) x12=1.;
        gi=(xid-xi)/x12;
        if(gi < -1.0e-6) goto L910;
        ms1[++nac]=-i;

L910:
        xid=vub[i];
        x12=fabs(xid);
        if(x12 < 1.) x12=1.;
        gi=(xi-xid)/x12;
        if( gi < -1.0e-6) continue;
        ms1[++nac]=i;
        }
cout << endl << endl << "                 There are" << setw(5) << nac << " active side constraints";
if(nac == 0) goto L930;
cout << "                 Decision variables at lower or upper bounds";
cout << " (minus indicates lower bound)";
for(int j=1; j<=nac; j++) cout << ms1[j];

L930:
cout << endl << endl << "                 Termination criterion";
if(iter >= itmax) cout << endl << "                                        iter equals itmax";
if(nfeas >= nfeasct) cout << endl <<
        "                                        nfeasct consecutive iterations failed to produce a feasible design";
if(iobj >= itrm) cout << endl <<
        "                                        abs(1-obj(i-1)/obj(i-1)) less than delfun for" <<
        setw(3) << itrm << " iterations";
if(kobj >= itrm) cout << endl <<
        "                                        abs(obj(i)-obj(i-1)) less than delfun for" <<
        setw(3) << itrm << " iterations";
cout << endl << endl << "                 Number of iterations =" << setw(5) << iter;
cout << endl << endl << "                 Objective function was evaluated                                " <<
        setw(5) << ncal[1] << "        times";
if(ncon > 0) cout << endl << endl << "                 Constraint functions were evaluated"
        << setw(10) << ncal[1] << "        times";
if(nfdg != Fdiff) cout << endl << endl << "                 Gradient of objective was calculated"
        << setw(9) << ncal[2] << "        times";
if(ncon > 0 && nfdg == External) cout << endl << endl
        << "                 Gradients of constraint were calculated" << setw(6) << ncal[2]
        << "        times";
//
//                                                                         RE-SET BASIC PARAMETERS TO INPUT VALUES
//

L940:
itrm=idm1;
itmax=idm2;
icndir=idm3;
delfun=dm1;
dabfun=dm2;
ct=dm3;
ctmin=dm4;
ctl=dm5;
ctlmin=dm6;
theta=dm7;
phi=dm8;
fdch=dm9;
fdchm=dm10;
abobj1=dm11;
alphax=dm12;
igoto = Finished;

L950:
if( nscal != 0 && igoto != Finished)
//                 UN-SCALE VARIABLES.
        for(int i=1; i<=ndv; i++)
                {
                c[i]=x[i];
                x[i]=x[i]*scal[i];
                }
return igoto;
}

