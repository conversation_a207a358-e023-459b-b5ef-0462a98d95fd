


#set terminal postscript eps  enhanced color
#set output "EnergySpectrum.eps"

set terminal  png  enhanced size 1200,800 fontscale 1.5 

#time="1.00961"
file="d2data_".time.".dat"
set output "Spectrum".time.".png"


set grid
set xlabel "{/Symbol k}"
set ylabel "Energy Spectrum"
set logscale xy 
set xrange [:200]
set title  "Energy spectrum" 
 

set title "Energy Spectrum" 
set arrow from 64,graph(0,0) to 64,graph(1,1) nohead
set arrow from 32,graph(0,0) to 32,graph(1,1) nohead
set logscale xy  
plot 	"../../Jimez/0.00" u 1 w l lw 3 title "DNS t=0.0" , \
	"../../Jimez/0.20" u 1 w l lw 3 title "DNS t=0.20" , \
	"../../Jimez/0.28" u 1 w l lw 3 title "DNS t=0.28" , \
	"../../Jimez/0.55" u 1 w l lw 3 title "DNS t=0.55", \
	"../../Jimez/1.45" u 1 w l lw 3 title "DNS t=1.45", \
 	file u 1:2  w l  lw 3 lc 1 title  "t=".time   ,\

