

-- RELEASE -- SVN Revision 134 -- LOG BEGIN , Tuesday, 17.June 2025, 11:05---------------------------------


 INFO:    <PERSON><PERSON><PERSON><PERSON><PERSON> called with parameter file: turbulence_validation_test.prm
 INFO:    Domain.cpp::InitSimData:  SimData.h:Parameters:
---------------------
 dt      0.005
 dx      0.001
 tau     0.65
 omega   1.53846
 nu      1e-05
 rho     1
 Re      16
 gx      0
 gy      0
 gz      0
 xLength 0.032
 yLength 0.016
 zLength 0.016
---------------------
 dt_L    1
 dx_L    1
 nu_L    0.05
 gx_L    0
 gy_L    0
 gz_L    0
 domainX 32
 domainY 16
 domainZ 16
 numFluidCells 0
---------------------

 INFO:    PatchField.cpp::Create:  PatchField.cpp:Parameters:
---------------------
 xNumPatches 1
 yNumPatches 1
 zNumPatches 1
---------------------

 WARNING: Domain.h::SetBC: Lattice density for prs_nils boundary condition at north side has extreme value: 0. Adjust resolution!
 WARNING: Domain.cpp::InitializeBoundaries:
 Even GE is activated through CMake config, there is no blocks named 'stl' in parameter file. You are not used the STL GeometricEngine !
