#ifndef __octree_node
#define __octree_node

#include <iterator>
#include "../../gmath/Point3D.h"



template< typename T , int d_, typename Alloc > class octree;

// =============================================================================
/**\brief An n-dimensional octree node.
  *
  * Each child node is indexed by an integer whose first d_ bits describe the
  *  node's position in its parent node.
  * A bit value of 0 means the node is to the on the lower side of the hyperplane 
  * bisecting the corresponding axis.
  * There are \f$2^{\mathrm{\texttt{\d_}}}\f$ child nodes foreach non-leaf node.
  * As an example, consider a 3-dimensional octree (coordinate axes x, y, and z)
  *  with a child node index of 5.
  * The value 5 corresponds to a bit vector of 101, which indicates 
  * that node 5 is on the +x, -y, and +z sides
  * of the x, y, and z (respectively) planes bisecting the parent.
  *
  * Octree nodes store application data, a list of pointers to child nodes (or NULL) and
  * a pointer to their direct octree node parent.
  * add by Cherif:
  * additionaly I will save the node center plus the lenght in each directions
  */
// =============================================================================
template< 
	  typename T ,
	  int  d_ = 3,  // dimension
	  typename Alloc = std::allocator<T >
	  >
struct octree_node
{
	// =====================================================================
	// 
	// =====================================================================
	
	typedef T  		value_type;
	typedef T* 		pointer;
	typedef T& 		reference;
	typedef const T* 	const_pointer;
	typedef const T& 	const_reference;
	
	typedef octree<T ,d_,Alloc>* 			octree_pointer;
	typedef octree_node<T ,d_,Alloc>* 		octree_node_pointer;
	typedef const octree_node<T ,d_,Alloc>* 	const_octree_node_pointer;
	typedef octree_node<T ,d_,Alloc>& 		octree_node_reference;
	typedef const octree_node<T ,d_,Alloc>& 	const_octree_node_reference;

	// =====================================================================
	// 
	// =====================================================================
	octree_node_pointer _M_parent;
	octree_node_pointer _M_children;
	
	// add by cherif
	double 	m_center[d_];// The node center point of the octree.
	double 	m_length[d_];// The node length of each side of the hypercube 
			     // defining the node. 
	
	value_type _M_data;
	
	Point3D<float>  *n_pMin ; // in 
	Point3D<float>  *n_pMax ; // in
	
 	Point3D<float>  n_center; // to be computed
  
	// =====================================================================
	// 
	// =====================================================================
	octree_node();
	
	octree_node(octree_node_pointer parent, const value_type& data ); 

	octree_node(octree_node_pointer parent, const value_type& data ,
		    const double* bouds ,
		     Point3D<float>* min, 
		     Point3D<float>* max	    
		    );
		
	~octree_node();
	
	
	
	
	// =====================================================================
	// Childe help functions
	// =====================================================================
	bool is_leaf_node() { return this->_M_children == 0; }
	int  num_children() { return this->_M_children ? (1<<d_) : 0; }
	
	bool add_children();
	bool add_children(const T& child_initializer );
	bool remove_children();
	
	inline void computBBs() {
		n_center =  (*n_pMin + *n_pMax) * 0.5  ;
	}
	
	
	// =====================================================================
	// get the data stored in this node 
	// (ofcourse reference, that mean you can chaged)
	// =====================================================================
	
	reference value() 	{ return this->_M_data; }
	reference value() const { return this->_M_data; }
	
	const_reference operator *() const { return _M_data; }	      
	      reference operator *()       { return _M_data; }	
	
	// =====================================================================
	// Access operator to child nodes
	// =====================================================================
	const_octree_node_reference  operator [] (const int& child ) const;
	      octree_node_reference  operator [] (const int& child );
	      
	inline void SetPMin(Point3D<float>* min) {n_pMin = min;}
	inline void SetPMax(Point3D<float>* max) {n_pMax = max;}
};

template<typename T,int d>
std::ostream& operator << (std::ostream & out , octree_node<T,d>& n)
{
      out<<"---> value()  = "<< n.value() << std::endl ; 
      out<<"---> number of child = "<< n.num_children() << std::endl;
      
      for (int i= 0 ; i< d; i++)
      {
	out<<"---> Center ["<< i<< "]  = "<< n.m_center [i] <<"   \t";
	out<<"---> length ["<< i<< "]  = "<< n.m_length  [i]<< std::endl ;
	out<<"---> p:q
	Min  "  << *n.n_pMin << std::endl ;
	out<<"---> pMax  "  << *n.n_pMax << std::endl ;	
      }
      return out;
}

// template<typename T,int d>
// std::ostream& operator << (std::ostream & out ,const octree_node<T,d>& n)
// {
//       out << "=============================================================\n";  
//       out<<"Node Info" << std::endl ;
//       out<<"---> value()  = "<< n.value() << std::endl ;  
//       out<<"---> number of child = "<< n.num_children() << std::endl;
//       for (int i= 0 ; i< d; i++)
//       {
// 	out<<"---> Center ["<< i<< "]  = "<< n.m_center [i] <<"   \t";
// 	out<<"---> length ["<< i<< "]  = "<< n.m_length  [i]<< std::endl ;
//       }
//       out << "=============================================================\n";
//       return out;
// }

// =============================================================================
#endif // __octree_node
