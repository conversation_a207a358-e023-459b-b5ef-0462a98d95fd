// Array of pointers to class objects

// If you have a class named "bar", then you can declare an array of pointers to objects of this type by this:


    bar *barArray[100]; // an array of 100 pointers to "bar" objects


// If you want to allocate memory for an array of pointers dynamically (using the new operator) you can do it like this:


    bar **barPointer;
    barPointer = new bar *[10] // memory for an array of 10 pointers


//Now in either case, you have something that can be considered to be an array of pointers. In order to be 
//useful they have to point to something. (That is, you have to make the pointer point to an actual object
//that holds your data items.)

// example of allocating and using arrays of pointers to objects
// --------------------------------------------------------------

#include <iostream>
using std::cout;
using std::endl;

//
// A trivial class to use for the example
//
class toy
{
    public:
        int x;
};
int main()
{
  
   // dynamicly allocation
    toy **tPoint; // a pointer to a pointer to an object from the "toy" class
    tPoint = new toy *[33]; // use as an array of 33 pointers to "toy" objects
    
    // static 
    toy *tArray[100];       // an array of 100 pointers to "toy" objects
    int i;

    for (i = 0; i < 5; i++) {
        tPoint[i] = new toy;
        tPoint[i]->x = 10*i;
        tArray[i] = new toy;
        tArray[i]->x = i;
    }

    for (i = 0; i < 5; i++) {
        cout << "tPoint[" << i << "]->x = "   << tPoint[i]->x
             << ", tArray[" << i << "]->x = " << tArray[i]->x
             << endl;
    }
    
    for (i = 0; i < 5; i++) {
        delete tPoint[i];
        delete tArray[i];
    }
    delete [] tPoint;
  return 0;
}



