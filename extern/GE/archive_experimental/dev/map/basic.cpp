#include <iostream>
#include <map>
//  ============================================================================
/*
    struct pos {}
    struct value {}
    
    map<pos,value>
    
 *)  you need comparator functor additionaly :
    
    inline bool operator < (const pos& p1, const pos& p2){
	return (    ) // depend if, 1D, 2D or 3D 
    }
*/
//  ============================================================================


template<typename T, int dim= 3>
struct p_pos// point position
{
  public:
    T array[dim] ; //  representaion
    // defalut constructor 
    p_pos (const T& inital_value= 0){
      for (int i= 0 ;i< dim ; ++i)
	array[i] = inital_value ;
    }
};
//  ============================================================================

template<typename T, int dim>
inline bool operator < (const p_pos<T,dim>& p1 , const p_pos<T,dim>& p2)
{
	if ( p1.array [1] == p2.array [1] ) //  y == p.y 
	{
		if ( p1.array [0] == p2.array [0]  ) // return ( x == p.x )	
		  return ( p1.array [0] < p2.array [0]  );//return (x < p.x)
		else 
		  return ( p1.array [2] < p2.array [2]  ); // return (z < p.z)
	}
	else {
		return ( p1.array [1] < p2.array [1]); // return (y < p.y)
        }
}
//  ============================================================================

template<typename T>
struct p_value //// point value
{
    T data ;
};


//  ============================================================================
template<typename T, int dim>
std::ostream& operator << (std::ostream& out,const p_pos<T,dim>& P)
{
    out << "< ";
      for (int i= 0 ;i< dim ; ++i)
          out << P.array[i] << "," ;
     out << ">"; 
    return out ;
}
//  ============================================================================
struct Configure_default{
	//  export configure
       typedef float  				ElementType  ;
       typedef p_value<ElementType>   	        ValueType    ;
       typedef p_pos<ElementType, 3>  	        PositionType ;
       typedef std::map<PositionType,ValueType> SetOfPoints  ;
};


template<typename Conf>
class Vertices
{
  // typedefs 
      typedef typename Conf::ElementType    	ElementType ;
      typedef typename Conf::ValueType		ValueType   ;
      typedef typename Conf::PositionType	PositionType;
      typedef typename Conf::SetOfPoints	SetOfPoints ;
  public:
      inline void Add(const PositionType& p, const val& v  )  ;
      inline void Remove(const PositionType& p ) ;

      /* FOR FIND IS BETTER IF YOU TAKE LOOL  to the file  "Find.cpp" */
      inline bool Find(const PositionType& p) const ;
  
  public :
      inline void Insert() ;
  private :
      SetOfPoints m_vertices_ ;
} ;



typedef  Vertices<Configure_default> m_Vertices ;


int main ()
{
 
  // test 
      p_pos<float,2> pos_ ;
      std::cout << " ---> " << pos_ << std::endl ;
      
  // 
      m_Vertices m ;

  
  
  
  return (0 ) ;
}