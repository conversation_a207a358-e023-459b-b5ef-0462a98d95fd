// g++ main2.cpp // -L/usr/local/lib -I/usr/local/include -lboost_thread  -lboost_system  -lboost_filesystem
// ./a.out
#include <boost/ptr_container/ptr_vector.hpp>
#include <boost/utility.hpp>
#include <algorithm>
#include <iostream>
#include <map>
#include <vector>
#include <tr1/unordered_map> // hash table
#include <boost/progress.hpp>
#include <boost/unordered_map.hpp>
// =============================================================================

using namespace std;
using namespace boost;


// doc :
// hash_table<key,value>
// template < class Key, class T, class Compare = less<Key>,
//            class Allocator = allocator<pair<const Key,T> > > class map;

int main()
{
 	//typedef std::tr1::unordered_map<int, string> map_t;
	//typedef boost::unordered_map<int, string> map_t;
	typedef std::map<int, string> map_t;
	const int size = 1E7;
	map_t imap;
	{
		boost::progress_timer t1;
		boost::progress_display prog_bar(size);
		for(int i=0; i<size; ++i) {
		      imap[i] = "test";
		      ++prog_bar;
		}
	}
	{
		boost::progress_timer t2;
		string buff;
		for(int i=0; i<size; ++i) {
		    buff = imap[i];
		}
	}
	return 0;
}

// runnning on fauia 144 
// =============================================================================
// no optimize 
// =============================================================================
	  // typedef std::tr1::unordered_map
		// 4.70 s	INSERT
		// 0.97 s	FIND

	  // typedef std::map<int, string> map_t;
		// 12.20 s	INSERT	
		// 7.16 s	FIND

	  // typedef boost::unordered_map
		// 4.95 s	INSERT
		// 1.41 s	FIND
		
// =============================================================================
//  Optimize code : g++ main2.cpp -O3 -DNDEBUG
// =============================================================================
	  // typedef std::tr1::unordered_map
		// 2.97 s	INSERT
		// 0.66 s	FIND

	  // typedef std::map<int, string> map_t;
		// 5.15 s	INSERT
		// 3.26 s	FIND

	  // typedef boost::unordered_map
		// 2.38 s	INSERT
		// 0.68 s	FIND

