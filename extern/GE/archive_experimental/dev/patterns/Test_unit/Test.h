#ifndef TEST_HH
#define TEST_HH 


//==============================================================================
/*!
//  \class Test 
//  \brief Interface class for any test
//
//  \author Cherif <PERSON>
*/
//==============================================================================

class Test
{
  public:
    // the new interface
    virtual ~Test(){}
    virtual void execute() = 0;
};


#endif

