#ifndef MESHIMPL_HH
#define MESHIMPL_HH
// =============================================================================
// 
// Implementation of the class Mesh
// 
// =============================================================================

template <typename MeshType>
class MeshImpl
{
  public: // typedefs 
      typedef MeshType STL_MESH ;
//       iterator , const_iterator
      
  public:      
      // Conctructor 
      MeshImpl (const char* fileName ) {}
      // Destructor
      // Set functions
      // Get functions 
      // Help function 
  private:    
};


#endif
