#ifndef CONE_HH
#define CONE_HH



// =============================================================================
// includes
// =============================================================================
#include "TriShapeBase.h"
#include <vector>
#include <cmath> // sqrt
#include <iomanip>
#include <iostream>
#include <fstream>
#include <cstdlib>
#include <cassert>// stdexcept header file contains runtime_error
#include "../gmath/Point3D.h"
#include "Cone.h"
// =============================================================================




//==============================================================================
/*!
// @briefclass Cone, to generate triangular-Mesh approximation of an Cone 
// 
// TODO use Point3D<float>, compute the normals
// TODO center , ray , binary 
// TODO object management advance c++
*/
//==============================================================================
namespace ge
{

	// =====================================================================
	
	// =====================================================================
	struct Cone : TriShapeBase<Cone>
	{
	    public:
	        // default constructor
    		explicit Cone () {}
    		
    		// construc Cone for given center and ray 
		explicit Cone (const Point3D<float>& center, const double& radius,const int& n_facet ); // The number of facets will be (4^iterations) * 8 
		// explicit Cone (const Point& center, const double& radius,const int& n_iteration ) ; // The number of facets will be (4^iterations) * 8 
		
		inline void implementation() const { } /* algorithm implementation. */
		inline void Trace() const{ std::cout << "Cone::Trace ----> Cone [ " << m_id << " ]\n" ;}
		inline bool Intersect (const Ray& ray ) ;

                //TODO
                inline void Transform() ;
                inline void Voxelize () ;
                inline void Move     (const double& dt) ;

	  private :
		Point3D<float>         m_center ; ///< center
		double                 m_radius ; ///< radius
		std::vector<Triangle > m_mesh   ; // set of triangle mesh

	  public:
	  
		inline int  Create 	 (const int& iter) ; // return true if succsess, else return false
		inline void ExportToStl  (const char* fname)const ;
		inline bool ComputeNorms (void) ;
		
	  public:
// 		/*virtual*/ void accept(class Visitor &v) ;

		inline friend std::ostream& operator << (std::ostream& output,const Cone&  p);
	};
	
	
	
	// =====================================================================
	// 	
	// =====================================================================
	inline std::ostream& operator<<(std::ostream& output,const Cone& P) 
	{
	  output << " I' Cone ["<< P.m_id << "] " << std::endl ;
	  return output ;
	}	
	
	
	
	
	// =====================================================================
	// 
	// Applying transformation for Cone
	// 
	// =====================================================================
	inline void Cone::Transform()
	{
	      std::cout<< " Cone["<< m_id << "]::Transform" << std::endl ;
	
	      // TODO allpaying the transformation -> TMatrix
	      // 
	      // 
	      // 
	}
	
	
	
	// =====================================================================
	// 
	// Move Cone
	// 
	// =====================================================================
	inline void Cone::Move(const double& dt)
	{
	  std::cout<< " Cone["<< m_id << "]::Move, dt  ="<< dt << std::endl ;
	  
	  // TODO allpaying the transformation -> TMatrix
	  // 
	  // 
	  // 
	}
	
	
	
	
	
	
	
	
	
	
	
	// Uniform Distribution -> hypercube rejection method :
	// To apply this to a unit cube at the origin, choose coordinates (x,y,z) each uniformly distributed on the interval [-1,1].
	// If the length of this vector is greater than 1 then reject it, otherwise normalise it and use it as a sample. 
	//!@return int number of faces (triangles)
	inline int Cone::Create (const int& iterations)
	{
		/*! <unit cube at the origin> */ 
		/*
		Point3D<float> corner [6] = {0 ,  0 ,  1 ,	// corner[0]
					    0 ,  0 , -1 ,	// corner[1]
					    -1 , -1 ,  0 ,	// corner[2]
					    1 , -1 ,  0 ,	// corner[3]
					    1 ,  1 ,  0 ,	// corner[4]
					    -1 ,  1 ,  0 };	// corner[5]
		*/
		Point3D<float> *corner[6] =
		{
			new Point3D<float> ( 0.0 ,  0.0   ,  1.0), 
			new Point3D<float> ( 0.0 ,  0.0   , -1.0), 
			new Point3D<float> (-1.0 , -1.0   ,  0.0),
			new Point3D<float> ( 1.0 , -1.0   ,  0.0), 
			new Point3D<float> ( 1.0 ,  1.0   ,  0.0), 
			new Point3D<float> (-1.0 ,  1.0   ,  0.0)
		};

		Point3D<float> pa,pb,pc;
		int nt = 0,ntold;
		const float a = 1.0 / sqrt(2.0);
		
		/* first create the octahredon*/
		for (int i=0;i<6;i++) {
		    corner[i]->m_x *= a;
		    corner[i]->m_y *= a;
		  //corner[i]->m_z *= a;
		}
			
		m_mesh.resize (8);// nt = 8;
		m_mesh[0].v0 = *corner[0];
		m_mesh[0].v1 = *corner[3];
		m_mesh[0].v2 = *corner[4];
			
		m_mesh[1].v0 = *corner[0]; m_mesh[1].v1 = *corner[4]; m_mesh[1].v2 = *corner[5];
		m_mesh[2].v0 = *corner[0]; m_mesh[2].v1 = *corner[5]; m_mesh[2].v2 = *corner[2];
		m_mesh[3].v0 = *corner[0]; m_mesh[3].v1 = *corner[2]; m_mesh[3].v2 = *corner[3];
		m_mesh[4].v0 = *corner[1]; m_mesh[4].v1 = *corner[4]; m_mesh[4].v2 = *corner[3];
		m_mesh[5].v0 = *corner[1]; m_mesh[5].v1 = *corner[5]; m_mesh[5].v2 = *corner[4];
		m_mesh[6].v0 = *corner[1]; m_mesh[6].v1 = *corner[2]; m_mesh[6].v2 = *corner[5];
		m_mesh[7].v0 = *corner[1]; m_mesh[7].v1 = *corner[3]; m_mesh[7].v2 = *corner[2];
		
		nt = 8 ;
			
		int tmp_ = nt * pow (4, iterations) ;
		std::cout << std::endl << " Numbert of triangles id  -> " << tmp_ << std::endl;
		    m_mesh.resize (tmp_);
			    
		/* refinment  */ 	
		/* Bisect each edge and move to the surface of a unit Cone */
		for (int it=0;it<iterations;it++)
		{
			ntold = nt;
			for (int i=0;i<ntold;i++)
			{
				pa  = 0.5f * ( m_mesh[i].v0 + m_mesh[i].v1 ) ;
				pb  = 0.5f * ( m_mesh[i].v1 + m_mesh[i].v2 ) ;
				pc  = 0.5f * ( m_mesh[i].v2 + m_mesh[i].v0 ) ; 
		
				Normalise(&pa);
				Normalise(&pb);
				Normalise(&pc);
				
				m_mesh[nt].v0 = m_mesh[i].v0;
				m_mesh[nt].v1 = pa;
				m_mesh[nt].v2 = pc;
				nt++;
		
				m_mesh[nt].v0 = pa; 
				m_mesh[nt].v1 = m_mesh[i].v1;
				m_mesh[nt].v2 = pb;
				nt++;
				
				
				m_mesh[nt].v0 = pb;
				m_mesh[nt].v1 = m_mesh[i].v2;
				m_mesh[nt].v2 = pc;
				nt++;
				
				
				m_mesh[i].v0 = pa;
				m_mesh[i].v1 = pb;
				m_mesh[i].v2 = pc;
			}
		}
		return nt;
	}
	
	
	
	// =====================================================================
	// Method which export the mesh to 3d-stl file format
	// =====================================================================
	inline void Cone::ExportToStl (const char* fname )const
	{
		FILE * pFile;
		pFile = fopen ( fname,"w");
		assert ( (pFile != NULL) );

		fprintf( pFile, 
			 "%s\n",
			 "solid 3d object Created Using GE tool. author:Cherif.Mihoubi");

		assert (m_mesh.size() !=0 ) ;
		//TODO compute the normals
		for (int i=0; i< m_mesh.size() ; i++ )
		{
		// fprintf(pFile, "%s %lf %lf %lf\n","facet normal ", Nm_mesh[0] , Nm_mesh[1] , Nm_mesh[2]);
		  fprintf(pFile, "%s %lf %lf %lf\n","facet normal ", 0. , 1. , 0.);
		  fprintf(pFile, "%s\n","outer loop" );//outer loop

		  Triangle tmp = m_mesh[i] ;

		  fprintf(pFile, "%s %lf %lf %lf\n", " vertex " ,tmp.v0.m_x , tmp.v0.m_y , tmp.v0.m_z);
		  fprintf(pFile, "%s %lf %lf %lf\n", " vertex " ,tmp.v1.m_x , tmp.v1.m_y , tmp.v1.m_z);
		  fprintf(pFile, "%s %lf %lf %lf\n", " vertex " ,tmp.v2.m_x , tmp.v2.m_y , tmp.v2.m_z);
		  fprintf(pFile, "%s\n", "endloop"); // end outer loop
		  fprintf(pFile, "%s\n","endfacet" ); // end facet
		}
		
		fprintf(pFile, "%s", "\nendsolid");

		fclose (pFile);
	}

	// =====================================================================
	// Method which compute and setup the normals
	// =====================================================================
	inline bool Cone::ComputeNorms (void) 
	{
	  
	}



}


#endif 








