#ifndef ALGORITHM_HH
#define ALGORITHM_HH

#include <algorithm> //#include <iterator_traits>
#include <iterator>
#include <vector>

//    namespace std {
//        template <class T>
//        struct iterator_traits {
//            typedef typename T::value_type            value_type;
//            typedef typename T::difference_type       difference_type;
//            typedef typename T::iterator_category     iterator_category;
//            typedef typename T::pointer               pointer;
//            typedef typename T::reference             reference; 
//        };
//    }



// for associative container 
// 
// 
//! useful in case when we have container (i.e. std::vector) of pointer
// TODO what about multithreading (i.e. openmp ) 
// ++ is dangerous

template<class InputIterator, class T>
inline InputIterator Find ( InputIterator first, InputIterator last , const T& _value )
{
    for ( ;first!=last; first++){ // TODO maybe ++first should be faster than first++

        if ( (*first)== _value ) break;
    }
    return first;
}



// if its T is pointer add "*" to  ( *(*first)== _value )
// template<class InputIterator, class T>
// inline InputIterator Find ( InputIterator first, InputIterator last , const T& _value )
// {
//     for ( ;first!=last; first++){ // TODO maybe ++first should be faster than first++
// 
//         if ( *(*first)== _value ) break;
//     }
//     return first;
// }



  
// to use binary search, you should first have sorted assosiative container 
template <class ForwardIterator, class T>
bool binary_search ( ForwardIterator first, ForwardIterator last, const T& value )
{
      first = lower_bound(first,last,value);
      return (first!=last && !(value<*first));
}


template <class ForwardIterator, class T, class Compare>
bool binary_search ( ForwardIterator first, ForwardIterator last,const T& value, Compare comp )
{
      first = lower_bound(first,last,value);// TODO add comp here, its missing please
      return (!comp(first,last) && !comp(value,*first));
}

// TODO  wrapper function not depend if the container is associative or not 



#endif
