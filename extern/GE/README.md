# GE - Geometric Engine

3D STL geometric module for computational fluid dynamics simulations, integrated with the waLBerla framework.

## Overview

GE (Geometric Engine) is a library for handling complex 3D geometries in physically accurate simulations. It provides tools for:
- Loading and processing STL (stereolithography) mesh files
- Spatial acceleration structures (Octree)
- Mesh manipulation and transformation
- Boundary condition detection
- Parallel processing support (MPI)
- Visualization export (POV-Ray, ParaView)

## Directory Structure

```
GE/
├── src/                    # Source code
│   ├── accel/             # Acceleration structures (Octree, spatial partitioning)
│   ├── configure/         # Configuration and precision settings
│   ├── gmath/             # Mathematical utilities and geometry operations
│   ├── io/                # File I/O for STL and parameter files
│   ├── log/               # Logging functionality
│   ├── mesh/              # Mesh data structures and operations
│   ├── misc/              # Miscellaneous utilities (colors, constants, timing)
│   ├── parallel/          # MPI parallel computing support
│   ├── simulation/        # Kinematics and dynamics simulation
│   ├── test/              # Test suite
│   ├── tool/              # File format converters and utilities
│   ├── topology/          # Topology management (World, Sectors)
│   ├── transformation/    # Geometric transformations (rotation, translation)
│   ├── util/              # Utilities (volume computation, collision detection)
│   ├── viz/               # Visualization exporters
│   └── waLBerlaInterface/ # Integration with waLBerla CFD framework
├── doc/                   # Documentation and images
├── external/              # External dependencies
├── turbulence/           # Turbulence simulation examples
├── archive_experimental/  # Archived experimental code
└── CMakeLists.txt        # Build configuration
```

## Building

GE is built as part of the waLBerla framework. The library supports:
- CMake build system (minimum version 2.6)
- Optional MPI support for parallel processing
- Debug and Release build configurations

### Libraries Created

When built, GE creates the following static libraries:
- `libgmath.a` - Mathematical operations
- `libconfigure.a` - Configuration management
- `libmisc.a` - Miscellaneous utilities
- `libaccel.a` - Acceleration structures
- `libio.a` - Input/output operations
- `libmesh.a` - Mesh handling
- `libtool.a` - Tools and converters
- `libtransformation.a` - Geometric transformations
- `libutil.a` - Utility functions
- `libviz.a` - Visualization exporters
- `libWaLBerlaGEInterface.a` - waLBerla integration
- `libglog.a` - Logging
- `libtopo.a` - Topology management
- `libPar.a` - Parallel processing (if MPI enabled)

## Usage

GE is designed to be used as an external library by waLBerla for handling geometric operations in CFD simulations. Key features:

1. **Mesh Loading**: Load STL files and process them for simulation
2. **Boundary Detection**: Detect boundary conditions using ray-triangle intersection
3. **Domain Decomposition**: Support for parallel domain decomposition
4. **Visualization**: Export simulation geometry to POV-Ray or ParaView

## Documentation

Generate the full documentation using:
```bash
make ge_doc
```

This requires Doxygen to be installed.

## Version

Current version: 0.1.11.5

## Author

M.M. Cherif (<EMAIL>)  
Germany

## License

Copyright (c) 2009-2010, M.C. Mihoubi. All Rights Reserved.