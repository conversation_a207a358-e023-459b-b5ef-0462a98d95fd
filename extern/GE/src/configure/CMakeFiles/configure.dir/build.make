# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.28

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/thesis/walberla-e7fa8985e052/extern/GE

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/thesis/walberla-e7fa8985e052/extern/GE

# Include any dependencies generated for this target.
include src/configure/CMakeFiles/configure.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include src/configure/CMakeFiles/configure.dir/compiler_depend.make

# Include the progress variables for this target.
include src/configure/CMakeFiles/configure.dir/progress.make

# Include the compile flags for this target's objects.
include src/configure/CMakeFiles/configure.dir/flags.make

src/configure/CMakeFiles/configure.dir/Config.cpp.o: src/configure/CMakeFiles/configure.dir/flags.make
src/configure/CMakeFiles/configure.dir/Config.cpp.o: src/configure/Config.cpp
src/configure/CMakeFiles/configure.dir/Config.cpp.o: src/configure/CMakeFiles/configure.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/thesis/walberla-e7fa8985e052/extern/GE/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object src/configure/CMakeFiles/configure.dir/Config.cpp.o"
	cd /home/<USER>/thesis/walberla-e7fa8985e052/extern/GE/src/configure && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT src/configure/CMakeFiles/configure.dir/Config.cpp.o -MF CMakeFiles/configure.dir/Config.cpp.o.d -o CMakeFiles/configure.dir/Config.cpp.o -c /home/<USER>/thesis/walberla-e7fa8985e052/extern/GE/src/configure/Config.cpp

src/configure/CMakeFiles/configure.dir/Config.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/configure.dir/Config.cpp.i"
	cd /home/<USER>/thesis/walberla-e7fa8985e052/extern/GE/src/configure && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/thesis/walberla-e7fa8985e052/extern/GE/src/configure/Config.cpp > CMakeFiles/configure.dir/Config.cpp.i

src/configure/CMakeFiles/configure.dir/Config.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/configure.dir/Config.cpp.s"
	cd /home/<USER>/thesis/walberla-e7fa8985e052/extern/GE/src/configure && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/thesis/walberla-e7fa8985e052/extern/GE/src/configure/Config.cpp -o CMakeFiles/configure.dir/Config.cpp.s

# Object files for target configure
configure_OBJECTS = \
"CMakeFiles/configure.dir/Config.cpp.o"

# External object files for target configure
configure_EXTERNAL_OBJECTS =

src/configure/libconfigure.a: src/configure/CMakeFiles/configure.dir/Config.cpp.o
src/configure/libconfigure.a: src/configure/CMakeFiles/configure.dir/build.make
src/configure/libconfigure.a: src/configure/CMakeFiles/configure.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/thesis/walberla-e7fa8985e052/extern/GE/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX static library libconfigure.a"
	cd /home/<USER>/thesis/walberla-e7fa8985e052/extern/GE/src/configure && $(CMAKE_COMMAND) -P CMakeFiles/configure.dir/cmake_clean_target.cmake
	cd /home/<USER>/thesis/walberla-e7fa8985e052/extern/GE/src/configure && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/configure.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
src/configure/CMakeFiles/configure.dir/build: src/configure/libconfigure.a
.PHONY : src/configure/CMakeFiles/configure.dir/build

src/configure/CMakeFiles/configure.dir/clean:
	cd /home/<USER>/thesis/walberla-e7fa8985e052/extern/GE/src/configure && $(CMAKE_COMMAND) -P CMakeFiles/configure.dir/cmake_clean.cmake
.PHONY : src/configure/CMakeFiles/configure.dir/clean

src/configure/CMakeFiles/configure.dir/depend:
	cd /home/<USER>/thesis/walberla-e7fa8985e052/extern/GE && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/thesis/walberla-e7fa8985e052/extern/GE /home/<USER>/thesis/walberla-e7fa8985e052/extern/GE/src/configure /home/<USER>/thesis/walberla-e7fa8985e052/extern/GE /home/<USER>/thesis/walberla-e7fa8985e052/extern/GE/src/configure /home/<USER>/thesis/walberla-e7fa8985e052/extern/GE/src/configure/CMakeFiles/configure.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : src/configure/CMakeFiles/configure.dir/depend

