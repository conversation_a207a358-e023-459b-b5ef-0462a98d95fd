# Compiler configuration
CXX      = g++  
CXXFLAGS =   -g  	#-DDEBUG its in main.cpp   

#CXXFLAGS = -O2  -DNDEBUG 



# Compiler environment
#if cluster
# LIBS    = -lm -L../../lib -lpe -L/central/boost/1.43/lib -lboost_thread -lboost_system -lboost_filesystem -L/opt/scali/lib64 -lmpi
#else no cluster
LIBS    = -lm -L../../lib \
	  -L/central/boost/1.43/lib\
	  -lboost_thread \
	  -lboost_system  \
	  -lboost_filesystem


INCLUDE = 

SRC	= main.cpp 
PROG	= Progress



# Build rules
$(PROG): $(SRC)
	$(CXX) $(CXXFLAGS) $(SRC) -o $@ $(INCLUDE) $(LIBS)


# Clean up rules
clean:
	@echo "Cleaning up..."
	rm -f $(PROG) *.*~ *~ deb 


# ----- Setting the independent commands -----
.PHONY: clean
