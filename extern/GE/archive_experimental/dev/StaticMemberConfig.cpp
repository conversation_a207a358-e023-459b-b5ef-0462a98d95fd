#include <iostream>
#include <vector>

#include <fstream>
#include "TestContainerWithLog.h"


#ifdef GE_USE_MPI
#include <mpi.h>
#endif

using namespace std ;
using namespace ge ;

struct ConfigSample{
      typedef int ElementType ;
      typedef std::size_t  SizeType ;
};

//==============================================================================
//!
//! @brief container based on std::vector example
//!
//==============================================================================

template<class Configure , class N>
struct VecContainer 
{
      typedef typename Configure::ElementType 	ElementType ;
      typedef typename Configure::SizeType	SizeType ;
      
      inline static void Add (const ElementType &e) ;
      inline static SizeType Size ();
  private:
      static std::vector<ElementType> m_data ; // note static member data
};
// initialize the static member data -> m_data
template<class Configure, class N>
std::vector< typename VecContainer <Configure,N>::ElementType> VecContainer <Configure,N>::m_data ;


// Add function implementation 
template<class Configure, class N>
inline void VecContainer<Configure,N>::Add(const typename VecContainer<Configure,N>::ElementType& e)
{
        // Add Logger
        m_data.push_back(e) ;
}

// Get Size, how much element where inserted in our container
template<class Configure, class N>
inline 
typename VecContainer<Configure,N>::SizeType VecContainer<Configure,N>::Size () {  
  return m_data.size() ;
}






//==============================================================================
//
// test program 
//
//==============================================================================
int main (int argc , char* argv[] )
{

    #ifdef GE_USE_MPI 
    MPI::Init(argc, argv);
    #endif


   // int rank = MPI::COMM_WORLD.Get_rank();
   // int size = MPI::COMM_WORLD.Get_size();
    
    
    // no logging 
    VecContainer <ConfigSample,int>::Add ( 5  ) ;
    VecContainer <ConfigSample,int>::Add ( 45 ) ;
    VecContainer <ConfigSample,int>::Add ( 95 ) ;

    LoggerBase test_me ;

    //whith Logging, now they are automatically called in LogBase.h 
    //LoggerBase::Log("//==============================================================================\n*") ;
    //LoggerBase::Log("* @brief: Log File: created by \"GE\" ") ;
    //LoggerBase::Log("* @author:Cherif Mihoubi") ;
    //LoggerBase::Log("*\n//==============================================================================\n") ;
    
    
    
    // =========================================================================
    // basicly TestContainerWithLog needs from ConfigSample only ConfigSample::ElementType
    // and no thing else
    // =========================================================================
    // note that TestContainerWithLog is container class which inherited from LoggerBase.
    // =========================================================================
    TestContainerWithLog<ConfigSample>::Add(5) ;
    TestContainerWithLog<ConfigSample>::Add(10) ;    
    TestContainerWithLog<ConfigSample>::Add(15) ;
    
    
    
    std::cout << "Vector Size() =  "
	      << VecContainer <ConfigSample,int>::Size() 
	      << std::endl ;
    
    #ifdef GE_USE_MPI 
    MPI::Finalize();
    #endif 
    return (0) ;
}
