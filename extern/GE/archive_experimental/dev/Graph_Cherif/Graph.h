
#ifndef GRAPH_HH
#define GRAPH_HH

#include "vertex.h"
// #include <algorithm>
#include "DoubleLinkedList.h"
#include <utility>
#include <queue>
#include <algorithm>

struct Edge
{
	vertex *_v1 ;
	vertex *_v2 ;
    
	int v1;
	int v2;
	
	int facet;
	Edge * NextEdge;
	
	Edge( vertex *v0, vertex *v1)
		:_v1(v0)  , _v2 (v1) {}
	
	Edge(int v_0, int v_1, int f=0)
		:v1(v_0)  , v2 (v_1), facet(f){}			
};


// typedef std::map<    vertex ,	// vertex struct-coordinations
// 			std::size_t,    // int, a.k.a : vertex indices
// 			VertexCompare
// 		> mymap_t;


// typedef std::vector<Edge> Edge_value ;

// typedef double_linked<Edge> Edge_value ;

typedef std::list<vertex> Edge_value ; 

typedef std::map< vertex ,	// vertex struct-coordinations
		Edge_value,	// int, a.k.a : vertex indices
		VertexCompare
		> mymap_t;
		

class Graph
{
   public:
	Graph (){
		assert ( adjcencyMap.empty() ) ;
	}
	
	// =====================================================================
	// 
	// =====================================================================
	inline bool isEmpty()const{return adjcencyMap.empty();}
	
	
	
	// =====================================================================
	inline std::size_t size()const{return adjcencyMap.size();}
	
	
	// =====================================================================
	// 
	// =====================================================================
	inline bool AddVertex(const vertex& v)
	{
		mymap_t::const_iterator it = adjcencyMap.find(v) ;
		if ( it != adjcencyMap.end() )
		{
//			std::cout << "was found "<< std::endl;
			return false;
		} else {
		// std::cout << "not found" << std::endl;
		// adjcencyMap.insert(std::pair<vertex,Edge_value>(v,*new double_linked<Edge>)); 
			adjcencyMap.insert(std::pair<vertex,Edge_value>(v,*new list<vertex>));
			return true;
		}
        }
        
        
	// =====================================================================
	// 
	// =====================================================================
	inline void print()/*const*/ 
	{
		std::cout << "Map size: " <<  size() << std::endl;
		mymap_t::const_iterator ii  ;
		for( ii=adjcencyMap.begin();ii !=adjcencyMap.end();++ii)
		{
			cout << (*ii).first << ":\n ";
			Edge_value tmp_list((*ii).second.begin(),(*ii).second.end());
// 			tmp_list.unique(  );
			Edge_value::const_iterator it = tmp_list.begin();

			cout << "   mylist contains: " << tmp_list.size()<<"\n";
			for ( ; it != tmp_list.end();++it)
				cout << "	" << *it <<"\n";
		}
	}
	
	
	
	// =====================================================================
	/*! 
	 @brief return the number of eges in this graph object.
	 @return the number of edges.
	*/
	// =====================================================================
	inline std::size_t GetEdgeCount()
	{
		std::size_t count = 0 ;
		mymap_t::const_iterator ii  ;
 		for( ii = adjcencyMap.begin() ; ii !=adjcencyMap.end();++ii){
	//		f ( (*ii).first ) 
			count  += (*ii).second.size();
		}
		return count ;
	}


	// =====================================================================
	/*! 
	 @brief add an edge, and vertices if not already exist.
	 @param v1 the beginning vertex object of the edge 
	 @param v2 the ending    vertex object of the edge		
	 @return true if the edge was added by this call
	*/
	// =====================================================================
	inline bool AddEdge (const vertex &v1, const vertex &v2)
	{
		AddVertex(v1)  ;
		AddVertex(v2)  ;
		Edge_value  mylist(adjcencyMap [v1].begin(),adjcencyMap[v1].end()) ;
		if( find(mylist.begin(), mylist.end(),v2) == mylist.end() )  
			( adjcencyMap [v1] ).push_back(v2);

		//	( adjcencyMap [v2] ).push_back(v1);//
		//	Edge *e = new Edge (&v1, &v2 );		  
		return true;
	}

	
	// =====================================================================
	/*! 
	 @brief add an Triangle, and vertices if not already exist.
	 @param v1 the beginning vertex object of the triangle 
	 @param v2 the ending    vertex object of the triangle
	 @param v3 the ending    vertex object of the triangle		 
	 @return true if the triangle was added by this call
	*/
	// =====================================================================
	inline bool AddTriangle (const vertex &v0,const vertex &v1,const vertex &v2 )
	{
		AddEdge(v0,v1);
		AddEdge(v0,v2);
		AddEdge(v1,v2);
		// AddVertex(v0)  ;( adjcencyMap [v0] ).push_back(v1);
		// AddVertex(v1)  ;( adjcencyMap [v0] ).push_back(v2); 
		// AddVertex(v2)  ;( adjcencyMap [v1] ).push_back(v2);
		return true;
	}



	// =====================================================================
	/*! 
	 @brief Lists of vertices reached using Breadth first traversal with
	 * specified starting point.
	 @param start a start vertex for the traversal
	 @note implementation valid only for an unweighted graph
	         tree
		 ----
		 j         <-- level 0
	       /   \
	      f      k     <-- level 1
	    /  \      \
	  a     h      z   <-- level 2
	    \
	    d              <-- level 3
		 
	*/
	// =====================================================================
	inline  void BreadthFirstTraversal( vertex & v1 );
  private:
	mymap_t adjcencyMap;
};


	// =====================================================================
	// 
	// =====================================================================
	inline void Graph::BreadthFirstTraversal(vertex & v1)
	{
		std::queue<vertex>  Queue ; //TODO = std::queue<vertex*> ();
		mymap_t reached;
		Queue.push(v1);
		vertex currentVertex;

		while ( ! Queue.empty())
		{
			currentVertex = Queue.front();
			Queue.pop();
			// list<Vertex>::iterator it;
			Edge_value::iterator it; //TODO const_iterator
			//for each currentDecescendant in adj[currentVertex]
			for(it =adjcencyMap[currentVertex].begin();
					it!=adjcencyMap[currentVertex].end();++it)
			{
				vertex currentDecescendant (*it);
				cout<< currentDecescendant<< endl;
		//		if( currentDecescendant !=  )
		//		{
		// 			 color[currentDecescendant] = GRAY;
		// 			 distance[currentDecescendant] = distance[currentVertex] + 1;
		// 			 parent[currentDecescendant] = currentVertex.getId();
					Queue.push(currentDecescendant);
		//			 color[currentVertex] = BLACK;
		//		}
			}
		}// end while  
	}





#endif
