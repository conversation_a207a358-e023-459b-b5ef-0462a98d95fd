/*--------------------------------*- C++ -*----------------------------------*\
| =========                 |                                                 |
| \\      /  F ield         | OpenFOAM: The Open Source CFD Toolbox           |
|  \\    /   O peration     | Version:  2.0.1                                 |
|   \\  /    A nd           | Web:      www.OpenFOAM.com                      |
|    \\/     M anipulation  |                                                 |
\*---------------------------------------------------------------------------*/
FoamFile
{
    version     2.0;
    format      ascii;
    class       dictionary;
    object      blockMeshDict;
}
// * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * //

convertToMeters 6.28318530718;

vertices        
(
    (0 0 0)
    (1 0 0)
    (1 1 0)
    (0 1 0)
    (0 0 0.1)
    (1 0 0.1)
    (1 1 0.1)
    (0 1 0.1)
);

blocks          
(
    hex (0 1 2 3 4 5 6 7) (80  80  1) simpleGrading (1 1 1)
    //hex (0 1 2 3 4 5 6 7) (160 160 1) simpleGrading (1 1 1)
    //hex (0 1 2 3 4 5 6 7) (320 320 1) simpleGrading (1 1 1)
);

edges           
(
);

boundary
(

    upperBoundary 
    {
        type cyclic;
        neighbourPatch lowerBoundary;
        faces
        (
            (3 7 6 2)
        );
    }
    lowerBoundary
    {
        type cyclic;
        neighbourPatch upperBoundary;
        faces
        (
            (1 5 4 0)
        );
     }    
     leftBoundary
     {
        type cyclic;
        neighbourPatch rightBoundary;
        faces
        (
            (0 4 7 3)
        );
      }
      rightBoundary
      {
         type cyclic;
         neighbourPatch leftBoundary;
         faces
         (
             (2 6 5 1)
         );
       }
    frontAndBack 
    {
        type empty;
        faces
        (
            (0 3 2 1)
            (4 5 6 7)
        );
    }
);

mergePatchPairs 
(
);

// ************************************************************************* //
