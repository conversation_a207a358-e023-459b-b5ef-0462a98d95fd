// Ahmed Body LES Simulation
// This parameter file demonstrates the use of curved boundaries with turbulence modeling
// for simulating flow around the Ahmed body at Re = 768,000

// Simulation parameters
simdata {
    // Domain size (in lattice units) - reduced for testing
    domainX     128;      // Streamwise
    domainY     64;       // Vertical
    domainZ     64;       // Spanwise

    // Simulation control
    timesteps   500;      // Reduced for testing

    // LBM parameters
    viscosity   1.5e-5;   // Kinematic viscosity [m²/s]
    omega       1.8;      // Relaxation parameter

    dump;  // Output all computed parameters
}

// Turbulence modeling
turbulence {
    model               dynamic_smagorinsky;  // Use dynamic Smagorinsky
    filterWidth         2.0;                  // Test filter width = 2*dx
    wallModel           wallfunction;         // Use wall functions
    strainRateMethod    nonequilibrium;       // Non-equilibrium method
    dynamicSmagorinskyAveraging lagrangian;   // Lagrangian averaging
    maxDynamicCs        0.3;                  // Limit Cs for stability
}

// Curved boundary configuration
curvedboundary {
    // Ahmed body
    body {
        name        ahmed;
        meshfile    meshes/ahmed_body_25deg.stl;  // 25-degree slant angle
        position    32.0 16.0 32.0;              // Center of body
        velocity    0.0 0.0 0.0;                  // Fixed body
        rotation    0.0 0.0 0.0;                  // No rotation
        refArea     0.01125;                      // Frontal area [m²]
        refLength   0.288;                        // Body length [m]
        outputForces true;                        // Enable force output
    }
    
    // Ground plane (optional - can use standard BC instead)
    body {
        name        ground;
        meshfile    meshes/ground_plane.stl;
        position    64.0 8.0 32.0;
        velocity    0.0 0.0 0.0;
        refArea     1.0;
        outputForces false;  // No force output for ground
    }
    
    // Global settings
    forceOutputInterval 100;              // Output forces every 100 steps
    forceOutputFile     ahmed_forces.dat; // Force output file
    useOctree          true;              // Use octree for efficiency
    qValueThreshold    0.01;              // Minimum q-value
}

// Boundary conditions
init {
    // Inlet (west)
    west {
        vel_in {
            y_L     8..'domainY'-8;       // Leave space for ground
            z_L     8..'domainZ'-8;       // Leave space for walls
            ux_L    0.08; // Inlet velocity in lattice units
            uy_L    0.0;
            uz_L    0.0;
        }
    }
    
    // Outlet (east)
    east {
        prs_nils {
            y_L     8..'domainY'-8;
            z_L     8..'domainZ'-8;
            prs_L   0.0;  // Zero gauge pressure
        }
    }
    
    // Ground (bottom)
    bottom {
        noslip {
            x_L     0..'domainX'+1;
            z_L     0..'domainZ'+1;
        }
    }
    
    // Top boundary - slip wall
    top {
        freeslip {
            x_L     0..'domainX'+1;
            z_L     0..'domainZ'+1;
        }
    }
    
    // Side walls - slip
    north {
        freeslip {
            x_L     0..'domainX'+1;
            y_L     0..'domainY'+1;
        }
    }
    
    south {
        freeslip {
            x_L     0..'domainX'+1;
            y_L     0..'domainY'+1;
        }
    }
}

// Output configuration
paraview {
    filename    ahmed_body;
    xstart      20;
    xend        108;
    ystart      0;
    yend        32;
    zstart      16;
    zend        48;
    writedensity   1;
    writevelocity  1;
    writeflags     1;
    writepressure  1;
    writeeddyvisc  1;    // Output eddy viscosity
    interval       1000;  // Output every 1000 steps
    physical       1;     // Use physical units
}

// Additional output - vertical plane through body center
paraview {
    filename    ahmed_centerplane;
    xstart      0;
    xend        128;
    ystart      0;
    yend        64;
    zstart      32;
    zend        32;       // Single plane
    writevelocity  1;
    writepressure  1;
    writeeddyvisc  1;
    interval       500;
    physical       1;
}

// Force monitoring
simoutput {
    DRAG_COEFFICIENT {
        time 10;  // Output drag coefficient every 10 steps
    }
    LIFT_COEFFICIENT {
        time 10;  // Output lift coefficient every 10 steps
    }
    FORCE_CONVERGENCE {
        time 100; // Check force convergence every 100 steps
    }
}

// Logging configuration
logging {
    logfile ahmed_body_les.log;
    append  0;
}