<ParaView>
  <ServerManagerState version="3.8.1">
    <Proxy group="animation" type="AnimationScene" id="7" servers="16">
      <Property name="AnimationTime" id="7.AnimationTime" number_of_elements="1">
        <Element index="0" value="0"/>
      </Property>
      <Property name="CacheLimit" id="7.CacheLimit" number_of_elements="1">
        <Element index="0" value="102400"/>
        <Domain name="range" id="7.CacheLimit.range"/>
      </Property>
      <Property name="Caching" id="7.Caching" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="bool" id="7.Caching.bool"/>
      </Property>
      <Property name="Cues" id="7.Cues" number_of_elements="1">
        <Proxy value="17"/>
        <Domain name="groups" id="7.Cues.groups">
          <Group value="animation"/>
        </Domain>
      </Property>
      <Property name="EndTime" id="7.EndTime" number_of_elements="1">
        <Element index="0" value="1"/>
      </Property>
      <Property name="EndTimeInfo" id="7.EndTimeInfo" number_of_elements="1">
        <Element index="0" value="1"/>
      </Property>
      <Property name="LockEndTime" id="7.LockEndTime" number_of_elements="1">
        <Element index="0" value="0"/>
        <Domain name="bool" id="7.LockEndTime.bool"/>
      </Property>
      <Property name="LockStartTime" id="7.LockStartTime" number_of_elements="1">
        <Element index="0" value="0"/>
        <Domain name="bool" id="7.LockStartTime.bool"/>
      </Property>
      <Property name="StartTime" id="7.StartTime" number_of_elements="1">
        <Element index="0" value="0"/>
      </Property>
      <Property name="StartTimeInfo" id="7.StartTimeInfo" number_of_elements="1">
        <Element index="0" value="0"/>
      </Property>
      <Property name="TimeKeeper" id="7.TimeKeeper" number_of_elements="1">
        <Proxy value="4"/>
      </Property>
      <Property name="ViewModules" id="7.ViewModules" number_of_elements="1">
        <Proxy value="21"/>
        <Domain name="groups" id="7.ViewModules.groups">
          <Group value="views"/>
        </Domain>
      </Property>
      <Property name="Duration" id="7.Duration" number_of_elements="1">
        <Element index="0" value="10"/>
      </Property>
      <Property name="FramesPerTimestep" id="7.FramesPerTimestep" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="range" id="7.FramesPerTimestep.range">
          <Min index="0" value="1"/>
        </Domain>
      </Property>
      <Property name="GoToFirst" id="7.GoToFirst"/>
      <Property name="GoToLast" id="7.GoToLast"/>
      <Property name="GoToNext" id="7.GoToNext"/>
      <Property name="GoToPrevious" id="7.GoToPrevious"/>
      <Property name="Loop" id="7.Loop" number_of_elements="1">
        <Element index="0" value="0"/>
        <Domain name="bool" id="7.Loop.bool"/>
      </Property>
      <Property name="NumberOfFrames" id="7.NumberOfFrames" number_of_elements="1">
        <Element index="0" value="10"/>
        <Domain name="range" id="7.NumberOfFrames.range">
          <Min index="0" value="1"/>
        </Domain>
      </Property>
      <Property name="Play" id="7.Play"/>
      <Property name="PlayMode" id="7.PlayMode" number_of_elements="1">
        <Element index="0" value="0"/>
        <Domain name="enum" id="7.PlayMode.enum">
          <Entry value="0" text="Sequence"/>
          <Entry value="1" text="Real Time"/>
          <Entry value="2" text="Snap To TimeSteps"/>
        </Domain>
      </Property>
      <Property name="Stop" id="7.Stop"/>
      <SubProxy name="AnimationPlayer" servers="16">
        <SubProxy name="RealtimeAnimationPlayer" servers="16"/>
        <SubProxy name="SequenceAnimationPlayer" servers="16"/>
        <SubProxy name="TimestepsAnimationPlayer" servers="16"/>
      </SubProxy>
    </Proxy>
    <Proxy group="animation" type="TimeAnimationCue" id="17" servers="16">
      <Property name="AnimatedDomainName" id="17.AnimatedDomainName" number_of_elements="1">
        <Element index="0" value=""/>
      </Property>
      <Property name="AnimatedElement" id="17.AnimatedElement" number_of_elements="1">
        <Element index="0" value="0"/>
      </Property>
      <Property name="AnimatedPropertyName" id="17.AnimatedPropertyName" number_of_elements="1">
        <Element index="0" value="Time"/>
      </Property>
      <Property name="AnimatedProxy" id="17.AnimatedProxy" number_of_elements="1">
        <Proxy value="4"/>
        <Domain name="groups" id="17.AnimatedProxy.groups">
          <Group value="sources"/>
          <Group value="filters"/>
        </Domain>
      </Property>
      <Property name="Enabled" id="17.Enabled" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="bool" id="17.Enabled.bool"/>
      </Property>
      <Property name="EndTime" id="17.EndTime" number_of_elements="1">
        <Element index="0" value="1"/>
      </Property>
      <Property name="StartTime" id="17.StartTime" number_of_elements="1">
        <Element index="0" value="0"/>
      </Property>
      <Property name="TimeMode" id="17.TimeMode" number_of_elements="1">
        <Element index="0" value="0"/>
        <Domain name="enum" id="17.TimeMode.enum">
          <Entry value="0" text="Normalized"/>
          <Entry value="1" text="Relative"/>
        </Domain>
      </Property>
      <Property name="UseAnimationTime" id="17.UseAnimationTime" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="bool" id="17.UseAnimationTime.bool"/>
      </Property>
      <Property name="KeyFrames" id="17.KeyFrames" number_of_elements="0">
        <Domain name="groups" id="17.KeyFrames.groups">
          <Group value="animation_keyframes"/>
        </Domain>
      </Property>
      <Property name="LastAddedKeyFrameIndex" id="17.LastAddedKeyFrameIndex" number_of_elements="1">
        <Element index="0" value="0"/>
      </Property>
      <SubProxy name="Manipulator" servers="16">
        <SubProxy name="CueStarter" servers="16"/>
      </SubProxy>
    </Proxy>
    <Proxy group="misc" type="RepresentationAnimationHelper" id="3352" servers="1">
      <Property name="Source" id="3352.Source" number_of_elements="1">
        <Proxy value="3339"/>
      </Property>
    </Proxy>
    <Proxy group="representations" type="GeometryRepresentation" id="3355" servers="1">
      <Property name="BackfaceRepresentation" id="3355.BackfaceRepresentation" number_of_elements="1">
        <Element index="0" value="400"/>
        <Domain name="enum" id="3355.BackfaceRepresentation.enum">
          <Entry value="400" text="Follow Frontface"/>
          <Entry value="401" text="Cull Backface"/>
          <Entry value="402" text="Cull Frontface"/>
          <Entry value="0" text="Points"/>
          <Entry value="1" text="Wireframe"/>
          <Entry value="2" text="Surface"/>
          <Entry value="5" text="Surface With Edges"/>
        </Domain>
      </Property>
      <Property name="CubeAxesVisibility" id="3355.CubeAxesVisibility" number_of_elements="1">
        <Element index="0" value="0"/>
        <Domain name="bool" id="3355.CubeAxesVisibility.bool"/>
      </Property>
      <Property name="Input" id="3355.Input" number_of_elements="1">
        <Proxy value="3339" output_port="0"/>
        <Domain name="input_type" id="3355.Input.input_type">
          <DataType value="vtkDataSet"/>
        </Domain>
      </Property>
      <Property name="Representation" id="3355.Representation" number_of_elements="1">
        <Element index="0" value="5"/>
        <Domain name="enum" id="3355.Representation.enum">
          <Entry value="3" text="Outline"/>
          <Entry value="0" text="Points"/>
          <Entry value="1" text="Wireframe"/>
          <Entry value="2" text="Surface"/>
          <Entry value="5" text="Surface With Edges"/>
        </Domain>
      </Property>
      <Property name="SelectionVisibility" id="3355.SelectionVisibility" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="bool" id="3355.SelectionVisibility.bool"/>
      </Property>
      <Property name="Visibility" id="3355.Visibility" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="bool" id="3355.Visibility.bool"/>
      </Property>
      <Property name="Ambient" id="3355.Ambient" number_of_elements="1">
        <Element index="0" value="0"/>
        <Domain name="range" id="3355.Ambient.range">
          <Min index="0" value="0"/>
          <Max index="0" value="1"/>
        </Domain>
      </Property>
      <Property name="AmbientColor" id="3355.AmbientColor" number_of_elements="3">
        <Element index="0" value="1"/>
        <Element index="1" value="1"/>
        <Element index="2" value="1"/>
        <Domain name="range" id="3355.AmbientColor.range">
          <Min index="0" value="0"/>
          <Min index="1" value="0"/>
          <Min index="2" value="0"/>
          <Max index="0" value="1"/>
          <Max index="1" value="1"/>
          <Max index="2" value="1"/>
        </Domain>
      </Property>
      <Property name="BackfaceAmbientColor" id="3355.BackfaceAmbientColor" number_of_elements="3">
        <Element index="0" value="1"/>
        <Element index="1" value="1"/>
        <Element index="2" value="1"/>
        <Domain name="range" id="3355.BackfaceAmbientColor.range">
          <Min index="0" value="0"/>
          <Min index="1" value="0"/>
          <Min index="2" value="0"/>
          <Max index="0" value="1"/>
          <Max index="1" value="1"/>
          <Max index="2" value="1"/>
        </Domain>
      </Property>
      <Property name="BackfaceDiffuseColor" id="3355.BackfaceDiffuseColor" number_of_elements="3">
        <Element index="0" value="1"/>
        <Element index="1" value="1"/>
        <Element index="2" value="1"/>
        <Domain name="range" id="3355.BackfaceDiffuseColor.range">
          <Min index="0" value="0"/>
          <Min index="1" value="0"/>
          <Min index="2" value="0"/>
          <Max index="0" value="1"/>
          <Max index="1" value="1"/>
          <Max index="2" value="1"/>
        </Domain>
      </Property>
      <Property name="BackfaceOpacity" id="3355.BackfaceOpacity" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="range" id="3355.BackfaceOpacity.range">
          <Min index="0" value="0"/>
          <Max index="0" value="1"/>
        </Domain>
      </Property>
      <Property name="ClippingPlanes" id="3355.ClippingPlanes" number_of_elements="0">
        <Domain name="groups" id="3355.ClippingPlanes.groups">
          <Group value="implicit_functions"/>
        </Domain>
      </Property>
      <Property name="ColorArrayName" id="3355.ColorArrayName" number_of_elements="1">
        <Element index="0" value=""/>
        <Domain name="array_list" id="3355.ColorArrayName.array_list"/>
      </Property>
      <Property name="ColorAttributeType" id="3355.ColorAttributeType" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="enum" id="3355.ColorAttributeType.enum">
          <Entry value="0" text="POINT_DATA"/>
          <Entry value="1" text="CELL_DATA"/>
          <Entry value="2" text="FIELD_DATA"/>
        </Domain>
      </Property>
      <Property name="CubeAxesColor" id="3355.CubeAxesColor" number_of_elements="3">
        <Element index="0" value="1"/>
        <Element index="1" value="1"/>
        <Element index="2" value="1"/>
        <Domain name="range" id="3355.CubeAxesColor.range">
          <Min index="0" value="0"/>
          <Min index="1" value="0"/>
          <Min index="2" value="0"/>
          <Max index="0" value="1"/>
          <Max index="1" value="1"/>
          <Max index="2" value="1"/>
        </Domain>
      </Property>
      <Property name="CubeAxesCornerOffset" id="3355.CubeAxesCornerOffset" number_of_elements="1">
        <Element index="0" value="0"/>
        <Domain name="range" id="3355.CubeAxesCornerOffset.range"/>
      </Property>
      <Property name="CubeAxesFlyMode" id="3355.CubeAxesFlyMode" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="enum" id="3355.CubeAxesFlyMode.enum">
          <Entry value="0" text="Outer Edges"/>
          <Entry value="1" text="Closest Triad"/>
          <Entry value="2" text="Furthest Triad"/>
          <Entry value="3" text="Static Triad"/>
          <Entry value="4" text="Static Edges"/>
        </Domain>
      </Property>
      <Property name="CubeAxesInertia" id="3355.CubeAxesInertia" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="range" id="3355.CubeAxesInertia.range">
          <Min index="0" value="1"/>
        </Domain>
      </Property>
      <Property name="CubeAxesTickLocation" id="3355.CubeAxesTickLocation" number_of_elements="1">
        <Element index="0" value="0"/>
        <Domain name="enum" id="3355.CubeAxesTickLocation.enum">
          <Entry value="0" text="Inside"/>
          <Entry value="1" text="Outside"/>
          <Entry value="2" text="Both"/>
        </Domain>
      </Property>
      <Property name="CubeAxesXAxisMinorTickVisibility" id="3355.CubeAxesXAxisMinorTickVisibility" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="bool" id="3355.CubeAxesXAxisMinorTickVisibility.bool"/>
      </Property>
      <Property name="CubeAxesXAxisTickVisibility" id="3355.CubeAxesXAxisTickVisibility" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="bool" id="3355.CubeAxesXAxisTickVisibility.bool"/>
      </Property>
      <Property name="CubeAxesXAxisVisibility" id="3355.CubeAxesXAxisVisibility" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="bool" id="3355.CubeAxesXAxisVisibility.bool"/>
      </Property>
      <Property name="CubeAxesXGridLines" id="3355.CubeAxesXGridLines" number_of_elements="1">
        <Element index="0" value="0"/>
        <Domain name="bool" id="3355.CubeAxesXGridLines.bool"/>
      </Property>
      <Property name="CubeAxesXTitle" id="3355.CubeAxesXTitle" number_of_elements="1">
        <Element index="0" value="X-Axis"/>
      </Property>
      <Property name="CubeAxesYAxisMinorTickVisibility" id="3355.CubeAxesYAxisMinorTickVisibility" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="bool" id="3355.CubeAxesYAxisMinorTickVisibility.bool"/>
      </Property>
      <Property name="CubeAxesYAxisTickVisibility" id="3355.CubeAxesYAxisTickVisibility" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="bool" id="3355.CubeAxesYAxisTickVisibility.bool"/>
      </Property>
      <Property name="CubeAxesYAxisVisibility" id="3355.CubeAxesYAxisVisibility" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="bool" id="3355.CubeAxesYAxisVisibility.bool"/>
      </Property>
      <Property name="CubeAxesYGridLines" id="3355.CubeAxesYGridLines" number_of_elements="1">
        <Element index="0" value="0"/>
        <Domain name="bool" id="3355.CubeAxesYGridLines.bool"/>
      </Property>
      <Property name="CubeAxesYTitle" id="3355.CubeAxesYTitle" number_of_elements="1">
        <Element index="0" value="Y-Axis"/>
      </Property>
      <Property name="CubeAxesZAxisMinorTickVisibility" id="3355.CubeAxesZAxisMinorTickVisibility" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="bool" id="3355.CubeAxesZAxisMinorTickVisibility.bool"/>
      </Property>
      <Property name="CubeAxesZAxisTickVisibility" id="3355.CubeAxesZAxisTickVisibility" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="bool" id="3355.CubeAxesZAxisTickVisibility.bool"/>
      </Property>
      <Property name="CubeAxesZAxisVisibility" id="3355.CubeAxesZAxisVisibility" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="bool" id="3355.CubeAxesZAxisVisibility.bool"/>
      </Property>
      <Property name="CubeAxesZGridLines" id="3355.CubeAxesZGridLines" number_of_elements="1">
        <Element index="0" value="0"/>
        <Domain name="bool" id="3355.CubeAxesZGridLines.bool"/>
      </Property>
      <Property name="CubeAxesZTitle" id="3355.CubeAxesZTitle" number_of_elements="1">
        <Element index="0" value="Z-Axis"/>
      </Property>
      <Property name="CustomBounds" id="3355.CustomBounds" number_of_elements="6">
        <Element index="0" value="0"/>
        <Element index="1" value="1"/>
        <Element index="2" value="0"/>
        <Element index="3" value="1"/>
        <Element index="4" value="0"/>
        <Element index="5" value="1"/>
      </Property>
      <Property name="CustomBoundsActive" id="3355.CustomBoundsActive" number_of_elements="3">
        <Element index="0" value="0"/>
        <Element index="1" value="0"/>
        <Element index="2" value="0"/>
      </Property>
      <Property name="Diffuse" id="3355.Diffuse" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="range" id="3355.Diffuse.range">
          <Min index="0" value="0"/>
          <Max index="0" value="1"/>
        </Domain>
      </Property>
      <Property name="DiffuseColor" id="3355.DiffuseColor" number_of_elements="3">
        <Element index="0" value="1"/>
        <Element index="1" value="1"/>
        <Element index="2" value="1"/>
        <Domain name="range" id="3355.DiffuseColor.range">
          <Min index="0" value="0"/>
          <Min index="1" value="0"/>
          <Min index="2" value="0"/>
          <Max index="0" value="1"/>
          <Max index="1" value="1"/>
          <Max index="2" value="1"/>
        </Domain>
      </Property>
      <Property name="EdgeColor" id="3355.EdgeColor" number_of_elements="3">
        <Element index="0" value="0"/>
        <Element index="1" value="0"/>
        <Element index="2" value="0.500008"/>
        <Domain name="range" id="3355.EdgeColor.range">
          <Min index="0" value="0"/>
          <Min index="1" value="0"/>
          <Min index="2" value="0"/>
          <Max index="0" value="1"/>
          <Max index="1" value="1"/>
          <Max index="2" value="1"/>
        </Domain>
      </Property>
      <Property name="InterpolateScalarsBeforeMapping" id="3355.InterpolateScalarsBeforeMapping" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="bool" id="3355.InterpolateScalarsBeforeMapping.bool"/>
      </Property>
      <Property name="Interpolation" id="3355.Interpolation" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="enum" id="3355.Interpolation.enum">
          <Entry value="0" text="Flat"/>
          <Entry value="1" text="Gouraud"/>
        </Domain>
      </Property>
      <Property name="LineWidth" id="3355.LineWidth" number_of_elements="1">
        <Element index="0" value="6"/>
        <Domain name="range" id="3355.LineWidth.range">
          <Min index="0" value="0"/>
        </Domain>
      </Property>
      <Property name="LookupTable" id="3355.LookupTable" number_of_elements="0">
        <Domain name="groups" id="3355.LookupTable.groups">
          <Group value="lookup_tables"/>
        </Domain>
      </Property>
      <Property name="MapScalars" id="3355.MapScalars" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="bool" id="3355.MapScalars.bool"/>
      </Property>
      <Property name="Material" id="3355.Material" number_of_elements="1">
        <Element index="0" value=""/>
      </Property>
      <Property name="NumberOfSubPieces" id="3355.NumberOfSubPieces" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="range" id="3355.NumberOfSubPieces.range">
          <Min index="0" value="1"/>
        </Domain>
      </Property>
      <Property name="Opacity" id="3355.Opacity" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="range" id="3355.Opacity.range">
          <Min index="0" value="0"/>
          <Max index="0" value="1"/>
        </Domain>
      </Property>
      <Property name="Orientation" id="3355.Orientation" number_of_elements="3">
        <Element index="0" value="0"/>
        <Element index="1" value="0"/>
        <Element index="2" value="0"/>
        <Domain name="range" id="3355.Orientation.range"/>
      </Property>
      <Property name="Origin" id="3355.Origin" number_of_elements="3">
        <Element index="0" value="0"/>
        <Element index="1" value="0"/>
        <Element index="2" value="0"/>
        <Domain name="range" id="3355.Origin.range"/>
      </Property>
      <Property name="Pickable" id="3355.Pickable" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="bool" id="3355.Pickable.bool"/>
      </Property>
      <Property name="PointSize" id="3355.PointSize" number_of_elements="1">
        <Element index="0" value="2"/>
        <Domain name="range" id="3355.PointSize.range">
          <Min index="0" value="0"/>
        </Domain>
      </Property>
      <Property name="Position" id="3355.Position" number_of_elements="3">
        <Element index="0" value="0"/>
        <Element index="1" value="0"/>
        <Element index="2" value="0"/>
        <Domain name="range" id="3355.Position.range"/>
      </Property>
      <Property name="Scale" id="3355.Scale" number_of_elements="3">
        <Element index="0" value="1"/>
        <Element index="1" value="1"/>
        <Element index="2" value="1"/>
        <Domain name="range" id="3355.Scale.range"/>
      </Property>
      <Property name="SelectionCellFieldDataArrayIndex" id="3355.SelectionCellFieldDataArrayIndex" number_of_elements="1">
        <Element index="0" value="0"/>
        <Domain name="range" id="3355.SelectionCellFieldDataArrayIndex.range">
          <Min index="0" value="0"/>
        </Domain>
      </Property>
      <Property name="SelectionCellFieldDataArrayName" id="3355.SelectionCellFieldDataArrayName" number_of_elements="1">
        <Element index="0" value=""/>
        <Domain name="array_list" id="3355.SelectionCellFieldDataArrayName.array_list"/>
      </Property>
      <Property name="SelectionCellLabelBold" id="3355.SelectionCellLabelBold" number_of_elements="1">
        <Element index="0" value="0"/>
        <Domain name="bool" id="3355.SelectionCellLabelBold.bool"/>
      </Property>
      <Property name="SelectionCellLabelColor" id="3355.SelectionCellLabelColor" number_of_elements="3">
        <Element index="0" value="0"/>
        <Element index="1" value="1"/>
        <Element index="2" value="0"/>
        <Domain name="range" id="3355.SelectionCellLabelColor.range">
          <Min index="0" value="0"/>
          <Min index="1" value="0"/>
          <Min index="2" value="0"/>
          <Max index="0" value="1"/>
          <Max index="1" value="1"/>
          <Max index="2" value="1"/>
        </Domain>
      </Property>
      <Property name="SelectionCellLabelFontFamily" id="3355.SelectionCellLabelFontFamily" number_of_elements="1">
        <Element index="0" value="0"/>
        <Domain name="enum" id="3355.SelectionCellLabelFontFamily.enum">
          <Entry value="0" text="Arial"/>
          <Entry value="1" text="Courier"/>
          <Entry value="2" text="Times"/>
        </Domain>
      </Property>
      <Property name="SelectionCellLabelFontSize" id="3355.SelectionCellLabelFontSize" number_of_elements="1">
        <Element index="0" value="35"/>
        <Domain name="range" id="3355.SelectionCellLabelFontSize.range">
          <Min index="0" value="0"/>
        </Domain>
      </Property>
      <Property name="SelectionCellLabelItalic" id="3355.SelectionCellLabelItalic" number_of_elements="1">
        <Element index="0" value="0"/>
        <Domain name="bool" id="3355.SelectionCellLabelItalic.bool"/>
      </Property>
      <Property name="SelectionCellLabelJustification" id="3355.SelectionCellLabelJustification" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="enum" id="3355.SelectionCellLabelJustification.enum">
          <Entry value="0" text="Left"/>
          <Entry value="1" text="Center"/>
          <Entry value="2" text="Right"/>
        </Domain>
      </Property>
      <Property name="SelectionCellLabelOpacity" id="3355.SelectionCellLabelOpacity" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="range" id="3355.SelectionCellLabelOpacity.range">
          <Min index="0" value="0"/>
          <Max index="0" value="1"/>
        </Domain>
      </Property>
      <Property name="SelectionCellLabelShadow" id="3355.SelectionCellLabelShadow" number_of_elements="1">
        <Element index="0" value="0"/>
        <Domain name="bool" id="3355.SelectionCellLabelShadow.bool"/>
      </Property>
      <Property name="SelectionCellLabelVisibility" id="3355.SelectionCellLabelVisibility" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="bool" id="3355.SelectionCellLabelVisibility.bool"/>
      </Property>
      <Property name="SelectionColor" id="3355.SelectionColor" number_of_elements="3">
        <Element index="0" value="1"/>
        <Element index="1" value="0"/>
        <Element index="2" value="1"/>
        <Domain name="range" id="3355.SelectionColor.range">
          <Min index="0" value="0"/>
          <Min index="1" value="0"/>
          <Min index="2" value="0"/>
          <Max index="0" value="1"/>
          <Max index="1" value="1"/>
          <Max index="2" value="1"/>
        </Domain>
      </Property>
      <Property name="SelectionLineWidth" id="3355.SelectionLineWidth" number_of_elements="1">
        <Element index="0" value="2"/>
        <Domain name="range" id="3355.SelectionLineWidth.range">
          <Min index="0" value="0"/>
        </Domain>
      </Property>
      <Property name="SelectionOpacity" id="3355.SelectionOpacity" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="range" id="3355.SelectionOpacity.range">
          <Min index="0" value="0"/>
          <Max index="0" value="1"/>
        </Domain>
      </Property>
      <Property name="SelectionPointFieldDataArrayIndex" id="3355.SelectionPointFieldDataArrayIndex" number_of_elements="1">
        <Element index="0" value="0"/>
        <Domain name="range" id="3355.SelectionPointFieldDataArrayIndex.range">
          <Min index="0" value="0"/>
        </Domain>
      </Property>
      <Property name="SelectionPointFieldDataArrayName" id="3355.SelectionPointFieldDataArrayName" number_of_elements="1">
        <Element index="0" value=""/>
        <Domain name="array_list" id="3355.SelectionPointFieldDataArrayName.array_list"/>
      </Property>
      <Property name="SelectionPointLabelBold" id="3355.SelectionPointLabelBold" number_of_elements="1">
        <Element index="0" value="0"/>
        <Domain name="bool" id="3355.SelectionPointLabelBold.bool"/>
      </Property>
      <Property name="SelectionPointLabelColor" id="3355.SelectionPointLabelColor" number_of_elements="3">
        <Element index="0" value="1"/>
        <Element index="1" value="1"/>
        <Element index="2" value="1"/>
        <Domain name="range" id="3355.SelectionPointLabelColor.range">
          <Min index="0" value="0"/>
          <Min index="1" value="0"/>
          <Min index="2" value="0"/>
          <Max index="0" value="1"/>
          <Max index="1" value="1"/>
          <Max index="2" value="1"/>
        </Domain>
      </Property>
      <Property name="SelectionPointLabelFontFamily" id="3355.SelectionPointLabelFontFamily" number_of_elements="1">
        <Element index="0" value="0"/>
        <Domain name="enum" id="3355.SelectionPointLabelFontFamily.enum">
          <Entry value="0" text="Arial"/>
          <Entry value="1" text="Courier"/>
          <Entry value="2" text="Times"/>
        </Domain>
      </Property>
      <Property name="SelectionPointLabelFontSize" id="3355.SelectionPointLabelFontSize" number_of_elements="1">
        <Element index="0" value="18"/>
        <Domain name="range" id="3355.SelectionPointLabelFontSize.range">
          <Min index="0" value="0"/>
        </Domain>
      </Property>
      <Property name="SelectionPointLabelItalic" id="3355.SelectionPointLabelItalic" number_of_elements="1">
        <Element index="0" value="0"/>
        <Domain name="bool" id="3355.SelectionPointLabelItalic.bool"/>
      </Property>
      <Property name="SelectionPointLabelJustification" id="3355.SelectionPointLabelJustification" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="enum" id="3355.SelectionPointLabelJustification.enum">
          <Entry value="0" text="Left"/>
          <Entry value="1" text="Center"/>
          <Entry value="2" text="Right"/>
        </Domain>
      </Property>
      <Property name="SelectionPointLabelOpacity" id="3355.SelectionPointLabelOpacity" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="range" id="3355.SelectionPointLabelOpacity.range">
          <Min index="0" value="0"/>
          <Max index="0" value="1"/>
        </Domain>
      </Property>
      <Property name="SelectionPointLabelShadow" id="3355.SelectionPointLabelShadow" number_of_elements="1">
        <Element index="0" value="0"/>
        <Domain name="bool" id="3355.SelectionPointLabelShadow.bool"/>
      </Property>
      <Property name="SelectionPointLabelVisibility" id="3355.SelectionPointLabelVisibility" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="bool" id="3355.SelectionPointLabelVisibility.bool"/>
      </Property>
      <Property name="SelectionPointSize" id="3355.SelectionPointSize" number_of_elements="1">
        <Element index="0" value="5"/>
        <Domain name="range" id="3355.SelectionPointSize.range">
          <Min index="0" value="0"/>
        </Domain>
      </Property>
      <Property name="SelectionRepresentation" id="3355.SelectionRepresentation" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="enum" id="3355.SelectionRepresentation.enum">
          <Entry value="0" text="Points"/>
          <Entry value="1" text="Wireframe"/>
          <Entry value="2" text="Surface"/>
        </Domain>
      </Property>
      <Property name="SelectionUseOutline" id="3355.SelectionUseOutline" number_of_elements="1">
        <Element index="0" value="0"/>
        <Domain name="bool" id="3355.SelectionUseOutline.bool"/>
      </Property>
      <Property name="Shading" id="3355.Shading" number_of_elements="1">
        <Element index="0" value="0"/>
        <Domain name="bool" id="3355.Shading.bool"/>
      </Property>
      <Property name="Specular" id="3355.Specular" number_of_elements="1">
        <Element index="0" value="0.1"/>
        <Domain name="range" id="3355.Specular.range">
          <Min index="0" value="0"/>
          <Max index="0" value="1"/>
        </Domain>
      </Property>
      <Property name="SpecularColor" id="3355.SpecularColor" number_of_elements="3">
        <Element index="0" value="1"/>
        <Element index="1" value="1"/>
        <Element index="2" value="1"/>
        <Domain name="range" id="3355.SpecularColor.range">
          <Min index="0" value="0"/>
          <Min index="1" value="0"/>
          <Min index="2" value="0"/>
          <Max index="0" value="1"/>
          <Max index="1" value="1"/>
          <Max index="2" value="1"/>
        </Domain>
      </Property>
      <Property name="SpecularPower" id="3355.SpecularPower" number_of_elements="1">
        <Element index="0" value="100"/>
        <Domain name="range" id="3355.SpecularPower.range">
          <Min index="0" value="0"/>
          <Max index="0" value="100"/>
        </Domain>
      </Property>
      <Property name="StaticMode" id="3355.StaticMode" number_of_elements="1">
        <Element index="0" value="0"/>
        <Domain name="bool" id="3355.StaticMode.bool"/>
      </Property>
      <Property name="SuppressLOD" id="3355.SuppressLOD" number_of_elements="1">
        <Element index="0" value="0"/>
        <Domain name="bool" id="3355.SuppressLOD.bool"/>
      </Property>
      <Property name="Texture" id="3355.Texture" number_of_elements="0">
        <Domain name="groups" id="3355.Texture.groups">
          <Group value="textures"/>
        </Domain>
      </Property>
      <Property name="UseLookupTableScalarRange" id="3355.UseLookupTableScalarRange" number_of_elements="1">
        <Element index="0" value="0"/>
        <Domain name="bool" id="3355.UseLookupTableScalarRange.bool"/>
      </Property>
      <SubProxy name="BackfaceSurfaceRepresentation" servers="1">
        <SubProxy name="GeometryFilter" servers="1"/>
        <SubProxy name="LODMapper" servers="20"/>
        <SubProxy name="Mapper" servers="20"/>
        <SubProxy name="MaterialLoader" servers="1"/>
        <SubProxy name="Prop3D" servers="20"/>
        <SubProxy name="Property" servers="20"/>
      </SubProxy>
      <SubProxy name="CubeAxesRepresentation" servers="1">
        <SubProxy name="OutlineFilter" servers="1"/>
        <SubProxy name="Prop2D" servers="20"/>
        <SubProxy name="Property" servers="20"/>
      </SubProxy>
      <SubProxy name="OutlineRepresentation" servers="1">
        <SubProxy name="Mapper" servers="20"/>
        <SubProxy name="OutlineFilter" servers="1"/>
        <SubProxy name="Prop3D" servers="20"/>
        <SubProxy name="Property" servers="20"/>
      </SubProxy>
      <SubProxy name="SelectionRepresentation" servers="1">
        <SubProxy name="GeometryFilter" servers="1"/>
        <SubProxy name="LODMapper" servers="20"/>
        <SubProxy name="LabelRepresentation" servers="1">
          <SubProxy name="Append" servers="1"/>
          <SubProxy name="CellCentersFilter" servers="21"/>
          <SubProxy name="CellLabelMapper" servers="20"/>
          <SubProxy name="CellLabelProp2D" servers="20"/>
          <SubProxy name="CellLabelProperty" servers="20"/>
          <SubProxy name="Collect" servers="21"/>
          <SubProxy name="PointLabelMapper" servers="20"/>
          <SubProxy name="PointLabelProp2D" servers="20"/>
          <SubProxy name="PointLabelProperty" servers="20"/>
          <SubProxy name="UpdateSuppressor" servers="21"/>
        </SubProxy>
        <SubProxy name="Mapper" servers="20"/>
        <SubProxy name="Prop3D" servers="20"/>
        <SubProxy name="Property" servers="20"/>
      </SubProxy>
      <SubProxy name="SurfaceRepresentation" servers="1">
        <SubProxy name="GeometryFilter" servers="1"/>
        <SubProxy name="LODMapper" servers="20"/>
        <SubProxy name="Mapper" servers="20"/>
        <SubProxy name="MaterialLoader" servers="1"/>
        <SubProxy name="Prop3D" servers="20"/>
        <SubProxy name="Property" servers="20"/>
      </SubProxy>
    </Proxy>
    <Proxy group="sources" type="stlreader" id="3339" servers="1">
      <Property name="FileNameInfo" id="3339.FileNameInfo" number_of_elements="1">
        <Element index="0" value="/Users/<USER>/Desktop/stl/Debug.stl"/>
      </Property>
      <Property name="FileNames" id="3339.FileNames" number_of_elements="1">
        <Element index="0" value="/Users/<USER>/Desktop/stl/Debug.stl"/>
        <Domain name="files" id="3339.FileNames.files"/>
      </Property>
      <Property name="TimestepValues" id="3339.TimestepValues"/>
      <SubProxy name="Reader" servers="1"/>
    </Proxy>
    <Proxy group="misc" type="TimeKeeper" id="4" servers="16">
      <Property name="Time" id="4.Time" number_of_elements="1">
        <Element index="0" value="0"/>
        <Domain name="range" id="4.Time.range"/>
      </Property>
      <Property name="TimeRange" id="4.TimeRange" number_of_elements="2">
        <Element index="0" value="0"/>
        <Element index="1" value="1"/>
      </Property>
      <Property name="TimeSources" id="4.TimeSources" number_of_elements="1">
        <Proxy value="3339"/>
      </Property>
      <Property name="TimestepValues" id="4.TimestepValues"/>
      <Property name="Views" id="4.Views" number_of_elements="1">
        <Proxy value="21"/>
      </Property>
    </Proxy>
    <Proxy group="views" type="RenderView" id="21" servers="1">
      <Property name="GUISize" id="21.GUISize" number_of_elements="2">
        <Element index="0" value="874"/>
        <Element index="1" value="552"/>
        <Domain name="range" id="21.GUISize.range">
          <Min index="0" value="1"/>
          <Min index="1" value="1"/>
        </Domain>
      </Property>
      <Property name="LODResolution" id="21.LODResolution" number_of_elements="1">
        <Element index="0" value="50"/>
        <Domain name="range" id="21.LODResolution.range">
          <Min index="0" value="0"/>
        </Domain>
      </Property>
      <Property name="LODThreshold" id="21.LODThreshold" number_of_elements="1">
        <Element index="0" value="5"/>
        <Domain name="range" id="21.LODThreshold.range">
          <Min index="0" value="0"/>
        </Domain>
      </Property>
      <Property name="RenderInterruptsEnabled" id="21.RenderInterruptsEnabled" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="bool" id="21.RenderInterruptsEnabled.bool"/>
      </Property>
      <Property name="Representations" id="21.Representations" number_of_elements="1">
        <Proxy value="3355"/>
      </Property>
      <Property name="UseImmediateMode" id="21.UseImmediateMode" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="bool" id="21.UseImmediateMode.bool"/>
      </Property>
      <Property name="UseLight" id="21.UseLight" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="bool" id="21.UseLight.bool"/>
      </Property>
      <Property name="UseOffscreenRenderingForScreenshots" id="21.UseOffscreenRenderingForScreenshots" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="bool" id="21.UseOffscreenRenderingForScreenshots.bool"/>
      </Property>
      <Property name="UseTriangleStrips" id="21.UseTriangleStrips" number_of_elements="1">
        <Element index="0" value="0"/>
        <Domain name="bool" id="21.UseTriangleStrips.bool"/>
      </Property>
      <Property name="ViewPosition" id="21.ViewPosition" number_of_elements="2">
        <Element index="0" value="0"/>
        <Element index="1" value="0"/>
        <Domain name="range" id="21.ViewPosition.range">
          <Min index="0" value="0"/>
          <Min index="1" value="0"/>
        </Domain>
      </Property>
      <Property name="ViewTime" id="21.ViewTime" number_of_elements="1">
        <Element index="0" value="0"/>
        <Domain name="range" id="21.ViewTime.range"/>
      </Property>
      <Property name="BackLightAzimuth" id="21.BackLightAzimuth" number_of_elements="1">
        <Element index="0" value="110"/>
        <Domain name="range" id="21.BackLightAzimuth.range">
          <Min index="0" value="60"/>
          <Max index="0" value="170"/>
          <Resolution index="0" value="1"/>
        </Domain>
      </Property>
      <Property name="BackLightElevation" id="21.BackLightElevation" number_of_elements="1">
        <Element index="0" value="0"/>
        <Domain name="range" id="21.BackLightElevation.range">
          <Min index="0" value="-45"/>
          <Max index="0" value="45"/>
          <Resolution index="0" value="1"/>
        </Domain>
      </Property>
      <Property name="BackLightK:B Ratio" id="21.BackLightK:B Ratio" number_of_elements="1">
        <Element index="0" value="3.5"/>
        <Domain name="range" id="21.BackLightK:B Ratio.range">
          <Min index="0" value="1"/>
          <Max index="0" value="15"/>
          <Resolution index="0" value="0.1"/>
        </Domain>
      </Property>
      <Property name="BackLightWarmth" id="21.BackLightWarmth" number_of_elements="1">
        <Element index="0" value="0.5"/>
        <Domain name="range" id="21.BackLightWarmth.range">
          <Min index="0" value="0"/>
          <Max index="0" value="1"/>
          <Resolution index="0" value="0.01"/>
        </Domain>
      </Property>
      <Property name="Background" id="21.Background" number_of_elements="3">
        <Element index="0" value="0.319997"/>
        <Element index="1" value="0.340002"/>
        <Element index="2" value="0.429999"/>
        <Domain name="range" id="21.Background.range">
          <Min index="0" value="0"/>
          <Min index="1" value="0"/>
          <Min index="2" value="0"/>
          <Max index="0" value="1"/>
          <Max index="1" value="1"/>
          <Max index="2" value="1"/>
        </Domain>
      </Property>
      <Property name="Background2" id="21.Background2" number_of_elements="3">
        <Element index="0" value="0"/>
        <Element index="1" value="0"/>
        <Element index="2" value="0.165"/>
        <Domain name="range" id="21.Background2.range">
          <Min index="0" value="0"/>
          <Min index="1" value="0"/>
          <Min index="2" value="0"/>
          <Max index="0" value="1"/>
          <Max index="1" value="1"/>
          <Max index="2" value="1"/>
        </Domain>
      </Property>
      <Property name="BackgroundTexture" id="21.BackgroundTexture" number_of_elements="0">
        <Domain name="groups" id="21.BackgroundTexture.groups">
          <Group value="textures"/>
        </Domain>
      </Property>
      <Property name="CameraClippingRange" id="21.CameraClippingRange" number_of_elements="2">
        <Element index="0" value="0.603014"/>
        <Element index="1" value="3.2433"/>
      </Property>
      <Property name="CameraClippingRangeInfo" id="21.CameraClippingRangeInfo" number_of_elements="2">
        <Element index="0" value="0.603014"/>
        <Element index="1" value="3.2433"/>
      </Property>
      <Property name="CameraFocalPoint" id="21.CameraFocalPoint" number_of_elements="3">
        <Element index="0" value="0.0504688"/>
        <Element index="1" value="-0.0273716"/>
        <Element index="2" value="0.597212"/>
      </Property>
      <Property name="CameraFocalPointInfo" id="21.CameraFocalPointInfo" number_of_elements="3">
        <Element index="0" value="0.0504688"/>
        <Element index="1" value="-0.0273716"/>
        <Element index="2" value="0.597212"/>
      </Property>
      <Property name="CameraParallelProjection" id="21.CameraParallelProjection" number_of_elements="1">
        <Element index="0" value="0"/>
        <Domain name="bool" id="21.CameraParallelProjection.bool"/>
      </Property>
      <Property name="CameraParallelScale" id="21.CameraParallelScale" number_of_elements="1">
        <Element index="0" value="0.75"/>
      </Property>
      <Property name="CameraParallelScaleInfo" id="21.CameraParallelScaleInfo" number_of_elements="1">
        <Element index="0" value="0.75"/>
      </Property>
      <Property name="CameraPosition" id="21.CameraPosition" number_of_elements="3">
        <Element index="0" value="-0.0425428"/>
        <Element index="1" value="2.15541"/>
        <Element index="2" value="-0.38368"/>
      </Property>
      <Property name="CameraPositionInfo" id="21.CameraPositionInfo" number_of_elements="3">
        <Element index="0" value="-0.0425428"/>
        <Element index="1" value="2.15541"/>
        <Element index="2" value="-0.38368"/>
      </Property>
      <Property name="CameraViewAngle" id="21.CameraViewAngle" number_of_elements="1">
        <Element index="0" value="30"/>
      </Property>
      <Property name="CameraViewUp" id="21.CameraViewUp" number_of_elements="3">
        <Element index="0" value="0.335171"/>
        <Element index="1" value="-0.374263"/>
        <Element index="2" value="-0.864631"/>
      </Property>
      <Property name="CameraViewUpInfo" id="21.CameraViewUpInfo" number_of_elements="3">
        <Element index="0" value="0.335171"/>
        <Element index="1" value="-0.374263"/>
        <Element index="2" value="-0.864631"/>
      </Property>
      <Property name="CenterOfRotation" id="21.CenterOfRotation" number_of_elements="3">
        <Element index="0" value="0"/>
        <Element index="1" value="0.5"/>
        <Element index="2" value="0.25"/>
      </Property>
      <Property name="DepthPeeling" id="21.DepthPeeling" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="bool" id="21.DepthPeeling.bool"/>
      </Property>
      <Property name="EyeAngle" id="21.EyeAngle" number_of_elements="1">
        <Element index="0" value="2"/>
        <Domain name="range" id="21.EyeAngle.range">
          <Min index="0" value="0"/>
        </Domain>
      </Property>
      <Property name="FillLightAzimuth" id="21.FillLightAzimuth" number_of_elements="1">
        <Element index="0" value="-10"/>
        <Domain name="range" id="21.FillLightAzimuth.range">
          <Min index="0" value="-90"/>
          <Max index="0" value="90"/>
          <Resolution index="0" value="1"/>
        </Domain>
      </Property>
      <Property name="FillLightElevation" id="21.FillLightElevation" number_of_elements="1">
        <Element index="0" value="-75"/>
        <Domain name="range" id="21.FillLightElevation.range">
          <Min index="0" value="-90"/>
          <Max index="0" value="10"/>
          <Resolution index="0" value="1"/>
        </Domain>
      </Property>
      <Property name="FillLightK:F Ratio" id="21.FillLightK:F Ratio" number_of_elements="1">
        <Element index="0" value="3"/>
        <Domain name="range" id="21.FillLightK:F Ratio.range">
          <Min index="0" value="1"/>
          <Max index="0" value="15"/>
          <Resolution index="0" value="0.1"/>
        </Domain>
      </Property>
      <Property name="FillLightWarmth" id="21.FillLightWarmth" number_of_elements="1">
        <Element index="0" value="0.4"/>
        <Domain name="range" id="21.FillLightWarmth.range">
          <Min index="0" value="0"/>
          <Max index="0" value="1"/>
          <Resolution index="0" value="0.01"/>
        </Domain>
      </Property>
      <Property name="HeadLightK:H Ratio" id="21.HeadLightK:H Ratio" number_of_elements="1">
        <Element index="0" value="3"/>
        <Domain name="range" id="21.HeadLightK:H Ratio.range">
          <Min index="0" value="1"/>
          <Max index="0" value="15"/>
          <Resolution index="0" value="0.1"/>
        </Domain>
      </Property>
      <Property name="HeadLightWarmth" id="21.HeadLightWarmth" number_of_elements="1">
        <Element index="0" value="0.5"/>
        <Domain name="range" id="21.HeadLightWarmth.range">
          <Min index="0" value="0"/>
          <Max index="0" value="1"/>
          <Resolution index="0" value="0.01"/>
        </Domain>
      </Property>
      <Property name="KeyLightAzimuth" id="21.KeyLightAzimuth" number_of_elements="1">
        <Element index="0" value="10"/>
        <Domain name="range" id="21.KeyLightAzimuth.range">
          <Min index="0" value="-90"/>
          <Max index="0" value="90"/>
          <Resolution index="0" value="1"/>
        </Domain>
      </Property>
      <Property name="KeyLightElevation" id="21.KeyLightElevation" number_of_elements="1">
        <Element index="0" value="50"/>
        <Domain name="range" id="21.KeyLightElevation.range">
          <Min index="0" value="0"/>
          <Max index="0" value="90"/>
          <Resolution index="0" value="1"/>
        </Domain>
      </Property>
      <Property name="KeyLightIntensity" id="21.KeyLightIntensity" number_of_elements="1">
        <Element index="0" value="0.75"/>
        <Domain name="range" id="21.KeyLightIntensity.range">
          <Min index="0" value="0"/>
          <Max index="0" value="2"/>
          <Resolution index="0" value="0.05"/>
        </Domain>
      </Property>
      <Property name="KeyLightWarmth" id="21.KeyLightWarmth" number_of_elements="1">
        <Element index="0" value="0.6"/>
        <Domain name="range" id="21.KeyLightWarmth.range">
          <Min index="0" value="0"/>
          <Max index="0" value="1"/>
          <Resolution index="0" value="0.01"/>
        </Domain>
      </Property>
      <Property name="LightAmbientColor" id="21.LightAmbientColor" number_of_elements="3">
        <Element index="0" value="1"/>
        <Element index="1" value="1"/>
        <Element index="2" value="1"/>
        <Domain name="range" id="21.LightAmbientColor.range">
          <Min index="0" value="0"/>
          <Min index="1" value="0"/>
          <Min index="2" value="0"/>
          <Max index="0" value="1"/>
          <Max index="1" value="1"/>
          <Max index="2" value="1"/>
        </Domain>
      </Property>
      <Property name="LightDiffuseColor" id="21.LightDiffuseColor" number_of_elements="3">
        <Element index="0" value="1"/>
        <Element index="1" value="1"/>
        <Element index="2" value="1"/>
        <Domain name="range" id="21.LightDiffuseColor.range">
          <Min index="0" value="0"/>
          <Min index="1" value="0"/>
          <Min index="2" value="0"/>
          <Max index="0" value="1"/>
          <Max index="1" value="1"/>
          <Max index="2" value="1"/>
        </Domain>
      </Property>
      <Property name="LightIntensity" id="21.LightIntensity" number_of_elements="1">
        <Element index="0" value="1"/>
        <Domain name="range" id="21.LightIntensity.range">
          <Min index="0" value="0"/>
          <Max index="0" value="1"/>
        </Domain>
      </Property>
      <Property name="LightSpecularColor" id="21.LightSpecularColor" number_of_elements="3">
        <Element index="0" value="1"/>
        <Element index="1" value="1"/>
        <Element index="2" value="1"/>
        <Domain name="range" id="21.LightSpecularColor.range">
          <Min index="0" value="0"/>
          <Min index="1" value="0"/>
          <Min index="2" value="0"/>
          <Max index="0" value="1"/>
          <Max index="1" value="1"/>
          <Max index="2" value="1"/>
        </Domain>
      </Property>
      <Property name="LightSwitch" id="21.LightSwitch" number_of_elements="1">
        <Element index="0" value="0"/>
        <Domain name="bool" id="21.LightSwitch.bool"/>
      </Property>
      <Property name="MaintainLuminance" id="21.MaintainLuminance" number_of_elements="1">
        <Element index="0" value="0"/>
        <Domain name="bool" id="21.MaintainLuminance.bool"/>
      </Property>
      <Property name="MaximumNumberOfPeels" id="21.MaximumNumberOfPeels" number_of_elements="1">
        <Element index="0" value="4"/>
        <Domain name="range" id="21.MaximumNumberOfPeels.range">
          <Min index="0" value="0"/>
        </Domain>
      </Property>
      <Property name="RenderWindowSizeInfo" id="21.RenderWindowSizeInfo" number_of_elements="2">
        <Element index="0" value="874"/>
        <Element index="1" value="552"/>
      </Property>
      <Property name="StereoRender" id="21.StereoRender" number_of_elements="1">
        <Element index="0" value="0"/>
        <Domain name="bool" id="21.StereoRender.bool"/>
      </Property>
      <Property name="StereoType" id="21.StereoType" number_of_elements="1">
        <Element index="0" value="2"/>
        <Domain name="enum" id="21.StereoType.enum">
          <Entry value="1" text="Crystal Eyes"/>
          <Entry value="2" text="Red-Blue"/>
          <Entry value="3" text="Interlaced"/>
          <Entry value="4" text="Left"/>
          <Entry value="5" text="Right"/>
          <Entry value="6" text="Dresden"/>
          <Entry value="7" text="Anaglyph"/>
          <Entry value="8" text="Checkerboard"/>
        </Domain>
      </Property>
      <Property name="UseGradientBackground" id="21.UseGradientBackground" number_of_elements="1">
        <Element index="0" value="0"/>
        <Domain name="bool" id="21.UseGradientBackground.bool"/>
      </Property>
      <Property name="UseTexturedBackground" id="21.UseTexturedBackground" number_of_elements="1">
        <Element index="0" value="0"/>
        <Domain name="bool" id="21.UseTexturedBackground.bool"/>
      </Property>
      <Property name="ViewSize" id="21.ViewSize" number_of_elements="2">
        <Element index="0" value="874"/>
        <Element index="1" value="552"/>
        <Domain name="range" id="21.ViewSize.range">
          <Min index="0" value="0"/>
          <Min index="1" value="0"/>
        </Domain>
      </Property>
      <SubProxy name="ActiveCamera" servers="16"/>
      <SubProxy name="Interactor" servers="16"/>
      <SubProxy name="InteractorStyle" servers="16"/>
      <SubProxy name="Light" servers="20"/>
      <SubProxy name="LightKit" servers="20"/>
      <SubProxy name="RenderWindow" servers="20"/>
      <SubProxy name="Renderer" servers="20"/>
      <SubProxy name="Renderer2D" servers="20"/>
    </Proxy>
    <ProxyCollection name="animation">
      <Item id="7" name="AnimationScene1"/>
      <Item id="17" name="TimeAnimationCue1"/>
    </ProxyCollection>
    <ProxyCollection name="pq_helper_proxies.3339">
      <Item id="3352" name="RepresentationAnimationHelper"/>
    </ProxyCollection>
    <ProxyCollection name="representations">
      <Item id="3355" name="DataRepresentation4"/>
    </ProxyCollection>
    <ProxyCollection name="sources">
      <Item id="3339" name="Debug.stl"/>
    </ProxyCollection>
    <ProxyCollection name="timekeeper">
      <Item id="4" name="TimeKeeper"/>
    </ProxyCollection>
    <ProxyCollection name="views">
      <Item id="21" name="RenderView1"/>
    </ProxyCollection>
    <CustomProxyDefinitions/>
    <Links/>
    <GlobalPropertiesManagers>
      <GlobalPropertiesManager group="misc" type="GlobalProperties" name="ParaViewProperties">
        <Link global_name="BackgroundColor" proxy="21" property="Background"/>
        <Link global_name="EdgeColor" proxy="3355" property="EdgeColor"/>
        <Link global_name="ForegroundColor" proxy="3355" property="AmbientColor"/>
        <Link global_name="ForegroundColor" proxy="3355" property="CubeAxesColor"/>
        <Link global_name="SelectionColor" proxy="3355" property="SelectionColor"/>
        <Link global_name="SurfaceColor" proxy="3355" property="DiffuseColor"/>
        <Link global_name="SurfaceColor" proxy="3355" property="BackfaceDiffuseColor"/>
      </GlobalPropertiesManager>
    </GlobalPropertiesManagers>
  </ServerManagerState>
  <ViewManager version="3.8.1">
    <MultiView>
      <Splitter index="0" orientation="Horizontal" count="1" sizes="878"/>
    </MultiView>
    <Frame index="0" view_module="21"/>
  </ViewManager>
</ParaView>
