CC = g++ 
LIB = -lm


OBJ = 	lib/conmin.o \
	lib/cnmn01.o \
	lib/cnmn02.o \
	lib/cnmn03.o \
	lib/cnmn04.o \
	lib/cnmn05.o \
	lib/cnmn06.o \
	lib/cnmn07.o \
	lib/cnmn08.o

all: 	parallel1 \
	build/example1 \
	build/example1a \
	build/example2 \
	build/example3

clean:
	rm -f lib/*.o example/*.o build/*

#==============================================================================
# parallel :
#==============================================================================
parallel1: lib/main.o lib/analysis.o $(OBJ)
	$(CC) lib/main.o lib/analysis.o $(OBJ) $(LIB) -o build/parallel1

lib/main.o: main.cpp 
	$(CC) -DUNIX -c main.cpp -o lib/main.o

lib/analysis.o: Analysis.cpp Analysis.h
	$(CC) -DUNIX -c Analysis.cpp -o lib/analysis.o
#==============================================================================

#==============================================================================
# example 1 :
#==============================================================================
build/example1: example/example1.o example/example1_analysis.o $(OBJ)
	$(CC) example/example1.o example/example1_analysis.o $(OBJ) $(LIB) -o build/example1

example/example1.o: example/example1.cpp 
	$(CC) -DUNIX -c example/example1.cpp -o example/example1.o

example/example1_analysis.o: example/example1_analysis.cpp example/example1_analysis.h
	$(CC) -DUNIX -c example/example1_analysis.cpp -o example/example1_analysis.o
#==============================================================================


#==============================================================================
# example 1.a :
#==============================================================================
build/example1a: example/example1a.o example/example1a_analysis.o $(OBJ)
	$(CC) example/example1a.o example/example1a_analysis.o $(OBJ) $(LIB) -o build/example1a

example/example1a.o: example/example1a.cpp 
	$(CC) -DUNIX -c example/example1a.cpp -o example/example1a.o

example/example1a_analysis.o: example/example1a_analysis.cpp example/example1a_analysis.h
	$(CC) -DUNIX -c example/example1a_analysis.cpp -o example/example1a_analysis.o


#==============================================================================
# example 2 :
#==============================================================================
build/example2:example/example2.o example/example2_analysis.o $(OBJ)
	$(CC) example/example2.o example/example2_analysis.o $(OBJ) $(LIB)  -o build/example2 

example/example2.o: example/example2.cpp 
	$(CC) -DUNIX -c example/example2.cpp -o example/example2.o

example/example2_analysis.o: example/example2_analysis.cpp example/example2_analysis.h
	$(CC) -DUNIX -c example/example2_analysis.cpp -o example/example2_analysis.o 

#==============================================================================
# example 3 :
#==============================================================================
build/example3: example/example3.o example/example3_analysis.o $(OBJ)
	$(CC) example/example3.o example/example3_analysis.o $(OBJ) $(LIB) -o build/example3

example/example3.o: example/example3.cpp 
	$(CC) -DUNIX -c example/example3.cpp -o example/example3.o 

example/example3_analysis.o: example/example3_analysis.cpp example/example3_analysis.h
	$(CC) -DUNIX -c example/example3_analysis.cpp -o example/example3_analysis.o



#==============================================================================
# lib 
#==============================================================================

lib/conmin.o: src/conmin.h src/conmin.cpp
	$(CC) -DUNIX -c src/conmin.cpp -o lib/conmin.o

lib/cnmn01.o: src/cnmn01.h src/cnmn01.cpp
	$(CC) -DUNIX -c src/cnmn01.cpp -o lib/cnmn01.o

lib/cnmn02.o: src/cnmn02.h src/cnmn02.cpp
	$(CC) -DUNIX -c src/cnmn02.cpp -o lib/cnmn02.o

lib/cnmn03.o: src/cnmn03.h src/cnmn03.cpp
	$(CC) -DUNIX -c src/cnmn03.cpp -o lib/cnmn03.o

lib/cnmn04.o: src/cnmn04.h src/cnmn04.cpp
	$(CC) -DUNIX -c src/cnmn04.cpp -o lib/cnmn04.o

lib/cnmn05.o: src/cnmn05.h src/cnmn05.cpp
	$(CC) -DUNIX -c src/cnmn05.cpp -o lib/cnmn05.o

lib/cnmn06.o: src/cnmn06.h src/cnmn06.cpp
	$(CC) -DUNIX -c src/cnmn06.cpp -o lib/cnmn06.o

lib/cnmn07.o: src/cnmn07.h src/cnmn07.cpp
	$(CC) -DUNIX -c src/cnmn07.cpp -o lib/cnmn07.o

lib/cnmn08.o: src/cnmn08.h src/cnmn08.cpp
	$(CC) -DUNIX -c src/cnmn08.cpp -o lib/cnmn08.o
#==============================================================================


