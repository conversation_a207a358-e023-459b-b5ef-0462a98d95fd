/*******************************************************************************
ConminC++, to solve constrained or unconstrained function minimization.
Copyright (C) 1999  <PERSON><PERSON> <PERSON><PERSON> Dunn

This program is free software; you can redistribute it and/or
modify it under the terms of the GNU General Public License
as published by the Free Software Foundation; either version 2
of the License, or (at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this program; if not, write to the Free Software
Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA  02111-1307, USA.
*******************************************************************************/

//---------------------------------------------------------------------------

#include "example1_analysis.h"

//---------------------------------------------------------------------------
#pragma package(smart_init)
double Ex1_Analysis(double *x, double *g)
{
	// Routine to calculate objective function and
	// constraints
	double aobj = x[1]*x[1] - 5.*x[1] + x[2]*x[2] - 5.*x[2] + 2.*x[3]*x[3]
	     - 21.*x[3] + x[4]*x[4] + 7.0*x[4] + 50.;

	//
	//
	// Constraint values
	//
	g[1] = x[1]*x[1] + x[1] + x[2]*x[2] - x[2] + x[3]*x[3] + x[3]
	     + x[4]*x[4] - x[4] -8.0;
	g[2] = x[1]*x[1] - x[1] + 2.*x[2]*x[2] + x[3]*x[3] + 2.*x[4]*x[4]
	     - x[4] - 10.0;
	g[3] = 2.*x[1]*x[1] + 2.*x[1] + x[2]*x[2] - x[2] + x[3]*x[3] - x[4] -5.0;

	return aobj;
}
