// =============================================================================
/*!
//  \file test_map 
//  \brief example how you can set up map container 
//  \brief std::map is consider to be like Red-Black tree.
//  \author Cherif <PERSON>
*/
// =============================================================================
#include "map_selector.h"
#include <iostream>
#include <cassert>


//! template <typename Key , typename Value, int selector> map_selector {}; 
//! -----------------------------------------------------------------------
//! selector: |	use map < , >
//! -------------------------------
//!  1	      |	 map<Key , Value  >
//!  2	      |  map<Key , Value *>
//!  3	      |  map<Key*, Value  >
//!  else     |  map<Key*, Value *>
//! -------------------------------

using std::cout ;
using std::endl ;
using ge::map_selector  ;

// =============================================================================
//
// =============================================================================
struct map_config
{
      typedef char  Key ; 
      typedef int   Value ;
      typedef map_selector<Key,Value,1>::type  	MapType;
      // beloow  are not necessary
      typedef MapType::iterator  		Iterator ;
      typedef MapType::const_iterator  		Const_Iterator ;      
      typedef std::pair<Key,Value>   	    Pair;//to use like this :>  mymap.insert(Pair('z',200));  
      typedef std::pair<Iterator,bool>      Ret;
};
// =============================================================================
//
//
//
// =============================================================================
template<class Configure>
struct MyMAP
{
    //=========================================================
    typedef typename Configure::Key 	        Key 	;
    typedef typename Configure::Value	        Value 	;
    typedef typename Configure::MapType	        MapType ;
    typedef typename Configure::Pair	        Pair 	;
    typedef typename Configure::Ret       	Ret 	;
    typedef typename Configure::Iterator  	Iterator;
    typedef typename Configure::Const_Iterator  Const_Iterator;
    //=========================================================
   public:

    MyMAP () {}// default constructor
	
	inline Iterator begin () {return m_map.begin() ;}
	inline Const_Iterator begin ()const {return m_map.begin() ;}

    //==========================================================================
	inline Iterator end () {return m_map.end() ;}
	inline Const_Iterator end ()const {return m_map.end() ;}

    //==========================================================================
	inline std::size_t max_size( ) const {return m_map.max_size(); }
	inline bool empty( ) const {return m_map.empty() ;} 	
	inline void clear( ) const {m_map.clear();}//TODO improov for pointers!!!

    //==========================================================================
    //==========================================================================
	inline Iterator erase( Iterator First,Iterator Last){ //TODO const ??
	  m_map.erase (First,Last) ;
	}


    //==========================================================================
	//Add function <=> to insert
    //==========================================================================
	inline Pair     Add(const Value&  _Val){return m_map.insert(_Val);}
	inline Iterator Add(Iterator _Where,const Value& _Val){ 
	  return m_map.insert(_Val);
	}
	
    //==========================================================================
    //==========================================================================
	inline Iterator Find( const Key& _Key)const { return m_map.find(_Key) ;}
	
  private:
	MapType m_map ;
};



// =============================================================================
//
//
//
// =============================================================================

int main ()
{
	map_config::MapType mymap ;
	map_config::Iterator it ;
	map_config::Ret ret  ;
	
	//!@note for pair you need only typedef, not instance 
	typedef map_config::Pair map_pair ; // in this case, Pair <=>  std::pair<char,int>
// assert       
	assert ( mymap.size() == 0  );
      
// first insert function version (single parameter):

	mymap.insert ( map_pair('a',100) );
	mymap.insert ( map_pair('z',200) );
// assert       
	assert ( mymap.size() == 2  ) ;
	
       ret = mymap.insert ( map_pair('z',500) );
      
       assert (ret.second==false );
      
       if (ret.second==false)
       {
		std::cout << "element 'z' already existed";
		std::cout << " with a value of " << ret.first->second << endl;
       }
      

// second insert function version (with hint position):
      it = mymap.begin();
      
     
      mymap.insert (it, map_pair('b',300));  // max efficiency inserting
      mymap.insert (it, map_pair('c',400));  // no max efficiency inserting

      
// third insert function version (range insertion):
      map_config::MapType anothermap;
      anothermap.insert( mymap.begin(),mymap.find('c') );
    
    
    

// showing contents:
      cout << "mymap contains:\n";
      for ( it=mymap.begin() ; it != mymap.end(); it++ )
	cout << (*it).first << " => " << (*it).second << endl;

      cout << "anothermap contains:\n";
      for ( it=anothermap.begin() ; it != anothermap.end(); it++ )
	cout << (*it).first << " => " << (*it).second << endl;

    return 0 ;
}


