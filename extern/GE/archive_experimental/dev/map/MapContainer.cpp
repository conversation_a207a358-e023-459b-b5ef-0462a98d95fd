#ifndef MAPCONTAINER_HH
#define MAPCON<PERSON>INER_HH

template<class Configure>
struct MapContainer{
        typedef typename Configure::Key 	Key 	;
        typedef typename Configure::Value	Value 	;
        typedef typename Configure::MapType	MapType ;
        typedef typename Configure::Pair	Pair 	;
        typedef typename Configure::Ret       	Ret 	;
	typedef typename Configure::Iterator  	Iterator;
  public:
	MapContainer () {}// default constructor
	inline Pair     Add(const Value&  _Val){return m_map.insert(_Val);}
	inline Iterator Add(Iterator _Where,const Value& _Val){ 
	  return m_map.insert(_Val);
	}
  private:
	MapType m_map ;
};

#endif