#ifndef OUTOUTCONTAINER_HH
#define OUTOUTCONTAINER_HH

#include "../accel/Sector.h"
#include <boost/ptr_container/ptr_vector.hpp>

// =======================================================================
//
// note its very similar to what Jan have implementes alreday in waLBerla
// 
// =======================================================================
template<typename Configure>
class OutputContainer
{
      typedef Configure configure ;
      typedef typename configure::WordType   WordType   ;
      typedef typename configure::SectorType SectorType ;
      

      OutputContainer() ; // default constructor 
  private:
      std::ptr_vector<SectorType >  m_OutputSectorList ;
};

template<typename configur>
OutputContainer::OutputContainer() {


}





#endif 
