// =============================================================================
/*!
// @file Sphere.h
// @brief Header file for Sphere class.
// <AUTHOR> <PERSON>
// @email  <EMAIL>
//
//  Modified on 2010-10-31, 2010 , 20:13:40
*/
// =============================================================================

#ifndef SPHERE_HH
#define SPHERE_HH

// =============================================================================
/*
//	This program is free software; you can redistribute it and/or
//	modify it under the terms of the GNU General Public License
//	as published by the Free Software Foundation; either version 2
//	of the License, or (at your option) any later version.
//
//	This program is distributed in the hope that it will be useful,
//	but WITHOUT ANY WARRANTY; without even the implied warranty of
//	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
//	GNU General Public License for more details.
//
//	You should have received a copy of the GNU General Public License
//	along with this program; if not, write to the Free Software
//	Foundation, Inc., 51 Franklin Street, Fifth Floor,
//	Boston, MA  02110-1301, USA.
//
//	
//	Copyright (C) 2010, Cherif <<EMAIL>>
*/
// =============================================================================



// =============================================================================
// includes
// =============================================================================
#include "TriShapeBase.h"
#include "../gmath/Point3D.h"
#include <vector>
#include <cmath> // sqrt
#include <iomanip>
#include <fstream>
#include <cstdlib>
#include <cassert>// stdexcept header file contains runtime_error
//==============================================================================




//==============================================================================
/*!
// @brief class Sphere, to generate triangular-Mesh approximation of an sphere 
// 
// 
// theoretical volume = 4/3 (PI  R^3) = ( PI /6 ) * d^ 3 , so if you use :
//  sphere (level = 4 ===> # triangles = 2048 ) have approximately volume and not
//  the theoretical one
// TODO use Point3D<float>, compute the normals
// TODO center , ray , binary 
// TODO object management advance c++
*/
//==============================================================================

namespace ge
{
	// =====================================================================
	// triangle class 
	// =====================================================================
	struct Triangle
	{
		Point3D  <float> v0 , v1, v2;  	/*! <vertices >*/
		Point3D  <float> c;	   	/*! <centroid >*/
		Vector3D <float> normal ;	/*! <vector normals >*/
	};
	// overload output-operator for the class triangle
	inline std::ostream& operator << (std::ostream& out, const Triangle& T)
	{
		out <<"< "<< T.v0<< " , " << T.v1<< " , " << T.v2<<" >\n" ;
		return out ;
	}
	// =====================================================================


	// =====================================================================
	// help function to normalize 3d-point coordinates
	// =====================================================================
	inline static void Normalise (Point3D<float>* P)
	{
		const float m = std::sqrt ( (P->m_x ) * (P->m_x) +
					    (P->m_y ) * (P->m_y) + 
					    (P->m_z ) * (P->m_z)  );
		assert (m != 0.0f) ;
		const float inv_m = 1.0f / m ;
		P->m_x *= inv_m ;
		P->m_y *= inv_m ;
		P->m_z *= inv_m ;
	}
	

	// =====================================================================
	/*!
	// @class Sphere
	// @brief Sphere class implementation
	*/
	// =====================================================================
	struct Sphere : TriShapeBase<Sphere>
	{
	  public:
	    	// =============================================================
	        // default constructor
	        // =============================================================
		explicit Sphere () ;
		
		// =============================================================
		// construct sphere for given center and ray 
		// =============================================================
		explicit Sphere (const Point3D<float>& center,const double& radius,const int& n_facet ); // The number of facets will be (4^iterations) * 8 
		// explicit Sphere (const Point& center, const double& radius,const int& n_iteration ) ; // The number of facets will be (4^iterations) * 8 
		
		
		// =============================================================
		// Algorithms implementation.
		// =============================================================
		inline void implementation() const { }// TODO rename it to Implementation
		inline void Trace() const ;
		inline bool Intersect (const Ray& ray ) ;
		

		// =============================================================
		// 
		// =============================================================
                //TODO
                inline void Transform() ;
                inline void Voxelize () ;
                inline void Move     (const double& dt) ;
		// =============================================================
		
	  private :
		// =============================================================
		// private member data
		// =============================================================
		Point3D<float>         m_center ; ///< center
		double                 m_radius ; ///< radius
		std::vector<Triangle > m_mesh   ; // set of triangle mesh

	  public:
	  
		inline int  Create	 (const int& iter) ; // return true if success, else return false
		inline void ExportToStl  (const char* fname)const ;
		inline bool ComputeNorms (void) ;
		
	  public:
//		/*virtual*/ void accept(class Visitor &v) ;

		inline friend std::ostream& operator << (std::ostream& output,const Sphere& p);
	};
	// =====================================================================
	
	
	
	
	
	
	// =====================================================================
	// default constructor
	// =====================================================================
	Sphere::Sphere () 
	{
		m_type = SPHERE ;
	}
    
	// =====================================================================
	// construct sphere for given center and ray
	// The number of facets will be (4^iterations) * 8 
	// =====================================================================
	Sphere::Sphere(const Point3D<float>& center,const double& radius,const int& n_facet) 
	{
		m_type = SPHERE ;
		// do the rest
			
	}		
// explicit Sphere (const Point& center, const double& radius,const int& n_iteration ) ; // The number of facets will be (4^iterations) * 8 
		
	
	
	
	
	
	
	
	inline void Sphere::Trace() const
	{
		std::cout << "Sphere::Trace ----> sphere [ " << m_id << " ]\n";
	}

	
	inline std::ostream& operator<<(std::ostream& output,const Sphere& P) 
	{
	 	output << " I' Sphere ["<< P.m_id << "]\n";
	 	return output ;
	}	
	
	
	
	
	// =====================================================================
	// 
	// Applying transformation for sphere
	// 
	// =====================================================================
	inline void Sphere::Transform()
	{
		std::cout<< " sphere["<< m_id << "]::Transform\n";
	
	 	// TODO applying the transformation -> TMatrix
	 	// 
	 	// 
	 	// 
	}
	
	
	
	// =====================================================================
	// 
	// Move sphere
	// 
	// =====================================================================
	inline void Sphere::Move(const double& dt)
	{
	 	std::cout<< " sphere["<< m_id << "]::Move, dt  ="<< dt << std::endl ;
	  
	 	// TODO applying the transformation -> TMatrix
	 	// 
	 	// 
	 	// 
	}
	
	
	// Uniform Distribution -> hypercube rejection method :
	// To apply this to a unit cube at the origin, choose coordinates (x,y,z) each uniformly distributed on the interval [-1,1].
	// If the length of this vector is greater than 1 then reject it, otherwise normalise it and use it as a sample. 
	//!@return int number of faces (triangles)
	inline int Sphere::Create (const int& iterations)
	{
		// unit cube at the origin. 
		Point3D<float> *corner[6] =
		{
			new Point3D<float> ( 0.0 ,  0.0   ,  1.0), // corner[0]
			new Point3D<float> ( 0.0 ,  0.0   , -1.0), // corner[1]
			new Point3D<float> (-1.0 , -1.0   ,  0.0), // corner[2]
			new Point3D<float> ( 1.0 , -1.0   ,  0.0), // corner[3]
			new Point3D<float> ( 1.0 ,  1.0   ,  0.0), // corner[4]
			new Point3D<float> (-1.0 ,  1.0   ,  0.0)  // corner[5]
		};

		Point3D<float> pa,pb,pc;
		int nt = 0,ntold;
		const float a = 1.0 / sqrt(2.0);
		
		/* first create the octahredon*/
		for (int i=0;i<6;i++) {
		    corner[i]->m_x *= a;
		    corner[i]->m_y *= a;
		  //corner[i]->m_z *= a;
		}
			
		m_mesh.resize (8);// nt = 8; // number of triangles = 8 
		
		m_mesh[0].v0 = *corner[0];
		m_mesh[0].v1 = *corner[3];
		m_mesh[0].v2 = *corner[4];
			
		m_mesh[1].v0 = *corner[0]; m_mesh[1].v1 = *corner[4]; m_mesh[1].v2 = *corner[5];
		m_mesh[2].v0 = *corner[0]; m_mesh[2].v1 = *corner[5]; m_mesh[2].v2 = *corner[2];
		m_mesh[3].v0 = *corner[0]; m_mesh[3].v1 = *corner[2]; m_mesh[3].v2 = *corner[3];
		m_mesh[4].v0 = *corner[1]; m_mesh[4].v1 = *corner[4]; m_mesh[4].v2 = *corner[3];
		m_mesh[5].v0 = *corner[1]; m_mesh[5].v1 = *corner[5]; m_mesh[5].v2 = *corner[4];
		m_mesh[6].v0 = *corner[1]; m_mesh[6].v1 = *corner[2]; m_mesh[6].v2 = *corner[5];
		m_mesh[7].v0 = *corner[1]; m_mesh[7].v1 = *corner[3]; m_mesh[7].v2 = *corner[2];
		
		nt = 8 ;
		
		int tmp_ = nt * pow (4, iterations) ;
		
		std::cout << std::endl 	<< " Number of triangles id  -> " 
			  << tmp_ 	<< std::endl;
		
		m_mesh.resize (tmp_);
		
		/* refinement  */
		/* Bisect each edge and move to the surface of a unit sphere */
		for (int it=0;it<iterations;it++)
		{
			ntold = nt;
			for (int i=0;i<ntold;i++)
			{
				pa  = 0.5f * ( m_mesh[i].v0 + m_mesh[i].v1 ) ;
				pb  = 0.5f * ( m_mesh[i].v1 + m_mesh[i].v2 ) ;
				pc  = 0.5f * ( m_mesh[i].v2 + m_mesh[i].v0 ) ; 
		
				Normalise(&pa);
				Normalise(&pb);
				Normalise(&pc);
				
				m_mesh[nt].v0 = m_mesh[i].v0;
				m_mesh[nt].v1 = pa;
				m_mesh[nt].v2 = pc;
				nt++;
		
				m_mesh[nt].v0 = pa; 
				m_mesh[nt].v1 = m_mesh[i].v1;
				m_mesh[nt].v2 = pb;
				nt++;
				
				m_mesh[nt].v0 = pb;
				m_mesh[nt].v1 = m_mesh[i].v2;
				m_mesh[nt].v2 = pc;
				nt++;
				
				m_mesh[i].v0 = pa;
				m_mesh[i].v1 = pb;
				m_mesh[i].v2 = pc;
			}
		}
		return nt;
	}
	
	
	
	// =====================================================================
	// Method which export the mesh to 3d-stl file format
	// =====================================================================
	inline void Sphere::ExportToStl (const char* fname )const
	{
		FILE * pFile;
		pFile = fopen ( fname,"w");
		assert ( (pFile != NULL) );
		fprintf( pFile, "%s\n", "solid 3d object Created Using GE tool. author:Cherif.Mihoubi");
		assert (m_mesh.size() !=0 ) ;
		//TODO compute the normals
		int count = 0 ;
		for (std::size_t i=0; i< m_mesh.size() ; i++ )
		{
		  Triangle tmp = m_mesh[i] ;		  
		  // fprintf(pFile, "%s %lf %lf %lf\n","facet normal ", Nm_mesh[0] , Nm_mesh[1] , Nm_mesh[2]);
		  fprintf(pFile,"%s %lf %lf %lf\n","  facet normal ", 0. , 1. , 0.);
		  fprintf(pFile,"%s\n","    outer loop");//outer loop
		  fprintf(pFile,"%s %lf %lf %lf\n", "      vertex " ,tmp.v0.m_x , tmp.v0.m_y , tmp.v0.m_z);
		  fprintf(pFile,"%s %lf %lf %lf\n", "      vertex " ,tmp.v1.m_x , tmp.v1.m_y , tmp.v1.m_z);
		  fprintf(pFile,"%s %lf %lf %lf\n", "      vertex " ,tmp.v2.m_x , tmp.v2.m_y , tmp.v2.m_z);
		  fprintf(pFile,"%s\n", "    endloop"); // end outer loop
		  fprintf(pFile,"%s\n","  endfacet"); // end fcet
		}
		fprintf(pFile, "%s", "endsolid");
		fclose (pFile);
	}

	// =====================================================================
	// Method which compute and setup the sphere->normals
	// =====================================================================
	inline bool Sphere::ComputeNorms (void) 
	{
		//	  
	}

}


#endif 


