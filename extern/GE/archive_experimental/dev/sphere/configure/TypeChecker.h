#ifndef TYPECHECKER_HH
#define TYPECHECKER_HH

#include <typeinfo>
#include <cassert>


namespace ge
{
    //! =====================================================================
    //! 				TypeChecker
    //! =====================================================================

    template <class ElementType>
    struct DynamicTypeChecker {
	  inline static void check(const ElementType& e) { 
	    assert( typeid(e)==typeid(ElementType) );
	  }
    };

    template <class ElementType>
    struct EmptyTypeChecker{
	  inline static void check(const ElementType& e) {} // does nothing
    };
    
    //! =====================================================================    
}    
#endif



