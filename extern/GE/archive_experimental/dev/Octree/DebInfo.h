#ifndef DEBINFO_HH
#define DEBINFO_HH

#include "octree"

// =============================================================================
//  				  the map
// =============================================================================
/*
				      F
				    /    \
				  B	   G
				/  \         \
			      A    D         I
				  / \       /
				c   E     H

*/
// =============================================================================
// * Preorder traversal sequence: F, B, A, D, C, E, G, I, H (root, left, right)
// * Inorder traversal sequence: A, B, C, D, E, F, G, H, I (left, root, right); note how this produces a sorted sequence
// * Postorder traversal sequence: A, C, E, D, B, H, I, G, F (left, right, root)
// * Level-order traversal sequence: F, B, G, A, D, I, C, E, H
// =============================================================================




// =============================================================================
// preorder traverse the tree
// all nodes wihtout exception
// =============================================================================
template< typename T_, int d_  >
inline static void AllNodesDepthFirstTraversal( octree<T_, d_>& My_octree )
{
      typename octree<T_,d_>::iterator it; 
      for ( it = My_octree.begin(false); it != My_octree.end(false); ++it )
      {
	std::cout<<"Node "<<" ("<<(it.level()) << ") "<<" = "<<it->value()<<"\n";
      }
}




// =============================================================================
// preorder traverse the tree
// only leaves nodes
// =============================================================================
template< typename T_, int d_  >
inline static void OnlyLeafsNodesDepthFirstTraversal( octree<T_, d_>& My_octree)
{
      typename octree<T_,d_>::iterator it; 
      for ( it = My_octree.begin(); it != My_octree.end(); ++it )
      {
	std::cout<<"Node "<<" ("<<(it.level()) << ") "<<" = "<<it->value()<<"\n";
      }
}



// =============================================================================
// Print only leaf nodes.inorder (symetric);
// it the opposit of what wikipedia say;
// traverse first right, then the parent, and after that the left
// =============================================================================
template< typename T_, int d_  >
inline static void OnlyLeafsNodesInorderSymetricDepthFirstTraversal( octree<T_, d_>& My_octree)
{
  typename octree<T_,d_>::iterator it; 
  it = My_octree.end();
  do
    {
    --it;
    std::cout
      << "Node  " << " (" << (it.level()) << ") "
      << " = " << it->value() << "\n";
    }
  while ( it != My_octree.begin() );
}
// =============================================================================




// =============================================================================
// Print only leaf nodes.inorder (symetric);
// it the opposit of what wikipedia say;
// traverse first right, then the parent, and after that the left
// =============================================================================
template< typename T_, int d_  >
inline static void AllNodesInorderSymetricDepthFirstTraversal( octree<T_, d_>& My_octree)
{
  typename octree<T_,d_>::iterator it; 
  it = My_octree.end( false );
  do
    {
    --it;
    std::cout
      << "Node  " 
      << " (" << (it.level()) << ") "
      << " = " << it->value() << "\n";
    }
  while ( it != My_octree.begin( false ) );
  
}
// =============================================================================





#endif

