//  for_each example
#include <iostream>
#include <algorithm>
#include <vector>

#ifdef USE_OMP
#include <omp.h>
#include <parallel/algorithm>
#include <parallel/settings.h>
#endif

// =============================================================================
// includes
// =============================================================================
#if defined(linux)||defined(__linux)||defined(__APPLE__)//any thing else than Windows
  #include <sys/time.h>
  #include <sys/types.h>
  #include <stdlib.h>
  #include <sys/resource.h>
#endif
// =============================================================================




using namespace std;

#define N 100000000 


inline void myfunction (int& i) {
	i++;
}

struct myclass {
	inline void operator() (int& i) 
	{
	      i++;
	}
} myobject;


// =============================================================================
// Compute the wall time.
// =============================================================================
inline double WcTiming(void)
{
	struct timeval tp;
	gettimeofday(&tp, NULL);
	return (double) (tp.tv_sec + tp.tv_usec/1000000.0);
}
// =============================================================================





int main ()
{
#ifdef USE_OMP
	// Explicitly set number of threads.
	  const int threads_wanted = 4;
	  omp_set_dynamic(true);
	  omp_set_num_threads(threads_wanted);
	__gnu_parallel::_Settings s;
	  s.algorithm_strategy = __gnu_parallel::force_parallel;
	__gnu_parallel::_Settings::set(s);
#endif

	vector<int> myvector;
	
	for (int i = 0 ; i <= N ; ++i )
	    myvector.push_back(i);


	const double t0  = WcTiming () ;

// 	#pragma omp single nowait
	#ifdef USE_OMP
	  std::__parallel::for_each (myvector.begin(), myvector.end(), myfunction);
	#else
	 std::for_each (myvector.begin(), myvector.end(), myfunction);
	#endif
	  
	std::cout<<" test 1 took : " << WcTiming ()  - t0 << " sec\n" ; 
	// or:

	
	std::cout << "\n\n" ;
	const double t1  = WcTiming () ;

#ifdef USE_OMP
	std::__parallel::for_each (myvector.begin(), myvector.end(), myobject);
#else
	std::for_each (myvector.begin(), myvector.end(), myobject);
#endif
	std::cout<<" test 2 took : " << WcTiming ()  - t1 << " sec\n" ;
	cout << endl;

	return 0;
}


// serial : 
// test 1 took : 1.23979 sec
// test 2 took : 1.25209 sec





// 4 threads 				speed up
// test 1 took : 0.462974 sec		2.6666
// test 2 took : 0.582457 sec		2.15



// 8 threads 				speed up  
// static
//  test 1 took : 0.711485 sec		
//  test 2 took : 0.432827 sec		

// dynamic
// test 1 took : 0.386355 sec
// test 2 took : 0.432069 sec

