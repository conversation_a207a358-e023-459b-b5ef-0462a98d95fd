// Exception
// c-style 

void f (int *p )
{
//     ...
    assert ( p != 0 ) // asseert that p != 0; abort() if p is sero (NULL)
// ....
}
// // // // // // // // // // // // // // // // // // // // // // // //
// c++ style ---> best  

template <class X , class A>
inline void Assert (A assertion)
{
    if ( ! assertion  ) throw  X() ;
}

// now example 
struc Bad_arg {};


void f (int *p)
{
  Assert<Bad_arg> (p!=0) ; // // asseert that p != 0; throw Bad_arg unless p != 0

//   ...
}

// more better 
void g (int *p)
{
  Assert<Bad_arg> (NDEBUG || p!=0) ; // either I'm not debuging or p != 0;
  //   ...
}

#ifndef NDEBUG
    const bool ARG_CHECK = false ;
#else 
    const bool ARG_CHECK = true ;   
#endif

void g (int *p)
{
  Assert<Bad_arg> (ARG_CHECK || p!=0) ; // either I'm not debuging or p != 0;
  //   ...
}

//note its under developement, not yet tested :)
template <class Exception, class T>
struct AssertSampleConfig
{
    inline static void Assert ( T assertion ){
            if ( NDEBUG || !assertion )
                    throw Exception( ) ; // either I'm not debuging or assertion is fullfied ;
    }
}







