//=================================================================================================
/*!
//  \file TurbulenceModel.cpp
//  \brief Implementation of base turbulence model methods
*/
//=================================================================================================

#include "TurbulenceModel.h"
#include "../Lattice.h"
#include "../Tensor.h"
#include "../Turbulence.h"
#include <cmath>

namespace walberla {
namespace turbulence {

//=================================================================================================
//
//  BASE CLASS METHOD IMPLEMENTATIONS
//
//=================================================================================================

Matrix3<Real> TurbulenceModel::computeStrainRateTensor(
    const Uint x, const Uint y, const Uint z,
    const Real& rho,
    const Vector3<Real>& vel,
    const PDFField& pdf,
    const VelField& velField,
    const Real& tau,
    Real& strainRateMagnitude) const
{
    Matrix3<Real> S;
    
    switch(strainRateMethod_)
    {
        case NON_EQUILIBRIUM:
        {
            // Compute from non-equilibrium stress tensor
            Matrix3<Real> piNeq = computeNonEquilibriumStressTensor(x, y, z, rho, vel, pdf);
            // Convert to strain rate tensor: S = -Pi^neq / (2 * rho * c_s^2 * tau)
            const Real factor = -1.0 / (2.0 * rho * tau);
            S = piNeq * factor;
            break;
        }
        
        case FINITE_DIFF:
        {
            // Compute from velocity gradients
            S = computeStrainRateTensorFD(x, y, z, velField);
            break;
        }
        
        case HYBRID:
        {
            // Average both methods (can be weighted differently)
            Matrix3<Real> piNeq = computeNonEquilibriumStressTensor(x, y, z, rho, vel, pdf);
            const Real factor = -1.0 / (2.0 * rho * tau);
            Matrix3<Real> S_neq = piNeq * factor;
            Matrix3<Real> S_fd = computeStrainRateTensorFD(x, y, z, velField);
            S = (S_neq + S_fd) * 0.5;
            break;
        }
    }
    
    strainRateMagnitude = computeStrainRateMagnitude(S);
    return S;
}

Real TurbulenceModel::computeStrainRateMagnitude(const Matrix3<Real>& S) const
{
    // |S| = sqrt(2 * S_ij * S_ij)
    Real sum = 0.0;
    for(int i = 0; i < 3; ++i) {
        for(int j = 0; j < 3; ++j) {
            sum += S(i,j) * S(i,j);
        }
    }
    return std::sqrt(2.0 * sum);
}

Matrix3<Real> TurbulenceModel::computeNonEquilibriumStressTensor(
    const Uint x, const Uint y, const Uint z,
    const Real& rho,
    const Vector3<Real>& vel,
    const PDFField& pdf) const
{
    Matrix3<Real> piNeq(0.0);
    const Real u_sqr = 1.5 * (vel[X]*vel[X] + vel[Y]*vel[Y] + vel[Z]*vel[Z]);
    
    // Pi^neq_ab = sum_i (c_ia * c_ib - c_s^2 * delta_ab) * (f_i - f_i^eq)
    for(int l = 0; l < D3Q19::Cellsize; ++l)
    {
        const Real cx = D3Q19::cx[l];
        const Real cy = D3Q19::cy[l];
        const Real cz = D3Q19::cz[l];
        
        // Compute equilibrium distribution
        const Real cu = cx*vel[X] + cy*vel[Y] + cz*vel[Z];
        const Real f_eq = D3Q19::w[l] * rho * (1.0 + 3.0*cu + 4.5*cu*cu - u_sqr);
        const Real f = pdf.GET_PDF(x, y, z, l);
        const Real f_neq = f - f_eq;
        
        // Add contribution to stress tensor
        const Real cs2 = 1.0/3.0; // c_s^2 for D3Q19
        
        piNeq(0,0) += (cx*cx - cs2) * f_neq;
        piNeq(0,1) += cx*cy * f_neq;
        piNeq(0,2) += cx*cz * f_neq;
        piNeq(1,0) += cy*cx * f_neq;
        piNeq(1,1) += (cy*cy - cs2) * f_neq;
        piNeq(1,2) += cy*cz * f_neq;
        piNeq(2,0) += cz*cx * f_neq;
        piNeq(2,1) += cz*cy * f_neq;
        piNeq(2,2) += (cz*cz - cs2) * f_neq;
    }
    
    return piNeq;
}

Matrix3<Real> TurbulenceModel::computeStrainRateTensorFD(
    const Uint x, const Uint y, const Uint z,
    const VelField& velField) const
{
    // Get velocity gradient tensor
    Matrix3<Real> gradU = computeVelocityGradient(x, y, z, velField);
    
    // Compute strain rate tensor: S_ij = 0.5 * (du_i/dx_j + du_j/dx_i)
    Matrix3<Real> S;
    S(0,0) = gradU(0,0);
    S(1,1) = gradU(1,1);
    S(2,2) = gradU(2,2);
    S(0,1) = 0.5 * (gradU(0,1) + gradU(1,0));
    S(0,2) = 0.5 * (gradU(0,2) + gradU(2,0));
    S(1,2) = 0.5 * (gradU(1,2) + gradU(2,1));
    S(1,0) = S(0,1);
    S(2,0) = S(0,2);
    S(2,1) = S(1,2);
    
    return S;
}

Matrix3<Real> TurbulenceModel::computeVelocityGradient(
    const Uint x, const Uint y, const Uint z,
    const VelField& velField,
    const Real& dx) const
{
    Matrix3<Real> gradU;
    
    // Second-order central differences
    Vector3<Real> u_xp, u_xm, u_yp, u_ym, u_zp, u_zm;
    velField.GET_VEL(x+1, y, z, u_xp);
    velField.GET_VEL(x-1, y, z, u_xm);
    velField.GET_VEL(x, y+1, z, u_yp);
    velField.GET_VEL(x, y-1, z, u_ym);
    velField.GET_VEL(x, y, z+1, u_zp);
    velField.GET_VEL(x, y, z-1, u_zm);
    
    const Real inv_2dx = 0.5 / dx;
    
    // du/dx components
    gradU(0,0) = (u_xp[X] - u_xm[X]) * inv_2dx;
    gradU(1,0) = (u_xp[Y] - u_xm[Y]) * inv_2dx;
    gradU(2,0) = (u_xp[Z] - u_xm[Z]) * inv_2dx;
    
    // du/dy components
    gradU(0,1) = (u_yp[X] - u_ym[X]) * inv_2dx;
    gradU(1,1) = (u_yp[Y] - u_ym[Y]) * inv_2dx;
    gradU(2,1) = (u_yp[Z] - u_ym[Z]) * inv_2dx;
    
    // du/dz components
    gradU(0,2) = (u_zp[X] - u_zm[X]) * inv_2dx;
    gradU(1,2) = (u_zp[Y] - u_zm[Y]) * inv_2dx;
    gradU(2,2) = (u_zp[Z] - u_zm[Z]) * inv_2dx;
    
    return gradU;
}

} // namespace turbulence
} // namespace walberla