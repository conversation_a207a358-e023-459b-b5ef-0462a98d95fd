#include <iostream>

//  ============================================================================
//  <!--- 		basic example		---!>
//  ============================================================================
class Car
{
  public: 
      enum Model {BMW =0 , TOYOTA, FORD};
      // constructor 
      Car(const Model& mod = BMW) : m_model (mod) {}
      
  private:
      Model m_model ;
};
//  ============================================================================



//  ============================================================================
//  <!--- 		Advances 		---!>
// 
//  note :  better use iterator style additionaly.
// 
//  ============================================================================
#define MAX_OBJECTNUMBERS 10
class ListOfObjects
{
    //  ========================================================================
    class Object1{
	  // constructor 
	  // Destructor
    } ;
    
    //  ========================================================================
    struct Object2{ // Jan.G style
      // constructor 
      // Destructor
    };
    
    //  ========================================================================
  public :
    inline void Add (const Object& n_obj ); // add new object to the list
    
  private: // 2 containers of each objects-typs 
    Object1 m_Obj_item [MAX_OBJECTNUMBERS ];//TODO u can used std::vector(list)
					    //or boost::ptr_vector is much better
    Object2 m_Obj_item2[MAX_OBJECTNUMBERS ];
};
//  ============================================================================





// =============================================================================
// 
// =============================================================================
int main()
{

  Car* mycar = new Car ( Car::FORD ) ;
 
  
  return (0) ;
}