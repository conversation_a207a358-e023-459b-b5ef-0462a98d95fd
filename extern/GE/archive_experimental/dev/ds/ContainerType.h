#ifndef CONTAINER_TYPE_HH
#define CONTAINER_TYPE_HH

#include <iostream>
#include <assert.h>


#include <vector>
#include <list>
#include <deque>
#include <limits>


// neu 
#include <boost/ptr_container/ptr_vector.hpp>
#include <boost/shared_ptr.hpp>



//!TODO stl::list neeed to be implement :)
// it only for vertex type no thing else

//!TODO stl::list neeed to be implement :)
// TODO trace container
// TODO std::allocator
// TODO T <=> configure
// TODO use if selector between :
// 	vector <T > or 
// 	vector <T*> or 
//      map <T>, or map <T*>

namespace ge
{
          /// Forward declaration of ContainerType type. its desinged to work only with vertex-types 
	  template<typename T , typename Type>
	  struct ContainerType{
	        typedef typename T::vertex vertex ; // container forward defintion
	  };

	  /// ContainerType type,  specialization-> std::vector
	  template<typename T >
	  struct ContainerType<T, std::vector <T> > : public std::vector<T >
	  {
		typedef typename T::vertex vertex ; //typedef T value_type;
		typedef typename ContainerType::size_type 	 size_type ;
		typedef typename ContainerType::iterator  	 iterator  ;
		typedef typename ContainerType::const_iterator   const_iterator  ;		
		typedef typename ContainerType::difference_type  difference_type ;
		typedef typename ContainerType::reference	 reference 	;
		typedef typename ContainerType::const_reference  const_reference ;		

		//!constructor
		ContainerType() {} 
		ContainerType(const size_type& size, const T&  value= T() )  : std::vector <T > (size,value) {}
		ContainerType(iterator first,iterator last)  : std::vector <T > (first,last) {}
		
		
		//! ContainerType by default inherited all things from std::vector, but on can implement save container (debug mode)
		//! to check and all these stuff
		#ifdef DEBUG  
		reference operator [] ( difference_type index )
		{
		    assert(index >= 0 && index<static_cast<difference_type > (this->size() ) ) ; // end assert
		    return std::vector<T>::operator[](index) ;
		}
		
		const_reference operator [] (difference_type index) const
		{
		    assert(index >= 0 && index<static_cast<difference_type > (this->size() ) ) ; // end assert
		    return std::vector<T>::operator[](index) ;
		}
		#endif
	  };


	  
	  
	  
	  
	  
	  //!@brief Double ended queue
	  template<typename T >
	  struct ContainerType<T, std::deque<T> >:public std::deque<T > // specialization-> std::deque
	  {
		typedef typename T::vertex vertex ; //typedef T value_type;
		typedef typename ContainerType::size_type 	     size_type ;
		typedef typename ContainerType::iterator  	     iterator  ;
		typedef typename ContainerType::const_iterator   const_iterator  ;
		typedef typename ContainerType::difference_type  difference_type ;
		typedef typename ContainerType::reference	     reference 	;
		typedef typename ContainerType::const_reference  const_reference ;
		
		//!constructor
		ContainerType() {} 
		ContainerType(const size_type& size, const T&  value= T() )  : std::deque <T > (size,value) {} 
		ContainerType(iterator first,iterator last)  : std::deque <T > (first,last) {}
		
		#ifdef DEBUG  
		reference operator [] ( difference_type index )
		{
		    assert(index >= 0 && index<static_cast<difference_type > (this->size() ) ) ; // end asssert
		    return std::deque<T>::operator[](index) ;
		}
		
		const_reference operator [] (difference_type index) const
		{
		    assert(index >= 0 && index<static_cast<difference_type > (this->size() ) ) ; // end asssert
		    return std::deque<T>::operator[](index) ;
		}
		#endif
	  };
	  
	  
	  // neu boost boost::ptr_vector
	  /// ContainerType type,  specialization-> boost::ptr_vector
	  template<typename T >
	  struct ContainerType<T,boost::ptr_vector<T> > : public boost::ptr_vector<T >
	  {
	    typedef typename T::vertex vertex ; //typedef T value_type;
	    typedef typename ContainerType::size_type 	     	size_type ;
	    typedef typename ContainerType::iterator  	     	iterator  ;
	    typedef typename ContainerType::const_iterator   	const_iterator  ;
	    typedef typename ContainerType::difference_type  	difference_type ;
	    typedef typename ContainerType::reference	     	reference ;
	    typedef typename ContainerType::const_reference  	const_reference ;

	    // =================================================================
	    //!constructors
	    // =================================================================
	    ContainerType() {} 
	    
	    ContainerType(const size_type& size, const T&  value= T() ) 
	    : std::vector <T > (size,value) {}
	    
	    ContainerType(iterator first,iterator last)  
	    : std::vector <T > (first,last) {}
	    // =================================================================
	    
	    
	    
	    //! ContainerType by default inherited all things from std::vector, but on can implement save container (debug mode)
	    //! to check and all these stuff
	    #ifdef DEBUG  
	    reference operator [] ( difference_type index )
	    {
	      assert(index >= 0 && index<static_cast<difference_type > (this->size() ) ) ; // end assert
	      return std::vector<T>::operator[](index) ;
	    }
	    
	    const_reference operator [] (difference_type index) const
	    {
	      assert(index >= 0 && index<static_cast<difference_type > (this->size() ) ) ; // end assert
	      return std::vector<T>::operator[](index) ;
	    }
	    #endif
	    
	  };
	  // ===================================================================
	  
	  
	  
	  
	  
	  
	  
	  
	  
	  
	  
	  
	  
}
#endif

