/*******************************************************************************
ConminC++, to solve constrained or unconstrained function minimization.
Copyright (C) 1999  <PERSON><PERSON> <PERSON><PERSON> Dunn

This program is free software; you can redistribute it and/or
modify it under the terms of the GNU General Public License
as published by the Free Software Foundation; either version 2
of the License, or (at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this program; if not, write to the Free Software
Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA  02111-1307, USA.
*******************************************************************************/

//---------------------------------------------------------------------------
#pragma hdrstop

#include "cnmn02.h"
#include "conmin.h"
#include <math.h>
#ifndef UNIX
#include <vcl.h>
#endif

//---------------------------------------------------------------------------
#pragma package(smart_init)
void Conmin::cnmn02(int &ncalc, double &slope, double &dftdf1,
          double *df, double *s)
{
//     ROUTINE TO DETERMINE CONJUGATE DIRECTION VECTOR OR DIRECTION
//     OF STEEPEST DESCENT FOR UNCONSTRAINED FUNCTION MINIMIZATION.
//     BY G. N. VANDERPLAATS                       APRIL, 1972.
//     NASA-AMES RESEARCH CENTER, MOFFETT FIELD, CALIF.
//     NCALC = CALCULATION CONTROL.
//         NCALC = 0,     S = STEEPEST DESCENT.
//         NCALC = 1,     S = CONJUGATE DIRECTION.
//     CONJUGATE DIRECTION IS FOUND BY FLETCHER-REEVES ALGORITHM.

//
//
//     revision history
//     v1.1, 5/1/95, sharon padula, use coding standards
//     v2.0, 6/1/98, jack dunn, converted to C++ class
//
//
//                   CALCULATE NORM OF GRADIENT VECTOR
//
double dfi, dftdf, beta, s1, s2, si;

dftdf=0.;
for(int i=1; i<=ndv; i++)
  {
  dfi=df[i];
  dftdf += dfi*dfi;
  }
//
//     **********                FIND DIRECTION S              **********
//
if(ncalc != 1) goto L30;
if(dftdf1 < 1.0e-20) goto L30;
//
//                 FIND FLETCHER-REEVES CONJUGATE DIRECTION
//
beta=dftdf/dftdf1;
slope=0.;
for(int i=1; i<=ndv; i++)
  {
  dfi=df[i];
  si=beta*s[i]-dfi;
  slope += si*dfi;
  s[i]=si;
  }
goto L50;

L30:
ncalc=0;
//
//                  CALCULATE DIRECTION OF STEEPEST DESCENT
//
for(int i=1; i<=ndv; i++) s[i]=-df[i];
slope=-dftdf;

L50:
//
//                  NORMALIZE S TO MAX ABS VALUE OF UNITY
//
s1=0.;
for(int i=1; i<=ndv; i++)
  {
  s2=fabs(s[i]);
  if(s2 > s1) s1=s2;
  }
if(s1<1.0e-20) s1=1.0e-20;
s1=1./s1;
dftdf1=dftdf*s1;
for(int i=1; i<=ndv; i++) s[i] *= s1;
slope *= s1;

return;
}
