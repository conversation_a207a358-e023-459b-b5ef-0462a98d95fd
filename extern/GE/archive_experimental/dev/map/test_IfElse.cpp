// #include "IfElse.h"
// #include <iostream>
// #include <map>
#include "Associative_Container.h"
#include <string>


using namespace ge ;

template <typename T>	
struct Object{
      Object (const char* name, const T& val)
	  :m_Name  (name), value (val)
	  {}
  private :
    std::string m_Name ;
    const T& value ;
} ;



//! map<Key, Value>
typedef Associative_Container<std::string, Object<int> , 1 > ObjectContainer_type_1 ; 



int main()
{
    ObjectContainer_type_1  my_Object_C1 ; 
    
    //     my_Object_C1.add ( "one", 1 ) ;  // TODO <- its realy important 
    
    return (0) ;
}
