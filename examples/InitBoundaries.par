//A init block must be given, either with a scenario, a filename, or manual boundary descriptions

// For further details on issues concerning manual boundary descriptions see function
// Domain::InitializeBoundaries

// The following manual boundary description describes a domain having noslip boundary conditions
// at east, west, and bottom, two small inflow holes at south, an acceleration wall at top, and 
// pressure boundary conditions at north.

// The coordinates that have to be given in each block correspond to the type of the super block
// (i.e. x and z for north, x and y for top)
// Ranges are always described by two values, separated by '..'. The values can be expressions that
// will be computed by PhysicalCheck class and may contain any values that can be given in the
// SimData block. Note that these values have to be enclosed in single quotes (e.g. 'xlength').
// The ranges can either be specified as coordinate ranges (in this case use the key names x, y, and z)
// or in lattice indices (then use x_L, y_L, and z_L, respectively). The range start and end
// expressions must accordingly evaluate to valid coordinates or lattice cell indices, respectively.
// Thus, for coordinates use multiples of 'dx' or relations of 'xlength', 'ylength', and 'zlength',
// for lattice values use 1,2,3... or relations of 'domainX', 'domainY', and 'domainZ'.

// Note: For an example of periodic boundary conditions see example file InitPeriodicBoundaries.par

init
{
  fill;                     // if "fill" is specified, all cells not defined by the boundary
                            // description will be filled with NOSLIP, otherwise the program
                            // will throw an error if you forgot to set a boundary cell.
  
  north{                    // description for north wall
    prs_nils{               // pressure BCs
        x_L  1..'domainX';   
        z_L  1..'domainZ';
        prs_L  0.0;         // value of pressure NOTE: CURRENTLY NOT EVALUATED YET
    }
  }
  
  south{                    // in one directional block it is possible to specify several different
                            // BC blocks. This block contains two inflow holes, missing cells not
                            // specified here will be filled with NOSLIP due to "fill" keyword above.
                            // The inflow direction and velocity can be described in different ways
                            // which includes: Defining the lattice velocity components as shown in first
                            // block, defining the inflow vector (physical sizes) as shown in second block,
                            // defining the velocity components with physical values as shown in acc boundary
                            // conditions below. For some of the possibilites, dx and dt have to be defined 
                            // or computable with help of simdata block.
    vel_in{
        x_L      ('domainX'/4)-10..('domainX'/4)+10;
        z_L      ('domainZ'/4)-10..('domainZ'/4)+10;
        ux_L   0.0;         // Lattice inflow velocity in x direction
        uy_L   0.05;        // Lattice inflow velocity in y direction
        uz_L   0.0;         // Lattice inflow velocity in z direction        
    }
    vel_in{
        x_L  ((3*'domainX')/4)-10..((3*'domainX')/4)+10;
        z_L  ((3*'domainZ')/4)-10..((3*'domainZ')/4)+10;
        UInflowVecX  1;     // x component of inflow vector
        UInflowVexY  1;     // y component of inflow vector
        UInflowVecZ  2;     // z component of inflow vector
        UInflowVecL  12.3;  // length of inflow vector
    }
  }
  
  east{
    noslip{
        y_L  1..'domainY';
        z_L  1..'domainZ';
    }
  }
  
  west{                     // It is possible to specify different slip conditions
                            // adjacency to each other. Note that at special treatment
                            // at the border effectively enlarges the area with less slip
                            // by half a cell. I.e. in the following case:
                            // noslip   1..('domainY'/2.0)-20.5
                            // freeslip ('domainY'/2.0)-19.5..('domainY'/2.0)+19.5
                            // partslip ('domainY'/2.0)+20.5..'domainY'
    noslip{
        y_L    1..('domainY'/2.0)-21;
        z_L    1..'domainZ';
    }
    freeslip{
        y_L    ('domainY'/2.0)-20..('domainY'/2.0)+20;
        z_L    1..'domainZ';
    }
    partslip{
        y_L    ('domainY'/2.0)+21..'domainY';
        z_L    1..'domainZ';
        psi  0.1;            // the parameter psi defines how slippy the wall is (-> material parameter)
                             // psi=0 results in freeslip, psi=1 in noslip
    } 
  }
  
  top{                   // It is possible to specify the ranges either in
			 // coordinates or in lattice indices (as above).
			 // For coordinates take keys x, y, z, for lattice
  			 // ranges take x_L, y_L, z_L, respectively.
    acc{                 // acceleration BCs at the top wall
        x    'dx'..'xLength';
        y    'dx'..'yLength';
        ux   0.0;         // Physical acceleration velocity in x direction
        uy   0.025;       // Physical acceleration velocity in y direction
        uz   0.0;         // Physical acceleration velocity in z direction                
    }
  }
  
  bottom{               // it is no problem to split the description for one directional side
                        // into several blocks
    noslip{
        x_L  0..('domainX'/2)-1;
        y_L  0..('domainY'/2)-1;
    }
  }  
  bottom{ 
    noslip{
        x_L  ('domainX'/2)..'domainX'+1;
        y_L  ('domainY'/2)..'domainY'+1;
    }
  }  

}
