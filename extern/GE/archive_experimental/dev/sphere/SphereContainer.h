#ifndef SPHERECONTAINER_HH
#define SPHERECONTAINER_HH


#include <vector>
#include <boost/shared_ptr.hpp>

#include "Sphere.h"

namespace ge
{
  
	template <  typename T1 , // configureation 
		    typename T2 , 
		    typename T3 
		>
	struct ObjectContainer
	{
	  
	};
  
  
  
	//The shared_ptr template class proposed in Technical Report 1 and available in the Boost 
	// library can be used as an alternative to auto_ptr for collections with ownership semantics
	// The auto_ptr template class describes an object that stores a pointer to an single 
	// allocated object of type Type* that ensures that the object to which it points gets destroyed automatically when control leaves a scope
	/*
	  
	auto_ptr< vector< int > > open_vec(new vector< int >);
 
	    open_vec->push_back(5);
	    open_vec->push_back(3);
	    open_vec->push_back(1);
	
	// Transfers control, but now the vector cannot be changed:
	
	    auto_ptr<const vector< int > > closed_vec(open_vec);  // see const 
	
	//  closed_vec->push_back(8); // Can no longer modify ( because of the const)
	
	
	// Safe during the lifetime of the autoptr:
	    map<string, const int  *> mymap;
	
	    mymap["First"]  = &(*closed_vec)[0];
	    mymap["Second"] = &(*closed_vec)[1];
	    mymap["Third"]  = &(*closed_vec)[2];
	
	    for (map<string, const int  *>::iterator it = mymap.begin(); it != mymap.end(); ++it) {
		cout << it->first << " -> " << *(it->second) << std::endl;
	    }
 

	
	
	
	
	
	
	*/
	
	
	
	
	
	typedef boost::shared_ptr< ShapeBase<derived> >  ShapeB_memory_manager ;
	
	
	template <class derived> 
	class SphereContainer
	{
	      inline std::size_t Add (const ShapeBase<derived>& shape ) ; 
	      inline std::size_t Add (const ShapeBase<derived>& shape ) ; 
	  
	private:
	    std::vector< ShapeB_memory_manager > Shapes  ;
	};



}
#endif
