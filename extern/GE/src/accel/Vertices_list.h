#ifndef VERTICES_LIST_HH
#define VERTICES_LIST_HH


// ============================================================================
/*!@class Vertices_list
 * @brief vertices list class
 *
 * 
*/
// ============================================================================
namespace ge
{
	template <class configure>
	struct Vertices_list 
	{
		//! get info-types from template-parameter
		typedef configure  Configure;//!Export Configure, needed for TaceVertices_list.	
		typedef typename Configure::ElementType ElementType ;
		typedef typename Configure::FinalContainerType FinalContainerType;
		//! then, Extract necessary info-types from FinalContainerType
		typedef typename FinalContainerType::size_type		size_type ;
		typedef typename FinalContainerType::iterator		iterator  ;
		typedef typename FinalContainerType::const_iterator	const_iterator;
		typedef typename FinalContainerType::difference_type	difference_type;
		typedef typename FinalContainerType::reference		reference ;
		typedef typename FinalContainerType::const_reference	const_reference;

	  public:
		inline void Add (const ElementType& elem) { m_vertices.Add (elem);}

		// Find element int the container  
		inline int  Find (const ElementType& element) {return m_vertices.Find(element);}

		inline iterator Remove (iterator position){ return m_vertices.Remove (position);} 

		inline iterator Remove (iterator first, iterator last )	{return m_vertices.Remove (first,last);} 	

		inline size_type Size()const{return m_vertices.size();}

	  private:
		// container
		FinalContainerType m_vertices ;
	};
} // end namespace ge
#endif
