// ============================================================================= 
// ============================================================================= 
#include <iostream>
#include <stdio.h>
#include <limits.h>
#include <cassert>
// ============================================================================= 
//
//
//   note, packing data not working probably for any 3 given data,
//          its designed for working only with 120000,1, 120
//	    in this example
////	http://en.wikipedia.org/wiki/Data_structure_alignment
////	http://www.codesynthesis.com/~boris/blog/2009/04/06/cxx-data-alignment-portability/    <- very nice
// ============================================================================= 


using namespace std ;

/* Bit print of an integral expression*/
inline void Print (int c)
{
  //std::cout << "x = " << c << "   " ;
	int n = sizeof(int)*CHAR_BIT;
	int mask = 1 << (n-1);
	for (int i=1; i<=n; ++i)
	{
	          putchar(((c & mask) == 0)? '0' : '1' );
  		  c <<= 1;
            	if (i% CHAR_BIT == 0 && i < n)
            	putchar (' ') ;
	}
}

inline int ChainThreeInts (const int& a, const int& b, const int& c)
{
	return ( (a << 8) | (b << 7) | c );
}

/* ============================================================================= 
Binay Representation          
----------------------------------
00000000 <USER> <GROUP> 00010000
 after shift to left (x<<1) : multiplay by 2
00000000 00000000 00000000 00100000         
-----------------------------------         
x= 16                                       
 x << 1  -> 32                              
 x >> 1  -> 8                               
 x << 2  -> 64                              
 x << 3  -> 128                             
 x >> 2  -> 4                               
 x >> 3  -> 2                               
-----------------------------------         
Binay Representation                        
--------------------------------------------
00000000 <USER> <GROUP> 10000000         
00000000 00000000 00000000 00000001         
00000000 00000000 00000000 01111000         
-----------------------------------         
00010010 <USER> <GROUP> 11111000         

-------------------------------------------------------------------------------------
(age << 8)                              00010010 01001111 10000000 00000000          
(gender << 7)                           00000000 00000000 00000000 10000000          
(age << 8) | (gender << 7)              00010010 01001111 10000000 10000000          
(age << 8) | (gender << 7) | height     00010010 01001111 10000000 11111000
---------------------------------------------------------------------
Check
Age:= 1200000
gender:= 1
height:= 120
////////////////////////////////////////////////////////
befor chainging
00000000 00010010 01001111 10000000
00000000 00000000 00000000 00000001
00010010 01001111 10000000 11111000
120
1
1200000

============================================================================= */


int main()
{

        const int x = 16 ;
        std::cout<<"Binay Representation\n----------------------------------\n";
	std::cout<<"const int x = 16 ; ";
            Print (x) ;
            std::cout <<"\n after shift to left (x<<1) : multiplay by 2"<< std::endl ;
            Print (x<<1) ;    
        std::cout<< "\n-----------------------------------\n" ;

// Shift left multiplies by 2; shift right divides by 2
//
// if a is int
// the results of  a >> n  its equal to    a * ( 2^n)
//


        cout<< "x= " << x << endl ;
        cout<<" x << 1  -> " << (x << 1) << endl; //  =
        cout<<" x >> 1  -> " << (x >> 1) << endl; //  =

        // multiplaying
        cout<<" x << 2  -> " << (x << 2) << endl; //  = 64
        cout<<" x << 3  -> " << (x << 3) << endl; //  = 128

        //divids by
        cout<<" x >> 2  -> " << (x >> 2) << endl; //  = 4
        cout<<" x >> 3  -> " << (x >> 3) << endl; //  = 2


        int age, gender, height, packed_info;


        // Assign values

        age    = 1200000;
        gender = 1 ;
        height = 120;

        std::cout<< "-----------------------------------\n" ;
        std::cout<<"Binay Representation\n--------------------------------------------\n";
            Print (age) ;       std::cout << std::endl ;
            Print (gender) ;    std::cout << std::endl ;
            Print (height) ;    std::cout << std::endl ;
        std::cout<< "-----------------------------------\n" ;




        // Pack as AAAAAAA G HHHHHHH using shifts and "or"
        // or its the like who connect parts together in one Big element
        packed_info = (age << 8) | (gender << 7) | height;

            Print (packed_info) ;    std::cout << std::endl ; 
            
        std::cout<<"\n-------------------------------------------------------------------------------------\n";
        std::cout<<"(age << 8)                      \t" ;
        Print (age << 8) ;

        std::cout<<"\n(gender << 7)                     \t" ;
        Print (gender << 7) ;

        std::cout<<"\n(age << 8) | (gender << 7)       \t" ;
        Print ( (age << 8) | (gender << 7) ) ;

        std::cout<<"\n(age << 8) | (gender << 7) | height   \t" ;
        Print ( (age << 8) | (gender << 7) | height ) ;
        std::cout<<"\n---------------------------------------------------------------------\n";


//---------------------------------------------------------------------------------------------
//(age << 8)                              00010010 01001111 10000000 00000000
//(gender << 7)                           00000000 00000000 00000000 10000000
//(age << 8) | (gender << 7)              00010010 01001111 10000000 10000000
//(age << 8) | (gender << 7) | height     00010010 01001111 10000000 11111000  <- resluts
//                                                                   01111111  const = 0*7F
//                                                                   01111000  which is height
//---------------------------------------------------------------------------------------------


        // Unpack with shifts and masking using "and"

        height =  packed_info & 0x7F  ;   // This constant is binary ...01111111 (== 127)
        gender = (packed_info >> 7) & 1;
        age    = (packed_info >> 8);

        cout<<"Check \n"
            <<"Age:= "       << age     << "\n"
            <<"gender:= "    << gender  << "\n"
            <<"height:= "    << height  << "\n" ;




/*

* Integers (int and long) can be considered as collections of 32 or 64 bits.

* Bitwise operators perform logical operations on each bit position, where 1 is regarded as true and zero false.

* Bitwise and (a & b) - Result is 1 in every bit position where both operands have a 1.
* Bitwise or  (a | b) - Result is 1 only in positions where one or both operands have a 1.
* Bitwise xor (a ^ b)- Result is 1 in positions where the two corresponding bits are different.
* Bitwise not (~a) - Unary operator. Result is each bit of operand inverted.
 * Shift left (a << n) - Shifts bits n positions left. Zeros added on right.
  * Shift right (a >> n) - Shifts bits n positions right. High-order bit inserted on left.
  * Shift right (a >>> n) - Shifts bits n positions right. Zeros inserted at left.
 * See Deitel&Deitel p 1117

*/

        std::cout<<"////////////////////////////////////////////////////////\n";

        int a = 1200000 ;
        int b = 1    ;
        int c = 120   ;

        std::cout<< "befor chainging\n" ;

        Print (a) ; std::cout << std:: endl ;
        Print (b) ; std::cout << std:: endl ;
        Print (ChainThreeInts (a,b, c) ) ;
        std::cout << std::endl ;       


        int d = ChainThreeInts (a,b,c) ;

        std::cout<<   (d &0x7F)      << std::endl ;
        std::cout<< ( (d >> 7) &1 )  << std::endl ;
        std::cout<<   (d >> 8)       << std::endl ;

	
// 	unsigned short month = 4;
// 	unsigned short day = 2;
// 	unsigned short year = 88;
// 	unsigned short dmy;
// 
// 	dmy = month << 12 | day << 7 | year;
	std::cout << "===========================\n" ;
	unsigned short int sint = 0;
	sint = 4<<12 | 2<< 7 | 88;
	printf ("%X\n", sint);
// 	TODO get back them from sint-code
// 	unsigned short month_ ;
// 	unsigned short day_   ;
// 	unsigned short year_  ;

	
	
        return 0;
}

