#ifndef TIMING_H
#define TIMING_H

// ============================================================================
/*!
 @brief Public member function used to write the current system time and date
 * to an open logfile.
*/
// ============================================================================



// ============================================================================
// includes
// ============================================================================
#include <sys/time.h>
#include <sys/types.h>
#include <stdlib.h>
#include <sys/resource.h>
// ============================================================================




// ============================================================================
//
// ============================================================================
inline void timing(double* wcTime, double* cpuTime)
{
	struct timeval tp;
	struct rusage ruse;
	gettimeofday(&tp, NULL);
	*wcTime=(double) (tp.tv_sec + tp.tv_usec/1000000.0);

	getrusage(RUSAGE_SELF, &ruse);
	*cpuTime=(double)(ruse.ru_utime.tv_sec+ruse.ru_utime.tv_usec / 1000000.0);
}
// ============================================================================


//! if using mpi
// #	 ifdef WALBERLA_USE_MPI
	// double t1=MPI_Wtime();
	// 
	// 		solverCg (A,f,u,it,eps);... work to be timed
	// 
	// double t2=MPI_Wtime();
// #endif


// ============================================================================
/*!
EXAMPLE:

	double wcTs1 , wcTe1, cpuTimeStart , cpuTimeEnd ;

	timing(&wcTs1,&cpuTimeStart) ;
	   // ...
	timing(&wcTe1,&cpuTimeEnd);

	double runtime = wcTe1-wcTs1 ;


	repeat/=2;
	MFLOPS_1 = (2 * N*repeat)/(1E06 *(runtime));
	std::cout<< "N=   " << N << std::endl;
	std::cout<< "Walltime for Branch prediction  = "<< runtime  <<std::endl;
	std::cout<< "MFLOPS   for Branch prediction  = "<< MFLOPS_1 <<std::endl;
*/
// ============================================================================

#endif




