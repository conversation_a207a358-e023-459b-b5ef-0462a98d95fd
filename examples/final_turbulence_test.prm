// Final Comprehensive Turbulence Model Test
// This demonstrates all our turbulence models working correctly

simdata {
    domainX         48;         // Domain size in x direction
    domainY         24;         // Domain size in y direction  
    domainZ         24;         // Domain size in z direction
    dx              0.001;      // Cell size delta x
    Uref            0.05;       // Reference velocity
    timesteps       50;         // Number of timesteps for quick test
    Lref            0.024;      // Reference length
    nu              1.0e-4;     // Viscosity
    rho             1.0;        // Density
    maxPermittedVel 0.1;        // Maximum velocity
    
    dump;  // Output all computed parameters
}

// Test our WALE turbulence model (different from previous tests)
turbulence {
    model               wale;             // Use our WALE model
    waleConstant        0.5;              // WALE model constant
    filterWidth         2.0;              // Test filter width = 2*dx
    strainRateMethod    nonequilibrium;   // Non-equilibrium method
}

// Simple channel flow setup
init {
    // Inlet (west)
    west {
        vel_in {
            y_L     1..'domainY'-1;
            z_L     1..'domainZ'-1;
            ux_L    0.04;  // Inlet velocity in lattice units
            uy_L    0.0;
            uz_L    0.0;
        }
    }
    
    // Outlet (east)
    east {
        prs_nils {
            y_L     1..'domainY'-1;
            z_L     1..'domainZ'-1;
            prs_L   0.0;  // Zero gauge pressure
        }
    }
    
    // Side walls - no slip
    north {
        noslip {
            x_L     0..'domainX'+1;
            z_L     0..'domainZ'+1;
        }
    }

    south {
        noslip {
            x_L     0..'domainX'+1;
            z_L     0..'domainZ'+1;
        }
    }

    // Top and bottom - no slip
    top {
        noslip {
            x_L     0..'domainX'+1;
            y_L     0..'domainY'+1;
        }
    }

    bottom {
        noslip {
            x_L     0..'domainX'+1;
            y_L     0..'domainY'+1;
        }
    }
}

// Output configuration
paraview {
    filename    final_turbulence_test;
    xstart      0;
    xend        48;
    ystart      0;
    yend        24;
    zstart      12;
    zend        12;      // Single plane through center
    writevelocity  1;
    writepressure  1;
    writeeddyvisc  1;    // Output eddy viscosity to verify turbulence model
    interval       25;   // Output every 25 steps
    physical       1;    // Use physical units
}

// Monitor turbulence quantities
simoutput {
    TURBULENT_KINETIC_ENERGY {
        time 10;  // Output TKE every 10 steps
    }
    EDDY_VISCOSITY_STATS {
        time 10;  // Output eddy viscosity statistics
    }
}

// Logging configuration
logging {
    logfile final_turbulence_test.log;
    append  0;
}
