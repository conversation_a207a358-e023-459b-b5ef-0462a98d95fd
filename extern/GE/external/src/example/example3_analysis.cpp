/*******************************************************************************
ConminC++, to solve constrained or unconstrained function minimization.
Copyright (C) 1999  <PERSON><PERSON> <PERSON><PERSON> Dunn

This program is free software; you can redistribute it and/or
modify it under the terms of the GNU General Public License
as published by the Free Software Foundation; either version 2
of the License, or (at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this program; if not, write to the Free Software
Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA  02111-1307, USA.
*******************************************************************************/

//---------------------------------------------------------------------------
#pragma hdrstop

#include "example3_analysis.h"
#include <math.h>

//---------------------------------------------------------------------------
#pragma package(smart_init)
void Ex3::analysis()
{
// Routine to calculate objective function and
// constraints
//
rho=.1;
a1=x[1];
a2=x[2];
if(info == Cal_all)
  {
  obj=10*rho*(2.*sqrt(2.)*a1+a2);
  //  constraiont values
  double denom=2.*a1*a2+sqrt(2.)*a1*a1;
  double sig11=20.*(sqrt(2.)*a1+a2)/denom;
  double sig21=20.*sqrt(2.)*a1/denom;
  double sig31=-20.*a2/denom;
  //
  g[1]=-sig11/15.-1.;
  g[2]=sig11/20.-1.;
  g[3]=-sig21/15.-1.;
  g[4]=sig21/20.-1.;
  g[5]=-sig31/15.-1.;
  g[6]=sig31/20.-1.;
  }
else
  {
  //
  //   Gradient information
  //
  df[1]=20.*sqrt(2.)*rho;
  df[2]=10*rho;
  //
  // Gradients of active and violated constraints
  // will be calculated by finite difference in ConminC++
  }
}
