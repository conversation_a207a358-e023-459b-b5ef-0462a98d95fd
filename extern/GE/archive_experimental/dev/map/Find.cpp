#include <iostream>
#include <map>
#include <vector>
#include <algorithm> // binary_research

#include <tr1/unordered_map> // hash table


#include <assert.h> 



// =============================================================================
/*! \README  
 *   be careful with std::vector , its not working yet :))
 *
 *
 *
 */
// =============================================================================



// RedBlack tree
// return true if found, else return false
template<typename KEY, typename VALUE>
inline bool find(const std::map<KEY,VALUE>& container, const KEY& key)
{
    typename std::map<KEY,VALUE>::const_iterator iter=container.find(key);
    return iter!=container.end();
}

// Array: randome access vector
template<typename VALUE>
inline bool find(const std::vector<VALUE>& container, const VALUE& value)
{
    // using default comparison:
//     std::sort (container.begin(), container.end() , );
    return std::binary_search (container.begin(), container.end(), value ) ;
}





// Hash Table
// unordered_map <=> Stores hash table of {key, mapped} pairs.
// TODO add Compare template parameter 
template<typename KEY, typename VALUE >
inline bool find(const std::tr1::unordered_map<KEY,VALUE>& container, const KEY& key)
{
    typename std::tr1::unordered_map<KEY,VALUE>::const_iterator iter=container.find(key);
    return iter!=container.end();
}



int main() 
{
    // =========================================================================  
    // std::map -> rb tree
    // =========================================================================
    std::map<int, int> m;
    m[5] = 10;
    assert (find(m, 5) == true  ) ;
    assert (find(m, 8) == false ) ;
    	  std::cout << find(m, 5) << std::endl;
	  std::cout << find(m, 6) << std::endl; 
	  
    std::cout << std::endl;
    
    // =========================================================================
    // std::map -> Hash_table 
    // =========================================================================
    std::tr1::unordered_map<int, int> Mymap;
    Mymap [5] = 10;
    assert (find(Mymap, 5) == true  ) ;
    assert (find(Mymap, 8) == false ) ;  
    
    std::cout << find(Mymap, 5) << std::endl;
    std::cout << find(Mymap, 6) << std::endl;

    std::cout << std::endl;
    
    
    // =========================================================================
    // std::vector 
    // =========================================================================
    std::vector<int> m_vect (6);
    
    m_vect [5] = 10 ;
    assert (find(m_vect, 10) == true  ) ;
    assert (find(m_vect, 8) == false ) ;  
    
    std::cout << find(m_vect, 5) << std::endl;
    std::cout << find(m_vect, 6) << std::endl;
      
    return 0;
}
