// ============================================================================
// 
// g++ test_Cool.cpp
// test using vector 



// fauia166:  g++ test_Cool.cpp  -O3 -msse -msse4.2 
// ============================================================================



// ============================================================================
// includes 
// ============================================================================
#include <stdio.h>
#include <iostream>
#include "../../misc/Timing.h" 
#include <cstdlib> // for randomize, rand() and srand()
// ============================================================================


typedef int	v4si __attribute__ ((vector_size (16))); // vector of four single ints
typedef float  v4sf __attribute__ ((vector_size (16))); // vector of four single floats



// Returns random float in [0, 1).
inline float RandF()
{
	return (float)(rand()) / (float)RAND_MAX;
}



union f4vector{
	v4sf v;
	float f[4];
};


struct Vect
{
   public:
	float m_f [0] ;
   public:
	Vect (const float& a) {
		m_f [0] =  a ;
		m_f [1] =  a ;
		m_f [2] =  a ;
		m_f [3] =  0.;
	}


	Vect (const float&a, const float& b, const float& c) {
		m_f [0] = a ;
		m_f [1] = b ;
		m_f [2] = c ;
		m_f [3] = 0.;
	}
	
	/*Vect(const Vect& other){
		m_f [0]  = other.m_f[0]; 	
		m_f [1]  = other.m_f[1]; 	
		m_f [2]  = other.m_f[2];
		m_f [3]  = 0. ;
	}*/
	
	//inline Vect& operator = (const Vect& other){
	//	return tmp ;
	//}
	
	inline const Vect& operator+=(const Vect& other)
	{
		this->m_f [0]  += other.m_f[0]; 
		this->m_f [1]  += other.m_f[1];
		this->m_f [2]  += other.m_f[2];
		//this->m_f [3]  = 0. ;
		return *this ;
	}

	inline const Vect& operator+(const Vect& other) 
	{
		(this->m_f[0] += other.m_f[0] )  ;	
		(this->m_f[1] += other.m_f[1] )  ;	
		(this->m_f[2] += other.m_f[2] ) 	;
	//	std::cout << "tmp : " << tmp << std::endl;
		return *this; 
		//Vect result = *this ;
		//result  += other ;
		//return result ;
		//return  (Vect (*this ) += other)  ;
	}


	inline friend std::ostream& operator<<(std::ostream& out,const Vect& vec)
	{
		//out << "LINE : " << __LINE__ << std::endl ;
		out << "<" << 	vec.m_f[0]<< " , " << 
				vec.m_f[1]<< " , " <<
				vec.m_f[2]<< " , " <<
				vec.m_f[3]<< ">" ;
		return out ;
	}
private :
        // not implemented
	Vect (const Vect&) ;// copy constructor 
	void operator=(const Vect& ); // assignement operator

};




struct Vector3D
{
	Vector3D (const float&a) {
		f [0] =  a ;	
		f [1] =  a ;	
		f [2] =  a ;	
		f [3] = 0. ;	

	}

	Vector3D (const float&a, const float& b, const float& c) {
		f [0] = a ;	
		f [1] = b ;	
		f [2] = c ;	
		f [3] = 0. ;	
	}

	Vector3D(const v4sf& other) {
		v  = other ;	
	}


	inline const Vector3D& operator+(const Vector3D& other) {
		this->v += other.v ;
		return *this;
		//return Vector3D( this->v + other.v ); 
	}

	inline const Vector3D& operator-(const Vector3D& other) {
		this->v -= other.v ;
		return *this;
		//return Vector3D( this->v - other.v ); 
	}
	
	inline const Vector3D operator*(const Vector3D& other) {
		return Vector3D( this->v * other.v ); 
	}
	
	inline const Vector3D operator/(const Vector3D& other) {
		return Vector3D( this->v / other.v ); 
	}

	friend std::ostream& operator<<(std::ostream& out,const Vector3D& vec)
	{
		out << "<" << 	vec.f[0]<< " , " << 
				vec.f[1]<< " , " <<
				vec.f[2]<< " , " <<
				vec.f[3]<< ">" ;
		return out ;
	}

public :
	union {
		v4sf v;
		float f[4];
	};
private :
        // not implemented
	Vector3D (const Vector3D&) ;// copy constructor 
	void operator=(const Vector3D& ); // assignement operator
	
};





inline float dot_pro_sse (float *v1, float *v2)
{
	float resutls= 0 , tmp[4];
	v4sf acc, X , Y ;
	X   = __builtin_ia32_loadups (v1) ; 
	Y   = __builtin_ia32_loadups (v2) ;
	acc = __builtin_ia32_addps(acc,__builtin_ia32_mulps(X,Y)) ; 
	__builtin_ia32_storeups(tmp,acc) ;
	return tmp[0] + tmp[1] + tmp[2] + tmp [3] ;
}







int main()
{
	union f4vector  a	, b, // input
		c , // c = a + b
		d , // d = a * b
		e   // e = a / b
		;   //

	a.f[0] = 1; a.f[1] = 2; a.f[2] = 3; a.f[3] = 4;
	b.f[0] = 5; b.f[1] = 6; b.f[2] = 7; b.f[3] = 8;

	c.v = a.v + b.v;
	d.v = a.v * b.v;
	e.v = a.v / b.v;

	printf("a	: %f, %f, %f, %f\n", a.f[0], a.f[1], a.f[2], a.f[3]);
	printf("b	: %f, %f, %f, %f\n", b.f[0], b.f[1], b.f[2], b.f[3]);
	printf("Addition	: %f, %f, %f, %f\n", c.f[0], c.f[1], c.f[2], c.f[3]);
	printf("Multiplication  : %f, %f, %f, %f\n", d.f[0], d.f[1], d.f[2], d.f[3]);
	printf("Devision  : %f, %f, %f, %f\n", e.f[0], e.f[1], e.f[2], e.f[3]);

	float x[4] = { 1.0 , 1.0 , 1.1 , 0.0 } ;
	float y[4] = { 1.0 , 1.0 , 1.1 , 0.0 } ;

	std::cout << "dot_product : "  << dot_pro_sse (x , y ) << std::endl ; 
	std::cout<< "===========================================================\n" ;
	std::cout<< "===========================================================\n"; 
	/*const*/ Vector3D A(a.v) ;
	/*const*/ Vector3D B(a.v) ;
	std::cout<< "Vector A : " << A 	<< std::endl ;
	std::cout<< "Vector B : " << B 	<< std::endl ;
	std::cout<< "Vector A+B : " << A + B << std::endl ;
	std::cout<< "Vector A-B : " << A - B << std::endl ;
	std::cout<< "Vector A*B : " << A * B << std::endl ;
	std::cout<< "Vector A/B : " << A / B << std::endl ;
	std::cout<< "===========================================================\n"; 
	
	/*const*/ Vect A_c(1.0 , 2.0 ,3.0 ) ; 
	/*const*/ Vect B_c(1.0 , 2.0 ,3.0 ) ; 
	std::cout<< "Vector A_c : " << A_c 	<< std::endl ;
	std::cout<< "Vector B_c : " << B_c 	<< std::endl ;
	std::cout<< "Vector A+B : " << (A_c + B_c) << std::endl ;
	std::cout<< "===========================================================\n"; 

	#ifdef DEBUG
		#define N 5 
	#else
		#define  N 100000000 
	#endif
	
/*	double tm_str_0 = WcTiming() ;
	// benchmarking :
	for (int i=0 ; i<N;++i ) 
	{
		A_c + B_c ;	
	}

	const double rt =  WcTiming() - tm_str_0 ;
	std::cout<< "run time without sse : " << rt  << std::endl;
	std::cout<< "===========================================================\n"; 



	const double tm_str_1 = WcTiming() ;
	// benchmarking :
	for (int i=0 ; i<N ;++i ) 
	{
		A + B ;	
	}

	const double rt_sse = WcTiming() - tm_str_1 ;
	std::cout<< "run time with sse : " << rt_sse  << std::endl;
	std::cout<< "===========================================================\n"; 

*/
	double tm_str_0 = WcTiming() ;
	// benchmarking :
	for (int i=0 ; i<N;++i ) 
	{
		Vect tmp_0 ( RandF()), tmp_1 ( RandF() ) ;
		#ifdef DEBUG
		std::cout<<"tmp_0 : " << tmp_0 << "\n"; 
		std::cout<<"tmp_1 : " << tmp_1 << "\n"; 
		#endif
		tmp_0 + tmp_1 ;	
	}
 
	const double rt =  WcTiming() - tm_str_0 ;
	std::cout<< "run time without sse : " << rt  << std::endl;
	std::cout<< "===========================================================\n"; 



	const double tm_str_1 = WcTiming() ;
	// benchmarking :
	for (int i=0 ; i<N ;++i ) 
	{
		Vector3D tmp_2 ( RandF() ) , tmp_3 ( RandF() ) ;
		#ifdef DEBUG
		std::cout<<"tmp_2 : " << tmp_2 << "\n"; 
		std::cout<<"tmp_3 : " << tmp_3 << "\n"; 
		#endif
		tmp_2  + tmp_3  ;	
	}

	const double rt_sse = WcTiming() - tm_str_1 ;
	std::cout<< "run time with sse : " << rt_sse  << std::endl;
	std::cout<< "===========================================================\n"; 






	return (0);
}
