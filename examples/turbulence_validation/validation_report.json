{"timestamp": "2025-06-17T12:48:44.740068", "total_runtime": 1.5933787822723389, "summary": {"total_cases": 2, "successful": 2, "failed": 0, "timeout": 0, "crashed": 0}, "cases": {"quick_channel": {"status": "success", "runtime": 0.5840322971343994, "stdout": "waLBerla solver. RELEASE version, Revision 286 , LOGGING = 0.\nThe local date and time is: Tue <PERSON> 17 12:48:43 2025\n\n OPTIMIZED.Reading parameter file /home/<USER>/walberla/examples/turbulence_validation/quick_validation/parameter_files/quick_channel_validation.prm...\nThe domain size is set to:20,10,10\nInfo: Lattice density for bc 0\nInitializeBoundaries  \nWe have 0 objects on process 0\n\n Domain.cpp::InitialConditions: Running timestep 0 of 50.\t--> number of iteration count\t0\trho\t1\n\t\tvel (after) <0,0,0>\tdelta_rho\t0\trho\t1\n\tSolve: Running timestep 1 of 50.\n\t\tu_mean :\t <0,0,0>\t u_meanMag\t0 ,\trho_mean  1\n\tt(phyical) : 0\t Ek : 0\tMax Velocity: Length: 0 Velocity: 0 0 0 Cell: 1 1 1\n\tMax Velocity: Length: 0 Velocity: 0 0 0 Cell: 1 1 1\n\tMax Velocity: Length: 0.333333 Velocity: 0 0.333333 0 Cell: 2 10 2\nWriting paraview in time step 1\n\n\t\tu_mean :\t <0.000633333,0.0316667,2.42861e-19>\t u_meanMag\t0.00100318 ,\trho_mean  0.968333\n\tt(phyical) : 0.00125\t Ek : 0.00326456\tMax Velocity: Length: 0.333333 Velocity: 0 0.333333 0 Cell: 2 10 2\n\tMax Velocity: Length: 0.333333 Velocity: 0 0.333333 0 Cell: 2 10 2\n\tMax Velocity: Length: 0.415905 Velocity: 0.0364491 0.412698 0.0364491 Cell: 2 10 2\n\n\t\tu_mean :\t <0.00105926,0.051286,3.79557e-18>\t u_meanMag\t0.00263138 ,\trho_mean  0.911831\n\tt(phyical) : 0.0025\t Ek : 0.00534787\tMax Velocity: Length: 0.415905 Velocity: 0.0364491 0.412698 0.0364491 Cell: 2 10 2\n\tMax Velocity: Length: 0.415905 Velocity: 0.0364491 0.412698 0.0364491 Cell: 2 10 2\n\tMax Velocity: Length: 0.43187 Velocity: 0.0498254 0.426083 0.0498254 Cell: 2 10 2\nWriting paraview in time step 3\n\n\t\tu_mean :\t <0.00137213,0.0653383,5.16948e-19>\t u_meanMag\t0.00427098 ,\trho_mean  0.863051\n\tt(phyical) : 0.00375\t Ek : 0.00630833\tMax Velocity: Length: 0.43187 Velocity: 0.0498254 0.426083 0.0498254 Cell: 2 10 2\n\tMax Velocity: Length: 0.43187 Velocity: 0.0498254 0.426083 0.0498254 Cell: 2 10 2\n\tMax Velocity: Length: 0.412282 Velocity: -0.0422272 0.407934 0.0422272 Cell: 19 10 2\n\n\t\tu_mean :\t <0.00161228,0.0788107,1.23478e-17>\t u_meanMag\t0.00621372 ,\trho_mean  0.824014\n\tt(phyical) : 0.005\t Ek : 0.00725392\tMax Velocity: Length: 0.412282 Velocity: -0.0422272 0.407934 0.0422272 Cell: 19 10 2\n\tMax Velocity: Length: 0.412282 Velocity: -0.0422272 0.407934 0.0422272 Cell: 19 10 2\n\tMax Velocity: Length: 0.419551 Velocity: 0.0221986 0.418375 0.0221986 Cell: 3 10 3\nWriting paraview in time step 5\n\n\t\tu_mean :\t <0.00180393,0.0936192,1.48935e-17>\t u_meanMag\t0.00876781 ,\trho_mean  0.790599\n\tt(phyical) : 0.00625\t Ek : 0.00870296\tMax Velocity: Length: 0.419551 Velocity: 0.0221986 0.418375 0.0221986 Cell: 3 10 3\n\tMax Velocity: Length: 0.419551 Velocity: 0.0221986 0.418375 0.0221986 Cell: 3 10 3\n\tMax Velocity: Length: 0.430037 Velocity: 0.0221666 0.428893 0.0221666 Cell: 3 10 3\n\n\t\tu_mean :\t <0.00195824,0.107177,4.56926e-18>\t u_meanMag\t0.0114907 ,\trho_mean  0.75555\n\tt(phyical) : 0.0075\t Ek : 0.00994705\tMax Velocity: Length: 0.430037 Velocity: 0.0221666 0.428893 0.0221666 Cell: 3 10 3\n\tMax Velocity: Length: 0.430037 Velocity: 0.0221666 0.428893 0.0221666 Cell: 3 10 3\n\tMax Velocity: Length: 0.435981 Velocity: -0.0218222 0.434888 0.0218222 Cell: 18 10 3\nWriting paraview in time step 7\n\n\t\tu_mean :\t <0.00208263,0.120375,1.02062e-17>\t u_meanMag\t0.0144946 ,\trho_mean  0.720326\n\tt(phyical) : 0.00875\t Ek : 0.0111622\tMax Velocity: Length: 0.435981 Velocity: -0.0218222 0.434888 0.0218222 Cell: 18 10 3\n\tMax Velocity: Length: 0.435981 Velocity: -0.0218222 0.434888 0.0218222 Cell: 18 10 3\n\tMax Velocity: Length: 0.434132 Velocity: -0.024674 0.432732 0.024596 Cell: 18 10 3\n\n\t\tu_mean :\t <0.002182,0.133155,-1.97845e-18>\t u_meanMag\t0.0177351 ,\trho_mean  0.685283\n\tt(phyical) : 0.01\t Ek : 0.01234\tMax Velocity: Length: 0.434132 Velocity: -0.024674 0.432732 0.024596 Cell: 18 10 3\n\tMax Velocity: Length: 0.434132 Velocity: -0.024674 0.432732 0.024596 Cell: 18 10 3\n\tMax Velocity: Length: 0.437764 Velocity: 0.0253295 0.436318 0.024953 Cell: 3 10 3\nWriting paraview in time step 9\n\n\t\tu_mean :\t <0.00225988,0.145881,1.04578e-17>\t u_meanMag\t0.0212865 ,\trho_mean  0.651039\n\tt(phyical) : 0.01125\t Ek : 0.013551\tMax Velocity: Length: 0.437764 Velocity: 0.0253295 0.436318 0.024953 Cell: 3 10 3\n\tMax Velocity: Length: 0.437764 Velocity: 0.0253295 0.436318 0.024953 Cell: 3 10 3\n\tMax Velocity: Length: 0.438052 Velocity: -0.0194978 0.437289 0.0169587 Cell: 17 10 4\n\n\t\tu_mean :\t <0.00231927,0.15815,-7.12885e-18>\t u_meanMag\t0.0250169 ,\trho_mean  0.61681\n\tt(phyical) : 0.0125\t Ek : 0.0146943\tMax Velocity: Length: 0.438052 Velocity: -0.0194978 0.437289 0.0169587 Cell: 17 10 4\n\tMax Velocity: Length: 0.438052 Velocity: -0.0194978 0.437289 0.0169587 Cell: 17 10 4\n\tMax Velocity: Length: 0.443897 Velocity: -0.0212706 0.443034 0.0176823 Cell: 17 10 4\nWriting paraview in time step 11\n\n\t\tu_mean :\t <0.00236413,0.170144,-1.01733e-17>\t u_meanMag\t0.0289547 ,\trho_mean  0.583139\n\tt(phyical) : 0.01375\t Ek : 0.0158274\tMax Velocity: Length: 0.443897 Velocity: -0.0212706 0.443034 0.0176823 Cell: 17 10 4\n\tMax Velocity: Length: 0.443897 Velocity: -0.0212706 0.443034 0.0176823 Cell: 17 10 4\n\tMax Velocity: Length: 0.447295 Velocity: -0.022815 0.44635 0.018001 Cell: 17 10 4\n\n\t\tu_mean :\t <0.00239893,0.181626,-1.21214e-18>\t u_meanMag\t0.0329939 ,\trho_mean  0.549912\n\tt(phyical) : 0.015\t Ek : 0.0169206\tMax Velocity: Length: 0.447295 Velocity: -0.022815 0.44635 0.018001 Cell: 17 10 4\n\tMax Velocity: Length: 0.447295 Velocity: -0.022815 0.44635 0.018001 Cell: 17 10 4\n\tMax Velocity: Length: 0.452758 Velocity: -0.0244281 0.452054 -0.00635532 Cell: 17 10 6\nWriting paraview in time step 13\n\n\t\tu_mean :\t <0.00242912,0.192525,-4.02933e-18>\t u_meanMag\t0.0370718 ,\trho_mean  0.517535\n\tt(phyical) : 0.01625\t Ek : 0.0179971\tMax Velocity: Length: 0.452758 Velocity: -0.0244281 0.452054 -0.00635532 Cell: 17 10 6\n\tMax Velocity: Length: 0.452758 Velocity: -0.0244281 0.452054 -0.00635532 Cell: 17 10 6\n\tMax Velocity: Length: 0.456007 Velocity: -0.0255322 0.455248 0.00630875 Cell: 17 10 5\n\n\t\tu_mean :\t <0.00245943,0.202575,-1.24844e-17>\t u_meanMag\t0.0410426 ,\trho_mean  0.485945\n\tt(phyical) : 0.0175\t Ek : 0.0190228\tMax Velocity: Length: 0.456007 Velocity: -0.0255322 0.455248 0.00630875 Cell: 17 10 5\n\tMax Velocity: Length: 0.456007 Velocity: -0.0255322 0.455248 0.00630875 Cell: 17 10 5\n\tMax Velocity: Length: 0.459549 Velocity: -0.0208691 0.459031 0.00632044 Cell: 16 10 5\nWriting paraview in time step 15\n\n\t\tu_mean :\t <0.00249409,0.211729,-1.1846e-17>\t u_meanMag\t0.0448354 ,\trho_mean  0.455496\n\tt(phyical) : 0.01875\t Ek : 0.0200093\tMax Velocity: Length: 0.459549 Velocity: -0.0208691 0.459031 0.00632044 Cell: 16 10 5\n\tMax Velocity: Length: 0.459549 Velocity: -0.0208691 0.459031 0.00632044 Cell: 16 10 5\n\tMax Velocity: Length: 0.462687 Velocity: -0.0221194 0.462117 0.00610167 Cell: 16 10 5\n\n\t\tu_mean :\t <0.00253597,0.219845,-6.54208e-18>\t u_meanMag\t0.0483384 ,\trho_mean  0.426152\n\tt(phyical) : 0.02\t Ek : 0.0209252\tMax Velocity: Length: 0.462687 Velocity: -0.0221194 0.462117 0.00610167 Cell: 16 10 5\n\tMax Velocity: Length: 0.462687 Velocity: -0.0221194 0.462117 0.00610167 Cell: 16 10 5\n\tMax Velocity: Length: 0.465546 Velocity: -0.023195 0.464931 -0.00583626 Cell: 16 10 6\nWriting paraview in time step 17\n\n\t\tu_mean :\t <0.00258697,0.226932,-1.16016e-17>\t u_meanMag\t0.051505 ,\trho_mean  0.398101\n\tt(phyical) : 0.02125\t Ek : 0.0217663\tMax Velocity: Length: 0.465546 Velocity: -0.023195 0.464931 -0.00583626 Cell: 16 10 6\n\tMax Velocity: Length: 0.465546 Velocity: -0.023195 0.464931 -0.00583626 Cell: 16 10 6\n\tMax Velocity: Length: 0.467044 Velocity: -0.0241111 0.466388 0.00556218 Cell: 16 10 5\n\n\t\tu_mean :\t <0.00264801,0.232942,-1.3713e-18>\t u_meanMag\t0.0542689 ,\trho_mean  0.371278\n\tt(phyical) : 0.0225\t Ek : 0.0225042\tMax Velocity: Length: 0.467044 Velocity: -0.0241111 0.466388 0.00556218 Cell: 16 10 5\n\tMax Velocity: Length: 0.467044 Velocity: -0.0241111 0.466388 0.00556218 Cell: 16 10 5\n\tMax Velocity: Length: 0.468367 Velocity: -0.0248814 0.467676 0.00526259 Cell: 16 10 5\nWriting paraview in time step 19\n\n\t\tu_mean :\t <0.00271919,0.237932,-7.12212e-18>\t u_meanMag\t0.0566192 ,\trho_mean  0.345784\n\tt(phyical) : 0.02375\t Ek : 0.0231334\tMax Velocity: Length: 0.468367 Velocity: -0.0248814 0.467676 0.00526259 Cell: 16 10 5\n\tMax Velocity: Length: 0.468367 Velocity: -0.0248814 0.467676 0.00526259 Cell: 16 10 5\n\tMax Velocity: Length: 0.468827 Velocity: -0.0255218 0.468105 0.00498013 Cell: 16 10 5\n\n\t\tu_mean :\t <0.00280012,0.241907,-9.08345e-19>\t u_meanMag\t0.0585268 ,\trho_mean  0.321533\n\tt(phyical) : 0.025\t Ek : 0.0236335\tMax Velocity: Length: 0.468827 Velocity: -0.0255218 0.468105 0.00498013 Cell: 16 10 5\n\tMax Velocity: Length: 0.468827 Velocity: -0.0255218 0.468105 0.00498013 Cell: 16 10 5\n\tMax Velocity: Length: 0.469145 Velocity: -0.021908 0.46861 0.00465758 Cell: 15 10 5\nWriting paraview in time step 21\n\n\t\tu_mean :\t <0.00288989,0.244941,6.8385e-18>\t u_meanMag\t0.0600042 ,\trho_mean  0.298565\n\tt(phyical) : 0.02625\t Ek : 0.024004\tMax Velocity: Length: 0.469145 Velocity: -0.021908 0.46861 0.00465758 Cell: 15 10 5\n\tMax Velocity: Length: 0.469145 Velocity: -0.021908 0.46861 0.00465758 Cell: 15 10 5\n\tMax Velocity: Length: 0.469096 Velocity: -0.0226091 0.46853 0.00441316 Cell: 15 10 5\n\n\t\tu_mean :\t <0.00298746,0.24707,8.99172e-18>\t u_meanMag\t0.0610528 ,\trho_mean  0.276805\n\tt(phyical) : 0.0275\t Ek : 0.0242382\tMax Velocity: Length: 0.469096 Velocity: -0.0226091 0.46853 0.00441316 Cell: 15 10 5\n\tMax Velocity: Length: 0.469096 Velocity: -0.0226091 0.46853 0.00441316 Cell: 15 10 5\n\tMax Velocity: Length: 0.46838 Velocity: -0.0231947 0.467786 0.00421221 Cell: 15 10 5\nWriting paraview in time step 23\n\n\t\tu_mean :\t <0.00309141,0.248391,-3.07111e-18>\t u_meanMag\t0.0617075 ,\trho_mean  0.256281\n\tt(phyical) : 0.02875\t Ek : 0.0243463\tMax Velocity: Length: 0.46838 Velocity: -0.0231947 0.467786 0.00421221 Cell: 15 10 5\n\tMax Velocity: Length: 0.46838 Velocity: -0.0231947 0.467786 0.00421221 Cell: 15 10 5\n\tMax Velocity: Length: 0.466646 Velocity: -0.0236694 0.466027 -0.00404604 Cell: 15 10 6\n\n\t\tu_mean :\t <0.00320045,0.248975,-2.58083e-18>\t u_meanMag\t0.0619988 ,\trho_mean  0.236957\n\tt(phyical) : 0.03\t Ek : 0.0243368\tMax Velocity: Length: 0.466646 Velocity: -0.0236694 0.466027 -0.00404604 Cell: 15 10 6\n\tMax Velocity: Length: 0.466646 Velocity: -0.0236694 0.466027 -0.00404604 Cell: 15 10 6\n\tMax Velocity: Length: 0.46419 Velocity: -0.0240399 0.463551 -0.00391412 Cell: 15 10 6\nWriting paraview in time step 25\n\n\t\tu_mean :\t <0.00331302,0.248936,-5.21263e-18>\t u_meanMag\t0.0619803 ,\trho_mean  0.218871\n\tt(phyical) : 0.03125\t Ek : 0.0242294\tMax Velocity: Length: 0.46419 Velocity: -0.0240399 0.463551 -0.00391412 Cell: 15 10 6\n\tMax Velocity: Length: 0.46419 Velocity: -0.0240399 0.463551 -0.00391412 Cell: 15 10 6\n\tMax Velocity: Length: 0.461094 Velocity: -0.0243069 0.460438 0.00379163 Cell: 15 10 5\n\n\t\tu_mean :\t <0.00342774,0.248367,-4.40988e-18>\t u_meanMag\t0.0616977 ,\trho_mean  0.202007\n\tt(phyical) : 0.0325\t Ek : 0.02404\tMax Velocity: Length: 0.461094 Velocity: -0.0243069 0.460438 0.00379163 Cell: 15 10 5\n\tMax Velocity: Length: 0.461094 Velocity: -0.0243069 0.460438 0.00379163 Cell: 15 10 5\n\tMax Velocity: Length: 0.45763 Velocity: -0.0244713 0.456961 -0.0036785 Cell: 15 10 6\nWriting paraview in time step 27\n\n\t\tu_mean :\t <0.00354315,0.24737,-6.16261e-19>\t u_meanMag\t0.0612043 ,\trho_mean  0.186386\n\tt(phyical) : 0.03375\t Ek : 0.0237875\tMax Velocity: Length: 0.45763 Velocity: -0.0244713 0.456961 -0.0036785 Cell: 15 10 6\n\tMax Velocity: Length: 0.45763 Velocity: -0.0244713 0.456961 -0.0036785 Cell: 15 10 6\n\tMax Velocity: Length: 0.45387 Velocity: -0.024536 0.453192 0.00355833 Cell: 15 10 5\n\n\t\tu_mean :\t <0.00365804,0.246025,1.6166e-17>\t u_meanMag\t0.0605419 ,\trho_mean  0.171978\n\tt(phyical) : 0.035\t Ek : 0.0234857\tMax Velocity: Length: 0.45387 Velocity: -0.024536 0.453192 0.00355833 Cell: 15 10 5\n\tMax Velocity: Length: 0.45387 Velocity: -0.024536 0.453192 0.00355833 Cell: 15 10 5\n\tMax Velocity: Length: 0.449923 Velocity: -0.0245079 0.449242 0.00343392 Cell: 15 10 5\nWriting paraview in time step 29\n\n\t\tu_mean :\t <0.00377116,0.244415,-7.79845e-18>\t u_meanMag\t0.059753 ,\trho_mean  0.158774\n\tt(phyical) : 0.03625\t Ek : 0.0231483\tMax Velocity: Length: 0.449923 Velocity: -0.0245079 0.449242 0.00343392 Cell: 15 10 5\n\tMax Velocity: Length: 0.449923 Velocity: -0.0245079 0.449242 0.00343392 Cell: 15 10 5\n\tMax Velocity: Length: 0.445798 Velocity: -0.0243933 0.445118 0.00329479 Cell: 15 10 5\n\n\t\tu_mean :\t <0.00388159,0.2426,-3.60302e-18>\t u_meanMag\t0.0588701 ,\trho_mean  0.146726\n\tt(phyical) : 0.0375\t Ek : 0.0227855\tMax Velocity: Length: 0.445798 Velocity: -0.0243933 0.445118 0.00329479 Cell: 15 10 5\n\tMax Velocity: Length: 0.445798 Velocity: -0.0243933 0.445118 0.00329479 Cell: 15 10 5\n\tMax Velocity: Length: 0.44159 Velocity: -0.020027 0.441125 0.00306789 Cell: 14 10 5\nWriting paraview in time step 31\n\n\t\tu_mean :\t <0.00398838,0.240642,-6.09723e-18>\t u_meanMag\t0.0579244 ,\trho_mean  0.135802\n\tt(phyical) : 0.03875\t Ek : 0.022407\tMax Velocity: Length: 0.44159 Velocity: -0.020027 0.441125 0.00306789 Cell: 14 10 5\n\tMax Velocity: Length: 0.44159 Velocity: -0.020027 0.441125 0.00306789 Cell: 14 10 5\n\tMax Velocity: Length: 0.437324 Velocity: -0.0198519 0.436863 -0.00290677 Cell: 14 10 6\n\n\t\tu_mean :\t <0.0040909,0.238585,6.90312e-18>\t u_meanMag\t0.0569394 ,\trho_mean  0.125944\n\tt(phyical) : 0.04\t Ek : 0.0220197\tMax Velocity: Length: 0.437324 Velocity: -0.0198519 0.436863 -0.00290677 Cell: 14 10 6\n\tMax Velocity: Length: 0.437324 Velocity: -0.0198519 0.436863 -0.00290677 Cell: 14 10 6\n\tMax Velocity: Length: 0.432944 Velocity: -0.0196251 0.432491 0.00273999 Cell: 14 10 5\nWriting paraview in time step 33\n\n\t\tu_mean :\t <0.0041885,0.236473,5.34512e-20>\t u_meanMag\t0.0559371 ,\trho_mean  0.117104\n\tt(phyical) : 0.04125\t Ek : 0.0216303\tMax Velocity: Length: 0.432944 Velocity: -0.0196251 0.432491 0.00273999 Cell: 14 10 5\n\tMax Velocity: Length: 0.432944 Velocity: -0.0196251 0.432491 0.00273999 Cell: 14 10 5\n\tMax Velocity: Length: 0.428473 Velocity: -0.0193598 0.428028 0.00256416 Cell: 14 10 5\n\n\t\tu_mean :\t <0.00428079,0.23434,-2.18217e-18>\t u_meanMag\t0.0549334 ,\trho_mean  0.109218\n\tt(phyical) : 0.0425\t Ek : 0.0212439\tMax Velocity: Length: 0.428473 Velocity: -0.0193598 0.428028 0.00256416 Cell: 14 10 5\n\tMax Velocity: Length: 0.428473 Velocity: -0.0193598 0.428028 0.00256416 Cell: 14 10 5\n\tMax Velocity: Length: 0.42395 Velocity: -0.0190633 0.423515 0.00238843 Cell: 14 10 5\nWriting paraview in time step 35\n\n\t\tu_mean :\t <0.0043674,0.232215,4.39145e-18>\t u_meanMag\t0.0539428 ,\trho_mean  0.102233\n\tt(phyical) : 0.04375\t Ek : 0.0208649\tMax Velocity: Length: 0.42395 Velocity: -0.0190633 0.423515 0.00238843 Cell: 14 10 5\n\tMax Velocity: Length: 0.42395 Velocity: -0.0190633 0.423515 0.00238843 Cell: 14 10 5\n\tMax Velocity: Length: 0.419415 Velocity: -0.0187475 0.41899 -0.00220967 Cell: 14 10 6\n\n\t\tu_mean :\t <0.00444816,0.23012,-2.05077e-18>\t u_meanMag\t0.0529751 ,\trho_mean  0.0960851\n\tt(phyical) : 0.045\t Ek : 0.0204963\tMax Velocity: Length: 0.419415 Velocity: -0.0187475 0.41899 -0.00220967 Cell: 14 10 6\n\tMax Velocity: Length: 0.419415 Velocity: -0.0187475 0.41899 -0.00220967 Cell: 14 10 6\n\tMax Velocity: Length: 0.414913 Velocity: -0.0184178 0.414499 -0.0020361 Cell: 14 10 6\nWriting paraview in time step 37\n\n\t\tu_mean :\t <0.00452293,0.228076,-3.77736e-19>\t u_meanMag\t0.052039 ,\trho_mean  0.0907182\n\tt(phyical) : 0.04625\t Ek : 0.0201411\tMax Velocity: Length: 0.414913 Velocity: -0.0184178 0.414499 -0.0020361 Cell: 14 10 6\n\tMax Velocity: Length: 0.414913 Velocity: -0.0184178 0.414499 -0.0020361 Cell: 14 10 6\n\tMax Velocity: Length: 0.410485 Velocity: -0.0180838 0.410082 0.00186437 Cell: 14 10 5\n\n\t\tu_mean :\t <0.00459178,0.226095,-6.39462e-18>\t u_meanMag\t0.0511399 ,\trho_mean  0.0860717\n\tt(phyical) : 0.0475\t Ek : 0.0198009\tMax Velocity: Length: 0.410485 Velocity: -0.0180838 0.410082 0.00186437 Cell: 14 10 5\n\tMax Velocity: Length: 0.410485 Velocity: -0.0180838 0.410082 0.00186437 Cell: 14 10 5\n\tMax Velocity: Length: 0.406176 Velocity: -0.017748 0.405784 0.001701 Cell: 14 10 5\nWriting paraview in time step 39\n\n\t\tu_mean :\t <0.00465474,0.224188,7.14272e-19>\t u_meanMag\t0.050282 ,\trho_mean  0.0820923\n\tt(phyical) : 0.04875\t Ek : 0.0194772\tMax Velocity: Length: 0.406176 Velocity: -0.017748 0.405784 0.001701 Cell: 14 10 5\n\tMax Velocity: Length: 0.406176 Velocity: -0.017748 0.405784 0.001701 Cell: 14 10 5\n\tMax Velocity: Length: 0.402019 Velocity: -0.0174173 0.401639 0.00154234 Cell: 14 10 5\n\n\t\tu_mean :\t <0.0047121,0.222363,1.22623e-18>\t u_meanMag\t0.0494673 ,\trho_mean  0.0787234\n\tt(phyical) : 0.05\t Ek : 0.0191705\tMax Velocity: Length: 0.402019 Velocity: -0.0174173 0.401639 0.00154234 Cell: 14 10 5\n\tMax Velocity: Length: 0.402019 Velocity: -0.0174173 0.401639 0.00154234 Cell: 14 10 5\n\tMax Velocity: Length: 0.398049 Velocity: -0.0170915 0.39768 -0.00139308 Cell: 14 10 6\nWriting paraview in time step 41\n\n\t\tu_mean :\t <0.00476408,0.220623,-1.24683e-18>\t u_meanMag\t0.0486973 ,\trho_mean  0.0759151\n\tt(phyical) : 0.05125\t Ek : 0.0188813\tMax Velocity: Length: 0.398049 Velocity: -0.0170915 0.39768 -0.00139308 Cell: 14 10 6\n\tMax Velocity: Length: 0.398049 Velocity: -0.0170915 0.39768 -0.00139308 Cell: 14 10 6\n\tMax Velocity: Length: 0.394289 Velocity: -0.0167756 0.39393 0.00124965 Cell: 14 10 5\n\n\t\tu_mean :\t <0.00481109,0.218972,-7.89798e-18>\t u_meanMag\t0.0479717 ,\trho_mean  0.073616\n\tt(phyical) : 0.0525\t Ek : 0.0186093\tMax Velocity: Length: 0.394289 Velocity: -0.0167756 0.39393 0.00124965 Cell: 14 10 5\n\tMax Velocity: Length: 0.394289 Velocity: -0.0167756 0.39393 0.00124965 Cell: 14 10 5\n\tMax Velocity: Length: 0.390757 Velocity: -0.016467 0.390409 0.00111517 Cell: 14 10 5\nWriting paraview in time step 43\n\n\t\tu_mean :\t <0.00485348,0.217409,-4.13905e-18>\t u_meanMag\t0.04729 ,\trho_mean  0.0717803\n\tt(phyical) : 0.05375\t Ek : 0.0183544\tMax Velocity: Length: 0.390757 Velocity: -0.016467 0.390409 0.00111517 Cell: 14 10 5\n\tMax Velocity: Length: 0.390757 Velocity: -0.016467 0.390409 0.00111517 Cell: 14 10 5\n\tMax Velocity: Length: 0.387465 Velocity: -0.0161693 0.387126 0.000986619 Cell: 14 10 5\n\n\t\tu_mean :\t <0.00489176,0.215933,-2.67299e-18>\t u_meanMag\t0.0466509 ,\trho_mean  0.0703614\n\tt(phyical) : 0.055\t Ek : 0.0181157\tMax Velocity: Length: 0.387465 Velocity: -0.0161693 0.387126 0.000986619 Cell: 14 10 5\n\tMax Velocity: Length: 0.387465 Velocity: -0.0161693 0.387126 0.000986619 Cell: 14 10 5\n\tMax Velocity: Length: 0.384416 Velocity: -0.0158787 0.384087 -0.000866045 Cell: 14 10 6\nWriting paraview in time step 45\n\n\t\tu_mean :\t <0.00492634,0.214543,1.58727e-19>\t u_meanMag\t0.046053 ,\trho_mean  0.0693182\n\tt(phyical) : 0.05625\t Ek : 0.0178927\tMax Velocity: Length: 0.384416 Velocity: -0.0158787 0.384087 -0.000866045 Cell: 14 10 6\n\tMax Velocity: Length: 0.384416 Velocity: -0.0158787 0.384087 -0.000866045 Cell: 14 10 6\n\tMax Velocity: Length: 0.381607 Velocity: -0.015598 0.381288 -0.000751315 Cell: 14 10 6\n\n\t\tu_mean :\t <0.00495774,0.213236,-4.23836e-18>\t u_meanMag\t0.0454943 ,\trho_mean  0.0686091\n\tt(phyical) : 0.0575\t Ek : 0.0176846\tMax Velocity: Length: 0.381607 Velocity: -0.015598 0.381288 -0.000751315 Cell: 14 10 6\n\tMax Velocity: Length: 0.381607 Velocity: -0.015598 0.381288 -0.000751315 Cell: 14 10 6\n\tMax Velocity: Length: 0.379034 Velocity: -0.0153228 0.378723 0.000643803 Cell: 14 10 5\nWriting paraview in time step 47\n\n\t\tu_mean :\t <0.00498635,0.21201,-1.26179e-18>\t u_meanMag\t0.0449733 ,\trho_mean  0.0681972\n\tt(phyical) : 0.05875\t Ek : 0.0174904\tMax Velocity: Length: 0.379034 Velocity: -0.0153228 0.378723 0.000643803 Cell: 14 10 5\n\tMax Velocity: Length: 0.379034 Velocity: -0.0153228 0.378723 0.000643803 Cell: 14 10 5\n\tMax Velocity: Length: 0.376682 Velocity: -0.0150555 0.376381 0.000542395 Cell: 14 10 5\n\n\t\tu_mean :\t <0.00501265,0.210862,-3.05615e-18>\t u_meanMag\t0.0444881 ,\trho_mean  0.068046\n\tt(phyical) : 0.06\t Ek : 0.0173095\tMax Velocity: Length: 0.376682 Velocity: -0.0150555 0.376381 0.000542395 Cell: 14 10 5\n\tMax Velocity: Length: 0.376682 Velocity: -0.0150555 0.376381 0.000542395 Cell: 14 10 5\n\tMax Velocity: Length: 0.37454 Velocity: -0.014792 0.374248 -0.000447955 Cell: 14 10 6\nWriting paraview in time step 49\n\n\t\tu_mean :\t <0.00503698,0.20979,-1.39428e-18>\t u_meanMag\t0.0440371 ,\trho_mean  0.0681234\n\tt(phyical) : 0.06125\t Ek : 0.017141\tMax Velocity: Length: 0.37454 Velocity: -0.014792 0.374248 -0.000447955 Cell: 14 10 6\n\tMax Velocity: Length: 0.37454 Velocity: -0.014792 0.374248 -0.000447955 Cell: 14 10 6\n\tMax Velocity: Length: 0.372592 Velocity: -0.0145347 0.372308 0.000360235 Cell: 14 10 5\nWriting 25 timesteps \n\n", "stderr": "\u001b[0;33m WARNING: Domain.h::SetBC: Lattice density for prs_nils boundary condition at north side has extreme value: 0. Adjust resolution!\n\u001b[0m\n\u001b[0;33m WARNING: Domain.cpp::InitializeBoundaries:\n Even GE is activated through CMake config, there is no blocks named 'stl' in parameter file. You are not used the STL GeometricEngine !\n\u001b[0m\nLBGK-Laminar lbm model will be used!!!\nGE is activated from cmake, but there is no stl Block in the given parameter file\n\n0%   10   20   30   40   50   60   70   80   90   100%\n|----|----|----|----|----|----|----|----|----|----|\n\n\t\n----->    CalcPatch::InitialConditions (sim_,t)\t  :timestep  = 0  file:/home/<USER>/walberla/src/InitialConditions/InitialConditions.h\n Starting the InitialConditions ...\n\t--> cell (1 1 1) number of iteration count\t0\trho\t1\n      Smagorinsky model,  sim.csmag =   0.03\n      use finite difference to calculate rate stres-tensor   \n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n", "metrics": {}}, "circular_cylinder_re40": {"status": "success", "runtime": 1.0066664218902588, "stdout": "waLBerla solver. RELEASE version, Revision 286 , LOGGING = 0.\nThe local date and time is: Tue <PERSON> 17 12:48:43 2025\n\n OPTIMIZED.Reading parameter file /home/<USER>/walberla/examples/turbulence_validation/quick_validation/parameter_files/circular_cylinder_re40_quick.prm...\nThe domain size is set to:40,20,10\nInfo: Lattice density for bc 0\nInitializeBoundaries  \nWe have 0 objects on process 0\n\n Domain.cpp::InitialConditions: Running timestep 0 of 100.\t--> number of iteration count\t0\trho\t1\n\t\tvel (after) <0,0,0>\tdelta_rho\t0\trho\t1\n\tSolve: Running timestep 1 of 100.\n\t\tu_mean :\t <0,0,0>\t u_meanMag\t0 ,\trho_mean  1\n\tt(phyical) : 0\t Ek : 0\tMax Velocity: Length: 0 Velocity: 0 0 0 Cell: 1 1 1\n\tMax Velocity: Length: 0 Velocity: 0 0 0 Cell: 1 1 1\n\tMax Velocity: Length: 0.333333 Velocity: 0 0.333333 0 Cell: 2 20 2\nWriting paraview in time step 1\n\n\t\tu_mean :\t <0.000284375,0.0159725,5.04805e-19>\t u_meanMag\t0.000255201 ,\trho_mean  0.984028\n\tt(phyical) : 0.00166667\t Ek : 0.00093141\tMax Velocity: Length: 0.333333 Velocity: 0 0.333333 0 Cell: 2 20 2\n\tMax Velocity: Length: 0.333333 Velocity: 0 0.333333 0 Cell: 2 20 2\n\tMax Velocity: Length: 0.391496 Velocity: 0.031893 0.388889 0.031893 Cell: 2 20 2\n\n\t\tu_mean :\t <0.000493203,0.0259289,6.38378e-19>\t u_meanMag\t0.00067255 ,\trho_mean  0.957672\n\tt(phyical) : 0.00333333\t Ek : 0.00143644\tMax Velocity: Length: 0.391496 Velocity: 0.031893 0.388889 0.031893 Cell: 2 20 2\n\tMax Velocity: Length: 0.391496 Velocity: 0.031893 0.388889 0.031893 Cell: 2 20 2\n\tMax Velocity: Length: 0.411706 Velocity: 0.0352704 0.408673 0.0352704 Cell: 2 20 2\n\n\t\tu_mean :\t <0.000660047,0.0338204,-4.64472e-19>\t u_meanMag\t0.00114425 ,\trho_mean  0.935113\n\tt(phyical) : 0.005\t Ek : 0.00178688\tMax Velocity: Length: 0.411706 Velocity: 0.0352704 0.408673 0.0352704 Cell: 2 20 2\n\tMax Velocity: Length: 0.411706 Velocity: 0.0352704 0.408673 0.0352704 Cell: 2 20 2\n\tMax Velocity: Length: 0.401048 Velocity: 0.0193627 0.400112 0.0193627 Cell: 3 20 3\n\n\t\tu_mean :\t <0.000797774,0.0409586,9.43993e-18>\t u_meanMag\t0.00167824 ,\trho_mean  0.914802\n\tt(phyical) : 0.00666667\t Ek : 0.00208818\tMax Velocity: Length: 0.401048 Velocity: 0.0193627 0.400112 0.0193627 Cell: 3 20 3\n\tMax Velocity: Length: 0.401048 Velocity: 0.0193627 0.400112 0.0193627 Cell: 3 20 3\n\tMax Velocity: Length: 0.417457 Velocity: 0.0186778 0.416621 0.0186778 Cell: 3 20 3\nWriting paraview in time step 5\n\n\t\tu_mean :\t <0.000914452,0.0483375,-5.75755e-18>\t u_meanMag\t0.00233735 ,\trho_mean  0.896583\n\tt(phyical) : 0.00833333\t Ek : 0.00246751\tMax Velocity: Length: 0.417457 Velocity: 0.0186778 0.416621 0.0186778 Cell: 3 20 3\n\tMax Velocity: Length: 0.417457 Velocity: 0.0186778 0.416621 0.0186778 Cell: 3 20 3\n\tMax Velocity: Length: 0.417131 Velocity: 0.0202386 0.416148 0.0202386 Cell: 3 20 3\n\n\t\tu_mean :\t <0.00101488,0.0552032,2.81611e-18>\t u_meanMag\t0.00304843 ,\trho_mean  0.878326\n\tt(phyical) : 0.01\t Ek : 0.00280525\tMax Velocity: Length: 0.417131 Velocity: 0.0202386 0.416148 0.0202386 Cell: 3 20 3\n\tMax Velocity: Length: 0.417131 Velocity: 0.0202386 0.416148 0.0202386 Cell: 3 20 3\n\tMax Velocity: Length: 0.422293 Velocity: 0.0192286 0.421417 0.0192286 Cell: 3 20 3\n\n\t\tu_mean :\t <0.00110219,0.0620353,-6.17605e-18>\t u_meanMag\t0.00384959 ,\trho_mean  0.860658\n\tt(phyical) : 0.0116667\t Ek : 0.0031579\tMax Velocity: Length: 0.422293 Velocity: 0.0192286 0.421417 0.0192286 Cell: 3 20 3\n\tMax Velocity: Length: 0.422293 Velocity: 0.0192286 0.421417 0.0192286 Cell: 3 20 3\n\tMax Velocity: Length: 0.422079 Velocity: 0.0149485 0.421601 0.0133998 Cell: 4 20 4\n\n\t\tu_mean :\t <0.00117881,0.0685791,-8.6103e-18>\t u_meanMag\t0.00470448 ,\trho_mean  0.842978\n\tt(phyical) : 0.0133333\t Ek : 0.00348948\tMax Velocity: Length: 0.422079 Velocity: 0.0149485 0.421601 0.0133998 Cell: 4 20 4\n\tMax Velocity: Length: 0.422079 Velocity: 0.0149485 0.421601 0.0133998 Cell: 4 20 4\n\tMax Velocity: Length: 0.429349 Velocity: 0.0172607 0.428743 0.0148948 Cell: 4 20 4\nWriting paraview in time step 9\n\n\t\tu_mean :\t <0.00124623,0.0750761,-7.38808e-18>\t u_meanMag\t0.00563798 ,\trho_mean  0.825671\n\tt(phyical) : 0.015\t Ek : 0.00382825\tMax Velocity: Length: 0.429349 Velocity: 0.0172607 0.428743 0.0148948 Cell: 4 20 4\n\tMax Velocity: Length: 0.429349 Velocity: 0.0172607 0.428743 0.0148948 Cell: 4 20 4\n\tMax Velocity: Length: 0.432025 Velocity: 0.0181958 0.431391 0.0146862 Cell: 4 20 4\n\n\t\tu_mean :\t <0.00130592,0.0813517,-9.66436e-18>\t u_meanMag\t0.0066198 ,\trho_mean  0.808369\n\tt(phyical) : 0.0166667\t Ek : 0.0041497\tMax Velocity: Length: 0.432025 Velocity: 0.0181958 0.431391 0.0146862 Cell: 4 20 4\n\tMax Velocity: Length: 0.432025 Velocity: 0.0181958 0.431391 0.0146862 Cell: 4 20 4\n\tMax Velocity: Length: 0.437865 Velocity: 0.0200402 0.437375 0.00523642 Cell: 4 20 5\n\n\t\tu_mean :\t <0.0013587,0.0875591,-1.12694e-17>\t u_meanMag\t0.00766845 ,\trho_mean  0.791344\n\tt(phyical) : 0.0183333\t Ek : 0.00447191\tMax Velocity: Length: 0.437865 Velocity: 0.0200402 0.437375 0.00523642 Cell: 4 20 5\n\tMax Velocity: Length: 0.437865 Velocity: 0.0200402 0.437375 0.00523642 Cell: 4 20 5\n\tMax Velocity: Length: 0.440786 Velocity: 0.0206866 0.440267 0.00543559 Cell: 4 20 5\n\n\t\tu_mean :\t <0.00140557,0.0935931,3.01538e-18>\t u_meanMag\t0.00876164 ,\trho_mean  0.774366\n\tt(phyical) : 0.02\t Ek : 0.00478002\tMax Velocity: Length: 0.440786 Velocity: 0.0206866 0.440267 0.00543559 Cell: 4 20 5\n\tMax Velocity: Length: 0.440786 Velocity: 0.0206866 0.440267 0.00543559 Cell: 4 20 5\n\tMax Velocity: Length: 0.446016 Velocity: -0.0174164 0.445645 0.00525401 Cell: 36 20 5\nWriting paraview in time step 13\n\n\t\tu_mean :\t <0.001447,0.0995527,3.26583e-18>\t u_meanMag\t0.00991283 ,\trho_mean  0.757614\n\tt(phyical) : 0.0216667\t Ek : 0.0050858\tMax Velocity: Length: 0.446016 Velocity: -0.0174164 0.445645 0.00525401 Cell: 36 20 5\n\tMax Velocity: Length: 0.446016 Velocity: -0.0174164 0.445645 0.00525401 Cell: 36 20 5\n\tMax Velocity: Length: 0.449168 Velocity: 0.0186473 0.448751 0.00515406 Cell: 5 20 5\n\n\t\tu_mean :\t <0.00148371,0.105368,-3.01181e-18>\t u_meanMag\t0.0111046 ,\trho_mean  0.74093\n\tt(phyical) : 0.0233333\t Ek : 0.00537934\tMax Velocity: Length: 0.449168 Velocity: 0.0186473 0.448751 0.00515406 Cell: 5 20 5\n\tMax Velocity: Length: 0.449168 Velocity: 0.0186473 0.448751 0.00515406 Cell: 5 20 5\n\tMax Velocity: Length: 0.45302 Velocity: 0.0196055 0.45257 0.00480341 Cell: 5 20 5\n\n\t\tu_mean :\t <0.00151601,0.111108,7.58942e-22>\t u_meanMag\t0.0123474 ,\trho_mean  0.724438\n\tt(phyical) : 0.025\t Ek : 0.005669\tMax Velocity: Length: 0.45302 Velocity: 0.0196055 0.45257 0.00480341 Cell: 5 20 5\n\tMax Velocity: Length: 0.45302 Velocity: 0.0196055 0.45257 0.00480341 Cell: 5 20 5\n\tMax Velocity: Length: 0.454743 Velocity: -0.020478 0.454259 0.00456201 Cell: 36 20 5\n\n\t\tu_mean :\t <0.00154444,0.116726,1.5059e-17>\t u_meanMag\t0.0136274 ,\trho_mean  0.708025\n\tt(phyical) : 0.0266667\t Ek : 0.00594807\tMax Velocity: Length: 0.454743 Velocity: -0.020478 0.454259 0.00456201 Cell: 36 20 5\n\tMax Velocity: Length: 0.454743 Velocity: -0.020478 0.454259 0.00456201 Cell: 36 20 5\n\tMax Velocity: Length: 0.456861 Velocity: 0.0211552 0.456352 0.00419606 Cell: 5 20 5\nWriting paraview in time step 17\n\n\t\tu_mean :\t <0.0015692,0.122271,-2.75523e-18>\t u_meanMag\t0.0149528 ,\trho_mean  0.69178\n\tt(phyical) : 0.0283333\t Ek : 0.0062227\tMax Velocity: Length: 0.456861 Velocity: 0.0211552 0.456352 0.00419606 Cell: 5 20 5\n\tMax Velocity: Length: 0.456861 Velocity: 0.0211552 0.456352 0.00419606 Cell: 5 20 5\n\tMax Velocity: Length: 0.457687 Velocity: 0.0183619 0.457302 0.00390476 Cell: 6 20 5\n\n\t\tu_mean :\t <0.00159071,0.127711,2.05651e-17>\t u_meanMag\t0.0163126 ,\trho_mean  0.675619\n\tt(phyical) : 0.03\t Ek : 0.00648827\tMax Velocity: Length: 0.457687 Velocity: 0.0183619 0.457302 0.00390476 Cell: 6 20 5\n\tMax Velocity: Length: 0.457687 Velocity: 0.0183619 0.457302 0.00390476 Cell: 6 20 5\n\tMax Velocity: Length: 0.459284 Velocity: 0.01922 0.458868 0.0035557 Cell: 6 20 5\n\n\t\tu_mean :\t <0.00160909,0.133081,2.17785e-17>\t u_meanMag\t0.0177132 ,\trho_mean  0.659606\n\tt(phyical) : 0.0316667\t Ek : 0.0067494\tMax Velocity: Length: 0.459284 Velocity: 0.01922 0.458868 0.0035557 Cell: 6 20 5\n\tMax Velocity: Length: 0.459284 Velocity: 0.01922 0.458868 0.0035557 Cell: 6 20 5\n\tMax Velocity: Length: 0.459722 Velocity: -0.0199397 0.459277 0.00327436 Cell: 35 20 5\n\n\t\tu_mean :\t <0.00162469,0.138359,-1.10878e-17>\t u_meanMag\t0.0191459 ,\trho_mean  0.643678\n\tt(phyical) : 0.0333333\t Ek : 0.00700282\tMax Velocity: Length: 0.459722 Velocity: -0.0199397 0.459277 0.00327436 Cell: 35 20 5\n\tMax Velocity: Length: 0.459722 Velocity: -0.0199397 0.459277 0.00327436 Cell: 35 20 5\n\tMax Velocity: Length: 0.460392 Velocity: -0.0205884 0.459922 0.00297727 Cell: 35 20 5\nWriting paraview in time step 21\n\n\t\tu_mean :\t <0.00163761,0.14357,-2.22158e-17>\t u_meanMag\t0.0206151 ,\trho_mean  0.627883\n\tt(phyical) : 0.035\t Ek : 0.00725202\tMax Velocity: Length: 0.460392 Velocity: -0.0205884 0.459922 0.00297727 Cell: 35 20 5\n\tMax Velocity: Length: 0.460392 Velocity: -0.0205884 0.459922 0.00297727 Cell: 35 20 5\n\tMax Velocity: Length: 0.460242 Velocity: -0.0211198 0.459749 0.00273319 Cell: 35 20 5\n\n\t\tu_mean :\t <0.0016482,0.148694,1.81029e-18>\t u_meanMag\t0.0221127 ,\trho_mean  0.612178\n\tt(phyical) : 0.0366667\t Ek : 0.00749463\tMax Velocity: Length: 0.460242 Velocity: -0.0211198 0.459749 0.00273319 Cell: 35 20 5\n\tMax Velocity: Length: 0.460242 Velocity: -0.0211198 0.459749 0.00273319 Cell: 35 20 5\n\tMax Velocity: Length: 0.460332 Velocity: -0.0215846 0.459819 -0.00248722 Cell: 35 20 6\n\n\t\tu_mean :\t <0.00165665,0.153744,-1.00063e-17>\t u_meanMag\t0.0236401 ,\trho_mean  0.596606\n\tt(phyical) : 0.0383333\t Ek : 0.00773328\tMax Velocity: Length: 0.460332 Velocity: -0.0215846 0.459819 -0.00248722 Cell: 35 20 6\n\tMax Velocity: Length: 0.460332 Velocity: -0.0215846 0.459819 -0.00248722 Cell: 35 20 6\n\tMax Velocity: Length: 0.4599 Velocity: -0.0192419 0.459492 0.00222175 Cell: 34 20 5\n\n\t\tu_mean :\t <0.00166342,0.158697,-3.86572e-18>\t u_meanMag\t0.0251874 ,\trho_mean  0.58114\n\tt(phyical) : 0.04\t Ek : 0.00796617\tMax Velocity: Length: 0.4599 Velocity: -0.0192419 0.459492 0.00222175 Cell: 34 20 5\n\tMax Velocity: Length: 0.4599 Velocity: -0.0192419 0.459492 0.00222175 Cell: 34 20 5\n\tMax Velocity: Length: 0.460079 Velocity: -0.0198023 0.459648 0.00201587 Cell: 34 20 5\nWriting paraview in time step 25\n\n\t\tu_mean :\t <0.00166885,0.163551,-5.63904e-18>\t u_meanMag\t0.0267517 ,\trho_mean  0.565825\n\tt(phyical) : 0.0416667\t Ek : 0.00819526\tMax Velocity: Length: 0.460079 Velocity: -0.0198023 0.459648 0.00201587 Cell: 34 20 5\n\tMax Velocity: Length: 0.460079 Velocity: -0.0198023 0.459648 0.00201587 Cell: 34 20 5\n\tMax Velocity: Length: 0.459846 Velocity: -0.0202914 0.459394 0.00183496 Cell: 34 20 5\n\n\t\tu_mean :\t <0.00167351,0.168279,2.92377e-18>\t u_meanMag\t0.0283206 ,\trho_mean  0.550652\n\tt(phyical) : 0.0433333\t Ek : 0.00841907\tMax Velocity: Length: 0.459846 Velocity: -0.0202914 0.459394 0.00183496 Cell: 34 20 5\n\tMax Velocity: Length: 0.459846 Velocity: -0.0202914 0.459394 0.00183496 Cell: 34 20 5\n\tMax Velocity: Length: 0.459815 Velocity: -0.0207126 0.459345 0.00165587 Cell: 34 20 5\n\n\t\tu_mean :\t <0.00167783,0.172873,-3.44581e-18>\t u_meanMag\t0.0298877 ,\trho_mean  0.535664\n\tt(phyical) : 0.045\t Ek : 0.00863896\tMax Velocity: Length: 0.459815 Velocity: -0.0207126 0.459345 0.00165587 Cell: 34 20 5\n\tMax Velocity: Length: 0.459815 Velocity: -0.0207126 0.459345 0.00165587 Cell: 34 20 5\n\tMax Velocity: Length: 0.459456 Velocity: -0.0210668 0.45897 0.00149192 Cell: 34 20 5\n\n\t\tu_mean :\t <0.00168237,0.177304,8.4352e-18>\t u_meanMag\t0.0314394 ,\trho_mean  0.520862\n\tt(phyical) : 0.0466667\t Ek : 0.00885355\tMax Velocity: Length: 0.459456 Velocity: -0.0210668 0.45897 0.00149192 Cell: 34 20 5\n\tMax Velocity: Length: 0.459456 Velocity: -0.0210668 0.45897 0.00149192 Cell: 34 20 5\n\tMax Velocity: Length: 0.459242 Velocity: -0.0213586 0.458743 0.0013281 Cell: 34 20 5\nWriting paraview in time step 29\n\n\t\tu_mean :\t <0.00168753,0.181562,-6.14276e-18>\t u_meanMag\t0.0329677 ,\trho_mean  0.506286\n\tt(phyical) : 0.0483333\t Ek : 0.00906361\tMax Velocity: Length: 0.459242 Velocity: -0.0213586 0.458743 0.0013281 Cell: 34 20 5\n\tMax Velocity: Length: 0.459242 Velocity: -0.0213586 0.458743 0.0013281 Cell: 34 20 5\n\tMax Velocity: Length: 0.45874 Velocity: -0.0215893 0.45823 0.00117409 Cell: 34 20 5\n\n\t\tu_mean :\t <0.0016938,0.185626,-1.3819e-17>\t u_meanMag\t0.0344598 ,\trho_mean  0.491938\n\tt(phyical) : 0.05\t Ek : 0.00926771\tMax Velocity: Length: 0.45874 Velocity: -0.0215893 0.45823 0.00117409 Cell: 34 20 5\n\tMax Velocity: Length: 0.45874 Velocity: -0.0215893 0.45823 0.00117409 Cell: 34 20 5\n\tMax Velocity: Length: 0.458406 Velocity: -0.0197339 0.45798 0.000957527 Cell: 33 20 5\n\n\t\tu_mean :\t <0.00170144,0.189487,3.57386e-18>\t u_meanMag\t0.0359081 ,\trho_mean  0.477852\n\tt(phyical) : 0.0516667\t Ek : 0.0094661\tMax Velocity: Length: 0.458406 Velocity: -0.0197339 0.45798 0.000957527 Cell: 33 20 5\n\tMax Velocity: Length: 0.458406 Velocity: -0.0197339 0.45798 0.000957527 Cell: 33 20 5\n\tMax Velocity: Length: 0.458002 Velocity: -0.0200489 0.457563 -0.000812294 Cell: 33 20 6\n\n\t\tu_mean :\t <0.00171081,0.19313,-1.14606e-17>\t u_meanMag\t0.037302 ,\trho_mean  0.464028\n\tt(phyical) : 0.0533333\t Ek : 0.0096573\tMax Velocity: Length: 0.458002 Velocity: -0.0200489 0.457563 -0.000812294 Cell: 33 20 6\n\tMax Velocity: Length: 0.458002 Velocity: -0.0200489 0.457563 -0.000812294 Cell: 33 20 6\n\tMax Velocity: Length: 0.457635 Velocity: -0.0203166 0.457183 -0.000669568 Cell: 33 20 6\nWriting paraview in time step 33\n\n\t\tu_mean :\t <0.00172207,0.196552,-2.73338e-18>\t u_meanMag\t0.0386355 ,\trho_mean  0.450492\n\tt(phyical) : 0.055\t Ek : 0.00984117\tMax Velocity: Length: 0.457635 Velocity: -0.0203166 0.457183 -0.000669568 Cell: 33 20 6\n\tMax Velocity: Length: 0.457635 Velocity: -0.0203166 0.457183 -0.000669568 Cell: 33 20 6\n\tMax Velocity: Length: 0.457027 Velocity: -0.0205337 0.456565 0.000534874 Cell: 33 20 5\n\n\t\tu_mean :\t <0.00173543,0.199744,-1.48557e-18>\t u_meanMag\t0.0399005 ,\trho_mean  0.437243\n\tt(phyical) : 0.0566667\t Ek : 0.0100162\tMax Velocity: Length: 0.457027 Velocity: -0.0205337 0.456565 0.000534874 Cell: 33 20 5\n\tMax Velocity: Length: 0.457027 Velocity: -0.0205337 0.456565 0.000534874 Cell: 33 20 5\n\tMax Velocity: Length: 0.456425 Velocity: -0.020709 0.455955 0.000404641 Cell: 33 20 5\n\n\t\tu_mean :\t <0.00175094,0.202707,1.33682e-19>\t u_meanMag\t0.0410931 ,\trho_mean  0.424298\n\tt(phyical) : 0.0583333\t Ek : 0.0101821\tMax Velocity: Length: 0.456425 Velocity: -0.020709 0.455955 0.000404641 Cell: 33 20 5\n\tMax Velocity: Length: 0.456425 Velocity: -0.020709 0.455955 0.000404641 Cell: 33 20 5\n\tMax Velocity: Length: 0.455608 Velocity: -0.0208407 0.455131 0.00028297 Cell: 33 20 5\n\n\t\tu_mean :\t <0.0017687,0.205437,1.69103e-18>\t u_meanMag\t0.0422075 ,\trho_mean  0.411655\n\tt(phyical) : 0.06\t Ek : 0.0103374\tMax Velocity: Length: 0.455608 Velocity: -0.0208407 0.455131 0.00028297 Cell: 33 20 5\n\tMax Velocity: Length: 0.455608 Velocity: -0.0208407 0.455131 0.00028297 Cell: 33 20 5\n\tMax Velocity: Length: 0.454784 Velocity: -0.0209363 0.454301 0.000167207 Cell: 33 20 5\nWriting paraview in time step 37\n\n\t\tu_mean :\t <0.00178867,0.207939,1.94257e-18>\t u_meanMag\t0.0432417 ,\trho_mean  0.399325\n\tt(phyical) : 0.0616667\t Ek : 0.0104817\tMax Velocity: Length: 0.454784 Velocity: -0.0209363 0.454301 0.000167207 Cell: 33 20 5\n\tMax Velocity: Length: 0.454784 Velocity: -0.0209363 0.454301 0.000167207 Cell: 33 20 5\n\tMax Velocity: Length: 0.453787 Velocity: -0.0194531 0.45337 -1.61062e-06 Cell: 32 20 5\n\n\t\tu_mean :\t <0.00181089,0.210212,-1.29009e-18>\t u_meanMag\t0.0441923 ,\trho_mean  0.387302\n\tt(phyical) : 0.0633333\t Ek : 0.0106139\tMax Velocity: Length: 0.453787 Velocity: -0.0194531 0.45337 -1.61062e-06 Cell: 32 20 5\n\tMax Velocity: Length: 0.453787 Velocity: -0.0194531 0.45337 -1.61062e-06 Cell: 32 20 5\n\tMax Velocity: Length: 0.452943 Velocity: -0.0196185 0.452518 0.000101108 Cell: 32 20 6\n\n\t\tu_mean :\t <0.00183525,0.212262,6.58924e-18>\t u_meanMag\t0.0450587 ,\trho_mean  0.375596\n\tt(phyical) : 0.065\t Ek : 0.0107336\tMax Velocity: Length: 0.452943 Velocity: -0.0196185 0.452518 0.000101108 Cell: 32 20 6\n\tMax Velocity: Length: 0.452943 Velocity: -0.0196185 0.452518 0.000101108 Cell: 32 20 6\n\tMax Velocity: Length: 0.451951 Velocity: -0.0197506 0.451519 -0.000192377 Cell: 32 20 5\n\n\t\tu_mean :\t <0.00186174,0.214093,-2.21871e-18>\t u_meanMag\t0.0458393 ,\trho_mean  0.364199\n\tt(phyical) : 0.0666667\t Ek : 0.0108399\tMax Velocity: Length: 0.451951 Velocity: -0.0197506 0.451519 -0.000192377 Cell: 32 20 5\n\tMax Velocity: Length: 0.451951 Velocity: -0.0197506 0.451519 -0.000192377 Cell: 32 20 5\n\tMax Velocity: Length: 0.450959 Velocity: -0.0198526 0.450522 -0.000277472 Cell: 32 20 5\nWriting paraview in time step 41\n\n\t\tu_mean :\t <0.0018902,0.215711,1.52591e-18>\t u_meanMag\t0.0465349 ,\trho_mean  0.353116\n\tt(phyical) : 0.0683333\t Ek : 0.0109326\tMax Velocity: Length: 0.450959 Velocity: -0.0198526 0.450522 -0.000277472 Cell: 32 20 5\n\tMax Velocity: Length: 0.450959 Velocity: -0.0198526 0.450522 -0.000277472 Cell: 32 20 5\n\tMax Velocity: Length: 0.449851 Velocity: -0.0199254 0.44941 -0.000355812 Cell: 32 20 5\n\n\t\tu_mean :\t <0.00192058,0.217121,-1.45955e-18>\t u_meanMag\t0.0471452 ,\trho_mean  0.342339\n\tt(phyical) : 0.07\t Ek : 0.0110113\tMax Velocity: Length: 0.449851 Velocity: -0.0199254 0.44941 -0.000355812 Cell: 32 20 5\n\tMax Velocity: Length: 0.449851 Velocity: -0.0199254 0.44941 -0.000355812 Cell: 32 20 5\n\tMax Velocity: Length: 0.448743 Velocity: -0.0199716 0.448299 -0.000429096 Cell: 32 20 5\n\n\t\tu_mean :\t <0.00195271,0.218331,-3.07328e-18>\t u_meanMag\t0.0476722 ,\trho_mean  0.331871\n\tt(phyical) : 0.0716667\t Ek : 0.011076\tMax Velocity: Length: 0.448743 Velocity: -0.0199716 0.448299 -0.000429096 Cell: 32 20 5\n\tMax Velocity: Length: 0.448743 Velocity: -0.0199716 0.448299 -0.000429096 Cell: 32 20 5\n\tMax Velocity: Length: 0.447532 Velocity: -0.0199922 0.447085 -0.000496971 Cell: 32 20 5\n\n\t\tu_mean :\t <0.00198649,0.219346,-1.30386e-18>\t u_meanMag\t0.0481167 ,\trho_mean  0.321705\n\tt(phyical) : 0.0733333\t Ek : 0.0111264\tMax Velocity: Length: 0.447532 Velocity: -0.0199922 0.447085 -0.000496971 Cell: 32 20 5\n\tMax Velocity: Length: 0.447532 Velocity: -0.0199922 0.447085 -0.000496971 Cell: 32 20 5\n\tMax Velocity: Length: 0.446301 Velocity: -0.0199898 0.445853 -0.00056059 Cell: 32 20 5\nWriting paraview in time step 45\n\n\t\tu_mean :\t <0.00202176,0.220176,-8.08381e-18>\t u_meanMag\t0.0484817 ,\trho_mean  0.31184\n\tt(phyical) : 0.075\t Ek : 0.0111629\tMax Velocity: Length: 0.446301 Velocity: -0.0199898 0.445853 -0.00056059 Cell: 32 20 5\n\tMax Velocity: Length: 0.446301 Velocity: -0.0199898 0.445853 -0.00056059 Cell: 32 20 5\n\tMax Velocity: Length: 0.444956 Velocity: -0.0188321 0.444556 -0.000667664 Cell: 31 20 5\n\n\t\tu_mean :\t <0.0020584,0.220827,-2.99782e-18>\t u_meanMag\t0.048769 ,\trho_mean  0.30227\n\tt(phyical) : 0.0766667\t Ek : 0.0111856\tMax Velocity: Length: 0.444956 Velocity: -0.0188321 0.444556 -0.000667664 Cell: 31 20 5\n\tMax Velocity: Length: 0.444956 Velocity: -0.0188321 0.444556 -0.000667664 Cell: 31 20 5\n\tMax Velocity: Length: 0.443653 Velocity: -0.0188747 0.44325 -0.000721086 Cell: 31 20 5\n\n\t\tu_mean :\t <0.00209622,0.22131,2.69825e-18>\t u_meanMag\t0.0489824 ,\trho_mean  0.292995\n\tt(phyical) : 0.0783333\t Ek : 0.011195\tMax Velocity: Length: 0.443653 Velocity: -0.0188747 0.44325 -0.000721086 Cell: 31 20 5\n\tMax Velocity: Length: 0.443653 Velocity: -0.0188747 0.44325 -0.000721086 Cell: 31 20 5\n\tMax Velocity: Length: 0.442203 Velocity: -0.0188943 0.441798 -0.000770746 Cell: 31 20 5\n\n\t\tu_mean :\t <0.00213513,0.221631,-8.07167e-18>\t u_meanMag\t0.0491249 ,\trho_mean  0.28401\n\tt(phyical) : 0.08\t Ek : 0.0111916\tMax Velocity: Length: 0.442203 Velocity: -0.0188943 0.441798 -0.000770746 Cell: 31 20 5\n\tMax Velocity: Length: 0.442203 Velocity: -0.0188943 0.441798 -0.000770746 Cell: 31 20 5\n\tMax Velocity: Length: 0.440653 Velocity: -0.0188937 0.440247 -0.000817437 Cell: 31 20 5\nWriting paraview in time step 49\n\n\t\tu_mean :\t <0.00217492,0.221802,-1.10712e-17>\t u_meanMag\t0.049201 ,\trho_mean  0.275316\n\tt(phyical) : 0.0816667\t Ek : 0.0111763\tMax Velocity: Length: 0.440653 Velocity: -0.0188937 0.440247 -0.000817437 Cell: 31 20 5\n\tMax Velocity: Length: 0.440653 Velocity: -0.0188937 0.440247 -0.000817437 Cell: 31 20 5\n\tMax Velocity: Length: 0.438935 Velocity: -0.0188734 0.438528 0.000861697 Cell: 31 20 6\n\n\t\tu_mean :\t <0.00221549,0.221833,6.27536e-18>\t u_meanMag\t0.0492146 ,\trho_mean  0.266907\n\tt(phyical) : 0.0833333\t Ek : 0.0111496\tMax Velocity: Length: 0.438935 Velocity: -0.0188734 0.438528 0.000861697 Cell: 31 20 6\n\tMax Velocity: Length: 0.438935 Velocity: -0.0188734 0.438528 0.000861697 Cell: 31 20 6\n\tMax Velocity: Length: 0.437092 Velocity: -0.018836 0.436685 -0.000904297 Cell: 31 20 5\n\n\t\tu_mean :\t <0.00225665,0.221733,-7.20908e-18>\t u_meanMag\t0.0491707 ,\trho_mean  0.258787\n\tt(phyical) : 0.085\t Ek : 0.0111126\tMax Velocity: Length: 0.437092 Velocity: -0.018836 0.436685 -0.000904297 Cell: 31 20 5\n\tMax Velocity: Length: 0.437092 Velocity: -0.018836 0.436685 -0.000904297 Cell: 31 20 5\n\tMax Velocity: Length: 0.435077 Velocity: -0.0187823 0.434671 -0.000945898 Cell: 31 20 5\n\n\t\tu_mean :\t <0.0022983,0.221514,-7.85797e-18>\t u_meanMag\t0.0490739 ,\trho_mean  0.25095\n\tt(phyical) : 0.0866667\t Ek : 0.011066\tMax Velocity: Length: 0.435077 Velocity: -0.0187823 0.434671 -0.000945898 Cell: 31 20 5\n\tMax Velocity: Length: 0.435077 Velocity: -0.0187823 0.434671 -0.000945898 Cell: 31 20 5\n\tMax Velocity: Length: 0.432933 Velocity: -0.0187142 0.432527 -0.000987009 Cell: 31 20 5\nWriting paraview in time step 53\n\n\t\tu_mean :\t <0.00234026,0.221187,-3.0413e-18>\t u_meanMag\t0.0489293 ,\trho_mean  0.243399\n\tt(phyical) : 0.0883333\t Ek : 0.0110109\tMax Velocity: Length: 0.432933 Velocity: -0.0187142 0.432527 -0.000987009 Cell: 31 20 5\n\tMax Velocity: Length: 0.432933 Velocity: -0.0187142 0.432527 -0.000987009 Cell: 31 20 5\n\tMax Velocity: Length: 0.430628 Velocity: -0.0186326 0.430224 -0.00102814 Cell: 31 20 5\n\n\t\tu_mean :\t <0.00238243,0.220762,1.61427e-18>\t u_meanMag\t0.0487414 ,\trho_mean  0.236131\n\tt(phyical) : 0.09\t Ek : 0.0109483\tMax Velocity: Length: 0.430628 Velocity: -0.0186326 0.430224 -0.00102814 Cell: 31 20 5\n\tMax Velocity: Length: 0.430628 Velocity: -0.0186326 0.430224 -0.00102814 Cell: 31 20 5\n\tMax Velocity: Length: 0.428204 Velocity: -0.0185391 0.427802 -0.00106943 Cell: 31 20 5\n\n\t\tu_mean :\t <0.00242465,0.220249,-9.39223e-18>\t u_meanMag\t0.0485154 ,\trho_mean  0.229145\n\tt(phyical) : 0.0916667\t Ek : 0.010879\tMax Velocity: Length: 0.428204 Velocity: -0.0185391 0.427802 -0.00106943 Cell: 31 20 5\n\tMax Velocity: Length: 0.428204 Velocity: -0.0185391 0.427802 -0.00106943 Cell: 31 20 5\n\tMax Velocity: Length: 0.425645 Velocity: -0.0184342 0.425244 0.00111121 Cell: 31 20 6\n\n\t\tu_mean :\t <0.00246683,0.219658,6.43257e-19>\t u_meanMag\t0.0482556 ,\trho_mean  0.222438\n\tt(phyical) : 0.0933333\t Ek : 0.010804\tMax Velocity: Length: 0.425645 Velocity: -0.0184342 0.425244 0.00111121 Cell: 31 20 6\n\tMax Velocity: Length: 0.425645 Velocity: -0.0184342 0.425244 0.00111121 Cell: 31 20 6\n\tMax Velocity: Length: 0.423018 Velocity: -0.0173898 0.422658 -0.00118477 Cell: 30 20 5\nWriting paraview in time step 57\n\n\t\tu_mean :\t <0.00250882,0.218998,7.2222e-18>\t u_meanMag\t0.0479666 ,\trho_mean  0.21601\n\tt(phyical) : 0.095\t Ek : 0.0107241\tMax Velocity: Length: 0.423018 Velocity: -0.0173898 0.422658 -0.00118477 Cell: 30 20 5\n\tMax Velocity: Length: 0.423018 Velocity: -0.0173898 0.422658 -0.00118477 Cell: 30 20 5\n\tMax Velocity: Length: 0.420297 Velocity: -0.0172973 0.419939 -0.00122679 Cell: 30 20 5\n\n\t\tu_mean :\t <0.00255054,0.21828,6.00279e-18>\t u_meanMag\t0.0476525 ,\trho_mean  0.209855\n\tt(phyical) : 0.0966667\t Ek : 0.0106402\tMax Velocity: Length: 0.420297 Velocity: -0.0172973 0.419939 -0.00122679 Cell: 30 20 5\n\tMax Velocity: Length: 0.420297 Velocity: -0.0172973 0.419939 -0.00122679 Cell: 30 20 5\n\tMax Velocity: Length: 0.417506 Velocity: -0.0171948 0.41715 -0.00126924 Cell: 30 20 5\n\n\t\tu_mean :\t <0.00259186,0.21751,5.32191e-18>\t u_meanMag\t0.0473173 ,\trho_mean  0.203972\n\tt(phyical) : 0.0983333\t Ek : 0.0105531\tMax Velocity: Length: 0.417506 Velocity: -0.0171948 0.41715 -0.00126924 Cell: 30 20 5\n\tMax Velocity: Length: 0.417506 Velocity: -0.0171948 0.41715 -0.00126924 Cell: 30 20 5\n\tMax Velocity: Length: 0.414644 Velocity: -0.0170827 0.41429 -0.00131233 Cell: 30 20 5\n\n\t\tu_mean :\t <0.00263273,0.216697,2.07695e-18>\t u_meanMag\t0.0469644 ,\trho_mean  0.198354\n\tt(phyical) : 0.1\t Ek : 0.0104633\tMax Velocity: Length: 0.414644 Velocity: -0.0170827 0.41429 -0.00131233 Cell: 30 20 5\n\tMax Velocity: Length: 0.414644 Velocity: -0.0170827 0.41429 -0.00131233 Cell: 30 20 5\n\tMax Velocity: Length: 0.411742 Velocity: -0.0169623 0.411391 -0.00135579 Cell: 30 20 5\nWriting paraview in time step 61\n\n\t\tu_mean :\t <0.00267303,0.215848,-6.13171e-19>\t u_meanMag\t0.0465974 ,\trho_mean  0.192998\n\tt(phyical) : 0.101667\t Ek : 0.0103716\tMax Velocity: Length: 0.411742 Velocity: -0.0169623 0.411391 -0.00135579 Cell: 30 20 5\n\tMax Velocity: Length: 0.411742 Velocity: -0.0169623 0.411391 -0.00135579 Cell: 30 20 5\n\tMax Velocity: Length: 0.408801 Velocity: -0.0168336 0.408452 -0.00139984 Cell: 30 20 5\n\n\t\tu_mean :\t <0.0027127,0.214969,-1.69688e-18>\t u_meanMag\t0.046219 ,\trho_mean  0.187897\n\tt(phyical) : 0.103333\t Ek : 0.0102785\tMax Velocity: Length: 0.408801 Velocity: -0.0168336 0.408452 -0.00139984 Cell: 30 20 5\n\tMax Velocity: Length: 0.408801 Velocity: -0.0168336 0.408452 -0.00139984 Cell: 30 20 5\n\tMax Velocity: Length: 0.405844 Velocity: -0.0166978 0.405497 -0.00144416 Cell: 30 20 5\n\n\t\tu_mean :\t <0.00275166,0.214067,-3.44288e-19>\t u_meanMag\t0.0458323 ,\trho_mean  0.183047\n\tt(phyical) : 0.105\t Ek : 0.0101846\tMax Velocity: Length: 0.405844 Velocity: -0.0166978 0.405497 -0.00144416 Cell: 30 20 5\n\tMax Velocity: Length: 0.405844 Velocity: -0.0166978 0.405497 -0.00144416 Cell: 30 20 5\n\tMax Velocity: Length: 0.402871 Velocity: -0.0165548 0.402527 -0.00148897 Cell: 30 20 5\n\n\t\tu_mean :\t <0.00278988,0.213147,1.78893e-19>\t u_meanMag\t0.0454395 ,\trho_mean  0.17844\n\tt(phyical) : 0.106667\t Ek : 0.0100902\tMax Velocity: Length: 0.402871 Velocity: -0.0165548 0.402527 -0.00148897 Cell: 30 20 5\n\tMax Velocity: Length: 0.402871 Velocity: -0.0165548 0.402527 -0.00148897 Cell: 30 20 5\n\tMax Velocity: Length: 0.3999 Velocity: -0.0164058 0.399561 -0.00153385 Cell: 30 20 5\nWriting paraview in time step 65\n\n\t\tu_mean :\t <0.00282726,0.212215,3.43275e-18>\t u_meanMag\t0.045043 ,\trho_mean  0.17407\n\tt(phyical) : 0.108333\t Ek : 0.00999585\tMax Velocity: Length: 0.3999 Velocity: -0.0164058 0.399561 -0.00153385 Cell: 30 20 5\n\tMax Velocity: Length: 0.3999 Velocity: -0.0164058 0.399561 -0.00153385 Cell: 30 20 5\n\tMax Velocity: Length: 0.396932 Velocity: -0.0162507 0.396596 -0.00157899 Cell: 30 20 5\n\n\t\tu_mean :\t <0.0028638,0.211274,-1.60256e-18>\t u_meanMag\t0.0446448 ,\trho_mean  0.16993\n\tt(phyical) : 0.11\t Ek : 0.00990185\tMax Velocity: Length: 0.396932 Velocity: -0.0162507 0.396596 -0.00157899 Cell: 30 20 5\n\tMax Velocity: Length: 0.396932 Velocity: -0.0162507 0.396596 -0.00157899 Cell: 30 20 5\n\tMax Velocity: Length: 0.39398 Velocity: -0.0160907 0.393648 -0.00162388 Cell: 30 20 5\n\n\t\tu_mean :\t <0.00289941,0.210329,-1.63682e-18>\t u_meanMag\t0.0442466 ,\trho_mean  0.166015\n\tt(phyical) : 0.111667\t Ek : 0.00980855\tMax Velocity: Length: 0.39398 Velocity: -0.0160907 0.393648 -0.00162388 Cell: 30 20 5\n\tMax Velocity: Length: 0.39398 Velocity: -0.0160907 0.393648 -0.00162388 Cell: 30 20 5\n\tMax Velocity: Length: 0.391042 Velocity: -0.0159257 0.390714 -0.00166871 Cell: 30 20 5\n\n\t\tu_mean :\t <0.0029341,0.209384,5.51062e-18>\t u_meanMag\t0.0438501 ,\trho_mean  0.162315\n\tt(phyical) : 0.113333\t Ek : 0.00971621\tMax Velocity: Length: 0.391042 Velocity: -0.0159257 0.390714 -0.00166871 Cell: 30 20 5\n\tMax Velocity: Length: 0.391042 Velocity: -0.0159257 0.390714 -0.00166871 Cell: 30 20 5\n\tMax Velocity: Length: 0.38813 Velocity: -0.0157571 0.387806 -0.00171288 Cell: 30 20 5\nWriting paraview in time step 69\n\n\t\tu_mean :\t <0.00296781,0.208442,5.16227e-18>\t u_meanMag\t0.0434568 ,\trho_mean  0.158824\n\tt(phyical) : 0.115\t Ek : 0.00962509\tMax Velocity: Length: 0.38813 Velocity: -0.0157571 0.387806 -0.00171288 Cell: 30 20 5\n\tMax Velocity: Length: 0.38813 Velocity: -0.0157571 0.387806 -0.00171288 Cell: 30 20 5\n\tMax Velocity: Length: 0.385242 Velocity: -0.0155848 0.384923 0.00175664 Cell: 30 20 6\n\n\t\tu_mean :\t <0.00300053,0.207506,-2.62361e-18>\t u_meanMag\t0.0430677 ,\trho_mean  0.155536\n\tt(phyical) : 0.116667\t Ek : 0.00953541\tMax Velocity: Length: 0.385242 Velocity: -0.0155848 0.384923 0.00175664 Cell: 30 20 6\n\tMax Velocity: Length: 0.385242 Velocity: -0.0155848 0.384923 0.00175664 Cell: 30 20 6\n\tMax Velocity: Length: 0.382389 Velocity: -0.0154103 0.382074 -0.00179936 Cell: 30 20 5\n\n\t\tu_mean :\t <0.00303223,0.206579,-8.83169e-18>\t u_meanMag\t0.042684 ,\trho_mean  0.152442\n\tt(phyical) : 0.118333\t Ek : 0.00944736\tMax Velocity: Length: 0.382389 Velocity: -0.0154103 0.382074 -0.00179936 Cell: 30 20 5\n\tMax Velocity: Length: 0.382389 Velocity: -0.0154103 0.382074 -0.00179936 Cell: 30 20 5\n\tMax Velocity: Length: 0.379569 Velocity: -0.0152335 0.379259 -0.00184134 Cell: 30 20 5\n\n\t\tu_mean :\t <0.0030629,0.205663,-3.51227e-19>\t u_meanMag\t0.0423066 ,\trho_mean  0.149537\n\tt(phyical) : 0.12\t Ek : 0.00936109\tMax Velocity: Length: 0.379569 Velocity: -0.0152335 0.379259 -0.00184134 Cell: 30 20 5\n\tMax Velocity: Length: 0.379569 Velocity: -0.0152335 0.379259 -0.00184134 Cell: 30 20 5\n\tMax Velocity: Length: 0.376791 Velocity: -0.0150558 0.376486 0.00188198 Cell: 30 20 6\nWriting paraview in time step 73\n\n\t\tu_mean :\t <0.0030925,0.20476,-7.33929e-18>\t u_meanMag\t0.0419364 ,\trho_mean  0.146812\n\tt(phyical) : 0.121667\t Ek : 0.00927675\tMax Velocity: Length: 0.376791 Velocity: -0.0150558 0.376486 0.00188198 Cell: 30 20 6\n\tMax Velocity: Length: 0.376791 Velocity: -0.0150558 0.376486 0.00188198 Cell: 30 20 6\n\tMax Velocity: Length: 0.374055 Velocity: -0.0148771 0.373754 -0.00192166 Cell: 30 20 5\n\n\t\tu_mean :\t <0.00312106,0.203873,-3.78056e-18>\t u_meanMag\t0.0415739 ,\trho_mean  0.144262\n\tt(phyical) : 0.123333\t Ek : 0.00919443\tMax Velocity: Length: 0.374055 Velocity: -0.0148771 0.373754 -0.00192166 Cell: 30 20 5\n\tMax Velocity: Length: 0.374055 Velocity: -0.0148771 0.373754 -0.00192166 Cell: 30 20 5\n\tMax Velocity: Length: 0.371368 Velocity: -0.0146988 0.371072 -0.00195978 Cell: 30 20 5\n\n\t\tu_mean :\t <0.00314853,0.203002,-3.0858e-18>\t u_meanMag\t0.0412197 ,\trho_mean  0.141879\n\tt(phyical) : 0.125\t Ek : 0.00911423\tMax Velocity: Length: 0.371368 Velocity: -0.0146988 0.371072 -0.00195978 Cell: 30 20 5\n\tMax Velocity: Length: 0.371368 Velocity: -0.0146988 0.371072 -0.00195978 Cell: 30 20 5\n\tMax Velocity: Length: 0.368731 Velocity: -0.0145205 0.36844 -0.00199683 Cell: 30 20 5\n\n\t\tu_mean :\t <0.00317494,0.202149,1.90245e-18>\t u_meanMag\t0.0408743 ,\trho_mean  0.139656\n\tt(phyical) : 0.126667\t Ek : 0.00903622\tMax Velocity: Length: 0.368731 Velocity: -0.0145205 0.36844 -0.00199683 Cell: 30 20 5\n\tMax Velocity: Length: 0.368731 Velocity: -0.0145205 0.36844 -0.00199683 Cell: 30 20 5\n\tMax Velocity: Length: 0.36615 Velocity: -0.0143439 0.365863 0.0020322 Cell: 30 20 6\nWriting paraview in time step 77\n\n\t\tu_mean :\t <0.00320027,0.201315,6.23931e-18>\t u_meanMag\t0.0405379 ,\trho_mean  0.137588\n\tt(phyical) : 0.128333\t Ek : 0.00896045\tMax Velocity: Length: 0.36615 Velocity: -0.0143439 0.365863 0.0020322 Cell: 30 20 6\n\tMax Velocity: Length: 0.36615 Velocity: -0.0143439 0.365863 0.0020322 Cell: 30 20 6\n\tMax Velocity: Length: 0.363624 Velocity: -0.0141683 0.363342 -0.00206647 Cell: 30 20 5\n\n\t\tu_mean :\t <0.00322453,0.2005,-1.11104e-18>\t u_meanMag\t0.0402108 ,\trho_mean  0.135667\n\tt(phyical) : 0.13\t Ek : 0.00888693\tMax Velocity: Length: 0.363624 Velocity: -0.0141683 0.363342 -0.00206647 Cell: 30 20 5\n\tMax Velocity: Length: 0.363624 Velocity: -0.0141683 0.363342 -0.00206647 Cell: 30 20 5\n\tMax Velocity: Length: 0.361161 Velocity: -0.0139952 0.360884 -0.00209901 Cell: 30 20 5\n\n\t\tu_mean :\t <0.00324772,0.199706,2.61808e-18>\t u_meanMag\t0.0398931 ,\trho_mean  0.133889\n\tt(phyical) : 0.131667\t Ek : 0.0088157\tMax Velocity: Length: 0.361161 Velocity: -0.0139952 0.360884 -0.00209901 Cell: 30 20 5\n\tMax Velocity: Length: 0.361161 Velocity: -0.0139952 0.360884 -0.00209901 Cell: 30 20 5\n\tMax Velocity: Length: 0.358809 Velocity: -0.0148493 0.358501 0.000624093 Cell: 30 19 5\n\n\t\tu_mean :\t <0.00326986,0.198933,4.30049e-19>\t u_meanMag\t0.039585 ,\trho_mean  0.132245\n\tt(phyical) : 0.133333\t Ek : 0.00874674\tMax Velocity: Length: 0.358809 Velocity: -0.0148493 0.358501 0.000624093 Cell: 30 19 5\n\tMax Velocity: Length: 0.358809 Velocity: -0.0148493 0.358501 0.000624093 Cell: 30 19 5\n\tMax Velocity: Length: 0.356556 Velocity: -0.0146932 0.356253 0.000603276 Cell: 30 19 5\nWriting paraview in time step 81\n\n\t\tu_mean :\t <0.00329094,0.198181,-3.10722e-18>\t u_meanMag\t0.0392864 ,\trho_mean  0.130731\n\tt(phyical) : 0.135\t Ek : 0.00868005\tMax Velocity: Length: 0.356556 Velocity: -0.0146932 0.356253 0.000603276 Cell: 30 19 5\n\tMax Velocity: Length: 0.356556 Velocity: -0.0146932 0.356253 0.000603276 Cell: 30 19 5\n\tMax Velocity: Length: 0.354369 Velocity: -0.0145399 0.35407 0.000582671 Cell: 30 19 5\n\n\t\tu_mean :\t <0.003311,0.19745,-5.21062e-18>\t u_meanMag\t0.0389973 ,\trho_mean  0.129341\n\tt(phyical) : 0.136667\t Ek : 0.00861559\tMax Velocity: Length: 0.354369 Velocity: -0.0145399 0.35407 0.000582671 Cell: 30 19 5\n\tMax Velocity: Length: 0.354369 Velocity: -0.0145399 0.35407 0.000582671 Cell: 30 19 5\n\tMax Velocity: Length: 0.352247 Velocity: -0.0143907 0.351952 0.00056304 Cell: 30 19 5\n\n\t\tu_mean :\t <0.00333004,0.19674,-3.46348e-18>\t u_meanMag\t0.0387176 ,\trho_mean  0.128069\n\tt(phyical) : 0.138333\t Ek : 0.00855335\tMax Velocity: Length: 0.352247 Velocity: -0.0143907 0.351952 0.00056304 Cell: 30 19 5\n\tMax Velocity: Length: 0.352247 Velocity: -0.0143907 0.351952 0.00056304 Cell: 30 19 5\n\tMax Velocity: Length: 0.350193 Velocity: -0.0142447 0.349903 0.000543576 Cell: 30 19 5\n\n\t\tu_mean :\t <0.0033481,0.196051,-6.41544e-18>\t u_meanMag\t0.0384472 ,\trho_mean  0.126909\n\tt(phyical) : 0.14\t Ek : 0.00849327\tMax Velocity: Length: 0.350193 Velocity: -0.0142447 0.349903 0.000543576 Cell: 30 19 5\n\tMax Velocity: Length: 0.350193 Velocity: -0.0142447 0.349903 0.000543576 Cell: 30 19 5\n\tMax Velocity: Length: 0.348208 Velocity: -0.0141028 0.347922 0.000525118 Cell: 30 19 5\nWriting paraview in time step 85\n\n\t\tu_mean :\t <0.00336517,0.195383,3.80175e-18>\t u_meanMag\t0.038186 ,\trho_mean  0.125856\n\tt(phyical) : 0.141667\t Ek : 0.00843531\tMax Velocity: Length: 0.348208 Velocity: -0.0141028 0.347922 0.000525118 Cell: 30 19 5\n\tMax Velocity: Length: 0.348208 Velocity: -0.0141028 0.347922 0.000525118 Cell: 30 19 5\n\tMax Velocity: Length: 0.346292 Velocity: -0.0139642 0.34601 0.000506812 Cell: 30 19 5\n\n\t\tu_mean :\t <0.00338132,0.194736,4.16952e-18>\t u_meanMag\t0.0379336 ,\trho_mean  0.124905\n\tt(phyical) : 0.143333\t Ek : 0.00837941\tMax Velocity: Length: 0.346292 Velocity: -0.0139642 0.34601 0.000506812 Cell: 30 19 5\n\tMax Velocity: Length: 0.346292 Velocity: -0.0139642 0.34601 0.000506812 Cell: 30 19 5\n\tMax Velocity: Length: 0.344445 Velocity: -0.0138299 0.344167 0.000489559 Cell: 30 19 5\n\n\t\tu_mean :\t <0.00339655,0.19411,-1.37542e-18>\t u_meanMag\t0.03769 ,\trho_mean  0.124051\n\tt(phyical) : 0.145\t Ek : 0.00832552\tMax Velocity: Length: 0.344445 Velocity: -0.0138299 0.344167 0.000489559 Cell: 30 19 5\n\tMax Velocity: Length: 0.344445 Velocity: -0.0138299 0.344167 0.000489559 Cell: 30 19 5\n\tMax Velocity: Length: 0.342668 Velocity: -0.0136989 0.342394 0.000472461 Cell: 30 19 5\n\n\t\tu_mean :\t <0.00341092,0.193503,2.80787e-18>\t u_meanMag\t0.0374549 ,\trho_mean  0.123289\n\tt(phyical) : 0.146667\t Ek : 0.00827356\tMax Velocity: Length: 0.342668 Velocity: -0.0136989 0.342394 0.000472461 Cell: 30 19 5\n\tMax Velocity: Length: 0.342668 Velocity: -0.0136989 0.342394 0.000472461 Cell: 30 19 5\n\tMax Velocity: Length: 0.340961 Velocity: -0.0135721 0.34069 0.000456477 Cell: 30 19 5\nWriting paraview in time step 89\n\n\t\tu_mean :\t <0.00342442,0.192915,-2.58539e-18>\t u_meanMag\t0.0372281 ,\trho_mean  0.122614\n\tt(phyical) : 0.148333\t Ek : 0.00822349\tMax Velocity: Length: 0.340961 Velocity: -0.0135721 0.34069 0.000456477 Cell: 30 19 5\n\tMax Velocity: Length: 0.340961 Velocity: -0.0135721 0.34069 0.000456477 Cell: 30 19 5\n\tMax Velocity: Length: 0.339323 Velocity: -0.0134486 0.339056 0.000440667 Cell: 30 19 5\n\n\t\tu_mean :\t <0.00343714,0.192347,-2.91379e-18>\t u_meanMag\t0.0370092 ,\trho_mean  0.122021\n\tt(phyical) : 0.15\t Ek : 0.00817523\tMax Velocity: Length: 0.339323 Velocity: -0.0134486 0.339056 0.000440667 Cell: 30 19 5\n\tMax Velocity: Length: 0.339323 Velocity: -0.0134486 0.339056 0.000440667 Cell: 30 19 5\n\tMax Velocity: Length: 0.337754 Velocity: -0.0133292 0.33749 0.00042603 Cell: 30 19 5\n\n\t\tu_mean :\t <0.00344907,0.191797,2.71365e-18>\t u_meanMag\t0.0367981 ,\trho_mean  0.121507\n\tt(phyical) : 0.151667\t Ek : 0.00812872\tMax Velocity: Length: 0.337754 Velocity: -0.0133292 0.33749 0.00042603 Cell: 30 19 5\n\tMax Velocity: Length: 0.337754 Velocity: -0.0133292 0.33749 0.00042603 Cell: 30 19 5\n\tMax Velocity: Length: 0.336252 Velocity: -0.0132128 0.335992 0.000411591 Cell: 30 19 5\n\n\t\tu_mean :\t <0.00346028,0.191266,4.47082e-18>\t u_meanMag\t0.0365946 ,\trho_mean  0.121066\n\tt(phyical) : 0.153333\t Ek : 0.0080839\tMax Velocity: Length: 0.336252 Velocity: -0.0132128 0.335992 0.000411591 Cell: 30 19 5\n\tMax Velocity: Length: 0.336252 Velocity: -0.0132128 0.335992 0.000411591 Cell: 30 19 5\n\tMax Velocity: Length: 0.334817 Velocity: -0.0131004 0.334561 -0.000398377 Cell: 30 19 6\nWriting paraview in time step 93\n\n\t\tu_mean :\t <0.00347078,0.190752,8.61182e-19>\t u_meanMag\t0.0363982 ,\trho_mean  0.120694\n\tt(phyical) : 0.155\t Ek : 0.0080407\tMax Velocity: Length: 0.334817 Velocity: -0.0131004 0.334561 -0.000398377 Cell: 30 19 6\n\tMax Velocity: Length: 0.334817 Velocity: -0.0131004 0.334561 -0.000398377 Cell: 30 19 6\n\tMax Velocity: Length: 0.333448 Velocity: -0.0129907 0.333195 0.000385381 Cell: 30 19 5\n\n\t\tu_mean :\t <0.00348064,0.190255,-8.83592e-18>\t u_meanMag\t0.0362089 ,\trho_mean  0.120388\n\tt(phyical) : 0.156667\t Ek : 0.00799905\tMax Velocity: Length: 0.333448 Velocity: -0.0129907 0.333195 0.000385381 Cell: 30 19 5\n\tMax Velocity: Length: 0.333448 Velocity: -0.0129907 0.333195 0.000385381 Cell: 30 19 5\n\tMax Velocity: Length: 0.332142 Velocity: -0.0128847 0.331892 0.000373643 Cell: 30 19 5\n\n\t\tu_mean :\t <0.00348988,0.189774,3.86919e-18>\t u_meanMag\t0.0360264 ,\trho_mean  0.120143\n\tt(phyical) : 0.158333\t Ek : 0.00795891\tMax Velocity: Length: 0.332142 Velocity: -0.0128847 0.331892 0.000373643 Cell: 30 19 5\n\tMax Velocity: Length: 0.332142 Velocity: -0.0128847 0.331892 0.000373643 Cell: 30 19 5\n\tMax Velocity: Length: 0.330899 Velocity: -0.0127812 0.330652 0.000362129 Cell: 30 19 5\n\n\t\tu_mean :\t <0.00349855,0.18931,5.6926e-18>\t u_meanMag\t0.0358504 ,\trho_mean  0.119956\n\tt(phyical) : 0.16\t Ek : 0.00792022\tMax Velocity: Length: 0.330899 Velocity: -0.0127812 0.330652 0.000362129 Cell: 30 19 5\n\tMax Velocity: Length: 0.330899 Velocity: -0.0127812 0.330652 0.000362129 Cell: 30 19 5\n\tMax Velocity: Length: 0.329716 Velocity: -0.0126809 0.329472 0.000351886 Cell: 30 19 5\nWriting paraview in time step 97\n\n\t\tu_mean :\t <0.00350666,0.188861,-1.6048e-17>\t u_meanMag\t0.0356808 ,\trho_mean  0.119823\n\tt(phyical) : 0.161667\t Ek : 0.00788291\tMax Velocity: Length: 0.329716 Velocity: -0.0126809 0.329472 0.000351886 Cell: 30 19 5\n\tMax Velocity: Length: 0.329716 Velocity: -0.0126809 0.329472 0.000351886 Cell: 30 19 5\n\tMax Velocity: Length: 0.328592 Velocity: -0.0125827 0.328351 0.000341856 Cell: 30 19 5\n\n\t\tu_mean :\t <0.00351428,0.188428,4.58379e-18>\t u_meanMag\t0.0355173 ,\trho_mean  0.11974\n\tt(phyical) : 0.163333\t Ek : 0.00784696\tMax Velocity: Length: 0.328592 Velocity: -0.0125827 0.328351 0.000341856 Cell: 30 19 5\n\tMax Velocity: Length: 0.328592 Velocity: -0.0125827 0.328351 0.000341856 Cell: 30 19 5\n\tMax Velocity: Length: 0.327525 Velocity: -0.0124874 0.327287 0.000333086 Cell: 30 19 5\n\n\t\tu_mean :\t <0.00352143,0.188009,1.02802e-17>\t u_meanMag\t0.0353598 ,\trho_mean  0.119704\n\tt(phyical) : 0.165\t Ek : 0.0078123\tMax Velocity: Length: 0.327525 Velocity: -0.0124874 0.327287 0.000333086 Cell: 30 19 5\n\tMax Velocity: Length: 0.327525 Velocity: -0.0124874 0.327287 0.000333086 Cell: 30 19 5\n\tMax Velocity: Length: 0.326513 Velocity: -0.0123938 0.326277 0.000324502 Cell: 30 19 5\nWriting 25 timesteps \n\n", "stderr": "\u001b[0;33m WARNING: Domain.h::SetBC: Lattice density for prs_nils boundary condition at north side has extreme value: 0. Adjust resolution!\n\u001b[0m\n\u001b[0;33m WARNING: Domain.cpp::InitializeBoundaries:\n Even GE is activated through CMake config, there is no blocks named 'stl' in parameter file. You are not used the STL GeometricEngine !\n\u001b[0m\nLBGK-Laminar lbm model will be used!!!\nGE is activated from cmake, but there is no stl Block in the given parameter file\n\n0%   10   20   30   40   50   60   70   80   90   100%\n|----|----|----|----|----|----|----|----|----|----|\n\n\t\n----->    CalcPatch::InitialConditions (sim_,t)\t  :timestep  = 0  file:/home/<USER>/walberla/src/InitialConditions/InitialConditions.h\n Starting the InitialConditions ...\n\t--> cell (1 1 1) number of iteration count\t0\trho\t1\n      Smagorinsky model,  sim.csmag =   0.03\n      use finite difference to calculate rate stres-tensor   \n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n*\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n\tStarting ----->    CalcPatch::OptimizedSweep(sim_,t)\n", "metrics": {}, "validation": {}}}}