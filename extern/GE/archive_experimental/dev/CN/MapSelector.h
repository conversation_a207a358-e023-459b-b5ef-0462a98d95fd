#ifndef MAPSELECTOR_HH
#define MAPSELECTOR_HH

#include "IfElse.h"
#include <iostream>
#include <map>



/*! @brief the map selsctor class allow you to choose betweeen diffrents
 * 	   configuration as follow:
 
 *  <table border>
 *  <tr>
 *     <td><b> Selector </b></td>
 *     <td><b> Use map</b></td>
 *  </tr>
 *  <tr>
 *     <td> 1 </td>
 *     <td> map<Key, Value> </td>
 *  </tr>
 *  <tr>
 *     <td> 2 </td>
 *     <td> map<Key, Value*> </td>
 *  </tr>
 *  <tr>
 *     <td> 3 </td>
 *     <td> map<Key*, Value> </td>
 *  </tr>
 *  <tr>
 *     <td> else </td>
 *     <td> map<Key*, Value*> </td>
 *  </tr>
 *  </table>
 */ 
  // abstract if you don't read doxygen doc  
  //! -------------------------------------\n
  //! selector: |	use map < , >	   \n
  //! -------------------------------------\n
  //!  1	      |	 map<Key , Value  >\n
  //!  2	      |  map<Key , Value *>\n
  //!  3	      |  map<Key*, Value  >\n
  //!  else           |  map<Key*, Value *>\n
  //! -------------------------------------\n
 
namespace ge
{
      template <typename Key  ,	typename Value,	int selector >
      struct MapSelector
      {
	    typedef typename IfElse<
			      selector == 1,                   //!<- if selector == 1:
			      std::map<Key, Value>,            //!<- use map<Key, Value>

			      typename IfElse<
				  selector == 2,               //!<- if selector == 2:
				  std::map<Key, Value *>,      //!<- use map<Key  , Value *> 

				  typename IfElse<
				      selector == 3,           //!<- if selector == 3:
				      std::map<Key *, Value>,  //!<- use map<Key *, Value>

				      //!<- otherwise:
				      std::map<Key *, Value *> //!<- use map<Key *, Value *>
				  >::type
			      >::type
			  >::type type;//!<- defining MapType as a map having plain types or pointers for either its key or value types, illustrates this approach

	typedef typename IfElse<
			      selector == 1,                   //!<- if selector == 1:
			      std::pair<Key, Value>,            //!<- use pair<Key, Value>

			      typename IfElse<
				  selector == 2,               //!<- if selector == 2:
				  std::pair<Key, Value *>,      //!<- use pair<Key  , Value *> 

				  typename IfElse<
				      selector == 3,           //!<- if selector == 3:
				      std::pair<Key *, Value>,  //!<- use pair<Key *, Value>

				      //!<- otherwise:
				      std::pair<Key *, Value *> //!<- use pair<Key *, Value *>
				  >::type
			      >::type
			  >::type Pair;//!<- defining pair Type as a map having plain types or pointers for either its key or value types.
	
	typedef typename type::iterator iterator ;
	
      };
}




#endif

