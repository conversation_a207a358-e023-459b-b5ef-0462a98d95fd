#ifndef EDGE_HH
#define EDGE_HH

#include <stdio.h>
#include <stdlib.h>
#include <time.h>
#include <limits>   // std::numeric_limits<>::min ()
#include <limits.h> // INT_MAX
#include <cstdlib>
#include <iostream>

// Edge Class 
struct Edge
{
    int src, dst ; // nodes
    
    Edge () {}
    Edge (const int& s,const int& d) : src (s) , dst (d) {}
  
    inline bool operator == (const Edge& e ) const 
    {
      return src == e.src && dst == e.dst ; 
    }

    friend std::ostream& operator << (std::ostream& out, const Edge& e) {
	    out <<  "  ->[" << e.src << " , " << e.dst << " ]\n"; 
    }

};


// random Edge generator fct 
struct randomEdge
{
  inline Edge operator()()const
  {
      Edge e (rand ()  %  RAND_MAX , rand()  % RAND_MAX ) ;
      while (e.src == e.dst ) 
	      e.dst = random() ;
      return e ; 
  }
};

//! edge comparaison functor 
struct Edge_cmp
{
  inline Edge min_value()const {return Edge (std::numeric_limits<int>::min (), 0 )  ;}
  inline Edge max_value()const {return Edge (std::numeric_limits<int>::max (), 0 )  ;}
  
  inline bool operator () (const Edge& a, const Edge& b)const 
  {
    return   a.src <  b.src || 
	    ( a.src == b.src &&
	      a.dst == b.dst     ) ; 
  }
};



#endif
