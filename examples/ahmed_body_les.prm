// Ahmed Body LES Simulation
// This parameter file demonstrates the use of curved boundaries with turbulence modeling
// for simulating flow around the Ahmed body at Re = 768,000

// Simulation parameters
simdata {
    // Domain size (in lattice units)
    domainX     512;      // Streamwise
    domainY     256;      // Vertical  
    domainZ     256;      // Spanwise
    
    // Physical parameters
    dx          0.002;    // Grid spacing [m]
    dt          'dx' * 'dx' * 'nu' / 0.02;  // Time step from stability
    
    // Flow parameters
    Uref        40.0;     // Reference velocity [m/s]
    Lref        0.288;    // Ahmed body length [m]
    Re          768000;   // Reynolds number
    nu          'Uref' * 'Lref' / 'Re';  // Kinematic viscosity
    rho         1.225;    // Air density [kg/m³]
    
    // Simulation control
    timesteps   100000;
    
    // LBM parameters
    omega       1.0 / (3.0 * 'nu' / ('dx' * 'dx' / 'dt') + 0.5);
    
    dump;  // Output all computed parameters
}

// Turbulence modeling
turbulence {
    model               dynamic_smagorinsky;  // Use dynamic Smagorinsky
    filterWidth         2.0;                  // Test filter width = 2*dx
    wallModel           wallfunction;         // Use wall functions
    strainRateMethod    nonequilibrium;       // Non-equilibrium method
    dynamicSmagorinskyAveraging lagrangian;   // Lagrangian averaging
    maxDynamicCs        0.3;                  // Limit Cs for stability
}

// Curved boundary configuration
curvedboundary {
    // Ahmed body
    body {
        name        ahmed;
        meshfile    meshes/ahmed_body_25deg.stl;  // 25-degree slant angle
        position    128.0 64.0 128.0;             // Center of body
        velocity    0.0 0.0 0.0;                  // Fixed body
        rotation    0.0 0.0 0.0;                  // No rotation
        refArea     0.01125;                      // Frontal area [m²]
        refLength   0.288;                        // Body length [m]
        outputForces true;                        // Enable force output
    }
    
    // Ground plane (optional - can use standard BC instead)
    body {
        name        ground;
        meshfile    meshes/ground_plane.stl;
        position    256.0 32.0 128.0;
        velocity    0.0 0.0 0.0;
        refArea     1.0;
        outputForces false;  // No force output for ground
    }
    
    // Global settings
    forceOutputInterval 100;              // Output forces every 100 steps
    forceOutputFile     ahmed_forces.dat; // Force output file
    useOctree          true;              // Use octree for efficiency
    qValueThreshold    0.01;              // Minimum q-value
}

// Boundary conditions
init {
    // Inlet (west)
    west {
        vel_in {
            y_L     32..'domainY'-32;     // Leave space for ground
            z_L     32..'domainZ'-32;     // Leave space for walls
            ux_L    'Uref' * 'dx' / 'dt'; // Inlet velocity
            uy_L    0.0;
            uz_L    0.0;
        }
    }
    
    // Outlet (east)
    east {
        prs_nils {
            y_L     32..'domainY'-32;
            z_L     32..'domainZ'-32;
            prs_L   0.0;  // Zero gauge pressure
        }
    }
    
    // Ground (bottom)
    bottom {
        noslip {
            x_L     0..'domainX'+1;
            z_L     0..'domainZ'+1;
        }
    }
    
    // Top boundary - slip wall
    top {
        freeslip {
            x_L     0..'domainX'+1;
            z_L     0..'domainZ'+1;
        }
    }
    
    // Side walls - slip
    north {
        freeslip {
            x_L     0..'domainX'+1;
            y_L     0..'domainY'+1;
        }
    }
    
    south {
        freeslip {
            x_L     0..'domainX'+1;
            y_L     0..'domainY'+1;
        }
    }
}

// Output configuration
paraview {
    filename    ahmed_body;
    xstart      100;
    xend        412;
    ystart      0;
    yend        128;
    zstart      64;
    zend        192;
    writedensity   1;
    writevelocity  1;
    writeflags     1;
    writepressure  1;
    writeeddyvisc  1;    // Output eddy viscosity
    interval       1000;  // Output every 1000 steps
    physical       1;     // Use physical units
}

// Additional output - vertical plane through body center
paraview {
    filename    ahmed_centerplane;
    xstart      0;
    xend        512;
    ystart      0;
    yend        256;
    zstart      128;
    zend        128;      // Single plane
    writevelocity  1;
    writepressure  1;
    writeeddyvisc  1;
    interval       500;
    physical       1;
}

// Force monitoring
simoutput {
    DRAG_COEFFICIENT {
        time 10;  // Output drag coefficient every 10 steps
    }
    LIFT_COEFFICIENT {
        time 10;  // Output lift coefficient every 10 steps
    }
    FORCE_CONVERGENCE {
        time 100; // Check force convergence every 100 steps
    }
}

// Logging configuration
logging {
    logfile ahmed_body_les.log;
    append  0;
}