
#include "ErrorLogging.h" 




// =============================================================================
// =============================================================================
// Logging& ErrorLog();
// ErrorLog()::Configure("errro_log.txt");

// ErrorLogging WarningLog;
// ErrorLogging InfoLog;

#define LOG_ERROR(msg) ErrorLogging::Instance().Log(msg)


// =============================================================================
//
// =============================================================================
int main ()
{
	std::cout << "First configure\n" ;
	ErrorLogging tmp  ;	
	ErrorLogging::Configure("errro_log.txt");
	std::cout << "log error\n" ;
	ErrorLogging::Log ("test 2....") ;
	ErrorLogging::Log ("test 2....") ;
	ErrorLogging::Log ("test 2....") ;
	
	std::cout << "Free Memo\n" ;
	return (0) ;
}
