# Compiler configuration
CXX      = g++  
CXXFLAGS =   -g  	#-DDEBUG its in main.cpp   

#CXXFLAGS = -O2  -DNDEBUG 



# Compiler environment
#if cluster
# LIBS    = -lm -L../../lib -lpe -L/central/boost/1.43/lib -lboost_thread -lboost_system -lboost_filesystem -L/opt/scali/lib64 -lmpi
#else no cluster
LIBS    = -lm -L../../lib  \
	-L/media/mihoubi/data/software/boost_1_60_0/stage/lib	\
	  -lboost_thread \
	  -lboost_system  \
	  -lboost_filesystem


INCLUDE = -I/media/mihoubi/data/software/boost_1_60_0/
#INCLUDE = -I./ \
#	-isystem \
#	/central/boost/1.43/include -isystem \
#	/opt/scali/include

# to check in'f boost lib  is installed 
SRC	= newBoost.cpp  # format.cpp
PROG	= dir
#SRC	= BoosfileSystem.cpp
#PROG	= dir_creat

# to determine file_size example -> ./size Makefile 
# SRC	= file_size.cpp
# PROG	= size


# ================================================
#test ls boost like example.
#SRC	= simple_ls.cpp
#PROG	= LS



# ================================================
#test DirectoryLoader class implementation.
#SRC	= test_DirectoryLoader.cpp DirectoryLoader.h
#PROG	= Loader


#test Frames class implementation.
#SRC	= test_Frames.cpp Frames.h DirectoryLoader.h
#PROG	= fLoader



# Build rules
$(PROG): $(SRC)
	$(CXX) $(CXXFLAGS) $(SRC) -o $@ $(INCLUDE) $(LIBS)


# Clean up rules
clean:
	@echo "Cleaning up..."
	rm -f $(PROG) *.*~ *~ deb 


# ----- Setting the independent commands -----
.PHONY: clean
