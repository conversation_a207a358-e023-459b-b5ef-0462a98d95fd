

-- RELEASE -- SVN Revision 134 -- LOG BEGIN , Tuesday, 17.June 2025, 12:54---------------------------------


 INFO:    wa<PERSON><PERSON><PERSON><PERSON> called with parameter file: /home/<USER>/walberla/examples/turbulence_validation/quick_validation/parameter_files/square_cylinder_re100_core.prm
 INFO:    Domain.cpp::InitSimData:  SimData.h:Parameters:
---------------------
 dt      0.00125
 dx      0.001
 tau     0.6875
 omega   1.45455
 nu      5e-05
 rho     1
 Re      9
 gx      0
 gy      0
 gz      0
 xLength 0.06
 yLength 0.03
 zLength 0.015
---------------------
 dt_L    1
 dx_L    1
 nu_L    0.0625
 gx_L    0
 gy_L    0
 gz_L    0
 domainX 60
 domainY 30
 domainZ 15
 numFluidCells 0
---------------------

 INFO:    PatchField.cpp::Create:  <PERSON><PERSON><PERSON>.cpp:Parameters:
---------------------
 xNumPatches 1
 yNumPatches 1
 zNumPatches 1
---------------------

 WARNING: Domain.h::SetBC: Lattice density for prs_nils boundary condition at north side has extreme value: 0. Adjust resolution!
 WARNING: Domain.cpp::InitializeBoundaries:
 Even GE is activated through CMake config, there is no blocks named 'stl' in parameter file. You are not used the STL GeometricEngine !


Simulation Output , Tuesday, 17.June 2025, 12:54---------------------------------

Output data: 
AVG_DENS

------------------------------------------------------------------------
SIMULATION OUTPUT
------------------------------------------------------------------------
Average Density: 0.989198
------------------------------------------------------------------------
------------------------------------------------------------------------
SIMULATION OUTPUT
------------------------------------------------------------------------
Average Density: 0.377394
------------------------------------------------------------------------
