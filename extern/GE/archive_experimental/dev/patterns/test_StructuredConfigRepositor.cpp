#include <iostream>
#include "StructuredConfigRepositor.h"
#include "ComputeConfig.h"

// use advanced config for Vector
typedef ComputeConfig<5,Precise>::ForVector sampleConfig ;
typedef sampleConfig::vector my_vector ;


// use standar config for matrix 
typedef SampleConfig::matrix my_matrix ;


int main ()
{
	my_vector v  ;
	my_matrix m  ;	

	std::cout << " v(2) -->  "  << v [2 ] << std::endl ;
	
	v [2] = 4564646 ;

	std::cout << " v(2) -->  "  << v [2 ] << std::endl ;

	return (0) ;
}



