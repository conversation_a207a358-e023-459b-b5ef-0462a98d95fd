%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\documentclass[12pt,a4paper]{article}
\usepackage[german]{babel}
\usepackage{array}
\usepackage{exscale}
\usepackage{amssymb}
%
%
\addtolength{\topmargin}{-3cm}
\addtolength{\oddsidemargin}{-2.3cm}
\addtolength{\textheight}{5\baselineskip}
\addtolength{\textheight}{3cm}
\addtolength{\textwidth}{3.6cm}
\setlength{\parindent}{0pt}
\setlength{\parskip}{5pt plus 2pt minus 1pt}
%
%
\newcommand{\reel}{\mathbb{R}}
\newcommand{\nat}{\mathbb{N}}
\newcommand{\comp}{\mathbb{C}}
\newcommand{\Int}{\int\limits}
\newcommand{\foot}[1]{_{\mbox{\tiny #1}}}
\newcommand{\head}[1]{^{\mbox{\tiny #1}}}
\newcommand{\D}{\displaystyle}
\newcommand{\ohne}{\backslash}
\newcommand{\grad}{\mbox{grad}}
\newcommand{\Div}{\mbox{div}}
\newcommand{\p}{\partial}
%
\pagestyle{plain}
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\begin{document}
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\section*{Erweiterung der Parallelizierung}

\begin{itemize}
\item Kommunikation pro Prozess\\
      Neu:
\begin{itemize}
\item Puffern von Daten  (Daten bleiben gleich)
\item Senden pro Prozess (MPI\_Kommunikation neu)
\end{itemize}
\item Generalisierung der Paralelisierung\\
      Neu:
\begin{itemize}
   \item Senden der Daten
   \item Aenderungen in der Klassenstruktur
\end{itemize}
\item Unterstruetzung v. Patchtypaenderungen
\item Load Balancing
\end{itemize}

Fazit:
\begin{itemize}
   \item Datenakquirierung, Datenextraktion und Datensenden muessen neu
   geschrieben werden.
   \item entspricht ~80\% der Paralelisierung (wobei Puffern v. Daten der kleinere Teil ist)
   \item Somit guter Zeitpunkt ueber bisherige Paralelisierungsstruktur
   nachzudenken.
\end{itemize}

\section*{Wichtigste Aenderungen}
\begin{itemize}
\item Es gibt keinen CommPatch mehr --- da von Prozess zu Prozess gesendet
wird brauch man den nicht mehr
\item Es gibt keine Nachbarschaftsinformationen auf Patchebene

\item Alle Felder sind nun von FieldInterface abgeleitet --- somit ist das
senden beliebiger Felder moeglich
\item Saemtliche Kommunikation findet in der Klasse Communication statt --- uebersichtlicher
\end{itemize}


\section*{Modifications to the Paralleization}

\begin{tabular}{|c|}
\hline
\begin{minipage}{\textwidth}
\begin{verbatim}
\\ welche Rankbuffers muss man anlegen?
class Communication{
   public:
      void LocalFieldCommunication(PatchField gird,Sweeps sweep);
      void MovingObjectCommunication(...);
      void SpecialLocalCommunication_Functions(...);
      void MPICommunication(PatchGrid grid, Obstacles ob);
      void AddStateChange(PatchID &uid);

   private:

   void CommunicatePatchChanges();

   vector<PatchUID> stateChanges;
   vector<Uint > neighborRank;
   \\ einfach zu benutzen ... allerdings langsam
   map<Uint, vector<byte> > rankSendBuffers;
   vector<byte> rankRecvBuffer;

};

\end{verbatim}
\end{minipage}\\
\hline
\end{tabular}

\begin{tabular}{|c|}
\hline
\begin{minipage}{\textwidth}
\begin{verbatim}
class Patch{

   public:
      // Nachrichten pro Richtung sind zusammengefasst pro Richtung,
      // desshalb vector<FieldID>
      void SetBorder(char* buffer, vector<FieldID> &uids,
                     PatchUID &send,Uint dir);
      void SetBorder(Patch &data, vector<FieldID> &uids,
                     PatchUID &send,Uint dir);
      void GetBorder(vector<bytes> &buffer,vector<FieldID> &uids,
                     PatchUID recv,Uint dir);
      PatchUID& GetPatchUID();

      private:
         // Ist so gross wie NUM_FIELDS aus Definitions.h
         vector<FieldInterfaceID> allFields;
        };

\end{verbatim}
\end{minipage}\\
\hline
\end{tabular}

\begin{tabular}{|c|}
\hline
\begin{minipage}{\textwidth}
\begin{verbatim}

class FieldInterface{

public:
// PatchUID braucht man nur bei Grid Refinement um festzustellen ob man Daten
// verfeinern muss
virtual void SetBorder(Patch patch, PatchUID &send,Uint dir)=0;
virtual void SetBorder(vector<bytes> &rankBuffer, PatchUID &send,Uint dir)=0;
\\ Daten Pointer im Field wird konvertiert zu char*
\\ Mit Reserve wird der Speicher gross genug gemacht und 
\\ Mit Insert eingefuegt.
// PatchUID brauch man nur bei Grid Refinement um festzustellen ob man Daten
// vergroebern muss
virtual void GetBorder(vector<char> &sendData, PatchUID &recv, Uint dir)=0;
//ID wird dem Konstruktor uebergeben ... siehe Definitions
FieldID GetFieldID();

private: 

FieldID uid;

};

\end{verbatim}
\end{minipage}\\
\hline
\end{tabular}

\begin{tabular}{|c|}
\hline
\begin{minipage}{\textwidth}
\begin{verbatim}
\\ Hat auch Ghost Layer fuer Randbehandlung und Periodic
\\ Wann und wie oft muss dass denn upgedatet werden
class PatchField{

   public:

      vector<PatchID> GetLocalPatches();
      // access functions to the patchgrid
      ...
      
   private:
      
      ScalarField<PatchStruct> worldStatus;
      vector<PatchID> localAllocatedPatches;
      ScalarField<PatchID> patchGrid;
};

\end{verbatim}
\end{minipage}\\
\hline
\end{tabular}


\begin{tabular}{|c|}
\hline
\begin{minipage}{\textwidth}
\begin{verbatim}
class PatchStruct{

   public:

      Uint GetRank();
      bool IsAllocated();
      Application& GetApp();
      PatchID& GetPatchID();
      
   private:

      PatchUid Uid;
      Application * app;
      bool IsAllocated;
      Uint rank;
      Uint level;
      bool isNeeded;
      AABB patchAABB;

};

\end{verbatim}
\end{minipage}\\
\hline
\end{tabular}


\begin{tabular}{|c|}
\hline
\begin{minipage}{\textwidth}
\begin{verbatim}

class PatchUID{

   public:
      Uint index;
      Uint level;
      Uint xindex;
      Uint yindex;
      Uint zindex;
};

\end{verbatim}
\end{minipage}\\
\hline
\end{tabular}

\begin{tabular}{|c|}
\hline
\begin{minipage}{\textwidth}
\begin{verbatim}

class PDFField : public FieldInterface{

   public:
// PatchUID braucht man nur bei Grid Refinement um festzustellen ob man Daten
// verfeinern muss
virtual void SetBorder(PatchID patch, PatchUID &send,Uint dir)=0;
virtual void SetBorder(vector<bytes>::iterator rankIt, PatchUID &send,Uint dir);
\\ Daten Pointer im Field wird konvertiert zu char*
\\ Mit Reserve wird der Speicher gross genug gemacht und 
\\ Mit Insert eingefuegt.
// PatchUID brauch man nur bei Grid Refinement um festzustellen ob man Daten
// vergroebern muss
virtual void GetBorder(vector<char> &sendData, PatchUID &recv, Uint dir);
};

\end{verbatim}
\end{minipage}\\
\hline
\end{tabular}

\begin{tabular}{|c|}
\hline
\begin{minipage}{\textwidth}
\begin{verbatim}

Application.h
// Gibt die Fields zurueck die im Sweep "sweep" zwischen App send und recv
// gesendet werden muessen
vector<FieldID> &GetFields(Sweeps sweep,App send, App recv);

\end{verbatim}
\end{minipage}\\
\hline
\end{tabular}

\begin{tabular}{|c|}
\hline
\begin{minipage}{\textwidth}
\begin{verbatim}

Definition.h
enum Sweeps {PDF, FILLLELVEL, NORMAL, ... }
enum FieldID{PDF_SRC=0,PDF_DEST,...,MAX_FIELDS}
const Uint NUM_FIELDS=MAX_FIELDS;

\end{verbatim}
\end{minipage}\\
\hline
\end{tabular}

\section*{Pseudo Functions for Local Communication to ClacPatches and Buffers}

\begin{tabular}{|c|}
\hline
\begin{minipage}{\textwidth}
\begin{verbatim}
\\ Nachbarn bekommt man mittels PatchUID und PatchGrid
\\ PatchUID aendert sich nie, da Patches immer die gleiche 
\\ physicalische Aufloesung haben
\\ Speicherspruenge ?
void Communication::LocalFieldCommunication(PatchField gird,Sweeps sweep){

\\ go over all patches p that are on this process and allocated
   \\ go over all 19 neighbors n
   if(n.rank==p.rank)
      n.SetBorder(p,GetFields(sweep,n.GetState(),p.GetState()),
                  p.GetPatchUID(),dir);
   else
      p.GetBorder(rankBuffers[n.rank] \\ sole slow access,
                  GetFields(sweep,n.GetState(),p.GetState(),
                  n.GetPatchUID()),dir);
}

\end{verbatim}
\end{minipage}\\
\hline
\end{tabular}

\begin{tabular}{|c|}
\hline
\begin{minipage}{\textwidth}
\begin{verbatim}
\\ Ist es moeglich das auf beiden Seiten zu machen?
void Patch::SetBorder(Patch &p,vector<FieldID> &fields,
                      PatchUID sendingID,Uint dir){


   \\ go over all fields in fields
   \\ virtueller Zugriff -- schlecht?
   fields[fields[i]].SetBorder(p,sendingID,dir);
}

\end{verbatim}
\end{minipage}\\
\hline
\end{tabular}

\begin{tabular}{|c|}
\hline
\begin{minipage}{\textwidth}
\begin{verbatim}

void Patch::GetBorder(vector<bytes> &rankBuffer,
                     vector<FieldID> &fields,
                     PatchUID &recvID, Uint dir){

   \\ push the header
   \\ go over all fields in registeredFields
   fields[registeredFields[i]].GetBorder(rankBuffer,recvID,dir);
}

\end{verbatim}
\end{minipage}\\
\hline
\end{tabular}

\begin{tabular}{|c|}
\hline
\begin{minipage}{\textwidth}
\begin{verbatim}

void PDFField::GetBorder( vector<bytes> &rankBuffer,
                          PatchUID &recvID, Uint dir){

   \\convert Real* form DataLayout into byte*
   \\calculate memory requirement
   \\resize rankbuffer
   \\use [] to write the byte pointer
}

\end{verbatim}
\end{minipage}\\
\hline
\end{tabular}

\section*{Pseudo Functions for MPI Communication}

\begin{tabular}{|c|c|c|c|c|c|}
\hline
Size&Type&...&&&\\
x&FIELD&PATCH\_ID&DIR&DATEN&...\\
\hline
\end{tabular}

\begin{tabular}{|c|}
\hline
\begin{minipage}{\textwidth}
\begin{verbatim}
\\ Performance?
\\ Arbeit ueberlappen in dem bereits gesendete schon abgearbeitet werden?
void Communication::MPICommunication(PatchGrid grid, Obstacles ob){

   CommunicatePatchChanges();
   \\ send all buffers, use iterator => no slow access
   MPI_Isend(&rankBuffer[rank].front(),sizeof(byte)*rankBuffer[rank].size(),
             MPI_CHARACTER,tag,target,MPI_COMM_WORLD,&request);
   \\ recv all buffers
   \\ in einer schleife oa.
   while (..) {
      MPI_IProbe(Source,Tag,MPI_COMM_WORLD,&falg,&status);
         if(flag){
            MPI_Get_count(&status,MPI_CHARACTER,&count);
            rankRecvBuffer.resize(count);
            MPI_recv(&rankRecvBuffer.front(),count,
                     MPI_CHARACTER,0,0,MPI_COMM_WORLD,request);
      \\ process rank buffer with an iterator
         \\parse message header -- Size, Type
            switch(type)
In eigene Funktion->  Field: 
                            \\ParseFieldHeader -- PatchID, dir
                            \\with dir neighbor n can be determined
                            grid(PatchID).SetBorder(rankIt,GetFields(Sweep,n.GetState(),
                                                                           p.GetState()),
                                                                           n.GetPatchID(),
                                                                           dir));
                      MO: 
                           \\Parse Moving Object Header -- ObType
                           \\create object according to Obtype
                     switch(ObType)
                        Sphere:
                           createSphere(rankIt,...);
                           \\insert Object in ObstacleMap
                           \\Push new entry in obstToPatch
                           obstToPatch.push_back(ObstToPatchStruct(grid,sphere));
         \\ go over patch messages 
            \\parse header --Size,Type, (PatchUID, Dir)
            \\ with dir the neighbor n can be determined
            grid(PATCH_ID).SetBorder(rankBuffer,GetFields(sweep,n.GetState(),
                                     p.GetState()),n.GetPatchUID(),dir);
         
         }
   }
   \\ clear all rank buffers
}

\end{verbatim}
\end{minipage}\\
\hline
\end{tabular}

\begin{tabular}{|c|}
\hline
\begin{minipage}{\textwidth}
\begin{verbatim}


void Patch::SetBorder(vector<bytes> &rankBuffer, vector<FieldID> fields,
                      PatchUID sendingID,Uint dir){
   // go over all fields in fields 
   Iterator it = rankbuffer.begin();
   filds[fieldID].SetBorder(it,sendingID,dir);
}

\end{verbatim}
\end{minipage}\\
\hline
\end{tabular}

\begin{tabular}{|c|}
\hline
\begin{minipage}{\textwidth}
\begin{verbatim}


void Communication::CommunicatePatchCahnges(vector<PatchID> &patchChanges,
                                            vector<Uint> &neighborRank{

\\ geh ueber alle neighborRanks
   \\insert header in rankSendBuffer
   \\geh ueber alle patchChanges
   \\fuege Daten ein
\\ Loesche alle PatchChanges
}

\end{verbatim}
\end{minipage}\\
\hline
\end{tabular}

\begin{tabular}{|c|c|c|c|c|c|c|}
\hline
Size&Type&NUM&PATCHID&DATA&PATCHID&DATA\\
x&PC&2&[...]&[...]&[...]&[...]\\
\hline
\end{tabular}


\begin{tabular}{|c|}
\hline
\begin{minipage}{\textwidth}
\begin{verbatim}


void PDFField::SetBorder(vector<bytes>::iterator rankIt,
                         PatchUID sendingID,Uint dir){

   \\convert Real* form DataLayout into byte*
   \\go over field border 
   data(i,j,k)=it++;
}

\end{verbatim}
\end{minipage}\\
\hline
\end{tabular}

\section*{Moving Object Comunication}

\begin{tabular}{|c|}
\hline
\begin{minipage}{\textwidth}
\begin{verbatim}

void Communication::MovingObejctCommunication(vector <ObstToPatchStruct>&
                                              obstToPatch,PatchField &gird){
   \\geh ueber alle Eintrage in obst ToPatch

      \\Teste Nachbarn der restlichen Patches, ob Object ueberlappt
         \\Wenn ja
         \\Ist patch global
         \\Falls Patch noch nicht in den "alten" Patches
            \\benutze ranksendBuffers um die Daten zu senden
            \\ wenn patch noch nicht MO, setz ihn zu MO
            \\modifizier obstToPatch
   \\ Teste ob ein Objekt gesendet werden muss
   \\ Teste ob Aempfengerpatch ein MovingObstacle-patch ist
   \\ Hole Daten aus der Pe 
   \\ Daten werden in rankBuffer gespeichert
}

\end{verbatim}
\end{minipage}\\
\hline
\end{tabular}

\begin{tabular}{|c|}
\hline
\begin{minipage}{\textwidth}
\begin{verbatim}

class Domain{


private:

void ApplyMovingObstacleStateChanges();
vector<ObstToPatchStruct> obstToPatch;

};

\end{verbatim}
\end{minipage}\\
\hline
\end{tabular}

\begin{tabular}{|c|}
\hline
\begin{minipage}{\textwidth}
\begin{verbatim}

void Domain::ApplyMovingObstacleStateChanges(){

   \\geh ueber alle Eintrage obstToPatch
      \\Teste ob sich Object immer noch in den "alten" Patches befindet,
        =>modifikation obstToPatch
          \\nur im parallelen Fall: Sollte sich darunter kein lokaler Patch
          \\sein=> raus damit
      \\Teste lokale Nachbarn der restlichen Patches ob Objekt ueberlappt
        \\seriel: \\Teste ob Patch MO + modfiziere obstToPatch falls Patch
                  \\noch nicht in den alten Patches
        \\parallel: \\Falls Patch noch nicht in den "alten" Patches
                    \\Test ob Patch MO + modifizier obstToPatch
                    \\Push gegebenfalls StateChange in Communication
}

\end{verbatim}
\end{minipage}\\
\hline
\end{tabular}


\begin{tabular}{|c|}
\hline
\begin{minipage}{\textwidth}
\begin{verbatim}

class ObjectToPatchStruct{

public:

ObjectToPatchStruct(PatchField &grid,ObjectID ob, vector<PatchID> &stateChenges);
ObjectToPatchStruct(PatchField &grid, ObjectID ob);
\\Access Functions
...


private:

vector<PatchID> p_ID;
ObjectID ob;
};
\end{verbatim}
\end{minipage}\\
\hline
\end{tabular}

\begin{tabular}{|c|}
\hline
\begin{minipage}{\textwidth}
\begin{verbatim}


ObjectToPatchStruct(PatchField &grid,ObjectID ob, vector<PatchID> &stateChenges){
   
   \\ go over all local allocated Patches and neighbor patches
      \\ is object in patch
         \\yes ... insert PatchID in p_ID
         \\test if Patch is MO => change Patch to MO
         \\Push of ID in stateChanges if it is a localPatch
}

\end{verbatim}
\end{minipage}\\
\hline
\end{tabular}



\section*{Offene Fragen}
\begin{itemize}
\item Grid Refinement
\begin{itemize}
   \item Sollte moeglich sein, wenn alle Patches physikalisch gleich gross sind.
   \item Levelunteschiede immer nur 1 gross sein koennen.
   \item Denn dann kann man das Level kodieren und der sendende/emfangende Patch kann 
         die Daten vergroebern/verfeineren.
\end{itemize}
\item Anlegen von rankBuffers, geht zwar ist aber vermutlich langsam ...
\item Patch Spezialisierungen, dynamische Anederungen, Load Balancing
\item Debugging Konzept
\item Performance
\item Wie sehen Spezialnachrichten ... Objekte, Aenderungsbefehle aus
\item Wie viel Speicher braucht man eigentlich bei einem bestimmten Run
\end{itemize}

\section*{Reorganization}

\begin{itemize}
\item Physikalische Aenderungen: 
\begin{itemize}
\item Werden durch Applikationen getriggert 
\item Kann jeden Zeitschritt passieren
\item Wird mit normaler Kommunikation verbunden
\item Erfordert update des Applikationstypes 
      auf jedem Prozess(bis jetzt,
      langsam?)
\end{itemize}
\item Refinement Aenderungen:
\begin{itemize}
      \item Werden durch Applikationen getriggert
      \item Kann in jedem Zeitschirtt passieren
      \item  Wird mit normaler Kommunikation verbunden
      \item Erfordert update des Applikationstypes 
      auf jedem Prozess(bis jetzt,
      langsam?)
   \end{itemize}
\item Load Balancing
\begin{itemize}
   \item Wird nur jeden x-ten Zeitschritt ausgefuehrt
   \item Wird von einem Prozess gesteuert (?)
   \item Extra Nachricht (?)
\end{itemize}
\end{itemize}

\begin{itemize}
\item Nur PatchField muss aktualisiert werden
\begin{itemize}
      \item Ranknummern 
      \item Applicationstypen
      \item Lokal allokierte Patches
   \end{itemize}
\end{itemize}


\begin{tabular}{|c|}
\hline
\begin{minipage}{\textwidth}
\begin{verbatim}

int main(int argc, char** argv){

int numprocs,count;
MPI_Datatype datatype;

MPI_Init(&argc,&argv);
MPI_Request request[2];
MPI_Status status;



std::vector<double> a;
std::vector<double> b;
// initialization
a.resize(5);
a[0]=0;a[1]=1;a[2]=2;a[3]=3;a[4]=4;

// convert into char pointer
char * c=(char*)(&a.front());

// send the char*
MPI_Isend(c,sizeof(double)*a.size(),MPI_CHARACTER,0,0,MPI_COMM_WORLD,&request[0]);

// test the size of the message
MPI_Probe(0,0,MPI_COMM_WORLD,&status);
MPI_Get_count(&status,MPI_CHARACTER,&count);

// reserve enough memory
b.resize(count/sizeof(double));

// recevie the message
MPI_Irecv(&b.front(),count,MPI_CHARACTER,0,0,MPI_COMM_WORLD,&request[1]);

// output
std::cerr<<"Result\n";
int size=b.size();
for(int i=0;i<size;++i)
std::cerr<<b[i]<<" ";
std::cerr<<"\n";

// fin
MPI_Finalize();

\end{verbatim}
\end{minipage}\\
\hline
\end{tabular}


\end{document}
