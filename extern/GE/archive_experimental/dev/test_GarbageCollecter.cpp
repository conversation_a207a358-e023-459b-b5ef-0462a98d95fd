
#include <iostream>
#include "GarbageCollecter.h"

// =============================================================================
//! @note To watch the action of the garbage collector, define DISPLAY.
// =============================================================================
//static bool collect ()     Destructor for GCPTr. Returns true if at least one object was freed
//static int  gclistSize ()  Return the size of gclist for this type of GCPtr.
//static void showlist ()    A utility function that displays gclist.
//static void shutdown ()    Clear gclist when program exits. 


int main ()
{
//  first test
//  ==================================
//       GCPtr<int> p ;
//       GCPtr<int> q ; 
// 	  p= new int (10) ;
// 	  q = p ;
// 	  GCPtr<int>::showlist () ;
//       GCPtr<int>::collect  () ;
//  ==================================



// 2 test 
//  ==================================
//       try {
//        GCPtr<int> p_ ;
//             p_= new int (2) ;
//             p_= new int (3) ;
//             p_= new int (4) ;	
// // 	  GCPtr<int>::collect  () ;
// 	  
//       }  catch (bad_alloc exc){
// 	  cout << "Allocation failur!\n" ;
// 	  return 1 ;
//       }
      
     
     
// 3 test : showing GCPtr indexing
// 	  try 
// 	  {
// 	  GCPtr<int,10> ArrayPtrs = new int [10] ;
// 
// 	  // initialize 
// 	  for (int i=0 ; i < 10 ; ++i) ArrayPtrs[i] = i ;
// 	    
// 	    // check 
// 	    for (int i=0 ; i < 10 ; ++i) std::cout << ArrayPtrs[i]<< " " ;
// 	    std::cout << "\n\n" ;
// 
// 	    GCPtr<int,10>::collect ();
// 	  
// 	  }  catch (bad_alloc exc){
// 	      std::cout << "Allocation failur!\n" ;
// 	      return 1 ;
// 	  }
 
 
 // using iterators
   try {
     
    GCPtr<int, 10> ap = new int[10];  // Create a GCPtr to an allocated array of 10 ints.
   
    GCPtr<int>::GCiterator itr;       // Declare an int iterator.
    
    itr = ap.begin();	      // Assign itr a pointer to the start of the array.
    
    // Give the array some values using array indexing.
    for(unsigned i=0; i < itr.size(); i++)  itr[i] = i;
    
    
    // Now, cycle through array using the iterator.
    for(itr = ap.begin(); itr != ap.end(); itr++)  cout << *itr << " ";
    
    
    std::cout << endl;
  } catch(bad_alloc exc) {
    std::cout << "Allocation failure!\n";
    return 1;
  } catch(OutOfRangeExc exc) {
    std::cout << "Out of range access!\n";
    return 1;
  }
  
  return (0) ;
}
