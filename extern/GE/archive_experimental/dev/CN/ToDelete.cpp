
#include <iostream>
#include <vector>

using namespace std ;

struct ConfigueSample{
    typedef int ElementType ;
};


template<class Configure>
struct svect:public std::vector<typename Configure::ElementType> // inheritance
{
    //!typedef 
    typedef typename Configure::ElementType  value_type ;
    typedef typename svect::size_type        size_type ;
    typedef typename svect::iterator         iterator  ;
    typedef typename svect::const_iterator   const_iterator  ;    
    typedef typename svect::difference_type  difference_type ;
    typedef typename svect::reference        reference  ;
    typedef typename svect::const_reference  const_reference ;    

    //!constructors 
    svect() {}  // default
    svect(const size_type& size,const value_type& value= value_type() )
	:std::vector <value_type> (size,value) {}
	
    svect(iterator first,iterator last)
        :std::vector<value_type>(first,last){}
    
    
    //! svect by default inherited all things from std::vector, but on can implement save container (debug mode)
    //! to check and all these stuff

    #ifdef DEBUG  
	inline reference operator [] ( difference_type index )
	{
	  assert(index >= 0 && index<static_cast<difference_type > (this->size() ) ) ; // end assert
	  return std::vector<T>::operator[](index) ;
	}
          
	 
	 inline const_reference operator [] (difference_type index) const
	 {
	   assert(index >= 0 && index<static_cast<difference_type > (this->size() ) ) ; // end assert
	   return std::vector<T>::operator[](index) ;
	 }
       	 #endif   
};


int main()
{
    svect<int>  v() ;
//     v.push_back (52) ;
    
    return (0) ;
}
