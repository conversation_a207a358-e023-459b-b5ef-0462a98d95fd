cmake_minimum_required(VERSION 2.6)
project(CMAKEDEMO)
FIND_PACKAGE (GLUT)
FIND_PACKAGE (OpenGL)



IF(GLUT_FOUND)

      IF(APPLE)
     	SET(CMAKE_CXX_LINK_FLAGS "-framework OpenGL -framework GLUT")
      ENDIF(APPLE)

      MESSAGE("GLUT Correctly Found") 
      INCLUDE_DIRECTORIES(${GLUT_INCLUDE_DIR})
      INCLUDE_DIRECTORIES(${OPENGL_INCLUDE_DIR})
      ADD_EXECUTABLE(sviewer demo.cpp solver.cpp )
      TARGET_LINK_LIBRARIES(sviewer ${GLUT_LIBRARY} ${OPENGL_LIBRARY})
ELSE (GLUT_FOUND)
      MESSAGE ("GLUT env is missing") 
ENDIF(GLUT_FOUND)

