#ifndef LIST_HH
#define LIST_HH

#include <boost/ptr_container/ptr_vector.hpp>
#include <boost/shared_ptr.hpp>

#include "../misc/Exception.h"

//
// TODO use Configure::Copier
// TODO add find()
// TODO this version work only with  std::vector, deque, map, 
// 				     boost::ptr_vector, ptr_deque and ptr_map
// , just because of the operator "[]" and "at()"
//
//  TODO , please try to extend to include std::set 
//
// TODO  until now I haven´t use the second template param "Compare" !!!


namespace ge
{
  	//======================================================================
	// Member-function definitions for class List 
	//======================================================================
	template< class Configure,
		  class Compare = std::less<typename Configure::ObjectType> >
	class List 
	{
	    public:
		//==============================================================
		// types
		//==============================================================
		// import
		typedef typename Configure::ObjectType 		ObjectType ;
		typedef typename Configure::ObjectArgType	ObjectArgType  ;
		typedef typename Configure::ContainerType	ContainerType ;

		typedef typename Configure::Copier		Copier ;
		typedef typename Configure::Destructor		Destructor ;

		// additional 	
		typedef ObjectType value_type;	 // new typdef
		typedef typename ContainerType::size_type    	size_type ;
		typedef typename ContainerType::iterator	iterator  ;
		typedef typename ContainerType::const_iterator  const_iterator;
		typedef typename ContainerType::difference_type difference_type;
		typedef typename ContainerType::reference       reference     ;
		typedef typename ContainerType::const_reference const_reference;
		
		// export
		typedef  List<Configure,Compare>		FinalListType ;  
		//==============================================================
		//!constructors
		//==============================================================

		explicit List(){
		      m_data_c = new ContainerType () ;
		}
		
		explicit List(const size_type& size,
			      const ObjectType& value = ObjectType() ) 
			      {
				m_data_c = new ContainerType (size,value);
			      }
			
		explicit List(iterator first,iterator last){
				m_data_c = new ContainerType (first,last) ;
		}
		
		~List () { delete m_data_c ;}

	  public:


		// =============================================================	    
		// Modifiers
		// =============================================================
		//TODO , insert  also
		inline void add(const ObjectType& c)//its the same as "void add(value_type*c)"
		{ 
			m_data_c -> push_back(&c );
		}
		
		inline void add( value_type* c ) 
		{ 
			m_data_c -> push_back( c );
		}


		inline void add( iterator where, value_type* c)	
		{ 
		      m_data_c -> insert ( where, c );//iterator insert(iterator, const T&);
		}
		
		inline void remove(iterator where ) { m_data_c->erase( where );}



		// =============================================================
		// iterators
		// =============================================================
		inline iterator  	begin()		{return m_data_c->begin();}
		inline const_iterator	begin() const 	{return m_data_c->begin();}
		inline iterator		end()		{return m_data_c->end() ;}
		inline const_iterator	end()	const	{return m_data_c->end() ;}



		// =============================================================
		// capacity
		// note in case of std::vector, its better if you use reserve()
		// =============================================================	
		inline bool empty() const 	   { return m_data_c->empty();}
		inline size_type    size()const    { return m_data_c->size() ;}
 
      
     

		// =============================================================
		// element access
		// Returns a reference to the n'th element
		// =============================================================
		//!@{
		//!@note  don't throw any BadIndex Exception
		inline reference       operator[](const size_type& index)
		{
			return (*m_data_c)[index];
		}

		inline const_reference operator[](const size_type& index) const
		{
			return (*m_data_c)[index]; 
		}
		
		//!@note  throw any BadIndex Exception if  index > size or negative
		inline reference 	at(const size_type& index )
		{
 			ASSERT_POSITIVE(index) ;
			ASSERT_BOUNDS  (index, size() ) ;		
			return m_data_c -> at(index);
		}

		// Returns a reference to the n'th element
		inline const_reference at(const size_type& index ) const
		{
			ASSERT_POSITIVE(index) ;
			ASSERT_BOUNDS(index, size() ) ;
			return  m_data_c->at(index);		
		}
		//!@}     
		// =============================================================
	  private:
		
		ContainerType	*m_data_c ;
	};
	
	
	// =====================================================================
	// overloaded output operator for class List
	// =====================================================================
	// 	template <class Configure, class Compare>
	// 	inline typename List<Configure, Compare>::reference
	// 	List<Configure, Compare>::operator[](const typename List<Configure, Compare>:: size_type& index )
	// 	{
	// 	  assert(index >= 0 && 
	// 				index < static_cast<difference_type >
	// 				(this->size() ) ) ; // end assert
	// 			return ContainerType::operator[](index) ;
	// 	}

} // end namespace ge


#endif

  
  
//  TODO  fixe me, it more elegant if  you can implement as the following code 
  
  
// 	//======================================================================
// 	// Member-function definitions for class List 
// 	//======================================================================
// 	template< class Configure,
// 		  class Compare = std::less<typename Configure::ObjectType> >
// 	class List : public Configure::ContainerType
// 	{
// 	  public:
// 		//==============================================================
// 		// types
// 		//==============================================================
// 		// import
// 		typedef typename Configure::ObjectType ObjectType ;
// 		typedef typename Configure::ObjectArgType  ObjectArgType  ;
// 		typedef typename Configure::Copier      Copier ;
// 		typedef typename Configure::ContainerType       ContainerType ;
// 		
// 		// additional 	
// 		typedef ObjectType value_type;	 // new typdef
// 		typedef typename ContainerType::size_type    	size_type ;
// 		typedef typename ContainerType::iterator	iterator  ;
// 		typedef typename ContainerType::const_iterator  const_iterator;
// 		typedef typename ContainerType::difference_type difference_type;
// 		typedef typename ContainerType::reference       reference     ;
// 		typedef typename ContainerType::const_reference const_reference;
// 		
// 		// export  		
// 		typedef  List<Configure,Compare>	FinalListType ;  
// 		//==============================================================
// 		//!constructors
// 		//==============================================================
// 		List() {}  // default
// 			
// 		List(const size_type& size,
// 		     const ObjectType& value = ObjectType() ) 
// 		     :ContainerType (size,value) {}
// 			
// 		List(iterator first,iterator last)
// 			  :ContainerType (first,last){}
// 
// 	  public:
// 	    
// 	    
// 		// =============================================================	    
// 		// Modifiers
// 		// =============================================================
// 		inline void add( value_type* c ) { push_back( c );    }  //TODO , insert  also
// 		inline void add( iterator where, value_type* c)
// 		{ 
// 		      insert ( where, c );//iterator insert(iterator, const T&);
// 		}
// 		
// 		inline void remove(iterator where ) { erase( where ); }
// 		
// 		
// 		
// 		
// 		// =============================================================
// 		// iterators
// 		// =============================================================
// 		inline iterator	begin()       { return begin() ;}
// 		inline const_iterator  begin() const { return begin() ;}
// 		inline iterator	end() 	     { return end() ;}
// 		inline const_iterator  end()   const { return end() ;}
// 		
// 		
// 		
// 		// =============================================================
// 		// capacity
// 		// note in case of std::vector, its better if you use reserve()
// 		// =============================================================		
// 		inline bool empty() const 	   { return  empty();}
// 		inline size_type    size() 	   { return size() ;}
// 		
// 		
// 		
// 		
// 		// =============================================================
// 		// element access
// 		// =============================================================
// 		//!@{
// 		//!@note  don't throw any BoundsViolation Exception
// 		reference 	operator[](const size_type& index )
// 		{
// 		  			
// 		  std::cout<< "index = " << index << std::endl ;
// // 			assert(index >= 0 && 
// // 				index < static_cast<difference_type >
// // 				(this->size() ) ) ; // end assert
// 			return ContainerType::operator[](index) ;
// 		}
// 		
// 		const_reference operator[](const size_type& index ) const// Returns a reference to the n'th element
// 		{
// // 			std::cout<< "index = " << index << std::endl ;
// // 			assert(index >= 0 && 
// // 				index < static_cast<difference_type >
// // 				(this->size() ) ) ; // end assert
// 			return ContainerType::operator[](index) ;
// 		}
// 		
// 		
// 		//!@note  throw any BoundsViolation Exception if  index > size or negative
// 		reference 	at(const size_type& index );
// 		const_reference at(const size_type& index ) const;// Returns a reference to the n'th element
// 
// 		//!@}     
// 		
// 	};
// 	
// 	
// 	// =====================================================================
// 	// overloaded output operator for class List
// 	// =====================================================================
// 	// 	template <class Configure, class Compare>
// 	// 	inline typename List<Configure, Compare>::reference
// 	// 	List<Configure, Compare>::operator[](const typename List<Configure, Compare>:: size_type& index )
// 	// 	{
// 	// 	  assert(index >= 0 && 
// 	// 				index < static_cast<difference_type >
// 	// 				(this->size() ) ) ; // end assert
// 	// 			return ContainerType::operator[](index) ;
// 	// 	}


