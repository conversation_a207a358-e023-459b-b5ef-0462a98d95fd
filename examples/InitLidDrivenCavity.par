
// Init block that initializes a Lid-Driven Cavity with the top wall moving with a constant
// velocity of 0.02 (lattices per timestep) into x direction.

init {
  north{
    acc{
        x_L         0..'domainX'+1;
        z_L         0..'domainZ'+1;
		ux_L      0.02;
		uy_L      0.0;
		uz_L      0.0;
    }
  }
  south{
    noslip{
        x_L  0..'domainX'+1;
        z_L  0..'domainZ'+1;
    }
  }
  east{
    noslip{
        y_L  1..'domainY';
        z_L  0..'domainZ'+1;
    }
  }
  west{
    noslip{
        y_L  1..'domainY';
        z_L  0..'domainZ'+1;
    }
  }
  top{
    noslip{
        x_L  1..'domainX';
        y_L  1..'domainY';
    }
  }
  bottom{
    noslip{
        x_L  1..'domainX';
        y_L  1..'domainY';
    }
  }
}