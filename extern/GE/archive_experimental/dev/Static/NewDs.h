#ifndef NEWDS_H
#define NEWDS_H


//==============================================================================
// includes
//==============================================================================
#include <iostream>
#include <vector>
#include <cmath>
#include "../../util/Utility.h"
#include "../../misc/Constants.h"
#include "../../gmath/GEMath.h"//#include "../gmath/Point3D.h"
//==============================================================================


// the main goal of this class is to find neighboring info's

//==============================================================================
// TODO add size, empty and so one
// TODO add iterators
//==============================================================================





namespace ge
{
	template<typename Configure>
	struct Mesh // imagin like polygons_list 
	{
		// =============================================================
		// frwd declaration
		// =============================================================
		struct Vertex;
		struct Edge;
		struct Face ;
	  private:
		struct PolyList ;

	  public:
		// =============================================================
		// types
		// =============================================================
		typedef Configure configure ;
		typedef typename configure::real    	real;//float or double
		typedef typename configure::point_type  point_type ;
		typedef typename configure::vector_type vector_type;
		typedef typename configure::edge_type	edge_type  ;
// 		typedef typename configure::aabb_type 	aabb_type  ;
// 		typedef typename configure::plane_type	plane_type ;
		typedef typename configure::length_type length_type;
		typedef Face triangle_type;
		typedef PolyList faces_conatiner_type;
		typedef typename faces_conatiner_type::polygons_list polygons_list;
// 		typedef std::vector <Face > polygons_list;
		typedef typename polygons_list::iterator	iterator;
		typedef typename polygons_list::const_iterator	const_iterator;
		typedef Mesh<configure> mesh_type;
		
		
		
		explicit Mesh() {
		  m_faces.clear();
		  m_vertices.clear() ;
		}
		
	  public:

		inline PolyList&       GetFacesContainer() {return m_faces;}
		inline const PolyList& GetFacesContainer()const{return m_faces;}
	    
		inline polygons_list&       GetPolyListMesh()	  {return m_faces;}
		inline const polygons_list& GetPolyListMesh()const{return m_faces;}
	    
		std::vector <point_type> m_vertices;
		polygons_list 		m_faces;
		
	  public: 
		inline void PrintVertices()const;
		inline void PrintEdges()const;
		inline void PrintFaces()const;
	};

	// =====================================================================
	// face class implementation
	// =====================================================================
	template<typename Conf>
	struct Mesh<Conf>::Face
	{
		typedef typename Mesh<Conf>::real		real;//float or double
		typedef typename Mesh<Conf>::point_type		point_type;
		typedef typename Mesh<Conf>::vector_type	vector_type;
// 		typedef typename configure::edge_type		edge_type  ;
		// constructor
		explicit Face( Mesh<Conf>& mesh,
			const int&V0,const int&V1,const int&V2,const vector_type&n)
			: m_mesh (mesh)
		{
			m_vert [0]= V0; 
			m_vert [1]= V1;
			m_vert [2]= V2;
			m_normal  = n ;
			SetBoundingBox();
		}
		// TOPO, the return value its point_type and not Vertex
		inline point_type& V0() {return (m_mesh.m_vertices) [m_vert [0]];}
		inline point_type& V1() {return (m_mesh.m_vertices) [m_vert [1]];}
		inline point_type& V2() {return (m_mesh.m_vertices) [m_vert [2]];}
		
		inline const point_type& V0()const {return (m_mesh.m_vertices) [m_vert [0]];}
		inline const point_type& V1()const {return (m_mesh.m_vertices) [m_vert [1]];}
		inline const point_type& V2()const {return (m_mesh.m_vertices) [m_vert [2]];}
		
		inline const point_type E1()const{return 
				Math::Sub( 	(m_mesh.m_vertices)[m_vert[1]],
						(m_mesh.m_vertices)[m_vert[0]]	);}
		inline const point_type E2()const{return
				Math::Sub( 	(m_mesh.m_vertices)[m_vert[1]],
						(m_mesh.m_vertices)[m_vert[0]]	);}
		
		inline Point3D<int>&        PtMin()      {return pMin ;}
		inline const Point3D<int>   PtMin()const {return pMin ;}
		inline Point3D<int>&        PtMax()      {return pMax ;}
		inline const Point3D<int>   PtMax()const {return pMax ;}
		
		// nomal
		inline vector_type& N() {return m_normal;}
		inline const vector_type& N()const {return m_normal;}
		
		// =============================================================
		// Output streams
		// =============================================================
		inline friend std::ostream& operator<<(std::ostream& output,const Face& F)
		{
			output<<"	vert_indices <" << F.m_vert[0] << ", "<< F.m_vert[1] << ", " << F.m_vert[2]<< '>';
			output<<"	normals	"<< F.N()       << "\n";
			output<<"	vertices ["<< F.V0()    << " , " << F.V1()    << " ," << F.V2() << "]\n";
			output<<"	aabb     ["<< F.PtMin() << " , " << F.PtMax() << "]\n";
			output<<"	edges    ["<< F.E1()    << " , " << F.E2()    << "]\n";
			return output;
		}
		
		Mesh<Conf>	m_mesh ;
	  private:
		int		m_vert [3] ;
		vector_type	m_normal;
		length_type	refcount; // number of facet
		Point3D<int>	pMin;
		Point3D<int>	pMax;
	  private:
		inline void SetBoundingBox();
		inline void ExtraBoundingBox();
// 	  private:
// 		inline Face& operator = (Face &eq) {} 
// 		Face(const Face &g ){}
	};
	// =====================================================================

	
	
	
	
	// I have stop here.
	// this class have never been testd
	template<typename Conf>
	struct Mesh<Conf>::PolyList
	{
		explicit PolyList( Mesh<Conf>& mesh) : m_mesh (mesh) {}
	  
		typedef typename Mesh<Conf>::real		real;//float or double
		typedef typename Mesh<Conf>::point_type		point_type;
		typedef typename Mesh<Conf>::vector_type	vector_type;
		typedef typename Mesh<Conf>::triangle_type	triangle_type;
		
		typedef std::vector <Face > 			polygons_list;
		typedef typename polygons_list::iterator	iterator;
		typedef typename polygons_list::const_iterator	const_iterator;
		
		
		inline std::size_t push_back(const point_type& v_)
		{
			// search if the vertex exist already
			for (int i=0 ; i !=  m_mesh.m_vertices.size() ; i++){
				if ( ( m_mesh.m_vertices[i]) == v_ )
				return i ; // found
			}
			// else. its new vertices 
			(m_mesh.m_vertices).push_back( v_ )  ; // new 
			return (m_mesh.m_vertices).size() -1 ;
		}

		
		// push_back triangle 
		inline void push_back(const point_type&v0_,const point_type&v1_,const point_type&v2_,const vector_type& n_)
		{
			(m_mesh.m_faces).push_back(
				triangle_type(  m_mesh,
						push_back(v0_),
						push_back(v1_),
						push_back(v2_) ,
						n_
					)
			) ;
		}
		
		inline friend std::ostream& operator<<(std::ostream& output,const PolyList& Poly)
		{
			typedef typename polygons_list::const_iterator const_iterator;
			for (const_iterator it = Poly.begin() ; it != Poly.end () ; ++ it  )
			  output << *it << std::endl ;
			return output;
		}
		inline iterator 	begin() {return (m_mesh.m_faces).begin();}
		inline const_iterator	begin() const {return (m_mesh.m_faces).begin();}
		
		inline iterator 	end() {return (m_mesh.m_faces).end();}
		inline const_iterator	end() const {return (m_mesh.m_faces).end();}
		
		inline const std::size_t size() const {return (m_mesh.m_faces).size();}
	
		Mesh<Conf>& m_mesh ;
	};

	// =====================================================================
	// Vertex class implementation
	// =====================================================================
	template<typename Conf>
	struct Mesh<Conf>::Vertex
	{
		point_type  m_pos ;
		length_type refcount; // number of facet
		std::vector<unsigned int> m_neighbors ;
	};
	
	// =====================================================================
	// Edge class implementation
	// =====================================================================	
	template<typename Conf>
	struct Mesh<Conf>::Edge{
		int m_vert [2] ;
		explicit Edge (const int&V0,const int&V1)
		{
			m_vert[0]=V0;
			m_vert[1]=V1;
		}
		length_type refcount; // number of facet
	};
	
	
	
}// end namespace ge


// TODO remove these 8 lines to *cpp file.
namespace ge
{
	
	template<typename Conf>
	inline void Mesh<Conf>::Face::SetBoundingBox()
	{
		pMin.X() = MIN3 ( floor( this->V0().X()), floor( this->V1().X()),floor( this->V2().X()) );
		pMin.Y() = MIN3 ( floor( this->V0().Y()), floor( this->V1().Y()),floor( this->V2().Y()) );
		pMin.Z() = MIN3 ( floor( this->V0().Z()), floor( this->V1().Z()),floor( this->V2().Z()) );
		pMax.X() = MAX3 ( ceil ( this->V0().X()), ceil ( this->V1().X()),ceil ( this->V2().X()) );
		pMax.Y() = MAX3 ( ceil ( this->V0().Y()), ceil ( this->V1().Y()),ceil ( this->V2().Y()) );
		pMax.Z() = MAX3 ( ceil ( this->V0().Z()), ceil ( this->V1().Z()),ceil ( this->V2().Z()) );
		this ->ExtraBoundingBox();
	}
	
	template<typename Conf>
	inline void Mesh<Conf>::Face::ExtraBoundingBox()
	{
		if( fabs(pMax.X()- pMin.X()) <= SMALL_NUM  )	// parellel to yz plane
		{
		    pMin.X()= pMin.X() - 1 ;
		    pMax.X()= pMax.X() + 1 ;
		}

		// parellel to xz plane
		else if (fabs(pMax.Y() - pMin.Y()) <= SMALL_NUM ) {
		    pMin.Y() = pMin.Y() - 1 ;
		    pMax.Y() = pMax.Y() + 1 ;
		}

		// parellel to xz plane
		else if (fabs(pMax.Z() - pMin.Z()) <= SMALL_NUM   ) {
		    pMin.Z() =  pMin.Z() - 1 ;
		    pMax.Z() =  pMax.Z() + 1 ;
		}
		if (pMin.X() < 0 ) pMin.X() = 0 ;
		if (pMin.Y() < 0 ) pMin.Y()  = 0 ;
		if (pMin.Z() < 0 ) pMin.Z()  = 0 ;
	}
	
	
	
	// =====================================================================
	// use it only to add first element
	// =====================================================================
// 	template<typename Conf>
// 	inline std::size_t Mesh<Conf>::push_back(const typename Mesh<Conf>::point_type& v_)
// 	{
// 		// search if the vertex exist already
// 		for (int i=0 ; i != m_vertices.size() ; i++){
// 			if ( (m_vertices[i]) == v_ )
// 			  return i ; // found
// 		}
// 		// else. its new one 
// 		m_vertices.push_back( v_ )  ; // new 
// 		return m_vertices.size() -1   ;
// 	}
	

	// =====================================================================
	// 
	// =====================================================================
// 	template<typename Conf>
// 	inline void Mesh<Conf>::push_back(	const typename Mesh<Conf>::point_type&v0_,
// 						const typename Mesh<Conf>::point_type&v1_,
// 						const typename Mesh<Conf>::point_type&v2_,
// 						const typename Mesh<Conf>::vector_type& n_)
// 	{
// 		m_faces.push_back( Face(this->push_back(v0_),
// 					this->push_back(v1_),
// 					this->push_back(v2_) ,
// 					n_
// 					) ) ;
// 	}
	
	// =====================================================================
	// 
	// =====================================================================
	template<typename Conf>
	inline void Mesh<Conf>::PrintVertices()const{
		typedef typename std::vector<point_type>::const_iterator iterator;
		std::cout<< "contains : " << m_vertices.size() << " vertices\n";
		for (iterator it=m_vertices.begin();it!=m_vertices.end();++it)
			std::cout << *it << std::endl ;
	}



	// =====================================================================
	// 
	// =====================================================================
	template<typename Conf>
	inline void Mesh<Conf>::PrintEdges()const{
		typedef typename Mesh<Conf>::const_iterator iterator;
		std::cout<< "Edges:\n";
		for (iterator it=m_faces.begin();it!=m_faces.end();++it)
			std::cout<< "["<< (*it).E1() << ","<< (*it).E2()<<"]\n";
	}


	// =====================================================================
	// 
	// =====================================================================
	template<typename Conf>
	inline void Mesh<Conf>::PrintFaces()const{
		typedef typename Mesh<Conf>::const_iterator iterator ;
		std::cout<< "contains : " << m_faces.size() << " faces\n";
		for (iterator it=m_faces.begin();it!=m_faces.end();++it )
			std::cout << *it << std::endl ;
	}
	// =====================================================================


}

	
	




#endif 
