/*
its work only for special numbers 
*/
#include <iostream>

inline void Print (int c) ;
//template<class T>
//inline T ChainThreeInts (const int& a, const int& b, const int& c ) ;
inline int ChainThreeInts (const int& a, const int& b, const int& c ) ;
inline unsigned short  ChainThreeIntstToShort (const int& a, const int& b, const int& c) ; // optim
	


int  main ()
{
	std::cout<<"////////////////////////////////////////////////////////\n";
	std::cout<<"			CODING		  	    \n";
	std::cout<<"////////////////////////////////////////////////////////\n";

        int a = 1200000 ;
        int b = 1    ;
        int c = 120   ;

        std::cout<< "befor chainging\n" ;
	
	std::cout << " a	" ;
        Print (a) ; 
	std::cout << " b	" ;
        Print (b) ;
	std::cout << " c	" ;
        Print (c) ;

        //const unsigned short  d = ChainThreeInts<unsigned short> (a,b,c) ;
        int  d = ChainThreeInts (a,b,c) ;
	std::cout << " d	" ;
        Print ( d )  ;
	
	
	const unsigned short tmp = ChainThreeIntstToShort (a,b,c) ; // optim
	std::cout << " tmp " ;
        Print ( tmp )  ;
	
	
	std::cout<<"////////////////////////////////////////////////////////\n";
	std::cout<<"			DECODING d		  	    \n";
	std::cout<<"////////////////////////////////////////////////////////\n";
        std::cout<<   (d &0x7F)      << std::endl ;
        std::cout<< ( (d >> 7) &1 )  << std::endl ;
        std::cout<<   (d >> 8)       << std::endl ;
	
	std::cout<<"////////////////////////////////////////////////////////\n";
	std::cout<<"			DECODING   tmp		  	    \n";
	std::cout<<"////////////////////////////////////////////////////////\n";
        std::cout<<   (tmp &0x7F)      << std::endl ;
        std::cout<< ( (tmp >> 7) &1 )  << std::endl ;
        std::cout<<   (tmp >> 8)       << std::endl ;



	std::cout<<"////////////////////////////////////////////////////////\n";
	// 
        //Print ( 173 ) ; 
        //Print ( 51 ) ; 

        //Print ( 173 && 51 ) ;// and bitwise operator  
        Print ( 24<<2    ) ;// and bitwise operator  



	return (0) ;
}

/* Bit print of an integral expression*/
inline void Print (int c)
{
  std::cout << " = " << c << "          	" ;
  int n = sizeof(int)*CHAR_BIT;
  int mask = 1 << (n-1);
  for (int i=1; i<=n; ++i)
  {
            putchar(((c & mask) == 0)? '0' : '1' );
            c <<= 1;
            if (i% CHAR_BIT == 0 && i < n)
            putchar (' ') ;
  } 
  putchar ('\n') ;
}

inline int ChainThreeInts (const int& a, const int& b, const int& c)
{
    return  ( (a << 8) | (b << 7) | c );
}

inline unsigned short  ChainThreeIntstToShort (const int& a, const int& b, const int& c)
{
    return  ( (a << 8) | (b << 7) | c );
}

