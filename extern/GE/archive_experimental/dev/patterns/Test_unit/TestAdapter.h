
#ifndef TESTADAPTER_HH
#define TESTADAPTER_HH 

//==============================================================================
// Include
//==============================================================================
#include "Test.h"
#include <iostream>
//==============================================================================


using std::cout ;
using std::cerr ;
using std::endl ;


//==============================================================================
/*!
//  \class TestAdapter
//  \brief "wrapper" or "adapter" class
//
//  \author Cherif Mihoubi
*/
//==============================================================================

template <class TYPE, class FcType>
class TestAdapter: public Test
{
	public:
		// constructor
		explicit TestAdapter( TYPE *o, FcType(TYPE:: *m)()  ) 
		{
			object = o;
			method = m;
		}

		// Destructor 
	       ~TestAdapter(){
			delete object;
   		}

		//The adapter/wrapper "maps" the new to the legacy implementation
		inline void execute()
		{
			(object->*method)();
		}

  		// member data  
  	private:
		TYPE *object;			// ptr-to-object attribute
		FcType(TYPE:: *method)();	// ptr-to-member-function attribute
};


#endif // end TESTADAPTER_HH

