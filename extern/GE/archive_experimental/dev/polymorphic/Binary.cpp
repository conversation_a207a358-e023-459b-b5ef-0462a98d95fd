#include <iostream>
#include <iterator>
#include <bitset>
using namespace std;


int main()
{
    std::copy(istream_iterator<char>(cin), 
	      istream_iterator<char>(),
	      ostream_iterator<bitset<8> >(cout, "\n"));
}


/*
  // =====================
  //  Experiments 
  // =====================

  >1                                     
  0001                                  
  >2                                     
  0010                                  
  >45                                    
  0100                                  
  0101

*/


// //Ask the user for an input string
// 
// std::string userInput;
// 
// std::cin >> userInput;
