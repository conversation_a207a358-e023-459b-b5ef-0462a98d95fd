
#include <iostream>
// #include <vector>
#include <fstream>
#include <string>
#include <ctime> // get time
#include <boost/scoped_ptr.hpp>	//scoped_ptr
#include <boost/utility.hpp>	//noncopyable
#include <stdexcept>
#include <memory>
// its similar to <PERSON> implementation in waLberla framework


// =============================================================================
// =============================================================================
struct Logging : private boost::noncopyable
{
	Logging();
	~Logging();
	inline static Logging& Instance ();
	inline static void Configure(const std::string& f_name);
	inline static void LogError (const std::string& msg) ;
	inline static void LogInfo  (const std::string& msg) ;
	inline static void LogWarning(const std::string& msg) ;
  public:
	std::fstream m_file ;
	static std::string fileName;
	std::time_t  m_Ctime;
};



	std::string Logging::fileName;// Initialize static data member

// =============================================================================
	Logging::Logging()
	{
		const std::string tmp = Logging::fileName;
		if ( tmp.empty() )
		  throw std::runtime_error( "Logging not configured!" );
		m_file.open (tmp.c_str(), std::ofstream::out | std::ofstream::app);
	}
	
	Logging::~Logging()
	{
		m_file.close();
	}
	
	
	inline Logging& Logging::Instance ()/*const*/
	{
		typedef std::auto_ptr<Logging>Ptr;//typedef boost::scoped_ptr<Logging>Ptr;
		static Ptr my_loger;
		if( !my_loger.get() )
		{
			my_loger = Ptr(new Logging() );
		}
		return *my_loger.get();
	}
	
	
	
	// =====================================================================
	// configure file nanme
	// =====================================================================
	inline void Logging::Configure(const std::string& f_name)
	{
		fileName=f_name;
	}
	
	
	inline void Logging::LogError (const std::string& msg)
	{
		(Logging::Instance().m_file) << "Error : " << msg  << "\n";
	}
	
	inline void Logging::LogInfo  (const std::string& msg)
	{
		Logging::Instance().m_file << "Info : " << msg  << "\n";
	}
	
	inline void Logging::LogWarning(const std::string& msg)
	{
		Logging::Instance().m_file << "Warning : " << msg  << "\n";
	}



// =============================================================================
// =============================================================================
// Logging& ErrorLog();
// ErrorLog()::Configure("errro_log.txt");

// Logging WarningLog;
// Logging InfoLog;

#define LOG_ERROR(msg) Logging::Instance().LogError(msg)


// =============================================================================
//
// =============================================================================
int main ()
{
	std::cout << "First configure\n" ;
	Logging::Configure("errro_log.txt");
	
	std::cout << "log error\n" ;
	Logging::LogError ("test ....\n") ;
	
	
	std::cout << "Free Memo\n" ;
	return (0) ;
}
