
#include <iostream>
#include <string>
#include <sstream>

#include <vector>
#include <boost/shared_ptr.hpp>

using namespace std;
// ----------------------------------------------------------------------
// ----------------------------------------------------------------------
template <typename T>
struct CompositeCounting /*: boost::noncopyable*/
{
    CompositeCounting()    { ++objects_created;   ++objects_alive;   }
    virtual ~CompositeCounting(){ --objects_alive; }
    static int objects_created;//!@brief  representaion
    static int objects_alive;  //!@brief  representaion
//!@brief  info functions     
    inline int GetCountObjects_created( ) const {return objects_created;}
    inline int GetCountObjects_alive  ( ) const {return objects_alive;}        
};

//!@brief initialization 
template <typename T> int CompositeCounting<T>::objects_created( 0 );
template <typename T> int CompositeCounting<T>::objects_alive  ( 0 );
// ----------------------------------------------------------------------
// ----------------------------------------------------------------------


class Component
{
  public:
    virtual void traverse() = 0;
};

class Primitive: public Component
{
    int value;
  public:
    Primitive(int val)
    {
        value = val;
    }
    void traverse()
    {
        cout << value << "  ";
    }
};



class Composite: public CompositeCounting < Composite >
{
    std::vector < Component * > children; // clear 
    int value;
  public:
    Composite(int val) :  value (val)  {}
    
    inline void add(Component *c)
    {
        children.push_back(c);
    }
    

    void traverse()
    {
        cout << value << "  ";
        for (int i = 0; i < children.size(); i++)
          children[i]->traverse();
    }
    
    std::string Name () {
	      stringstream CompositeName ;  
	      CompositeName.str("");
	      int d = /*static_cast<const Composite<Component> & >*/(*this).GetCountObjects_created( ) ;
// 	      GetCountObjects_created( )
	      CompositeName << "Composite"<< d  ;
    }
};

class Row: public Composite 
{
  public : 
      typedef boost::shared_ptr< Row > Ptr; //  generic programming
  public:
    Row(int val): Composite (val){}

    void traverse()
    {
        cout << "Row "; // base class.
        Composite::traverse();
    }
};

class Column: public Composite 
{
  public:
    Column(int val): Composite (val){}
    void traverse()
    {
        cout << "Col";
        Composite::traverse();
    }
};
 

// template <typename Component> 
// inline void add( Composite<Component>& c,const Component& f)
// {
//       c.add (f);
// }


int main ()
{
      Row    first(1);                 // Row1
      Column second(2);                //   |
      Column third(3);                 //   +-- Col2
      Row    fourth(4);                //   |     |
      Row    fifth(5);                 //   |     +-- 7
      
        first.add ((Component*)&second);           		//   +-- Col3
	first.add ((Component*)&third);            		//   |     |
//  	third.add ((Component*)&fourth);           		//   |     +-- Row4
// 	third.add ((Component*)&fifth);            		//   |     |     |
// 	first.add ((Component*)&Primitive(6));     		//   |     |     +-- 9
// 	second.add((Component*)&Primitive(7));    		//   |     +-- Row5
// 	third.add ((Component*)&Primitive(8));     		//   |     |     |
// 	fourth.add((Component*)&Primitive(9));    		//   |     |     +-- 10
// 	fifth.add ((Component*)&Primitive(10));    		//   |     +-- 8
// 	
	first.traverse();             		//   +-- 6
      
      cout << '\n';  

      return (0) ;
}

