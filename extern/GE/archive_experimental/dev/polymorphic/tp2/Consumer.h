#ifndef CONSUMER_HH
#define CONSUMER_HH

#include <stdio.h> // 
#include <string.h>


struct customer
{
	    virtual char* name ()const =0 ;/* Get Name */	  
	    inline bool operator< (customer& p){
		return strcmp ( m_name, (p.m_name) );// its wrong but i used just for testing purpose
	    }
	protected :
	    char* m_name ;	/* representation*/	    
};

class Company :public customer
{
        public:
             Company (const char* name) {m_name = (char*) name ;}
             char* name () const {return  m_name;} ;
};

class Person :public customer
{
        public:
                Person (const char* name) {m_name = (char*) name ;}
                char* name ()const  {return m_name ;} ;
};






#endif
