#include <iostream>
#include "GarbageCollecter.h"

// =============================================================================
//! @note To watch the action of the garbage collector, define DISPLAY.
// =============================================================================
//static bool collect ()     Destructor for GCPTr. Returns true if at least one object was freed
//static int  gclistSize ()  Return the size of gclist for this type of GCPtr.
//static void showlist ()    A utility function that displays gclist.
//static void shutdown ()    Clear gclist when program exits. 

using namespace std;

class MyClass {
  int a, b;
public:
  double val;
  MyClass() { a = b = 0; }
  MyClass(int x, int y) {
    a = x;
    b = y;
    val = 0.0;
  }
  ~MyClass() {
    cout << "Destructing MyClass(" <<
         a << ", " << b << ")\n";
  }
  int sum() {
    return a + b;
  }
  friend ostream &operator<<(ostream &strm, MyClass &obj);
};
// An overloaded inserter to display MyClass.
ostream &operator<<(ostream &strm, MyClass &obj) {
  strm << "(" << obj.a << " " << obj.b << ")";
  return strm;
}




int main ()
{
 try {
   
    GCPtr<MyClass> ob = new MyClass(10, 20);
    
    cout << *ob << endl;// Show value via overloaded inserter.
    
    ob = new MyClass(11, 21);// Change object pointed to by ob.
    
    cout<< *ob << endl;
    
    cout<<"Sum is : "<<ob->sum()<<endl;//Call a member function through a GCPtr.
    
      
    ob->val = 98.6;// Assign a value to a class member through a GCPtr.
    cout << "ob->val: " << ob->val << endl;
    cout << "ob is now " << *ob << endl;
    
  } catch(bad_alloc exc){
    cout << "Allocation error!\n";
    return 1;
  }
  return 0;
}
