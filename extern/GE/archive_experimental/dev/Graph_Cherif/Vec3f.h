#ifndef VEC3F_H
#define VEC3F_H

#include <math.h>
#include <cassert>

// #include "../misc/Common.h"


using namespace std;



class Vec3f
{
   private:

	friend class Matrix;
	float 	m_data[3];	// REPRESENTATION

   public:

	// CONSTRUCTORS	
	Vec3f() { m_data[0] = m_data[1] = m_data[2] = 0; }
	Vec3f(const Vec3f &V)
	{
		m_data[0] = V.m_data[0];
		m_data[1] = V.m_data[1];
		m_data[2] = V.m_data[2];
	}
	Vec3f(const float& d0,const float& d1,const float& d2)
	{
		m_data[0] = d0;
		m_data[1] = d1;
		m_data[2] = d2;
	}
	Vec3f(const Vec3f &V1, const Vec3f &V2)
	{
		m_data[0] = V1.m_data[0] - V2.m_data[0];
		m_data[1] = V1.m_data[1] - V2.m_data[1];
		m_data[2] = V1.m_data[2] - V2.m_data[2];
	}
// DESTRUCTOR
	~Vec3f() { }

	// ACCESSORS
	inline void Get(float& d0,float& d1,float& d2)const{
		d0 = m_data[0];
		d1 = m_data[1];
		d2 = m_data[2]; 
	}

	inline float operator[](const int& i) const
	{ 
		assert (i >= 0 && i < 3); 
		return m_data[i]; 
	}

	inline float x() const { return m_data[0]; }
	inline float y() const { return m_data[1]; }
	inline float z() const { return m_data[2]; }

	inline float r() const { return m_data[0]; }
	inline float g() const { return m_data[1]; }
	inline float b() const { return m_data[2]; }

	inline float Length() const
	{
		return (float )sqrt( 	m_data[0] * m_data[0] +
					m_data[1] * m_data[1] +
					m_data[2] * m_data[2] ); 
	}

 // MODIFIERS
	inline void Set(const float& d0,const float& d1,const float& d2)
	{
		m_data[0] = d0;
		m_data[1] = d1;
		m_data[2] = d2;
	}
	inline void Scale(const float& d0,const float& d1,const float& d2)
	{
		m_data[0] *= d0;
		m_data[1] *= d1;
		m_data[2] *= d2;
	}
	inline void Divide(const float& d0,const float& d1,const float& d2)
	{
		m_data[0] /= d0;
		m_data[1] /= d1;
		m_data[2] /= d2;
	}


	inline void Normalize()
	{
		float	l = Length();
		if (l > 0) {
			m_data[0] /= l;
			m_data[1] /= l;
			m_data[2] /= l; 
		}
	}
	inline void Negate()
	{
		m_data[0] = -m_data[0];
		m_data[1] = -m_data[1];
		m_data[2] = -m_data[2];
	}

	inline void Clamp(const float& low = 0,const float& high = 1)
	{
		if (m_data[0] < low) m_data[0] = low;	if (m_data[0] > high) m_data[0] = high;
		if (m_data[1] < low) m_data[1] = low;	if (m_data[1] > high) m_data[1] = high;
		if (m_data[2] < low) m_data[2] = low;	if (m_data[2] > high) m_data[2] = high;
	}




	// OVERLOADED OPERATORS
	inline Vec3f& operator=(const Vec3f &V)
	{
		m_data[0] = V.m_data[0];
		m_data[1] = V.m_data[1];
		m_data[2] = V.m_data[2];
		return *this;
	}

	inline int operator==(const Vec3f &V)
	{
		return( (m_data[0] == V.m_data[0] ) &&
			(m_data[1] == V.m_data[1] ) &&
			(m_data[2] == V.m_data[2] ) );
	}

	inline int operator!=(const Vec3f &V)
	{
		return ((m_data[0] != V.m_data[0]) ||
			(m_data[1] != V.m_data[1]) ||
			(m_data[2] != V.m_data[2]));
	}


	inline Vec3f& operator+=(const Vec3f &V)
	{
		m_data[0] += V.m_data[0];
		m_data[1] += V.m_data[1];
		m_data[2] += V.m_data[2];
		return *this; 
	}
		
	inline Vec3f& operator-=(const Vec3f &V)
	{
		m_data[0] -= V.m_data[0];
		m_data[1] -= V.m_data[1];
		m_data[2] -= V.m_data[2];
		return *this;
	}
		
	inline Vec3f& operator*=(const int& i)
	{
		m_data[0] = float (m_data[0] * i);
		m_data[1] = float (m_data[1] * i);
		m_data[2] = float (m_data[2] * i);
		return *this;
	}
	
	inline Vec3f& operator*=(const float&	f)
	{
		m_data[0] *= f;
		m_data[1] *= f;
		m_data[2] *= f;
		return *this;
	}
	
	inline Vec3f& operator/=(const int& i) 
	{
		m_data[0] = float (m_data[0] / i);
		m_data[1] = float (m_data[1] / i);
		m_data[2] = float (m_data[2] / i);
		return *this; 
	}
	
	inline Vec3f& operator/=(const float&	f)
	{
		m_data[0] /= f;
		m_data[1] /= f;
		m_data[2] /= f;
		return *this;
	}
	
	inline friend Vec3f operator+(const Vec3f &v1, const Vec3f &v2)
	{ 
		Vec3f v3; Add(v3,v1,v2); return v3;
	}
	
	
	inline friend Vec3f operator-(const Vec3f &v1)
	{
		Vec3f v3 = v1;
		v3.Negate();
		return v3;
	}
	
	
	inline friend Vec3f operator-(const Vec3f &v1, const Vec3f &v2)
	{
		Vec3f v3;
		Sub(v3,v1,v2);
		return v3;
	}
	
	
	inline friend Vec3f operator*(const Vec3f &v1, float& f)
	{
		Vec3f v2;
		CopyScale(v2,v1,f);
		return v2;
	}
	
	
	inline friend Vec3f operator*(float&	f, const Vec3f &v1)
	{
		Vec3f v2;
		CopyScale(v2,v1,f);
		return v2;
	}
	
	inline friend Vec3f operator*(const Vec3f &v1, const Vec3f &v2)
	{
		Vec3f v3;
		Mult(v3,v1,v2);
		return v3;
	}





	// OPERATIONS
	inline float Dot3(const Vec3f &V) const
	{
		return	 m_data[0] * V.m_data[0] +
			 m_data[1] * V.m_data[1] +
			 m_data[2] * V.m_data[2] ;
	}

	// STATIC OPERATIONS
	inline static void Add(Vec3f &a, const Vec3f &b, const Vec3f &c )
	{
		a.m_data[0] = b.m_data[0] + c.m_data[0];
		a.m_data[1] = b.m_data[1] + c.m_data[1];
		a.m_data[2] = b.m_data[2] + c.m_data[2];
	}
	
	
	inline static void Sub(Vec3f &a, const Vec3f &b, const Vec3f &c )
	{
		a.m_data[0] = b.m_data[0] - c.m_data[0];
		a.m_data[1] = b.m_data[1] - c.m_data[1];
		a.m_data[2] = b.m_data[2] - c.m_data[2];
	}
	
	
	inline static void Mult(Vec3f &a, const Vec3f &b, const Vec3f &c )
	{
		a.m_data[0] = b.m_data[0] * c.m_data[0];
		a.m_data[1] = b.m_data[1] * c.m_data[1];
		a.m_data[2] = b.m_data[2] * c.m_data[2];
	}
	
	inline static void CopyScale(Vec3f &a,const Vec3f &b,const float& c)
	{
		a.m_data[0] = b.m_data[0] * c;
		a.m_data[1] = b.m_data[1] * c;
		a.m_data[2] = b.m_data[2] * c;
	}
	
	inline static void AddScale(Vec3f &a,const Vec3f &b,const Vec3f &c,const float& d) 
	{
		a.m_data[0] = b.m_data[0] + c.m_data[0] * d;
		a.m_data[1] = b.m_data[1] + c.m_data[1] * d;
		a.m_data[2] = b.m_data[2] + c.m_data[2] * d; 
	}
	 
	 
	inline static void Average(Vec3f &a, const Vec3f &b, const Vec3f &c )
	{
		a.m_data[0] = (b.m_data[0] + c.m_data[0]) * 0.5f;
		a.m_data[1] = (b.m_data[1] + c.m_data[1]) * 0.5f;
		a.m_data[2] = (b.m_data[2] + c.m_data[2]) * 0.5f;
	}
	
	
	inline static void WeightedSum(Vec3f &a, const Vec3f &b, float	c, const Vec3f &d, float	e )
	{
		a.m_data[0] = b.m_data[0] * c + d.m_data[0] * e;
		a.m_data[1] = b.m_data[1] * c + d.m_data[1] * e;
		a.m_data[2] = b.m_data[2] * c + d.m_data[2] * e;
	}
	
	
	inline static void Cross3(Vec3f &c, const Vec3f &v1, const Vec3f &v2)
	{
		float	x = v1.m_data[1]*v2.m_data[2] - v1.m_data[2]*v2.m_data[1];
		float	y = v1.m_data[2]*v2.m_data[0] - v1.m_data[0]*v2.m_data[2];
		float	z = v1.m_data[0]*v2.m_data[1] - v1.m_data[1]*v2.m_data[0];
		c.m_data[0] = x; c.m_data[1] = y; c.m_data[2] = z;
	}
		

	inline static void Min(Vec3f &a, const Vec3f &b, const Vec3f &c )
	{
		a.m_data[0] = (b.m_data[0] < c.m_data[0]) ? b.m_data[0] : c.m_data[0];
		a.m_data[1] = (b.m_data[1] < c.m_data[1]) ? b.m_data[1] : c.m_data[1];
		a.m_data[2] = (b.m_data[2] < c.m_data[2]) ? b.m_data[2] : c.m_data[2];
	}
	
	
	inline static void Max(Vec3f &a, const Vec3f &b, const Vec3f &c )
	{
		a.m_data[0] = (b.m_data[0] > c.m_data[0]) ? b.m_data[0] : c.m_data[0];
		a.m_data[1] = (b.m_data[1] > c.m_data[1]) ? b.m_data[1] : c.m_data[1];
		a.m_data[2] = (b.m_data[2] > c.m_data[2]) ? b.m_data[2] : c.m_data[2];
	}
	
	
	// INPUT / OUTPUT

	inline friend ostream& operator<< (ostream &ostr, const Vec3f &v)
	{
		ostr<<"<"<< v.m_data[0] << " , " << v.m_data[1] << " , " << v.m_data[2] << ">\n";
		return ostr;
	}



};

#endif

