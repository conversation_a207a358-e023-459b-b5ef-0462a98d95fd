#ifndef MYCLASS_HH
#define MYCLASS_HH

#include <cassert>


//==============================================================================
/*!	@brief C-Style implementation of container */ 
//==============================================================================
template<typename Config_>
struct MyClass
{
	typedef  Config_ configure ; // export configure
	typedef typename configure::ElementType   ElementType  ;  

	//======================================================================
	// constructor, copy constructor and destructor
	//======================================================================
	MyClass (const int& size = 1000  )	  // default constructor
		: m_size(size) {
		m_DynArrPtrs = new ElementType *[m_size] ;
	}
      
	MyClass( const MyClass & other);          // copy constructor
      
	~MyClass();                               // destructor
	//====================================================================== 
 
	    
	// =====================================================================
	// insert element 
	// =====================================================================
	inline void insert (const int& where,const ElementType& elem);
     
   

	inline int Size() const;               // return size
     
	// =====================================================================
	// insert element 
	// =====================================================================
	// @{
	// subscript operator for non-const objects returns modifiable lvalue
	inline ElementType& operator[](const int& );
	// subscript operator for const objects returns rvalue
	inline ElementType  operator[](const int& ) const;
	// @}
	// =====================================================================
     
  private:
	int m_size ;
	ElementType** m_DynArrPtrs ;// an array of "size" pointers to "ElementType" objects
 
  private: // not implemented  
	const MyClass &operator = ( const MyClass & );	// assignment operator
	bool operator == ( const MyClass & ) const; 	// equality operator
};





	// =====================================================================
	// copy constructor
	// =====================================================================
	template<typename Config>
	MyClass<Config>::MyClass( const MyClass<Config> & other)
	{
		assert ( Size() == other.Size()  ) ;
		for (int i= 0 ; i <  Size() ;++i )
			m_DynArrPtrs[i] = other [i] ;
	}

	// =====================================================================
	// destructor
	// =====================================================================
	template<typename Config>
	MyClass<Config>::~MyClass()
	{
		for (int i= 0 ; i < m_size ; ++i)
			delete m_DynArrPtrs[i];
	}

	// =====================================================================
	// 
	// =====================================================================
	template<typename Config>
	inline 
	void  MyClass<Config>::insert (const int& where,const  MyClass<Config>::ElementType& elem){
		assert (where >0 &&  where < m_size );
		m_DynArrPtrs [where]  = elem;
	}


	// =====================================================================
	// subscript operator for non-const objects returns modifiable lvalue
	// =====================================================================
	// 
	template<typename Config>
	inline 
	typename MyClass<Config>::ElementType& MyClass<Config>::operator[](const int& index){
	// TODO ASSERT
	      assert (index >0 &&  index < m_size );
	      return m_DynArrPtrs [index] ;
	  }
	  
	  
	  
	// =====================================================================
	// subscript operator for const objects returns rvalue
	// =====================================================================
	// 
	template<typename Config >
	inline
	typename MyClass<Config>::ElementType MyClass<Config>::operator[](const int& index) const{
	      // TODO ASSERT
	      assert (index >0 &&  index < m_size );
	      return m_DynArrPtrs [index] ;
	}

#endif 

