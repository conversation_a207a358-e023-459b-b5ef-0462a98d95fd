
simdata{
   domainX   21;
   domainY   200;
   domainZ   21;
   dx        1; 
   dt        1;
   Uref      0.02;
   timesteps 30;
   Lref       40;
   tau 1.7;
   rho    1.0;
   dump;
}


brownianMotion{


};

// If no 'simoutput' block is given, no simulation output is written to the output file
// For VEL_LINES and DENS_LINES a separate file is written out per time step
// All other cases just write out one file which contain a graph over
// time

SimOutput{

   FileName myfile.txt;  // writes all non xmgrace output to myfile.txt. If this parameter is omitted,
   // same file as log file will be taken.


   XmGrace{

      FileName LineData.agr;
      yAxis <PERSON>ce velocity/density ;
      xAxis Lattice distance along the line;


      VEL_LINE{ // Writes out the velocity along one line 
         time 25;
         p_x 10.5;
         p_y 0.5;
         p_z 10.5;
         d_x 0;
         d_y 1;
         d_z 0;
         spacing 10;

         GraceData{ // Determines color width and the symbols of the line
            r 0;
            g 0;
            b 0;
            Symbol 1;
            LineWidth 3.0;}
      }

      DENS_LINE{ // Writes out the density along one line
         time 25;
         p_x 10.5;
         p_y 0.5;
         p_z 10.5;
         d_x 0;
         d_y 1;
         d_z 0;
         spacing 10;

         GraceData{
            r 0;
            g 0;
            b 0;
            Symbol 2;
            LineWidth 3.0;}
      }
   }

   XmGrace{
      FileName TimeDataDens.agr;
      yAxis Lattice Density;
      xAxis Time;

      DENSITY{  //Writes out the density at one physical point over time
         time 5;
         x 10.5;
         y 1.5;
         z 10.5;
         GraceData{
            r 0;
            g 0;
            b 0;
            Symbol 2;
            LineWidth 3.0;}
      }

      AVG_DENS{  //Writes out the average density of the whole domain 
         time 5; 
         GraceData{
            r 150;
            g 0;
            b 0;
            Symbol 3;
            LineWidth 3.0;}

      }

      MAX_DENS{ //Writes out the max density of the whole domain
         time 5;
         GraceData{
            r 255;
            g 0;
            b 0;
            Symbol 5;
            LineWidth 3.0;}

      }
   }

   XmGrace{
      FileName TimeDataVel.agr;
      yAxis Lattice velocity ;
      xAxis Time;

      VELOCITY{ //Writes out the velocity at a physical point
         time 5;
         x 10.5;
         y 2.5;
         z 10.5;
         GraceData{
            r 0;
            g 0;
            b 0;
            Symbol 1;
            LineWidth 3.0;}
      }

      MAX_VEL{ //Writes out the max velocity of the whole domain
         time 5;
         GraceData{
            r 200;
            g 0;
            b 0;
            Symbol 4;
            LineWidth 3.0;}

      }
   }
}



// Init block that initializes a Canal with inflow at south and outflow at north.
// Inflow velocity is 0.02 (lattices per timestep).
init
{
   north{                    
      vel_in{               
         x  0..'domainX'+1;   
         z  0..'domainZ'+1;
         ux_L   0.0;
         uy_L   0.02;
         uz_L   0.0; 
      }
   }

   south{
      vel_in{
         x      0..'domainX'+1;
         z      0..'domainZ'+1;
         ux_L   0.0;
         uy_L   0.02;
         uz_L   0.0; 
      }
   }

   east{
      freeslip{
         y  1..'domainY';
         z  0..'domainZ'+1;
      }
   }

   west{
      freeslip{
         y  1..'domainY';
         z  0..'domainZ'+1;
      }
   }

   top{
      freeslip{
         x    1..'domainX';
         y    1..'domainY';
      }
   }

   bottom{
      freeslip{
         x  1..'domainX';
         y  1..'domainY';
      }
   }  
}


patches
{
   xNumPatches 4; //Number of patches in x direction. If not given, then xNumPatches=1
   yNumPatches 1; //Number of patches in y direction. If not given, then yNumPatches=1
   zNumPatches 1; //Number of patches in z direction. If not given, then zNumPatches=1
}







