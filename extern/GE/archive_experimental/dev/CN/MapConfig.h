#ifndef MAPCONFIG_HH
#define MAPCONFIG_HH

#include "MapSelector.h"
#include <iostream>
#include <cassert>


//! template <typename Key , typename value_type, int selector> MapSelector {}; 
//! -----------------------------------------------------------------------
//! selector: |	use map < , >
//! -------------------------------
//!  1	      |	 map<Key , value_type  >
//!  2	      |  map<Key , value_type *>
//!  3	      |  map<Key*, value_type  >
//!  else     |  map<Key*, value_type *>
//! -------------------------------

using std::cout ;
using std::endl ;
using  ge::MapSelector  ;

// ====================================================================
/*!
 * @brief  Sample of map cinfiguration 
 *
 *
*/
// ====================================================================

struct Map_DefConfig // map default configuration
{
      typedef char  Key ; 
      typedef int   value_type ;
      typedef MapSelector<Key,value_type,1>::type  	MapType;
      // beloow  are not necessary
      typedef MapType::iterator  	    	Iterator ;
      typedef MapType::const_iterator		Const_Iterator ;
      typedef std::pair<Key,value_type>   	pair; // to use like this :>    mymap.insert ( pair('z',200) );  
      typedef std::pair<Iterator,bool>      Ret;
};

#endif 
