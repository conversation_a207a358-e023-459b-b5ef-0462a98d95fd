//=================================================================================================
/*!
//  \file WallModel.h
//  \brief Wall models for LES near-wall treatment
*/
//=================================================================================================

#ifndef _WALBERLA_TURBULENCE_WALL_MODEL_H
#define _WALBERLA_TURBULENCE_WALL_MODEL_H

#include "../../Definitions.h"
#include "../../Vector3.h"
#include "../../VelField.h"
#include "../../FlagField.h"
#include "../../ScalarField.h"

namespace walberla {
namespace turbulence {

//=================================================================================================
//
//  WALL MODEL BASE CLASS
//
//=================================================================================================

/*!\brief Abstract base class for wall models
//
// Wall models provide boundary conditions and near-wall treatment for LES
// at high Reynolds numbers where the viscous sublayer cannot be resolved.
*/
class WallModel
{
public:
    virtual ~WallModel() = default;
    
    /*!\brief Compute wall shear stress
    */
    virtual Vector3<Real> computeWallShearStress(
        const Vector3<Real>& velocity,
        const Real& distance,
        const Real& density,
        const Real& viscosity) const = 0;
    
    /*!\brief Apply wall model to compute effective eddy viscosity near walls
    */
    virtual Real computeNearWallEddyViscosity(
        const Real& distance,
        const Real& frictionVelocity,
        const Real& viscosity) const = 0;
    
    /*!\brief Get y+ (dimensionless wall distance)
    */
    Real computeYPlus(
        const Real& distance,
        const Real& frictionVelocity,
        const Real& viscosity) const
    {
        return distance * frictionVelocity / viscosity;
    }
    
    /*!\brief Get friction velocity from wall shear stress
    */
    Real computeFrictionVelocity(
        const Vector3<Real>& wallShearStress,
        const Real& density) const
    {
        Real tau_w = std::sqrt(wallShearStress[X]*wallShearStress[X] + 
                              wallShearStress[Y]*wallShearStress[Y] + 
                              wallShearStress[Z]*wallShearStress[Z]);
        return std::sqrt(tau_w / density);
    }
};

//=================================================================================================
//
//  VAN DRIEST DAMPING FUNCTION
//
//=================================================================================================

/*!\brief Van Driest damping function for near-wall eddy viscosity
//
// Damps the eddy viscosity near walls to account for viscous effects.
// f_mu = 1 - exp(-y+/A+) where A+ is typically 26.
*/
class VanDriestDamping : public WallModel
{
public:
    explicit VanDriestDamping(Real aPlus = 26.0) : aPlus_(aPlus) {}
    
    virtual Vector3<Real> computeWallShearStress(
        const Vector3<Real>& velocity,
        const Real& distance,
        const Real& density,
        const Real& viscosity) const override;
    
    virtual Real computeNearWallEddyViscosity(
        const Real& distance,
        const Real& frictionVelocity,
        const Real& viscosity) const override;
    
    /*!\brief Get damping function value
    */
    Real getDampingFunction(const Real& yPlus) const
    {
        return 1.0 - std::exp(-yPlus / aPlus_);
    }
    
    void setAPlus(const Real& aPlus) { aPlus_ = aPlus; }

private:
    Real aPlus_; // Van Driest constant
};

//=================================================================================================
//
//  WALL FUNCTION MODEL
//
//=================================================================================================

/*!\brief Standard wall function based on log-law
//
// Uses the logarithmic law of the wall:
// u+ = (1/kappa) * ln(y+) + B
// where kappa ≈ 0.41 and B ≈ 5.2
*/
class WallFunction : public WallModel
{
public:
    WallFunction(Real kappa = 0.41, Real B = 5.2)
        : kappa_(kappa), B_(B) {}
    
    virtual Vector3<Real> computeWallShearStress(
        const Vector3<Real>& velocity,
        const Real& distance,
        const Real& density,
        const Real& viscosity) const override;
    
    virtual Real computeNearWallEddyViscosity(
        const Real& distance,
        const Real& frictionVelocity,
        const Real& viscosity) const override;
    
    /*!\brief Compute u+ from y+ using log law
    */
    Real computeUPlus(const Real& yPlus) const;
    
    /*!\brief Newton iteration to find friction velocity
    */
    Real solveFrictionVelocity(
        const Real& velocity,
        const Real& distance,
        const Real& viscosity) const;

private:
    Real kappa_; // von Karman constant
    Real B_;     // Log law constant
};

//=================================================================================================
//
//  WERNER-WENGLE WALL MODEL
//
//=================================================================================================

/*!\brief Werner-Wengle power-law wall model
//
// Uses power law near the wall:
// u+ = y+           for y+ < 11.81
// u+ = A*(y+)^B     for y+ >= 11.81
// where A = 8.3 and B = 1/7
*/
class WernerWengleModel : public WallModel
{
public:
    WernerWengleModel(Real A = 8.3, Real B = 1.0/7.0)
        : A_(A), B_(B), yCrit_(11.81) {}
    
    virtual Vector3<Real> computeWallShearStress(
        const Vector3<Real>& velocity,
        const Real& distance,
        const Real& density,
        const Real& viscosity) const override;
    
    virtual Real computeNearWallEddyViscosity(
        const Real& distance,
        const Real& frictionVelocity,
        const Real& viscosity) const override;

private:
    Real A_;      // Power law coefficient
    Real B_;      // Power law exponent
    Real yCrit_;  // Critical y+ value
};

//=================================================================================================
//
//  WALL MODEL MANAGER
//
//=================================================================================================

/*!\brief Manages wall models and wall distance computation
//
// This class handles the application of wall models throughout the domain,
// including wall distance calculation and model selection.
*/
class WallModelManager
{
public:
    WallModelManager();
    
    /*!\brief Set the wall model to use
    */
    void setWallModel(std::unique_ptr<WallModel> model) {
        wallModel_ = std::move(model);
    }
    
    /*!\brief Compute wall distances for all fluid cells
    */
    void computeWallDistances(
        const FlagField<Uint>& flagField,
        ScalarField<Real>& wallDistance);
    
    /*!\brief Apply wall model to modify eddy viscosity field
    */
    void applyWallModel(
        const VelField& velField,
        const ScalarField<Real>& wallDistance,
        ScalarField<Real>& eddyViscosity,
        const Real& molecularViscosity,
        const Real& density);
    
    /*!\brief Get wall-adjacent velocity for wall function
    */
    Vector3<Real> getWallAdjacentVelocity(
        const VelField& velField,
        const Uint x, const Uint y, const Uint z,
        const FlagField<Uint>& flagField) const;
    
    /*!\brief Find nearest wall cell
    */
    void findNearestWall(
        const Uint x, const Uint y, const Uint z,
        const FlagField<Uint>& flagField,
        Uint& wallX, Uint& wallY, Uint& wallZ,
        Real& distance) const;

private:
    std::unique_ptr<WallModel> wallModel_;
    Real maxSearchRadius_;
};

} // namespace turbulence
} // namespace walberla

#endif // _WALBERLA_TURBULENCE_WALL_MODEL_H