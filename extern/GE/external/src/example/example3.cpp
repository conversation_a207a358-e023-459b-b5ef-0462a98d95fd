/*******************************************************************************
ConminC++, to solve constrained or unconstrained function minimization.
Copyright (C) 1999  <PERSON><PERSON> <PERSON><PERSON> Dunn

This program is free software; you can redistribute it and/or
modify it under the terms of the GNU General Public License
as published by the Free Software Foundation; either version 2
of the License, or (at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this program; if not, write to the Free Software
Foundation, Inc., 59 Temple Place - Suite 330, Boston, MA  02111-1307, USA.
*******************************************************************************/

#pragma hdrstop
#ifdef UNIX
#define USEUNIT(x) // x
#else
#include <vcl.h>
#endif
#include "example3_analysis.h"
//---------------------------------------------------------------------------
#pragma argsused

int main(int argc, char **argv)
{
	const int ndv=2, ncon=6, ncap1=10;

//   Initialize
Ex3 ex3(ndv, ncon, ncap1);
//   set parameters 
ex3.iprint = Debug4;
ex3.info = Cal_all;
ex3.itmax = 40;
ex3.infog = All;
ex3.nfdg = Combined;
ex3.nside = Bounded;
ex3.icndir = 0;
ex3.nscal = 0;
ex3.fdch = 0;
ex3.fdchm = 0.;
ex3.ct = 0.;
ex3.ctmin = 0.;
ex3.ctl = 0.;
ex3.ctlmin = 0.;
ex3.theta = 0.;
ex3.phi = 0.;
ex3.delfun = 0.;
ex3.dabfun = 0.;
ex3.linear_obj = Linear;
ex3.itrm = 0;
ex3.alphax=0.;
ex3.abobj1=0;

//   Initialize constraint identification vector, isc.
for(int j=1; j<=ncon; j++)
  ex3.isc[j] = 0;

//   Initialize x-vector, upper and lower bound
for(int j=1; j<=ndv; j++)
  {
  ex3.x[j]=1.;
  ex3.vub[j]=1.0e+10;
  ex3.vlb[j]=.001;
  }

int Nlim=ex3.itmax*(ndv+5);
Igoto igoto;
igoto=ex3.igoto = Finished;
//
//  iterative part of analysis
//
for(int i=1; i<Nlim; i++)
  {
  int loopcnt=i;
  //
  //  call the optimization routine conmin
  //
  if ((igoto=ex3.conmin()) != Finished) loopcnt=-999;
  //
  //  analysis module
  //
  ex3.analysis();
  if( igoto == Finished) break;
  }

        return 0;
}
 
