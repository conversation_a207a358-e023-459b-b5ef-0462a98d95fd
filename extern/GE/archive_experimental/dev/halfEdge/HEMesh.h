#ifndef HEMESH_HH
#define HEMESH_HH

// =============================================================================
// 
// 
// =============================================================================
//
namespace ge
{ 
	struct HEMesh
	{
		// =============================================================
		/*!@brief HEMesh constructor */  
		// =============================================================
		HEMesh(const int& nv,const int& nf):m_nvertices(nv),m_nfaces(nf)
		{
			m_halfedges.reserve(3*m_nfaces) ; //!Reserves a storage for a vector object
			VERTICES.reserve(m_nvertices);
		}

		// =============================================================      
		/*!@brief build halfEdge table	*/ 
		//==============================================================
		 inline void BuildHEDataStructure () ;

      
		//==============================================================
		/*!
		@brief build halfEdge table 
		@note  it's not optimized !!!
		*/ 
		//==============================================================
		inline void Extra () ;

	private : 
		// =============================================================
		// HEMesh privat data -> representation
		// ============================================================= 
		int		m_nvertices ;// number of vertices
		int		m_nfaces    ;// number of faces (triangles)
		HalfEdges	m_halfedges; // each facet have at least "3 he"
		EdgeMap		m_edgemap;   // help data-structor
		// =============================================================
	public :
		//==============================================================
		// help function -> print 
		//==============================================================
		inline void Info () const; // for debug stuff 
	private :
		//==============================================================
		inline void PrintFaces ()const ;
	
		//==============================================================
		//
		//==============================================================
		inline void PrintVertices ()const ;

		//==============================================================
		//
		//==============================================================
		 inline void PrintEdgeMap ()const  ;	

		//==============================================================
		//
		//==============================================================
		inline void PrintHEtable () const ;
	};

// =============================================================================
//  end class HEMesh
// =============================================================================







	//======================================================================  
	/*!@brief build halfEdge table	*/ 
	//======================================================================
	inline void HEMesh::BuildHEDataStructure ()
	{
		int const* face = faces; // pointer to first element  faces[0]  
		int v0 , v1 , v2;

		for (int f = 0 ; f < m_nfaces ; ++f)
		{
			// increment each time the pointer with ++ 
			v0 = *face++;	// increment
			v1 = *face++;	// 
			v2 = *face++;	// 
			// std::cout << " f ["<<f<< "]  <v0, v1, v2>   <"<<
			// v0<< " , " <<
			// v1<< " , " <<
			// v2<<">"<<std::endl;

			int he0 = 3*f;
		  
		  
		  
		        // Remarqu: maybe its better like this:
		        // (v0,v1) , (v1,v2)and (v2,v0)
			m_edgemap[Edge (v1 , v2) ] =  he0      ;
			// Edge e00 (v1 , v2) ;//edgemap.insert (std::pair< Edge,int> (e00,he0 ) );
			m_edgemap[Edge (v2 , v0) ] =  he0 + 1  ;
			// Edge e10 (v2 , v0) ;//edgemap.insert (std::pair< Edge,int> (e10,he0+1) );
			m_edgemap[Edge (v0 , v1) ] =  he0 + 2  ;// Edge e20 (v0 , v1) ;
			//edgemap.insert (std::pair< Edge,int> (e20 , he0 + 2) );

			m_halfedges.push_back ( std::pair<int,int> (v2 , -1)) ; // halfedges[he0] 
			m_halfedges.push_back ( std::pair<int,int> (v0 , -1)) ; // halfedges[he0+1] 
			m_halfedges.push_back ( std::pair<int,int> (v1 , -1)) ; // halfedges[he0+2]     

			// opposite direction too
			Edge e0 (v2 , v1);   


			EdgeMap::iterator it = m_edgemap.find (e0 ) ;
			if ( it!= m_edgemap.end() ) /*edge found	*/
			{
				int opp = (*it).second;
				m_halfedges[opp].second = he0;
				m_halfedges[he0].second = opp;
			}
		  
			/*		*/
			Edge e1 (v0 , v2); 
			EdgeMap::iterator it2 = m_edgemap.find (e1 ) ;
			if ( it2!= m_edgemap.end() )/*edge found	*/
			{	
				int opp = (*it2).second;
				m_halfedges[opp].second    =  he0+1;
				m_halfedges[he0+1].second =  opp;
			}
		    
			 /* */  
			Edge e2 (v1 , v0);
			EdgeMap::iterator it3 = m_edgemap.find (e2) ;
			if ( it3!= m_edgemap.end() )/*edge found */
			{
				int opp = (*it3).second;
				m_halfedges[opp].second   = he0 + 2;
				m_halfedges[he0+2].second = opp ;
			}
		}
	} // end BuildHEDataStructure ()
      


	//======================================================================
	/*!
		@brief build halfEdge table 
		@note  it's not optimized !!!
	*/ 
	//======================================================================
	inline void HEMesh::Extra()
	{
	//	typedef std::pair< int, int >  VerID ;		//! <vertex_id, He_id >
		std::vector< VerID > Vertex_id ;
	//	typedef std::pair  <int , int>  Halfedge;	//! < v_end ,  opp >
	//	typedef std::vector<Halfedge > HalfEdges;	//!  HalfEdges table 
		std::vector< Halfedge >::const_iterator  it;
		int count = 0 ;
		for  (it = m_halfedges.begin() ; it != m_halfedges.end() ; ++ it)
		{
			//int v_end  = (*it).first;
			int opp    = (*it).second; 
			int tmp    = m_halfedges [opp].first ;
			Vertex_id.push_back(std::pair<int,int> ( tmp, count ) ) ; 
			//cout << " he ["<< count << "]  <v_end , opp>   <"<< 
			//               v_end <<" , "<< opp <<">"<<endl;
			++count ;
		}
	}


	//=====================================================================
	// help function -> print 
	//=====================================================================
	inline void HEMesh::Info () const// for debug stuff 
	{
		std::cout <<"\n==============\n Vertices :\n==============\n";	
		PrintVertices() ;
		std::cout <<"\n==============\n Faces    :\n==============\n";
		PrintFaces() ;
		std::cout <<"\n==============\n EdgeMap  :\n==============\n";
		PrintEdgeMap() ;
		std::cout<<"\n==================\n Half-edges table :\n==================\n";
		PrintHEtable() ;
		std::cout <<std::endl;
	}


	//=====================================================================
	// 
	//=====================================================================
	inline void HEMesh::PrintFaces ()const
	{
		int const* face = faces; // pointer to first element  faces[0]  
		int v0 , v1 , v2;

		for (int f = 0 ; f < m_nfaces ; ++f)
		{
			v0 = *face++;
			v1 = *face++;
			v2 = *face++; 
			std::cout << " f ["<<f<< "]  <v0, v1, v2>   <"<<
				v0<<" , "<<
				v1<<" , "<<
				v2<<">"<<	std::endl;   
		}
	}

	//=====================================================================
	//
	//=====================================================================
	inline void HEMesh::PrintVertices ()const
	{
		float const* vertex = vertices;
		float x , y , z ;
		for  (int i = 0 ; i < m_nvertices ; i++ )
		{
			x = *vertex++; // 
			y = *vertex++; // 
			z = *vertex++; // 
			cout << " vertex ["<< i<< "]  <x, y, z>   <"<<
			 x <<" , "<< y <<" , "<< z <<">"<<endl;
		// optimizing 
		// cout << " vertex ["<< i<< "]  <x, y, z>   <"
		//	 << *vertex++ <<" , "<< *vertex++ <<" , "<< *vertex++ <<" >\n";
	
		}
	}

	//=====================================================================
	//
	//=====================================================================
	inline void HEMesh::PrintEdgeMap ()const 
	{
		std::map < Edge, int>::const_iterator m_it ;  
		 cout<< "they are  " << m_edgemap.size() << " edges."<< endl ;
		for (m_it = m_edgemap.begin() ; m_it != m_edgemap.end() ; ++ m_it)
		{
			Edge  e    = (*m_it).first  ; // e <v_start, v_end>
			int edgeindex = (*m_it).second ; //   
			cout << " Map ["<< edgeindex << "]  <v_start , v_end>   <"<<
				e.first<<" , "<< e.second  <<">"<<endl;
		} 
	}

	//=====================================================================
	//
	//=====================================================================
	inline void HEMesh::PrintHEtable () const
	{
		std::vector< Halfedge >::const_iterator  it;
		std::cout<< "they are  " << m_halfedges.size() << " half edges."<< std::endl ;
		int count = 0 ;
		for  (it = m_halfedges.begin() ; it != m_halfedges.end() ; ++ it)
		{
			int v_end  = (*it).first;
			int opp    = (*it).second;
			std::cout << " he ["<< count << "]  <v_end , opp>   <"<< 
				 v_end <<" , "<< opp <<">"<<std::endl;
			++count ;
		}
	}
	//=====================================================================
} // end namespace ge

#endif
