 template<class SingletonBaseClass>
 class Singleton : public SingletonBaseClass
 {
  public:
    /** Singleton creation function */
    inline static Singleton & instance()
    {
      if(_instance.get() == NULL)
      {
        _instance = auto_ptr<Singleton<SingletonBaseClass> >(new Singleton);
        assert(_instance.get() != NULL);
      }
      return *_instance;
    }

  protected:
    /** Singleton instance holder */
    static auto_ptr< Singleton<SingletonBaseClass> > _instance;
 };


 /** static instance definition */
 template<class SingletonBaseClass> 
 auto_ptr<Singleton<SingletonBaseClass> > Singleton<SingletonBaseClass>::_instance;
 
 
 
// =============================================================================
// =============================================================================
//  If we have a class called MyClass:
//  The constructor can either be public or protected depending on the need. 
//  A protected constructor can ensure that the class will only be possible to 
//  be constructed by subclasses in this case the Singleton MixIn. 
// =============================================================================
 class MyClass
 {
  public:
    virtual void bar();
    virtual ~MyClass();
  protected:
    MyClass();
 };
// =============================================================================
 
 
 // this technique can be use in 2 ways:
//  a) 
 
	void foo()
	{
		Singleton<MyClass>::instance().bar();
	}


// or b)

	class SingletonMyClass : public Singleton<MyClass>
	{
		
	};


	void foo()
	{
		SingletonMyClass::instance().bar();
	}



 
 
 
 
 
