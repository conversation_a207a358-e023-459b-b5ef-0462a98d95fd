#include <iostream>



using namespace std;



struct Filter
{
	struct Retry{
	    virtual const char* message () {return 0 ; } // public
	};
	virtual void start  ()  {} 
	virtual int  read   () =  0 ; // pure 
	virtual void write  ()  {} 
	virtual void compute()  {}
	virtual int  result ()  {}
	virtual int retry (Retry& m )
	{
	      std::cerr << m.message() << "\n" ;
	      return 2 ;
	}
	virtual ~Filter () {}
};

// =============================================================================
inline static int main_loop (Filter* p)
{
	for (;;)
	  try {
		  p-> start () ;
		  while (p -> read () )
		  {
		      p-> compute() ;
		      p-> write  () ;
		  }
		  return p -> result () ;
	  }
	  catch (Filter::Retry& m){
	      if (int i = p->retry(m) )
		return i ;
	  }
	      
	  catch (std::exception & e ){
	    std::cerr << "Fatal Error\n" ;
	    return 1 ;
	  }
}
// =============================================================================
class My_Filter : public Filter
{
   std::istream& is ;
   std::ostream& os ;
   int nchar    ;
 public : // only pure
   //int  read   () { char c; is.get(c) ; std::cout<< "read : "<< c << std::endl;
   int  read   () { char c; is.get(c) ; std::cout<< "read : "<< c << std::endl;
   	return is.good () ; 
	}
   void compute() { nchar++ ; }
   int  result () {os << nchar << " Characters read\n";
     return 0 ; // important
   }
   My_Filter (std::istream& ii, std::ostream& oo) : is (ii), os (oo), nchar (0){}
};
// =============================================================================  
int main()
{
        My_Filter f (std::cin, std::cout) ;
        return main_loop (& f) ;
}
// =============================================================================
