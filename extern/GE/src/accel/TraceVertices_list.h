#ifndef TRACEVERTICES_LIST_HH
#define TRACEVERTICES_LIST_HH 


#include <iostream>

// =============================================================================
/*!
 * \class TraceVertices_list
 * \brief you can use this class for tracing/debuguing any Base-member function.
 *
 * TODO change the inpout, to log file instead to std::log
 * 
*/
// =============================================================================
namespace ge
{
  template <class Base>
  struct TraceVertices_list : public Base
  {
	    typedef Base     Configure ; // import Config
	    typedef typename Configure::FinalContainerType FinalContainerType ;

	    // then, Extract necessary info-types from FinalContainerType
	    typedef typename FinalContainerType::ElementType     ElementType ;
	    typedef typename FinalContainerType::size_type       size_type ;
	    typedef typename FinalContainerType::iterator        iterator  ;
	    typedef typename FinalContainerType::const_iterator  const_iterator;
	    typedef typename FinalContainerType::difference_type difference_type;
	    typedef typename FinalContainerType::reference       reference     ;
	    typedef typename FinalContainerType::const_reference const_reference;
	    
	    // to do , better use functor object to log file
	    inline std::ostream& TraceMe ()const {return std::clog << "Trace :" ;}
    public:
	    inline void Add (const ElementType& elem)
	    {
		  TraceMe() << "Add ( "<< elem << " )\n"  ;
		  Base::Add (elem);
	    }
	    
	  inline iterator Remove (iterator position)
	  {
	    TraceMe() <<"Remove element ( "<< *position << " ) \n";
	    return Base::Remove (position);
	  } 
			    
	  inline iterator Remove (iterator first, iterator last )
	  {
		TraceMe() <<"Remove range of elements ( " ;
		while (first != last )
		{ 
		    TraceMe() << *first << " , " ;
		    ++ first ;
		}
		  
		TraceMe() << " ) \n";
		return Base::Remove (first,last);
	  }
  };
  
}// end namespace ge
// =============================================================================

#endif 
