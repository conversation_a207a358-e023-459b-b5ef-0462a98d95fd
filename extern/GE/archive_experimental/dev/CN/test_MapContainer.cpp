// ============================================================================
/*
 *  test Program for Map configuration, following 
 *   GenVocb pattern ( or concept/ technique)
 *   Author: Cherif <PERSON>
 */ 
// ============================================================================



// ============================================================================
// includes 
// ============================================================================
#include "MapContainer.h"
#include "MapConfig.h"
// ============================================================================



	// ====================================================================
	//		 typedef Map_DefConfig
	// ====================================================================
	struct Map_Config1 // map default configuration
	{   
		typedef char  Key ; 
		typedef int   value_type ;
		typedef MapSelector<Key,value_type,1>::type  	MapType;
		// below  are not necessary
		typedef MapType::iterator  		Iterator ;
		typedef MapType::const_iterator		Const_Iterator;
		typedef std::pair<Key,value_type>   	pair; // to use like this :>	mymap.insert ( pair('z',200) );  
		typedef std::pair<Iterator,bool>  	Ret;
	};

int main()
{
	// ========================================================================
	//		 typedef Map_DefConfig
	// ========================================================================
	//
	MapContainer<Map_DefConfig> my_map_0 ;

	typedef MapContainer<Map_Config1> my_map_type_1 ;
	typedef Map_DefConfig::pair Pair ;
		
	my_map_type_1 my_map_1 ;
	
	//my_map_1.Add ( Pair ('a',100) );


	
	//my_map_1.Add ( my_map_type_1::pair ('z',200) );
	
	return (0) ;
}
//=============================================================================

