// Simple Channel Flow with Turbulence
// This tests our turbulence models with basic channel flow (no obstacles)
// Focuses on validating turbulence model functionality

simdata {
    // Simple channel domain
    domainX     60;       // Streamwise
    domainY     20;       // Cross-stream
    domainZ     20;       // Spanwise
    dx          0.001;    // Grid spacing
    
    // Flow parameters that satisfy physics checker
    Uref        0.05;     // Reference velocity
    Lref        0.020;    // Reference length (channel height)
    Re          10;       // Low Reynolds number for stability
    
    // Simulation control
    timesteps   200;      // Sufficient for flow development
    nu          1.0e-4;   // Kinematic viscosity
    rho         1.0;      // Density
    maxPermittedVel 0.08; // Maximum velocity limit
    
    dump;  // Output all computed parameters
}

// Test our Smagorinsky turbulence model
turbulence {
    model               smagorinsky;      // Use our Smagorinsky model
    smagorinskyConstant 0.18;            // Standard Smagorinsky constant
    filterWidth         2.0;             // Filter width = 2*dx
    strainRateMethod    nonequilibrium;  // Non-equilibrium method
}

// Simple channel flow setup
init {
    // Inlet (west) - uniform velocity profile
    west {
        vel_in {
            y_L     1..'domainY'-1;
            z_L     1..'domainZ'-1;
            ux_L    0.04;  // Inlet velocity
            uy_L    0.0;
            uz_L    0.0;
        }
    }
    
    // Outlet (east) - pressure outlet
    east {
        prs_nils {
            y_L     1..'domainY'-1;
            z_L     1..'domainZ'-1;
            prs_L   0.0;  // Zero gauge pressure
        }
    }
    
    // Channel walls (top and bottom) - no slip
    north {
        noslip {
            x_L     0..'domainX'+1;
            z_L     1..'domainZ'-1;
        }
    }
    
    south {
        noslip {
            x_L     0..'domainX'+1;
            z_L     1..'domainZ'-1;
        }
    }
}

// Output configuration for turbulence visualization
paraview {
    filename    simple_channel_turb;
    xstart      0;
    xend        60;
    ystart      0;
    yend        20;
    zstart      10;
    zend        10;       // Mid-plane slice
    writevelocity  1;
    writepressure  1;
    writeeddyvisc  1;     // Critical: output eddy viscosity to verify turbulence
    interval       50;    // Output every 50 steps
    physical       1;     // Use physical units
}

// Monitor turbulence quantities
simoutput {
    TURBULENT_KINETIC_ENERGY {
        time 25;  // Output TKE every 25 steps
    }
    EDDY_VISCOSITY_STATS {
        time 25;  // Output eddy viscosity statistics
    }
}

// Logging configuration
logging {
    logfile simple_channel_turbulence.log;
    append  0;
}
