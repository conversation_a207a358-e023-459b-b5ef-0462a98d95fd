# Makefile for turbulence model tests
CXX = g++
CXXFLAGS = -std=c++11 -O2 -Wall -I../src -I../extern/pe
LDFLAGS = -L../build/X9DAi_par/bin -lwalberla -L../build/X9DAi_par/extern/pe/lib -lpe

# Boost libraries (adjust paths as needed)
BOOST_LIBS = -lboost_system -lboost_filesystem

test_turbulence: test_turbulence_models.cpp
	$(CXX) $(CXXFLAGS) -o test_turbulence test_turbulence_models.cpp $(LDFLAGS) $(BOOST_LIBS)

clean:
	rm -f test_turbulence

.PHONY: clean
