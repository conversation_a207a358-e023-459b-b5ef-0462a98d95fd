#ifndef BINARY_DEREF_FUNCTION_HH
#define BINARY_DEREF_FUNCTION_HH

//==============================================================================
/*! 
  *@brief pointer trait class 
  *
*/
//==============================================================================

  template< typename T, template <typename S> class Ptr  >
  struct ptr_traits
  {
    typedef Ptr<T> const type;
  };

  
  template< typename T, template <typename S> class Ptr  >
  struct ptr_traits<T const&, Ptr> { // specialisation for const ref
    typedef Ptr<T> const type;
  };
  
  template <typename T, template <typename S> class Ptr>
  struct ptr_traits<T&, Ptr> { // specialisation for ref without const
    typedef Ptr<T> type;
  };



  
//==============================================================================
/*! 
  *@brief binary trait class 
  *
*/
//============================================================================== 
  template <
	    template <typename T> class Ptr,
	    typename Fct
	    >
  struct binary_deref_function
  {
    // typedefs required to be a binary function object:
    typedef typename ptr_traits<typename Fct::first_arg_type, Ptr>::type first_arg_type;
    typedef typename ptr_traits<typename Fct::scnd_arg_type, Ptr>::type scnd_arg_type;
    typedef typename Fct::result_type       result_type;
 
    // constructor:
    binary_deref_function(Fct const& f = Fct()): fct(f) {}
 
    // function call operators:
    result_type operator()(first_arg_type& arg1,scnd_arg_type& arg2)
      { return fct(*arg1, *arg2); }

    result_type operator()(first_arg_type& arg1,scnd_arg_type& arg2) const
      { return fct(*arg1, *arg2); }
 
  private: // functor
    Fct fct;
  };

  
  
  
  /* template adaptor function */
  template   < template <typename T> 
			class Ptr,
		typename Result,
		typename Arg1, 
		typename Arg2 >
		binary_deref_function<Ptr, std::pointer_to_binary_function<Arg1, Arg2, Result> 
	      >
  binary_deref_fct(Result (*fct)(Arg1, Arg2)) 
  {
    return std::ptr_fun(fct);
  }
    
  
  
  
  
  
#endif 

