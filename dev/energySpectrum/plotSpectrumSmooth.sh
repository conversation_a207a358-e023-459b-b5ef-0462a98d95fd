#!/bin/bash

rm -f d2data*.png 

declare -a   NAMES=()

j=0
for i in d2data_*.dat ; 
    do  
	## gnuplot 
	#data="$i"
	#output=$i.png 
	#echo "data  " ${data} 	
	#echo "output" ${output} 
	#gnuplot -e "filename='${data}'; outputname='${output}'"  plotSpectrum.gnu 
#gnuplot<<EOF
	filename="$i"
	outputname=$i.png 


fname=$(basename "$filename")
extension="${fname##*.}"
fname="${fname%.*}"
time=${fname#*_}
echo "extension" $extension   "     fname " $fname  "   time   " ${fname#*_}  
#echo "extension" $extension   "     fname " $fname  "   time   " "${fname%%_*}"

#NAME=`echo "$i" | cut -d'.' -f1`
#EXTENSION=`echo "$i" | cut -d'.' -f2`
#echo $EXTENSION "  " $NAME 

gnuplot -persist <<-EOFMarker
set term  pngcairo size 900,700 enhanced 
set output "$fname.png"
set grid
set xlabel "Wavenumber {/Symbol k}"
set ylabel "Energy density, E({/Symbol k})"
set logscale xy
set xrange [:256]
#set arrow   from 64,graph(0,0) to 64,graph(1,1) nohead lt 1 lc 0 
set arrow  from 64,graph(0,0) to 64,graph(1,1) nohead lt 2 dashtype 2  lc 0 
#set title  "Energy spectrum"
#set title "Energy Spectrum"
set title "time = $time [s]"
set xtics  1, 8 ,10
set xtics add (16)
set xtics add (32)
set xtics add (64)
set xtics add (128)
set xtics add (256)
set grid xtics ytics mxtics mytics

plot "$filename" using 2 w l lw 2  smooth acsplines  t "LBM t=",\
   "../../Jimez/0.00" w lp lw 2 ps 0.5 pt 5 t "DNS t=0.00" ,\
   "../../Jimez/1.45" w lp lw 2 ps 0.5 pt 5 t "DNS t=1.45"
EOFMarker



	NAMES[j]=$i
	j+=+1
done 




#j=0 
#for i in "${NAMES[@]}"
#	do :
#	 echo "NAME[ "  " ] =" $i
#	 j+=1
#done


#echo "NAMES items and indexes:"
#for index in ${!NAMES[*]}
#do
#	    printf "%4d: %s\n" $index ${NAMES[$index]}
#done


