// The init block supports an inner block which specifies objects inside the domain.
// These objects are specified according to the standards of the pe Physical Engine.
// Currently, spheres, capsules, boxes, planes and clusters of the pre-mentioned object
// types are supported.
// For further details see documentation of pe.
// In contrast to pe, nearly for every key word, an appropriate "lattice key word" exists,
// which has the same name plus "_L", and receives lattice numeration instead of coordinates.
// All values can be numbers or expressions that will be evaluated by PhysicalCheck class, thus,
// every quantity specified directly or indirectly in simdata block can be used.
// Additionally to the parameters supported by pe, a slip parameter can be specified. If omitted,
// noslip boundary condition will be used for this object.
// For explicit examples see the end of this file.


init {
  include BoundaryDescriptions.par

  // The setup of a sphere object will create a cluster containing a single sphere.
   sphere {
      id x;                                // User-specific ID of the sphere
      position <x,y,z>;                    // Global position of the sphere
      radius r;                            // Radius of the sphere
      density d;                           // Density of the sphere
      mass m;                              // Total mass of the sphere
      linear <vx,vy,vz>;                   // Sets the linear velocity of the sphere
      angular <ax,ay,az>;                  // Sets the angular velocity of the sphere
      restitution cor;                     // Coefficient of restitution of the sphere
      translate <dx,dy,dz>;                // Translation of the sphere
      rotate <xangle,yangle,zangle>;       // Euler rotation of the sphere
      fixed;                               // Fixes the sphere's position
      invisible;                           // Makes the sphere invisible
      slip value;                          // 1.0 -> noslip, 0.0 -> freeslip, else -> partslip
   }

// Necessary parameters for a shpere:
//  - id: the user-specific ID of the sphere.
//  - position: the global position of the sphere's center of mass.
//  - radius: the radius of the sphere.
//  - density: the density of the sphere. Either density or mass have to be specified.
//  - mass: the total mass of the sphere. Either density or mass have to be specified.
//
// Optional parameters:
//  - linear: specifies the linear velocity of the sphere in x-, y- and z-direction.
//  - angular: specifies the angular velocity of the sphere around the x-, y- and z-axis.
//  - restitution: sets the coefficient of restitution of the sphere. The coefficient has
//       to be in the range \f$ [0..1] \f$. If the restitution is not set, the coefficient
//       of restitution of the simulation world will be used.
//  - translate: translates the sphere from the specified position by the displacement
//       vector <dx,dy,dz>. For each sphere, several translate commands may be specified.
//       The translate command is always executed after the position command, regardless
//       of the order of the commands.
//  - rotate: specifies a rotation around the x-, y- and z-axis, in this order. The
//       given angles are Euler angles in radian measurement. In order to change the order
//       of rotations, several rotations can be specified.
//  - fixed: fixes the global position of the sphere within the global world frame.
//  - invisible: if set, the sphere will be invisible and will not be shown in any
//       visualization.



// The setup of a box object will create a cluster containing a single box.

   box {
      id x;                                // User-specific ID of the box
      position <x,y,z>;                    // Global position of the box
      lengths <lx,ly,lz>;                  // Side lengths of the box
      density d;                           // Density of the box
      mass m;                              // Total mass of the box
      linear <vx,vy,vz>;                   // Sets the linear velocity of the box
      angular <ax,ay,az>;                  // Sets the angular velocity of the box
      restitution cor;                     // Coefficient of restitution of the box
      translate <dx,dy,dz>;                // Translation of the box
      rotate <xangle,yangle,zangle>;       // Euler rotation of the box
      fixed;                               // Fixes the box's position
      invisible;                           // Makes the box invisible
      slip value;                          // 1.0 -> noslip, 0.0 -> freeslip, else -> partslip
   }

// Necessary parameters for a box:
//  - id: the user-specific ID of the box.
//  - position: the global position of the center of mass of the box.
//  - lengths: lengths of the sides in x-, y- and z-direction.
//  - density: the density of the box. Either density or mass have to be specified.
//  - mass: the total mass of the box. Either density or mass have to be specified.
//
// Optional parameters:
//  - linear: specifies the linear velocity of the box in x-, y- and z-direction.
//  - angular: specifies the angular velocity of the box around the x-, y- and z-axis.
//  - restitution: sets the coefficient of restitution of the box. The coefficient has
//       to be in the range \f$ [0..1] \f$. If the restitution is not set, the coefficient
//       of restitution of the simulation world will be used.
//  - translate: translates the box from the specified position by the displacement
//       vector <dx,dy,dz>. For each box, several translate commands may be specified.
//       The translate command is always executed after the position command, regardless
//       of the order of the commands.
//  - rotate: specifies a rotation around the x-, y- and z-axis, in this order. The
//       given angles are Euler angles in radian measurement. In order to change the
//       order of rotations, several rotations can be specified.
//  - fixed: fixes the global position of the box within the global world frame.
//  - invisible: if set, the box will be invisible and will not be shown in any
//       visualization.


// The setup of a capsule object will create a cluster containing a single capsule lying
// along the x-axis.

   capsule {
      id x;                                // User-specific ID of the capsule
      position <x,y,z>;                    // Global position of the capsule
      radius radius;                       // Radius of the capsule
      length l;                            // Length of the cylinder part
      density d;                           // Density of the capsule
      mass m;                              // Total mass of the capsule
      linear <vx,vy,vz>;                   // Sets the linear velocity of the capsule
      angular <ax,ay,az>;                  // Sets the angular velocity of the capsule
      restitution cor;                     // Coefficient of restitution of the capsule
      translate <dx,dy,dz>;                // Translation of the capsule
      rotate <xangle,yangle,zangle>;       // Euler rotation of the capsule
      fixed;                               // Fixes the capsule's position
      invisible;                           // Makes the capsule invisible
      slip value;                          // 1.0 -> noslip, 0.0 -> freeslip, else -> partslip
   }

// Necessary parameters for a capsule:
//  - id: the user-specific ID of the capsule.
//  - position: the global position of the capsule's center of mass.
//  - radius: the radius of the capsule.
//  - length: the length of the cylinder part within the capsule.
//  - density: the density of the capsule. Either density or mass have to be specified.
//  - mass: the total mass of the capsule. Either density or mass have to be specified.
//
// Optional parameters:
//  - linear: specifies the linear velocity of the capsule in x-, y- and z-direction.
//  - angular: specifies the angular velocity of the capsule around the x-, y- and z-axis.
//  - restitution: sets the coefficient of restitution of the capsule. The coefficient has
//       to be in the range \f$ [0..1] \f$. If the restitution is not set, the coefficient
//       of restitution of the simulation world will be used.
//  - translate: translates the capsule from the specified position by the displacement
//       vector <dx,dy,dz>. For each capsule, several translate commands may be specified.
//       The translate command is always executed after the position command, regardless
//       of the order of the commands.
//  - rotate: specifies a rotation around the x-, y- and z-axis, in this order. The
//       given angles are Euler angles in radian measurement. In order to change the order
//       of rotations, several rotations can be specified.
//  - fixed: fixes the global position of the capsule within the global world frame.
//  - invisible: if set, the capsule will be invisible and will not be shown in any
//       visualization.


// The setup of a plane object will create a cluster containing a single plain with the
// specified normal and the specified distance/displacement from the origin of the global
// world frame.

   plane {
      id x;                                // User-specific ID of the capsule
      normal <a,b,c>;                      // Normal of the plane
      displacement d;                      // Distance to the origin of the global frame
      restitution cor;                     // Coefficient of restitution of the plane
      rotate <xangle,yangle,zangle>;       // Euler rotation of the plane
      invisible;                           // Makes the plane invisible
      slip value;                          // 1.0 -> noslip, 0.0 -> freeslip, else -> partslip
   }

// Necessary parameters:
//  - id: the user-specific ID of the plane.
//  - normal: the normal of the plane pointing to the outside of the plane.
//  - displacement: the distance to the origin of the global world frame.
//
// Optional parameters:
//  - restitution: sets the coefficient of restitution of the plane. The coefficient has
//       to be in the range \f$ [0..1] \f$. If the restitution is not set, the coefficient
//       of restitution of the simulation world will be used.
//  - rotate: specifies a rotation around the x-, y- and z-axis, in this order. The
//       given angles are Euler angles in radian measurement. In order to change the order
//       of rotations, several rotations can be specified.
//  - invisible: if set, the plane will be invisible and will not be shown in any
//       visualization.



// The setup of a cluster object will create a cluster containing an arbitrary number of spheres,
// boxes, capsules and planes. Between the contained objects, links can be defined, which
// will be automatically finished by setting the sections. A cluster object represents the most
// general object to be created by the cluster reader.

   cluster {
      id x;                                // User-specific ID of the cluster
      center <x,y,z>;                      // Moves the cluster's center of mass to <x,y,z>
      linear <vx,vy,vz>;                   // Sets the linear velocity of the cluster
      angular <ax,ay,az>;                  // Sets the angular velocity of the cluster
      restitution cor;                     // Coefficient of restitution of the cluster
      translate <dx,dy,dz>;                // Translation of the cluster
      rotate <xangle,yangle,zangle>;       // Rotates the entire cluster by the Euler angles
                                           //   'xangle', 'yangle' and 'zangle'
      fixed;                               // Fixes the cluster's position
      invisible;                           // Makes the entire cluster invisible
      slip value;                          // 1.0 -> noslip, 0.0 -> freeslip, else -> partslip

      sphere {
         id x;                             // Creates the single sphere 'x' with radius 'r'
         position <x,y,z>;                 //   at the the global position <x,y,z> within the
         radius r;                         //   cluster. Either the density or the mass have
         density d;                        //   to be specified.
         mass m;                           //   Optionally, the sphere's coefficient of
         restitution cor;                  //   restitution can be set individually and
         translate <dx,dy,dz>;             //   the sphere can be translated and rotated
         rotate <xangle,yangle,zangle>;    //   within the cluster. If the cluster is not
         invisible;                        //   invisible the sphere can be made invisible.
         slip value;                       // 1.0 -> noslip, 0.0 -> freeslip, else -> partslip
      }
      box {
         id x;                             // Creates the single box 'x' with the side lengths
         position <x,y,z>;                 //   <lx,ly,lz> at the global position <x,y,z> within
         lengths <lx,ly,lz>;               //   the cluster. Either the density or the mass have
         density d;                        //   to be specified.
         mass m;                           //   Optionally, the box's coefficient of
         restitution cor;                  //   restitution can be set individually and
         translate <dx,dy,dz>;             //   the box can be translated and rotated
         rotate <xangle,yangle,zangle>;    //   within the cluster. If the cluster is not
         invisible;                        //   invisible the box can be made invisible.
         slip value;                       // 1.0 -> noslip, 0.0 -> freeslip, else -> partslip
      }
      capsule {
         id x;                             // Creates the single capsule 'x' with the radius 'r'
         position <x,y,z>;                 //   and the cylinder length 'l' within the cluster.
         radius r;                         //   Either the density or the mass have to be
         length l;                         //   specified.
         density d;                        //   Optionally, the capsule's coefficient of
         mass m;                           //   restitution can be set individually and
         restitution cor;                  //   the capsule can be translated and rotated
         translate <dx,dy,dz>;             //   within the cluster. If the cluster is not
         rotate <xangle,yangle,zangle>;    //   invisible the capsule can be made invisible.
         invisible;                        //
         slip value;                       // 1.0 -> noslip, 0.0 -> freeslip, else -> partslip
      }
      plane {
         id x;                             // Creates the single plane 'x' with the specified
         normal <a,b,c>;                   //   normal and the distance 'd' from the origin of
         displacement d;                   //   the global world frame. Optionally, the plane's
         restitution cor;                  //   coefficient of restitution can be set individually
         rotate <xangle,yangle,zangle>;    //   and the plane can be rotated within the cluster.
         invisible;                        //   If the cluster is not invisible the plane can be
                                           //   made invisible
         slip value;                       // 1.0 -> noslip, 0.0 -> freeslip, else -> partslip
      }
   }

// Necessary parameters for a cluster:
//  - id: the user-specific ID of the cluster.
//  - at least one sphere or box has to be specified
//
// Optional parameters:
//  - center: Places the cluster at the global coordinates <x,y,z>. The center command
//       may change the position of all objects within the cluster depending on their relative
//       distance to the center of the cluster.
//  - linear: Sets the linear velocity of the entire cluster. Since the entire rigid body can
//       have only one linear velocity, this command affects both the objects commands before
//       and after the linear command.
//  - angular: Sets the angular velocity of the entire cluster. Since the entire rigid body can
//       have only one angular velocity, this command affects both the objects commands before
//       and after the angular command.
//  - restitution: Sets the coefficient of restitution of the cluster. All specified objects
//       within the cluster that are not assigned an individual coefficient of restitution will
//       be given this restitution. The coefficient has to be in the range \f$ [0..1] \f$. If
//       the restitution is not set, the cluster will be given the coefficient of restitution
//       of the simulation world.
//  - translate: translates the entire cluster from the specified center by the displacement
//       vector <dx,dy,dz>. For each cluster, several translate commands may be specified.
//       The translate command is always executed after the center command, regardless of the
//       order of the commands.
//  - rotate: Rotates all objects of the cluster around the x-, y- and z-axis according to
//       the Euler angles <xangle,yangle,zangle>. The rotate command affects all objects of
//       the cluster, even those specified after the rotate command. The order of rotations
//       is first around the x-axis, then the y-axis and last around the z-axis. All angles are
//       given in radian degrees. In order to change the order of rotations, several rotations
//       can be specified.
//  - fixed : Fixes the cluster's global position to its current location. The fixed
//       command may appear before or after the center command.
//  - invisible: If set, the entire cluster will be invisible and will not be shown in any
//       visualization. If the invisible command is specified for the cluster, all
//       invisible commands for the contained objects are neglected.
//  - sphere: Creates a sphere with the user-specific ID x, radius r and density d.
//       Alternatively, the mass can be specified instead of the density. The sphere is placed
//       at the global coordinates <x,y,z>. Optionally, the sphere's coefficient of restitution
//       can be set individually and the sphere can be translated and rotated within the cluster.
//       If the cluster is not set to be invisible, the invisible command can be used to
//       make a specific sphere invisible.
//  - box: Creates a box with the user-specific ID x, side lenghts <lx,ly,lz> and density
//       d. Alternatively, the mass can be specified instead of the density. The box
//       is placed at the global coordinates <x,y,z>. Optionally, the box's coefficient of
//       restitution can be set individually and the box can be translated and rotated within
//       the cluster. If the cluster is not set to be invisible, the invisible command can be
//       used to make a specific box invisible.
//  - capsule: Creates a capsule with the user-specific ID x, the radius r, the cylinder
//       length l and the density d. Alternatively, the mass can be specified instead of
//       the density. The capsule is placed at the global coordinates <x,y,z>. Optionally, the
//       capsule's coefficient of restitution can be set individually and the capsule can be
//       translated and rotated within the cluster. If the cluster is not set to be invisible,
//       the invisible command can be used to make a specific capsule invisible.
//  - plane: Creates a plane with the user-specific ID x, the normal n and the
//       distance/displacement from the origin of the global world frame d. Creating a
//       plane within a cluster makes the cluster infinite. The center of mass of the cluster
//       will be at the origin of the global world frame. Optionally, the plane's coefficient
//       of restitution can be set individually and the plane can be rotated within the cluster
//       around the origin of the global frame. If the cluster is not set to be invisible, the
//       invisible command can be used to make a specific plane invisible.



// Some explicit examples:

// Set a sphere at the center of the domain, with a diameter of half the domain height.
    sphere{
      id 1;
      position_L <'domainX'/2.0,'domainY'/2.0,'domainZ'/2.0>;
      radius_L 'domainZ'/4.0;
      mass 1;
      restitution 0.2;
      fixed;
      slip 1.0;    
    }

// Set a box at the end of the domain, having lengths that exceed the domain size.
    box {
     id 5;
     position <'xlength','ylength'/2.0,'zlength'/2.0>;
     lengths  <2*'dx',2*'dx',2*'dx'>;
     mass 0.2;
     restitution 0.4;
     slip 0.0;
     }    

// Create a cluster with a sphere and a box
     cluster {
      id 2;
      center_L <10,'domainY'/2.0,'domainZ'/2.0>; 
      angular <-0.0003,0.0003,-0.00030>;
      restitution 1.0;
      rotate_L <-1,0,0>;
                        
      fixed;

      sphere {
         id 3;
         position_L <10,'domainY'/2.0,'domainZ'/2.0>;
         radius_L 'domainZ'/4.0;
         mass 0.1;
         restitution 0.1;
         slip 0.5;
      }
      box {
         id 4;
         position_L <16,'domainY'/2.0,'domainZ'/2.0>;
         lengths_L <'domainZ'/4.0,'domainZ'/4.0,'domainZ'/4.0>;
         mass 0.2;
         restitution 0.4;
         slip 0.0;
      }
    }
}