
// =============================================================================
// includes 
// =============================================================================
#include <iostream>
#include <cassert>
#include "ObjManagerBase.h"
#include "../configure/Config.h"
#include "TriShapeBase.h"
// =============================================================================



using namespace ge;


	// generate spatial query on a 3D objects in the 3D world

	// =====================================================================
	// example of config_struct for Manager
	// =====================================================================
	template<class T>
	struct ConfigSample // sample how you would use GenVocab technique
	{
		//1. config first the basic type
		typedef T 		 			ObjectType ;  // TODO Base class of all triangles Mesh
		typedef const ObjectType		 	ObjectArgType;
		
		//2. List 	 
		typedef EmptyCopier<ObjectArgType>		Copier; // or
		//typedef PolymorphicCopier<ObjectType>	Copier;

		//typedef ElementDestructor ; //
		typedef EmptyDestructor   <ObjectType>   	Destructor ;
		typedef DynamicTypeChecker<ObjectType>   	TypeChecker;// or
		// typedef EmptyTypeChecker			TypeChecker;
		
		typedef boost::ptr_vector<ObjectType>  		ContainerType ;
		typedef std::less<ObjectType>   		Compare ;// i will use fo accosiative container

		typedef List <ConfigSample> 	 		FinalListType ;
	
	  	//3. Object manager
		typedef ObjManagerBase<ConfigSample>		FinalObjectManagerType ;
	};


	// =====================================================================
	template<typename Config>
	struct World
	{
		typedef Config configure ;
	
		typedef typename configure::FinalObjectManagerType  ObjectManagerType ;
		
		World() ;
	
	  private :
		static ObjectManagerType m_objects_manager ;
	};

	// initiaze the satatic world-member data -> m_objects_manager
	template<typename T>
	typename World<T>::ObjectManagerType World<T>::m_objects_manager ;
	
	// =====================================================================
	


int main ()
{
	
	  // ===================================================================
	  // typdefs
	  // ===================================================================

	  /*! @brief   Objects-Manager base class */
	  ConfigSample <int>::FinalObjectManagerType  obj_m  ; // save objects pointers of type int
	
	
	  /*! @brief   Object type */
	  typedef ConfigSample<int>::ObjectType m_type ;

	  // =======================================================================
  
  
  
	  m_type *alpha = new int ( 5 )  ;			///! @brief  add
	
	

	  std::cout << "-----> " << *alpha << std::endl ;

	  obj_m.Add ( *alpha  ) ;
	
	  obj_m.Add ( *new int(42)   ) ;
	
	
	  std::cout<<"Size(  ) -> " << obj_m.Size(  ) << std::endl ;


	// =====================================================================
	// test operator []
	// =====================================================================
	
	std::cout<< "First  Element  ->  "   << obj_m [0] << std::endl ;
	std::cout<< "Second Element  ->  "   << obj_m [1] << std::endl ;
	
	// =====================================================================
	// test modifier []
	// =====================================================================
	std::cout << "test modifier through [] operator \n" ;
	obj_m [0] =   0;
	obj_m [1] =   0;
	
  
	assert (obj_m [0]  ==   0 ) ;
	assert (obj_m [1]  ==   0 ) ;
	
	// =====================================================================
	// test modifier []
	// =====================================================================
	
	 ++ (obj_m [0]) ;
	
	
	assert (obj_m [0]  ==   1 ) ;
	
  
	 std::cout<< "First Element  ->  "   << obj_m [0]<< std::endl ;
	 std::cout<< "Second Element  ->  "   << obj_m [1]<< std::endl ;
	
	///@note ObjManagerBase -> Destructor shoud be called only one time !!!










	  // =======================================================================
	  // Real world test
	  // =======================================================================
	  // =======================================================================
	  // typdefs
	  // =======================================================================

	  /*! @brief   Objects-Manager base class */
	  ConfigSample <TriShapeBase >::FinalObjectManagerType  obj_m  ; // save objects pointers of type int


	  /*! @brief   Object type */
	  typedef ConfigSample<TriShapeBase>::ObjectType m_type ;











	return (0);
}
