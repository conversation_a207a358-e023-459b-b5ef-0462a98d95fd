#!/bin/bash

rm -f d2data*.png 

declare -a   NAMES=()

j=0
for i in d2data_*.dat ; 
    do  
	filename="$i"
	outputname=$i.png 
	
	fname=$(basename "$filename")
	extension="${fname##*.}"
	fname="${fname%.*}"
	time=${fname#*_}
	echo "extension" $extension   "     fname " $fname  "   time   " ${fname#*_}  

gnuplot -persist <<-EOFMarker
set term  pngcairo size 900,700 enhanced 
set output "$fname.png"
set grid
set xlabel "Wavenumber {/Symbol k}"
set ylabel "Energy density, E({/Symbol k})"
set logscale xy
set xrange [:32]
set arrow   from 32,graph(0,0) to 32,graph(1,1) nohead lt 1 lc 0 
#set title  "Energy spectrum"
#set title "Energy Spectrum"
set title "time = $time [s]"
set xtics  1, 8 ,10
set xtics add (16)
set grid xtics ytics mxtics mytics

plot "$filename" using 2 w l lw 2 t "LBM t=$time "

EOFMarker



	NAMES[j]=$i
	j+=+1
done 











#j=0 
#for i in "${NAMES[@]}"
#	do :
#	 echo "NAME[ "  " ] =" $i
#	 j+=1
#done


#echo "NAMES items and indexes:"
#for index in ${!NAMES[*]}
#do
#	    printf "%4d: %s\n" $index ${NAMES[$index]}
#done


