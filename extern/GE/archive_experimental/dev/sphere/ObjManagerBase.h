#ifndef OBJMANAGERBASE_HH
#define OBJMANAGERBASE_HH

#include <boost/shared_ptr.hpp>
#include <vector>

template<typename T >
struct Object_trait
{
  typedef T Object_type ;
};

template<typename T >
struct Object_trait<T*>
{
  typedef boost::shared_ptr<T> Object_type ;
};



template<class T>
struct config_test{
    typedef typename Object_trait<T>::Object_type  Object_type;
};



// accessed both by occurrence (std::vector) and by ordering relationship (std::set).
template<class T>  // object type
class ObjManagerBase 	   // manage set of objects
{
      public:
  //        typedef typename Object_trait<T>::Object_type  Object_type;
          typedef typename Object_trait<T>::Object_type  Object_type;
      public:
          inline std::size_t   Add    (const Object_type& object ) ;
          inline std::size_t   Find   (const Object_type& object ) ;	
          inline bool 	       Remove (const Object_type& object ) ;
      protected:
          inline bool Move   (Object_type& object ) ;
          inline bool Update (Object_type& object ) ;	
          inline bool Rotate (Object_type& object ) ; 

      protected: // TODO use Configure::VContainer, better
          std::vector< Object_type > m_m_Objects ;// objects memory manager
    public: // help functions 
          inline std::size_t Size(  ) const {
              return m_m_Objects.size(  ) ;
          }

};

template <class T>
inline std::size_t //  return 
ObjManagerBase<T>::Add (const typename ObjManagerBase<T>::Object_type& object )
{
      m_m_Objects.push_back ( object) ;
}



/*
template<class T>
inline ObjManagerBase::Move   (Object_typeT& object ){
      
}	

template<class T>
inline ObjManagerBase::Update (Object_typeT& object ) {

}	
	
template<class T>
inline ObjManagerBase::Rotate (Object_typeT& object ) {

}*/



#endif

