


namespace ge{
    template <class Derived>
	struct TriShapeBase // base class 
	{
	...
		//Interface , example how you can implement function
		inline void ImplementationInterface() { //@note don't use const
		      static_cast<Derived*>(this)->implementation(); 
		}

	};
	
	
	struct Sphere : TriShapeBase<Sphere> // derived class
	{
	    ...
	    ...
	      
	    inline void implementation() const {/* algorithm implementation. */	
	    
	    } 
	    ...
	    ...
	    ...
	};
		
} // end namespace ge


      