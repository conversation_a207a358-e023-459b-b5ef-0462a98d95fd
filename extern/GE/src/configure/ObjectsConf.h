
#ifndef OBJECTSCONF_HH
#define OBJECTSCONF_HH

// =============================================================================
/*
//	This program is free software; you can redistribute it and/or
//	modify it under the terms of the GNU General Public License
//	as published by the Free Software Foundation; either version 2
//	of the License, or (at your option) any later version.
//
//	This program is distributed in the hope that it will be useful,
//	but WITHOUT ANY WARRANTY; without even the implied warranty of
//	MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
//	GNU General Public License for more details.
//
//	You should have received a copy of the GNU General Public License
//	along with this program; if not, write to the Free Software
//	Foundation, Inc., 51 Franklin Street, Fifth Floor,
//	Boston, MA  02110-1301, USA.
//
//	
//	Copyright (C) 2010, Cherif <<EMAIL>>
*/
// =============================================================================


#include "Config_Def.h"

      // =======================================================================
      // TODO I don't know how I should deal in case of different Objects
      // Solution: use Object-Base* , and any type of objects should be derived  
      // from this base class
      // =======================================================================
      //!@note use small letters not big one to export typedefs
      // TODO add polygoneList  to this
      // =======================================================================
      template<class ObjectType> // BaseClass of all kind of (triangles mesh)objects
      struct ObjectsConf
      {
	      typedef ObjectType	      objectType;//triangles
	      typedef typename ConfigListDefault<objectType>::FinalListType  objectsList;//boost::ptr_vector<>
      };
      // =======================================================================

#endif

