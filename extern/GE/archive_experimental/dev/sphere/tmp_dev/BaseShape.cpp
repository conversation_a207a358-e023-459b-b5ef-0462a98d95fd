#include <iostream>
#include <vector>
#include <boost/shared_ptr.hpp>


using std::cout ;
using std::endl ;

class Ray {} ;
class Point {} ;


/* ================================================
      Realy good example of using template
 ================================================*/



// ================================================
// TODO implement a destructor
// rotate 
// move, translate 
// reshape,scale ... 
// Center of Mass (also known as center of gravity) and Radius of Gyration
// 
//! @note concerning the destructor, you have to use delete obj1 ; delete obj2 ,... ;delete objn 
//! 	  instead of delete obj1, obj2 ,,,,  objn
// ================================================






// ==========================================================================================================
template <class Derived>
struct ShapeBase
{
	ShapeBase ()   { id_ = total_++ ;}
	~ShapeBase ()  { id_ = total_-- ;  std::cout << "Base::Destructor::Trace ----> object [ " << id_ << " ]\n" ;}
	inline void interface() { static_cast<Derived*>(this)->implementation(); }//!@note don use const
	inline bool Intersect (const Ray& ray ) const { static_cast<Derived* >(*this) -> Intersect (ray) ;}
	inline void TraceInterface() {  static_cast<Derived*>(this)->Trace(); }
protected:
	int         id_	  ; //
	static int  total_; // TODO configure::lenghtype instead of int !!!
};

template <class Derived> int ShapeBase<Derived>::total_ = 0; 
// ==========================================================================================================
 
 
 
 
 
 
struct derived : ShapeBase<derived>
{
  public:
	inline void implementation() const 
	{ // ==========================
	  // algorithm implementation.
	} // ==========================
	inline void Trace() const  { std::cout << "derived::Trace ----> circle [ " << id_ << " ]\n" ;} 
	inline bool Intersect (const Ray& ray ) ;
	// TODO  private:  inline void Info (void)const {	std::cout<<"the total number of created objects == " << total_ << std::endl ;}
	// 	~derived () {} 
};
// ==========================================================================================================



// ============================================
/*
 * main program for testing ShapBase technique
 *
*/
// ============================================
int main ()
{
	ShapeBase<derived> * bb   = new derived  ;
	ShapeBase<derived> * bb_1 = new derived  ;
	ShapeBase<derived> * bb_2 = new derived  ;
	
	bb   -> TraceInterface() ;  
	bb_1 -> TraceInterface() ;  	
	bb_2 -> TraceInterface() ;	

	// delete bb_1; delete bb_2 ; delete bb_1 ;
		
		ShapeBase<derived> * bb_3 = new derived  ;
		bb_3 -> TraceInterface() ;		
	
	// first example : shared_ptr
	
	// =============================================================================
	//!@note when using the vector<shared_ptr<..> > , 
	//!its better to add/insert (new object) than (use only one object  + reset it )
	// =============================================================================
	typedef boost::shared_ptr< ShapeBase<derived> >  ShapeB_memory_manager ;
	std::vector< ShapeB_memory_manager > Shapes  ;
	
	
	for (int i = 0 ; i < 10 ; i++ )
	  Shapes.push_back  ( ShapeB_memory_manager  ( new  derived )   )  ;
	
	
		
		
	for (int i=0 ; i< Shapes.size() ; ++ i )
	      Shapes [i]-> TraceInterface() ; 


// Second example :	std::vector
// 	
      // 	Shapes.push_back (bb);
      // 	
      // 	for (int i=0 ; i< 10 ; ++ i )
      // 	{
      // 	  derived * tmp = new derived ;
      // 	  Shapes.push_back (tmp);
      // 	}
      // 	
      // 	
      // 	
      // 	for (int i=0 ; i< Shapes.size() ; ++ i )
      // 	{
      // 	  bb-> TraceInterface() ; 
      // 	}

	
//     	delete bb   ;
//     	delete bb_1 ;
// 	delete bb_2 ;
// 	delete bb_3 ;
	
      	return (0) ;
}
