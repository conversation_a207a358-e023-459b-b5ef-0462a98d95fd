#pragma once

#include "../../Definitions.h"
#include "core/math/Vector3.h"
#include "domain_decomposition/BlockDataID.h"
#include "domain_decomposition/IBlock.h"
#include "../mesh/TriangleMesh.h"
#include "VoxelData.h"
#include "Voxelizer.h"
#include "FlagFieldMapper.h"
#include <memory>
#include <vector>

namespace walberla {
namespace curvedboundary {

// Handles voxelization updates for moving meshes
class MovingMeshHandler {
public:
    // Moving mesh data
    struct MovingMesh {
        std::shared_ptr<TriangleMesh> mesh;
        Uint meshID;
        Vector3<Real> velocity;
        Vector3<Real> angularVelocity;
        Vector3<Real> rotationCenter;
        FlagFieldMapper::MeshMapping mapping;
        
        // Tracking data
        Vector3<Real> previousPosition;
        Matrix3<Real> previousRotation;
        std::shared_ptr<VoxelDataField> previousVoxelization;
        
        MovingMesh(std::shared_ptr<TriangleMesh> m, Uint id)
            : mesh(m), meshID(id), velocity(0.0), angularVelocity(0.0),
              rotationCenter(0.0), previousPosition(0.0) {
            previousRotation = Matrix3<Real>::getIdentity();
        }
    };
    
    MovingMeshHandler(std::shared_ptr<Voxelizer> voxelizer,
                     std::shared_ptr<FlagFieldMapper> flagMapper);
    
    // Add a moving mesh
    void addMovingMesh(std::shared_ptr<MovingMesh> movingMesh);
    
    // Update all moving meshes for a timestep
    void updateAllMeshes(
        Real dt,
        const Domain& domain,
        IBlock* block,
        BlockDataID flagFieldID,
        BlockDataID voxelDataFieldID
    );
    
    // Update a single mesh
    void updateMesh(
        MovingMesh& movingMesh,
        Real dt,
        const Domain& domain,
        IBlock* block,
        BlockDataID flagFieldID,
        BlockDataID voxelDataFieldID
    );
    
    // Get mesh by ID
    std::shared_ptr<MovingMesh> getMesh(Uint meshID) const;
    
    // Set mesh velocity
    void setMeshVelocity(Uint meshID, const Vector3<Real>& velocity);
    void setMeshAngularVelocity(Uint meshID, const Vector3<Real>& angularVel);
    
    // Predict future position for collision detection
    Vector3<Real> predictMeshPosition(Uint meshID, Real dt) const;
    
    // Handle mesh-mesh collisions
    void handleMeshCollisions(Real dt);
    
    // Get cells affected by mesh movement
    std::vector<Cell> getAffectedCells(
        const MovingMesh& mesh,
        Real dt
    ) const;
    
    // Integration with PE (Physics Engine)
    void syncWithPE(
        const std::vector<std::pair<Uint, Matrix4<Real>>>& peTransforms
    );
    
private:
    std::shared_ptr<Voxelizer> voxelizer_;
    std::shared_ptr<FlagFieldMapper> flagMapper_;
    std::vector<std::shared_ptr<MovingMesh>> movingMeshes_;
    
    // Update mesh position and orientation
    void updateMeshTransform(
        MovingMesh& mesh,
        Real dt
    );
    
    // Clear flags from old position
    void clearOldPosition(
        const MovingMesh& mesh,
        IBlock* block,
        BlockDataID flagFieldID
    );
    
    // Apply flags at new position
    void applyNewPosition(
        MovingMesh& mesh,
        const Domain& domain,
        IBlock* block,
        BlockDataID flagFieldID,
        BlockDataID voxelDataFieldID
    );
    
    // Handle transition cells
    void handleTransitionCells(
        const VoxelDataField& oldVoxel,
        const VoxelDataField& newVoxel,
        IBlock* block,
        BlockDataID flagFieldID
    );
    
    // Compute bounding box for moving mesh
    AABB computeMovementBounds(
        const MovingMesh& mesh,
        Real dt
    ) const;
};

// Helper class for mesh trajectory interpolation
class MeshTrajectory {
public:
    // Trajectory point
    struct Point {
        Real time;
        Vector3<Real> position;
        Matrix3<Real> rotation;
        Vector3<Real> velocity;
        Vector3<Real> angularVelocity;
    };
    
    MeshTrajectory();
    
    // Add trajectory point
    void addPoint(const Point& point);
    
    // Interpolate position at time t
    Vector3<Real> interpolatePosition(Real t) const;
    Matrix3<Real> interpolateRotation(Real t) const;
    
    // Get velocity at time t
    Vector3<Real> getVelocity(Real t) const;
    Vector3<Real> getAngularVelocity(Real t) const;
    
    // Clear trajectory
    void clear() { points_.clear(); }
    
private:
    std::vector<Point> points_;
    
    // Find surrounding points for interpolation
    std::pair<size_t, size_t> findSurroundingPoints(Real t) const;
};

// Collision handler for moving meshes
class MeshCollisionHandler {
public:
    struct CollisionInfo {
        Uint meshID1;
        Uint meshID2;
        Vector3<Real> contactPoint;
        Vector3<Real> contactNormal;
        Real penetrationDepth;
    };
    
    // Detect collisions between meshes
    std::vector<CollisionInfo> detectCollisions(
        const std::vector<std::shared_ptr<MovingMeshHandler::MovingMesh>>& meshes
    );
    
    // Resolve collisions
    void resolveCollisions(
        std::vector<std::shared_ptr<MovingMeshHandler::MovingMesh>>& meshes,
        const std::vector<CollisionInfo>& collisions,
        Real dt
    );
    
private:
    // Check collision between two meshes
    bool checkMeshCollision(
        const TriangleMesh& mesh1,
        const TriangleMesh& mesh2,
        CollisionInfo& info
    );
    
    // Compute collision response
    void computeCollisionResponse(
        MovingMeshHandler::MovingMesh& mesh1,
        MovingMeshHandler::MovingMesh& mesh2,
        const CollisionInfo& info,
        Real dt
    );
};

} // namespace curvedboundary
} // namespace walberla