#ifndef UTILITY_H
#define UTILITY_H


	 template <class T >
	 inline const T MAX(const T& a ,const T& b)  {
		 return (a >=  b ? a :  b);
	 }

	 template <class T >
	 inline const T MIN(const T& x ,const T& y)  {
		 return (x <=  y ? x :  y);
	 }
	 
	 template <class T >           //function template
	 inline const T MAX3(const T& x,const T& y,const T& z)
	 {
		 return ( (x >= y && x >= z) ? x: ( (y>=x && y>=z)? y:z ) ) ;
	 }

	 template <class T >           //function template
	 inline const T MIN3(const T& x,const T& y,const T& z)
	 {
		 return ( (x <= y && x <= z) ? x: ( (y <= x && y <= z )? y:z ) ) ;
	 }

	 template <class T >           //function template
	 inline const T MAX4(const T& x,const T& y,const T& z,const T& w )
	 {
		 return MAX( MAX(x,y) , MAX(z,w) ) ;
	 }

	 template <class T >           //function template
	 inline const T MIN4(const T& x,const T& y,const T& z,const T& w )
	 {
		 return MIN( MIN(x,y) , MIN(z,w) ) ;
	 }
  
	 template <class T >           //function template
	 inline void MINMAX(const T& x0,const T& x1,const T& x2 , T& min, T& max)
	 {
		 if(x0<min) min=x0; 
		 if(x0>max) max=x0; 
		 if(x1<min) min=x1; 
		 if(x1>max) max=x1; 
		 if(x2<min) min=x2; 
		 if(x2>max) max=x2;
	 }

	 template <class T >           //function template
	 inline void MINMAX3(const T& x0,const T& x1,const T& x2 , T& min, T& max)
	 {
		 min = x0; 
		 max = x0; 
		 if(x1<min) min=x1; 
		 if(x1>max) max=x1; 
		 if(x2<min) min=x2; 
		 if(x2>max) max=x2; 
	 }

	 //!@brief minmax use floor and ceil functions
	 template <class T >           //function template
	 inline void MINMAXFC(const T& x0,const T& x1,const T& x2 , T& min, T& max)
	 {
		 min = floor(x0); max=ceil(x0); 
		 if(floor(x1)<min) min=x1; 
		 if(ceil (x1)>max) max=x1; 
		 if(floor(x2)<min) min=x2; 
		 if(ceil (x2)>max) max=x2; 
	 } 
	 
	 
	 //!@brief return true if the given integer is power of 2, else false
	 inline bool is_PowerOf2(int i)
	 {
	    return i > 0 && (i & (i - 1)) == 0;
	 } 
	  
	  
	  //!@brief return true if the given integer is odd number, else false
	 inline bool IsOdd (const int& i)
	 {
		return ( (i%2) ==1 );
	 }
	    
	  //!@brief return true if the given integer is even number, else false  
	 inline bool is_even(const int& x)
	 {
		return ( x & 1 ) == 0 ;
	 }



//inline FLOAT MAX(FLOAT a, FLOAT b) { return (a > b ? a : b); }
//#define	ABS(x)	 ((x) >= 0 ? x : -(x));
//#define	MAX(x,y)  ((x) > (y) ? (x) : (y));
//#define	MIN(x,y)  ((x) < (y) ? (x) : (y));

//#define NORM(v)    sqrt(dot(v,v))  // norm     = length of vector
//#define D(u,v)     norm(u-v)       // distance = norm of difference


#endif





