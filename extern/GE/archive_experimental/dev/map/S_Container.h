#ifndef S_CONTAINER_HH
#define S_CONTAINER_HH

#include "IfElse.h"

#include <vector>
#include <list> 
#include <deque> 

/*! ============================================================================

	@file Sequence container's 
	
=============================================================================== */

namespace ge
{
	//!=====================================================================
	//!===================				========================
	//!=============	<Sequence container's 	>	================
	//!===================				========================
	//!=====================================================================
  
	// ---------------------------------------------------------------------
	// template parametrization :
	template <  
		  typename Type,  	// obj-type
		  bool isPolymorphic,	// polymorphic or not, if yes obj needs to have virtual clone() function
		  template <typename T, 
		            typename Allocator = std::allocator <T> // for special memo-allocator-optimization (future version)
			    > class Container  = std::vector        // or deque //-> container type, needs to have a [] access member,a - operator,
		 >
	class S_Container //!<- sequence container.
	{
	    public:
	        //!@note IfElse's type defines the actual data type to use for S_Container's vector data type.
	        // if    IfElse<true>  --> Type *
	        // else  IfElse<false> --> Type
		typedef typename IfElse<isPolymorphic,Type *, Type>::type DataType;
		
// 		typedef typename Allocator::reference         reference;
// 		typedef typename Allocator::const_reference   const_reference;
// 		typedef typename Container::iterator          iterator;
// 		typedef typename Container::const_iterator    const_iterator;
// 		typedef typename Allocator::size_type         size_type;
// 		typedef typename Allocator::difference_type   difference_type;
// 		typedef Type                                  value_type;
// 		typedef Allocator                             allocator_type;
// 		typedef typename Allocator::pointer           pointer;
// 		typedef typename Allocator::const_pointer     const_pointer
// 		typedef std::reverse_iterator<iterator>       reverse_iterator;
// 		typedef std::reverse_iterator<const_iterator> const_reverse_iterator;
		
	    private:  
		Container< DataType, std::allocator < DataType > > d_data; // std::vector by default
		
	    private://let the compiler select the appropriate overloaded version based on the template's non-type selector parameter.
		  inline void add(const Type& obj, Int_type<true > );
		  inline void add(const Type& obj, Int_type<false> );
	    public:// public adaptator/wrapp function
		  inline void add(const Type& obj) ;
		  
		  
		  
            // Iteratoren
// 	    iterator       begin();
// 	    const_iterator begin()const;
// 	    iterator       end();
// 	    const_iterator end()const;
// 	
// 	    reverse_iterator       rbegin();
// 	    const_reverse_iterator rbegin()const;
// 	    reverse_iterator       rend();
// 	    const_reverse_iterator rend()const;

		  
	};
	
	
	template <typename Type,bool isPolymorphic, template < typename T, typename  Allocator > class Container >
	inline void S_Container<Type, isPolymorphic, Container >::add(Type const &obj, Int_type<true>)
	{
	      d_data.push_back( obj.clone() ); 
	}

	template <typename Type, bool isPolymorphic, template < typename T, typename  Allocator > class Container >
	inline void S_Container<Type, isPolymorphic, Container >::add(Type const &obj, Int_type<false>)
	{  
	      d_data.push_back( obj );
	}

	template <typename Type, bool isPolymorphic, template < typename T, typename  Allocator > class Container >
	inline void S_Container<Type, isPolymorphic, Container >::add(Type const &obj)
	{ 
	      add(obj, Int_type<isPolymorphic>() );
	}

} // end namespace ge

#endif 
