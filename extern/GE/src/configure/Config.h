#ifndef CONFIG_HH
#define CONFIG_HH


//==============================================================================
// includes
//==============================================================================
#include <cassert>
#include <typeinfo> 	// typeid
#include "../dev/LoggerBase.h"
#include <iostream>
//==============================================================================





//==============================================================================
/*!   
      @brief Some configure utilities for general purpose, the names 
      * are self-explanatory (selbsterklärend)
      <AUTHOR> Mihoubi
*/
//==============================================================================



namespace ge
{
 
	  static LoggerBase gLoggerFile ("ge_login.txt") ;  // global

  
	  //====================================================================
	  // Destructors
	  //====================================================================

	  template <class ElementType>
	  struct ElementDestructor { // rename it to Deleter
	    inline static void destroy(ElementType* e) { 
	      delete e; 
	    }
	  };

	  template <class ElementType>
	  struct EmptyDestructor{
	    inline static void destroy(ElementType* e) 
	    {}
	  };
	  //====================================================================
	  
	  
	  
	  
	  
	  //====================================================================
	  // TypeCheckers
	  //====================================================================

	  template <class ElementType>
	  struct DynamicTypeChecker {
	    inline static void check(const ElementType& e) { 
	      assert(typeid(e)==typeid(ElementType));
	    }
	  };

	  template <class ElementType>
	  struct EmptyTypeChecker {
	    inline static void check(const ElementType& e)
	    {} 	// does no thing
	  };
	  //====================================================================
  
  
  
  
  
  
  
	  //====================================================================
	  // Copiers
	  //!@note the template-parameter "ElementType" 
	  // *	   can also be <=> ( const T) or just (T )
	  //====================================================================

	  template <class ElementType>
	  struct EmptyCopier {
	    inline static ElementType* copy(const ElementType& e) {
		return &e; 
	    }
	  };

	  template <class ElementType>
	  struct PolymorphicCopier {
	    inline static ElementType* copy(const ElementType& e) 
	    { 
		return e.clone(); 
	    }
	  };

	  template <class ElementType>
	  struct MonomorphicCopier
	  {
	    inline static ElementType* copy(const ElementType& e) { 
		return new ElementType(e);
	    }
	  };
	  //====================================================================





	  // Logging utilities,
	  // TODO delete template argument, you don´t need!!!
	  //====================================================================
	  // empty -> no loggin 
	  template <class ElementType>
	  struct EmptyLogger {
	  inline static void Logme(const std::string& msg)
	    {
		  // does no thing
	    }
	  };

	  // pump it out all your looge messages to a file (one)
	  // gLoggerFile -> ge_loggin.txt by default
	  template <class ElementType>
	  struct FileLogger
	  {
	      inline static void Logger(const std::string& msg)
	      { 
		  gLoggerFile.Log(msg);// gLoggerFile is global static object
	      }
	  };
	  
	  
	  template <class ElementType>
	  struct ConsoleLogger
	  {
	    inline static void Logger(const std::string& msg)
	    { 
		  std::clog <<  msg; // std::cerr is also fine, 
	    }
	  };
	  //====================================================================
	  
	  
	  
} // end namespace ge


#endif
