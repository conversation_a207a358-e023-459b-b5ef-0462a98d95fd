#ifndef OBJECTSMANAGER_HH
#define OBJECTSMANAGER_HH

#include <algorithm>
#include <vector>
#include <functional>

class Mesh {}; // forward declaration
// class SectorGrid {} ;// grid of the sector
class transformation {}; // frwd
class voxelization   {}; // frwd


class TriangleMeshObjectBase
{
  public:
    virtual bool transform () = 0 ; // aplying transformation, trenslation rotation...etc
    virtual bool voxelize  () = 0 ; // aplying transformation, but we need world-grid   
};


class TriangleMeshObject : public TriangleMeshObjectBase
{
  public:
	// constructor
	TriangleMeshObject(const char* file_name )
		:m_name( (char*)file_name) {}

  public:
	inline /*virtual*/ bool transform () {std::cout << "start transfomation process ..	" << m_name << std::endl; }
	inline /*virtual*/ bool voxelize  () {std::cout << "start voxelization process ..	" << std::endl; } ;	
  private:
	char* 		m_name ;
	Mesh 		m_mesh ;
	transformation 	m_transformation ;
};


template<typename ObjType = TriangleMeshObject >
class ObjectsManager
{
    public:
	  bool insert (const ObjType* obj) {
		  m_Objects.push_back ( (ObjType*) obj ) ;
	  }
	  // insert
	  // find 
	  // erase, remove
	  inline bool Transform ();
	  inline bool Voxelize  ();    
    private:
	  std::vector< ObjType*  > m_Objects ;// TODO boost::ptr_vector 
};

// =============================================================================
/*! @brief Apply the same transfomation to all objects
	   
*/
// =============================================================================
template<typename ObjType>
bool ObjectsManager<ObjType>::Transform ()
{
      std::for_each(m_Objects.begin(),
		    m_Objects.end(),
		    std::mem_fun(&ObjType::transform )
		    );
}


// =============================================================================
/*! @brief voxelize all objects
	   
*/
// =============================================================================
template<typename ObjType>
bool ObjectsManager<ObjType>::Voxelize ()
{
      std::for_each(m_Objects.begin(),
		    m_Objects.end(),
		    std::mem_fun(&ObjType::voxelize )
		    );
}



#endif
