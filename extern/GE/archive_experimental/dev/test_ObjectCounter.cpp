#include "ObjectCounter.h" 
#include <iostream> 
 
template <typename CharT> 
class MyString : public ObjectCounter<MyString<CharT> >
{ 
//   ... 
}; 
 
int main() 
{ 
   MyString<char> s1, s2;  // 2 object
   MyString<wchar_t> ws;   // onlly 1 object
   
   std::cout << "number of MyString<char>: "        << MyString<char>::live() << std::endl; 
   std::cout << "number of MyString<wchar_t>: "     << ws.live() << std::endl; 
   return (0) ;
} 


// the output 
// number of MyString<char>: 2
// number of MyString<wchar_t>: 1
